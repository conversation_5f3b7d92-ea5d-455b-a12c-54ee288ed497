<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<title>

</title>
<style type = 'text/css'>
<!--
.flxmain_table {table-layout:fixed; border-collapse:collapse;border-spacing: 0}
table.flxmain_table td {overflow:hidden;padding: 0 1.5pt}
.flxmain_bordered_table {table-layout:fixed; border-collapse:collapse;border-spacing: 0;border:1px solid silver}
.flxmain_bordered_table td {overflow:hidden;padding: 0 1.5pt;border:1px solid silver}
 .imagediv {position:absolute;border:none}
 table td.imagecell {vertical-align:top;text-align:left;padding:0}
table td.flxHeading {background-color:#E7E7E7;text-align:center;border: 1px solid black;font-family:helvetica,arial,sans-serif;font-size:10pt}
 .flx0 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx1 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx2 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx3 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx4 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx5 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx6 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx7 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx8 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx9 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx10 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx11 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx12 {
  background-color:#ff8080;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx13 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx14 {
  background-color:#ff8080;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx15 {
  background-color:#ffffcc;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx16 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:center;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx17 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx18 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx19 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx20 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx21 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx22 {
  background-color:#333399;
  color:#003366;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx23 {
  background-color:white;
  color:#003366;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx24 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx25 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx26 {
  background-color:#333399;
  color:#003366;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx27 {
  background-color:#ffffcc;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx28 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx29 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx30 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx31 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx32 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx33 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:宋体;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx34 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx35 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx36 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx37 {
  background-color:silver;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx38 {
  background-color:silver;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx39 {
  background-color:#ff6600;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx40 {
  background-color:#ccccff;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx41 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx42 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:normal;
 }

 .flx43 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx44 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx45 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:normal;
 }

 .flx46 {
  background-color:#993300;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx47 {
  background-color:#993300;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx48 {
  background-color:silver;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx49 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx50 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx51 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx52 {
  background-color:white;
  color:red;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx53 {
  background-color:#ff6600;
  color:black;
  font-size:24pt;
  font-weight:normal;
  font-style:normal;
  font-family:PDCA;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx54 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx55 {
  background-color:white;
  color:black;
  font-size:24pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx56 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx57 {
  background-color:white;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx58 {
  background-color:#ff6600;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx59 {
  background-color:#993300;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx60 {
  background-color:maroon;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx61 {
  background-color:maroon;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx62 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx63 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:宋体;
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx64 {
  background-color:white;
  color:black;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:top;
  white-space:normal;
 }

 .flx65 {
  background-color:white;
  color:black;
  font-size:24pt;
  font-weight:normal;
  font-style:normal;
  font-family:华文行楷;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx66 {
  background-color:white;
  color:black;
  font-size:24pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx67 {
  background-color:#333399;
  color:white;
  font-size:18pt;
  font-weight:normal;
  font-style:normal;
  font-family:'Bosch Office Sans';
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

-->
</style>
</head>
<body>
<table class='flxmain_bordered_table' border='1' cellpadding='0' cellspacing='0' style='width:1758.8pt' summary="Excel Sheet: PUQ_8D_Report">
 <col class='flx0' style ='width:19.34pt;'>
 <col class='flx0' style ='width:9pt;'>
 <col class='flx0' style ='width:90.5pt;'>
 <col class='flx0' style ='width:117.87pt;'>
 <col class='flx0' style ='width:73.93pt;'>
 <col class='flx0' style ='width:9pt;'>
 <col class='flx0' style ='width:58.73pt;'>
 <col class='flx0' style ='width:24.86pt;'>
 <col class='flx0' style ='width:29.72pt;'>
 <col class='flx0' style ='width:33.87pt;'>
 <col class='flx0' style ='width:58.02pt;'>
 <col class='flx0' style ='width:31.1pt;'>
 <col class='flx0' style ='width:73.93pt;'>
 <col class='flx0' style ='width:53.2pt;'>
 <col class='flx0' style ='width:10.38pt;'>
 <col class='flx0' style ='width:46.3pt;'>
 <col class='flx0' style ='width:67.02pt;'>
 <col class='flx0' style ='width:51.82pt;'>
 <col class='flx0' style ='width:49.73pt;'>
 <col class='flx0' style ='width:64.26pt;'>
 <col class='flx0' style ='width:11.76pt;'>
 <col class='flx0' style ='width:104.32pt;'>
 <col class='flx0' style ='width:101.55pt;'>
 <col class='flx0' style ='width:58.73pt;'>
 <col class='flx0' style ='width:60.11pt;'>
 <col class='flx0' style ='width:9pt;'>
 <col class='flx0' style ='width:133.99pt;'>
 <col class='flx0' style ='width:185.82pt;'>
 <col class='flx0' style ='width:64.26pt;'>
 <col class='flx0' style ='width:9pt;'>
 <col class='flx0' style ='width:47.68pt;'>
  <tr style='display:none'>
    <td style = 'padding:0;width:19.34pt;'></td>
    <td style = 'padding:0;width:9pt;'></td>
    <td style = 'padding:0;width:90.5pt;'></td>
    <td style = 'padding:0;width:117.87pt;'></td>
    <td style = 'padding:0;width:73.93pt;'></td>
    <td style = 'padding:0;width:9pt;'></td>
    <td style = 'padding:0;width:58.73pt;'></td>
    <td style = 'padding:0;width:24.86pt;'></td>
    <td style = 'padding:0;width:29.72pt;'></td>
    <td style = 'padding:0;width:33.87pt;'></td>
    <td style = 'padding:0;width:58.02pt;'></td>
    <td style = 'padding:0;width:31.1pt;'></td>
    <td style = 'padding:0;width:73.93pt;'></td>
    <td style = 'padding:0;width:53.2pt;'></td>
    <td style = 'padding:0;width:10.38pt;'></td>
    <td style = 'padding:0;width:46.3pt;'></td>
    <td style = 'padding:0;width:67.02pt;'></td>
    <td style = 'padding:0;width:51.82pt;'></td>
    <td style = 'padding:0;width:49.73pt;'></td>
    <td style = 'padding:0;width:64.26pt;'></td>
    <td style = 'padding:0;width:11.76pt;'></td>
    <td style = 'padding:0;width:104.32pt;'></td>
    <td style = 'padding:0;width:101.55pt;'></td>
    <td style = 'padding:0;width:58.73pt;'></td>
    <td style = 'padding:0;width:60.11pt;'></td>
    <td style = 'padding:0;width:9pt;'></td>
    <td style = 'padding:0;width:133.99pt;'></td>
    <td style = 'padding:0;width:185.82pt;'></td>
    <td style = 'padding:0;width:64.26pt;'></td>
    <td style = 'padding:0;width:9pt;'></td>
    <td style = 'padding:0;width:47.68pt;'></td>
  </tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-top:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:thick solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:24.42pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;' colspan = '26'>The customer must be immediately informed in writing of the next steps following a complaint. 客户必须被立即通知，并通过书面形式跟踪投诉的后续措施。 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx1' style = 'border-top:1px solid black;'></td>
<td class = 'flx1' style = 'border-top:1px solid black;'></td>
<td class = 'flx1' style = 'border-top:1px solid black;'></td>
<td class = 'flx1' style = 'border-top:1px solid black;border-right:1px solid black;'></td>

</tr>
 <tr  style='height:24.42pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;' colspan = '26'>The following deadlines apply as soon as the complaint has been received by RB (1-2-14-60 rule): 一旦博世投诉生成，下列步骤的期限将被实施（1-2-14-60原则） &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class='flx3' style ='border-right:1px none black;'>mandatory field</td>
  <td class='flx4' style ='border-left:1px solid black;border-right:1px solid black;' colspan = '3' rowspan ='1'></td>
</tr>
 <tr  style='height:26.8pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:1px solid black;border-right:1px solid black;' colspan = '30' rowspan ='1'>1working day: Header, D1 and D2 are filled out. 2 working day: D3 is implemented. 14 working day: D4, D5 completed, actions in D6 and D7 defined. 60 working days: entire 8D report is completed</td>
</tr>
 <tr  style='height:24.42pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-bottom:1px solid black;' colspan = '23'>1个工作日内：标题，D1和D2完成； 2个工作日内：D3完成； 14个工作日内：D4，D5完成，D6，D7措施定义；60个工作日内，完整的8D报告完成。</td>
  <td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx1' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx7' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'vertical-align:top;text-align:left;padding:0;border-left:1px solid black;border-top:1px solid black;'>  <img src='data:image/png;base64,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' width='579' height='113' alt="Bosch logo" class = 'imagediv' style='margin-top: 2.22pt; margin-left: 0.71pt; z-index:1;width:434.25pt;height:84.75pt;'  >
</td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;border-right:1px none black;'></td>

  <td class='flx8' style ='border-left:1px solid black;border-top:1px solid black;' colspan = '12' rowspan ='3'><div class='flx8' style ='height:58.56pt;'>PT-PAP/PUQ-Hz 8D Report&nbsp;</div></td>
  <td class = 'flx3' style = 'border-left:1px none black;border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;border-right:1px solid black;'></td>

  <td class='flx9' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '1' rowspan ='6'><div class='flx9' style ='height:119.05pt;'>Days天数</div></td>
</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

  <td class = 'flx3' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

  <td class = 'flx3' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:24.42pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:1px solid black;' colspan = '4'>投诉号<span style ='font-family:"Bosch Office Sans";'>Reference no.:&nbsp;</span></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

  <td class='flx6' style ='border-left:1px solid black;' colspan = '3'>首次发布Initial issue: &nbsp;&nbsp;</td>
  <td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:24.42pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx8' style ='border-left:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '9' rowspan ='2'>Power Tools</td>
  <td class='flx6' style ='border-left:1px solid black;' colspan = '6'>让步接受号Concession no.:</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

  <td class='flx6' style ='border-left:1px solid black;' colspan = '3'>最终报告Final report: &nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:11.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx3' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-right:1px solid black;'></td>

  <td class='flx12' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;padding:0' colspan = '1' rowspan ='39'><div class='flx12' style ='height:960.76pt;'>  <img src='data:image/png;base64,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' width='64' height='1281' alt="Working Day 1&#x0A;一个工作日" style='display: block; border:none; width:48pt;height:960.75pt;' >
</div></td>
</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>

  <td class='flx13' style ='border-left:1px solid black;'><span class='flx13' style ='height:38.79pt;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td>
  <td class='flx13' colspan = '27' rowspan ='1'>标题数据Header data &nbsp;&nbsp;</td>
  <td class = 'flx13' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx8' style = 'border-left:1px solid black;'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8'></td>
<td class = 'flx8' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx15' colspan = '2'>投诉主题Complaint title:</td>
  <td class = 'flx15'></td>
<td class = 'flx6'></td>

  <td class='flx16' style ='border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class = 'flx17'></td>

  <td class='flx15' colspan = '5'>客户投诉Complaint by customer:&nbsp;</td>
  <td class = 'flx15'></td>
<td class = 'flx15'></td>

  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '7' rowspan ='1'></td>
  <td class = 'flx18' style = 'border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx6'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx17'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx15' colspan = '2'>产品Product: &nbsp;&nbsp;</td>
  <td class = 'flx15'></td>
<td class = 'flx6'></td>

  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class = 'flx0'></td>

  <td class='flx15' colspan = '3'>发布者Issuer: &nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>

  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '7' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-top:1px solid white;border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx6'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx15'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px solid white;border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx15' colspan = '2'>博世料Bosch no. &nbsp;</td>
  <td class = 'flx15'></td>
<td class = 'flx6'></td>

  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class = 'flx0'></td>

  <td class='flx15' colspan = '7'>供应商代码/供应商Supplier No. / Supplier: &nbsp;</td>
  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '7' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-top:1px solid white;border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px solid white;border-right:1px solid black;border-bottom:1px solid white;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '27' rowspan ='1'>问题解决团队D1 Problem solving team &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx20' style = 'border-bottom:1px none black;'></td>
<td class = 'flx20' style = 'border-bottom:1px none black;'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx20'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;' colspan = '2'>领导者Team leader: &nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx22'></td>

  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '9' rowspan ='1'></td>
  <td class = 'flx0'></td>

  <td class='flx4' colspan = '3' rowspan ='1'>保证人Sponsor:&nbsp;</td>
  <td class='flx23' style ='border-bottom:1px solid black;' colspan = '9' rowspan ='1'></td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx24' style = 'border-bottom:1px none black;'></td>
<td class = 'flx24' style = 'border-bottom:1px none black;'></td>
<td class = 'flx24'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx24' style = 'border-top:1px none black;'></td>
<td class = 'flx25' style = 'border-top:1px none black;'></td>
<td class = 'flx25' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;' colspan = '3'>小组成员Team members:</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx17' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx21' colspan = '2' rowspan ='1'>质量Quality</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class='flx27' colspan = '3'>物流Logistic</td>
  <td class = 'flx27'></td>
<td class = 'flx27'></td>

  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class='flx11' colspan = '2' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx27' colspan = '2' rowspan ='1'>生产Production</td>
  <td class='flx19' style ='border-top:1px none black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class='flx27' colspan = '5'>设备维护Maintenance</td>
  <td class='flx19' style ='border-top:1px none black;border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class='flx11' colspan = '2' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx27' colspan = '2' rowspan ='1'>工程Engineering</td>
  <td class='flx19' style ='border-top:1px none black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class='flx27' colspan = '3'>其他Others</td>
  <td class = 'flx27'></td>
<td class = 'flx27'></td>

  <td class='flx19' style ='border-top:1px none black;border-bottom:1px solid black;' colspan = '8' rowspan ='1'></td>
  <td class='flx11' colspan = '2' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:12.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx13' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx13' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '4'>问题描述D2 Problem description &nbsp;</td>
  <td class = 'flx28'></td>
<td class = 'flx28'></td>
<td class = 'flx28'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx29'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx6' colspan = '3' rowspan ='1'>投诉日期Complaint Date:</td>
  <td class = 'flx24'></td>

  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx0'></td>

  <td class='flx6' colspan = '6'>生产日期Manufacturing date: &nbsp;</td>
  <td class='flx11' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx0' colspan = '3' rowspan ='1'>送货日期Delivery no.:&nbsp;</td>
  <td class = 'flx31'></td>

  <td class='flx3' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx13'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:24.23pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx0' colspan = '19' rowspan ='1'>问题描述Description of the Problem:</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:48.45pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'><span class='flx32' style ='height:48.45pt;'>When &amp; Who &amp; Where detected? 发现时间，人员，地点？</span></td>
  <td class='flx9' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '12' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-top:1px solid black;'></td>
<td class = 'flx5' style = 'border-top:1px solid black;'></td>
<td class = 'flx5' style = 'border-top:1px solid black;'></td>

  <td class='flx33' style ='border-top:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='5'>附图片</td>
  <td class = 'flx5' style = 'border-top:1px solid black;'></td>
<td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:48.45pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>What is the problem? <br>问题是什么？</td>
  <td class='flx34' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '12' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;'></td>
<td class = 'flx5'></td>
<td class = 'flx5'></td>

  <td class = 'flx5'></td>
<td class = 'flx5' style = 'border-right:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:48.45pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>How detected?<br>怎样发现的？</td>
  <td class='flx34' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '12' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;'></td>
<td class = 'flx5'></td>
<td class = 'flx5'></td>

  <td class = 'flx5'></td>
<td class = 'flx5' style = 'border-right:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:48.45pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>How many defects/ rate?<br>不良数/不良率？</td>
  <td class='flx9' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '12' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;'></td>
<td class = 'flx5'></td>
<td class = 'flx5'></td>

  <td class = 'flx5'></td>
<td class = 'flx5' style = 'border-right:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:115.99pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'><span class='flx32' style ='height:115.99pt;'>Analysis result base on the defect sample or phenomenon (especially for effect at customer side).<br>基于不良样品或现象的分析结果(考虑对装配和终端客户的影响)</span></td>
  <td class='flx34' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '12' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-bottom:1px solid black;'></td>

  <td class = 'flx5' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:51.37pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'><span class='flx32' style ='height:51.37pt;'>Which lot numbers are suspected?哪个批次有怀疑？</span></td>
  <td class='flx34' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:24.23pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='2'>Repeated problem?重复发生的吗？</td>
  <td class='flx9' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx5' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'>&nbsp;If&nbsp;<span style ='color:#ff6600;'>Yes</span>, what is the trend (continuous, random, cyclical):</td>
  <td class='flx35' style ='border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '7' rowspan ='1'>如果是，趋势怎样（连续，很少，循环发生）</td>
  <td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx5' style='height:25.68pt;'>
  <td class = 'flx5' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx5' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class = 'flx5' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-right:1px none black;'></td>

  <td class='flx12' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;padding:0' colspan = '1' rowspan ='14'><div class='flx12' style ='height:637.19pt;'>  <img src='data:image/png;base64,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' width='64' height='850' alt="Working Day 2&#x0A;2个工作日" style='display: block; border:none; width:48pt;height:637.5pt;' >
</div></td>
</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '5'>围堵措施D3 Containment action(s)</td>
  <td class = 'flx28'></td>
<td class = 'flx28'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:77.19pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx4' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>是否有库存 Stock available</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'><span class='flx36' style ='height:77.19pt;'>Quantity/Unit<br>数量/单位</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'><span class='flx36' style ='height:77.19pt;'>Checked qty<span style ='font-family:宋体;'>检查数量</span></span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'><span class='flx36' style ='height:77.19pt;'>Faulty qty<br>不良数量</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'><span class='flx36' style ='height:77.19pt;'>Defect rate (%)<span style ='font-family:宋体;'>不良率</span></span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'><span class='flx36' style ='height:77.19pt;'>Sorting time<br>挑选时间</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>&nbsp;PIC<br>负责人</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>&nbsp;Date<br>时间</td>
  <td class='flx4' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Checking Method</td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.7pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Raw Material<br>原材料</td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.7pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Semi-finished<br>半成品</td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>0.0%</td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.7pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Finished goods<br>成品</td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>#DIV/0!</td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.7pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Parts in transit<br><span style ='font-family:宋体;'>运输途中</span></td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.7pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Stock at Bosch<br><span style ='font-family:宋体;'>博世库存</span><br></td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:89.24pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx37' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'><span class='flx37' style ='height:89.24pt;'>Final products (affected by problem)<span style ='font-family:宋体;'>受影响的成品工具</span><br><br></span></td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx40' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx3' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:89.24pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'><span class='flx32' style ='height:89.24pt;'>Working instructions for sorting/ rework sent to Bosch?<br>挑选/返工的作业指导书是否发给Bosch？</span></td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx41' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;' colspan = '18' rowspan ='1'>Effectiveness of containment action [%]:&nbsp;<span style ='font-family:宋体;'>临时措施的有效性</span><br><br></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px solid black;'></td>

</tr>
 <tr  style='height:85.59pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx43' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'><span class='flx43' style ='height:85.59pt;'>100% sorting/ rework ID label mandatory enclosed to each boxes?<br>100%挑选/返工的标签是否已贴在每个箱子上</span></td>
  <td class='flx39' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx44' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '18' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;page-break-after: always;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx18' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-right:1px none black;'></td>

  <td class='flx46' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;padding:0' colspan = '1' rowspan ='91'><div class='flx46' style ='height:3060.75pt;'>  <img src='data:image/png;base64,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' width='64' height='4081' alt="Working Day 14&#x0A;14个工作日" style='display: block; border:none; width:48pt;height:3060.75pt;' >
</div></td>
</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx28' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '21'>D4 Root cause(s) (RC) and verification of effectiveness: Why could the non-conformity occur ?根本原因分析和验证：为什么不良会发生？</td>
  <td class = 'flx13'></td>
<td class = 'flx13'></td>
<td class = 'flx13'></td>
<td class = 'flx13'></td>
<td class = 'flx13'></td>
<td class = 'flx13'></td>
<td class = 'flx13' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:48.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx36' style ='border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '27' rowspan ='1'>5Why analysis - Potential root causes - OCCURRENCE<br>5why 分析-潜在原因-发生</td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>1st Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>1st Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>2nd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>2nd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>3rd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>3rd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>4th Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>4th Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'>5th Why?</td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px solid black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>5th Why?</td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx11' style ='border-left:1px solid black;border-top:1px solid black;border-right:thick solid black;border-bottom:1px solid black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx11' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '25'>D4 Root cause(s) (RC) and verification of effectiveness: Why has the non-conformity not been detected ? 根本原因分析和验证：为什么不良没有被发现？ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:48.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;'></td>

  <td class='flx36' style ='border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '27' rowspan ='1'>5Why analysis - Potential root causes - NON-DETECTION<br>5why 分析-潜在原因-发现</td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>1st Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>1st Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>2nd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>2nd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx6' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>3rd Why?</td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>3rd Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>4th Why?</td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px dashed black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>4th Why?</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx48' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>5th Why?</td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:thick solid black;border-bottom:1px solid black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>5th Why?</td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:58.26pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx11' style ='border-left:1px solid black;border-top:1px solid black;border-right:thick solid black;border-bottom:1px solid black;' colspan = '14' rowspan ='1'></td>
  <td class='flx48' style ='border-left:thick solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:64.67pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx32' colspan = '7' rowspan ='1'><span class='flx32' style ='height:64.67pt;'>Risk assessment base on root cause：<br>(including effect to other products or process)</span></td>
  <td class='flx0' colspan = '11'>基于根本原因的风险评估，包括对其他产品或流程的影响</td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx5' style = 'border-right:1px none black;'></td>

  <td class='flx35' style ='border:1px solid black;' colspan = '26' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-right:1px solid black;'></td>

</tr>
 <tr class='flx0' style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '14'>永久的改善措施和有效性验证D5 Corrective actions and proof of effectiveness &nbsp;</td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:79.77pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '22' rowspan ='1'><span class='flx32' style ='height:79.77pt;'>Corrective actions plan (What do you do to avoid the same problem in the future? How can you avoid the same problem in the future?---OCCURRENCE<br>改善措施的计划（后续为了避免重复问题发生，你的措施是什么？你要怎样做？）--- 发生</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'>Planned Date<br>计划时间</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Responsible<br>负责人</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx51' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx30' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx52' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx52' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx52' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx52' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx52' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:81.03pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '22' rowspan ='1'><span class='flx32' style ='height:81.03pt;'>Corrective actions plan (How can you detect the problem in the future? How will you control your production?)---NON DETECTION <br>改善措施的计划（后续为了避免重复问题发生，你怎样发现此不良？你怎样控制你的生产）---未发现</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'>Planned Date<br>计划时间</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Responsible<br>负责人</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '22' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;page-break-after: always;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-right:1px none black;'></td>

</tr>
 <tr class='flx0' style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '16'>纠正措施的实施及有效性的跟踪D6 Introduction of corrective actions and tracking of effectiveness</td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:48.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'>Permanent Corrective Action(s)---OCCURRENCE 永久改善措施---发生</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'><span class='flx36' style ='height:48.65pt;'>Responsible<br>负责人</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>&nbsp;Deadline <br>截止日期</td>
  <td class='flx32' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'><span class='flx32' style ='height:48.65pt;'>Status状态<br></span></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx3' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:48.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'>Permanent Corrective Action(s)---NON-DETECTION 永久改善措施---未发现</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'><span class='flx36' style ='height:48.65pt;'>Responsible<br>负责人</span></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>&nbsp;Deadline <br>截止日期</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'><span class='flx36' style ='height:48.65pt;'>Status<br>状态</span></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx54' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx54' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px dashed black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '3' rowspan ='1'></td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px dotted black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px dashed black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px dotted black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx53' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:10.58pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:43.16pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '15'>Tracking of effectiveness of the introduced corrective action(s): 措施的有效性追踪与确认</td>
  <td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Responsible<br>负责人<br></td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Completed on<br>完成时间</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx54' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:8.2pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx3' style ='border-top:1px solid black;border-bottom:1px solid black;' colspan = '27' rowspan ='1'><div class='flx3' style ='height:8.2pt;'></div></td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:44.28pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx21' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '9'>Removal of containment action(s): 围堵措施是否可以取消</td>
  <td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx21' style = 'border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Responsible<br>负责人</td>
  <td class='flx36' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Removed at<br>取消时间</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx35' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx49' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-right:1px none black;'></td>

  <td class='flx31' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '21' rowspan ='1'></td>
  <td class='flx31' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx31' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>

  <td class='flx3' style ='border-top:1px solid black;border-bottom:1px solid black;' colspan = '27' rowspan ='1'><div class='flx3' style ='height:9.66pt;'></div></td>
  <td class = 'flx0' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr class='flx0' style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx13' colspan = '9'>预防重复发生的措施D7 Preventive Corrective Action(s)</td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:48.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx4' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '10' rowspan ='1'>Updated文件更新</td>
  <td class='flx4' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'>Remark备注</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>FMEA</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Updated</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Control Plan</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Updated</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Work instructions</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Updated</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Failure catalogue</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Process flow :&nbsp;</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Updated</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Leason learn :&nbsp;</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx3' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx0' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'>Other : &hellip;&hellip;.</td>
  <td class='flx58' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>Updated</td>
  <td class='flx19' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '17' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

</tr>
 <tr  style='height:27.19pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:27.19pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx6' colspan = '11'>Feedback and Evaluate from lesson learn: 经验教训的反馈和评估</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:27.19pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:27.19pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx10' colspan = '27' rowspan ='1'>类似依靠人为控制的工序，在PFMEA制定时尽可能考虑全面，提前预防不良发生；</td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px solid black;'></td>

</tr>
 <tr class='flx0' style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-top:1px solid black;'></td>
<td class = 'flx0' style = 'border-right:1px solid black;'></td>
<td class = 'flx59' style = 'border-left:1px solid black;border-right:1px solid black;'></td>

</tr>
 <tr class='flx0' style='height:38.79pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx13' colspan = '4'><span class='flx13' style ='height:38.79pt;'>关闭会议D8 Final Meeting &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td>
  <td class='flx0' colspan = '22' rowspan ='1'></td>
  <td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px solid black;'></td>

  <td class='flx60' style ='border-left:1px solid black;border-right:1px solid black;border-bottom:1px solid black;padding:0' colspan = '1' rowspan ='27'><span class='flx60' style ='height:810.25pt;'>  <img src='data:image/png;base64,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' width='64' height='1080' alt="Working Day 60&#x0A;60个工作日" style='display: block; border:none; width:48pt;height:810pt;' >
</span></td>
</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx29'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:30.1pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx62' colspan = '3' rowspan ='1'>参加人员Participants: &nbsp;&nbsp;</td>
  <td class = 'flx6'></td>

  <td class='flx54' style ='border-bottom:1px solid black;' colspan = '23' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:30.1pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:30.1pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx62' colspan = '3' rowspan ='1'>结果Results: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx6'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx18'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:145.65pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx63' style ='border-bottom:1px solid black;' colspan = '23' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx64' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:28.6pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-right:1px none black;'></td>

  <td class='flx62' style ='border-left:1px solid black;' colspan = '6'>Signatures of team member 小组成员的签字</td>
  <td class = 'flx62'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:9.66pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx66' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx6' style ='text-align:right;' colspan = '4' rowspan ='1'></td>
  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx66' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx6' style ='text-align:right;' colspan = '4' rowspan ='1'></td>
  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px solid black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx19' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx49' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx65' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx51'></td>
<td class = 'flx66' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:25.15pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>

  <td class='flx67' colspan = '7' rowspan ='1'>Signatures of plant manager工厂经理的签字</td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:34.96pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

  <td class='flx6' colspan = '2'>Name: &nbsp;</td>
  <td class='flx19' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2'>Date: &nbsp;</td>
  <td class='flx49' style ='border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx51'></td>

  <td class='flx51' colspan = '2' rowspan ='1'>Signature:&nbsp;</td>
  <td class = 'flx51'></td>

  <td class='flx65' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx24'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

</tr>
 <tr  style='height:16.94pt;'>
  <td class = 'flx0' style = 'border-left:thick solid black;border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx6' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx24' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:29.13pt;'>
  <td class = 'flx6' style = 'border-left:thick solid black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;border-bottom:thick solid black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>
<td class = 'flx6' style = 'border-top:1px none black;'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>

</tr>
</table>
</body>
</html>
