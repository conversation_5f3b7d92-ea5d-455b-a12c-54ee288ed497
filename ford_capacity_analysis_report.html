<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>产能分析工具</title>
<style>
  body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
  .container { width: 100%; max-width: 1200px; margin: 0 auto; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); border: 1px solid #c0c0c0; border-radius: 15px; } /* Reverted border to original */
  h1 { text-align: center; font-size: 1.5em; margin-bottom: 20px; } /* Removed blue background, white text, padding and border-radius */
  table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
  th, td { border: 1px solid #a0a0a0; padding: 6px; text-align: left; vertical-align: top; font-size: 0.85em; }
  th { background-color: #e0e0e0; font-weight: bold; }
  .section-title { background-color: #d0d0d0; font-weight: bold; text-align: center; padding: 8px; font-size: 1em; }
  .subsection-title { background-color: #e8e8e8; font-weight: bold; padding: 6px; font-size: 0.9em; }
  input[type="text"], textarea { width: calc(100% - 12px); padding: 4px; border: 1px solid #ccc; border-radius: 2px; font-size: 0.85em; box-sizing: border-box; }
  textarea { resize: vertical; min-height: 30px; }
  .small-input { width: 60px; }
  .center-text { text-align: center; }
  .header-row th, .header-row td { text-align: center; background-color: #e0e0e0; font-weight: bold; }
  .signature-area { display: flex; justify-content: space-between; margin-top: 30px; font-size: 0.8em; }
  .signature-item { flex: 1; text-align: center; padding: 0 10px; }
  .signature-item input { border: none; border-bottom: 1px solid #000; width: 90%; margin-top: 5px; }
  .footer { text-align: center; font-size: 0.7em; color: #666; margin-top: 50px; }
  .logo { text-align: center; margin-top: 20px; }
  .logo img { width: 70px; }
  .checkbox-item { display: flex; align-items: center; justify-content: center; margin-bottom: 5px; }
  .checkbox-item input { margin-right: 5px; width: auto; }
  .random-data { color: #808080; } /* Grey color for random data */
  .nav-buttons { text-align: center; margin-bottom: 20px; background-color: #e0f2f7; padding: 10px; border-radius: 8px; border: 1px solid #007bff; background-color: #e0f2f7; } /* Changed border to blue and kept background color */
  .nav-buttons button { padding: 10px 15px; margin: 0 5px; border: 1px solid #007bff; background-color: #007bff; color: white; border-radius: 5px; cursor: pointer; font-size: 0.9em; }
  .nav-buttons button:hover { background-color: #0056b3; border-color: #0056b3; }
  .nav-buttons button.active { background-color: #0056b3; border-color: #0056b3; }
.chat-box {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 300px;
    background: white;
    border: 1px solid #007bff;
  }
  .chat-messages {
    height: 300px;
    overflow-y: auto;
    padding: 10px;
  }
  .chat-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid #ddd;
  }
  .chat-input input {
    flex: 1;
    margin-right: 10px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .chat-input button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
  }
  </style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
<div class="container">
  <h1>产能分析工具</h1>
<div class="nav-buttons">
  <button class="active">初期产能规划</button>
  <button>共线产能分配规划</button>
  <button>Phase 0 PPAP (节拍生产)</button>
  <button>Phase 3 PPAP (产能验证)</button>
</div>
<div class="nav-buttons">
  <button id="generateRandomData">随机生成数据</button>
  <button id="simulateCalculation">模拟计算OEE</button>
</div>
  <table>
    <tr>
      <td colspan="2" class="section-title">A. 新车型所需OEE (设备综合效率)</td>
      <td colspan="8" class="section-title"></td>
    </tr>
    <tr>
      <td colspan="2" class="subsection-title">A1) 供应商与零件信息</td>
      <td colspan="3" class="subsection-title">A2) 产能需求信息</td>
      <td colspan="3" class="subsection-title">A3) 联系人信息</td>
      <td colspan="2" class="subsection-title"></td>
    </tr>
    <tr>
      <td colspan="2">
        <p>供应商名称: <input type="text" name="supplier_name"></p>
        <p>地址/GSDB代码: <input type="text" name="address_gsdb_code"></p>
        <p>零件名称: <input type="text" name="part_name"></p>
        <p>零件号: <input type="text" name="part_number"></p>
      </td>
      <td colspan="3">
        <p>项目代码: <input type="text" name="project_code"></p>
        <p>车型年份: <input type="text" name="model_year"></p>
        <p>日期: <input type="text" name="date"></p>
        <p>更新的需求量: <input type="text" name="updated_demand"></p>
      </td>
      <td colspan="3">
        <p>姓名: <input type="text" name="contact_name"></p>
        <p>电话: <input type="text" name="phone"></p>
        <p>邮箱: <input type="text" name="email"></p>
      </td>
      <td colspan="2"></td>
    </tr>
    <tr>
      <td colspan="10" class="section-title">Capacity Requirements</td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">A4) 生产规划 & 净可用时间</td>
    </tr>

    <tr class="header-row">
      <th>工序描述</th>
      <td>工序1</td>
      <td>工序2</td>
      <td>工序3</td>
      <td>工序4</td>
      <td>工序5</td>
      <td>工序6</td>
      <td>工序7</td>
      <td>工序8</td>
    </tr>
    <tr>
      <td>B. 每周生产天数</td>
      <td><input type="text" class="small-input" id="weekly_production_days_process1" value="5"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process2" value="5"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process3" value="5"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process4" value="5"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process5"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process6"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process7"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process8"></td>
      <td><input type="text" class="small-input" id="weekly_production_days_process9"></td>
    </tr>
    <tr>
      <td>C. 每天的班次数</td>
      <td><input type="text" class="small-input" id="shifts_per_day_process1" value="3"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process2" value="3"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process3" value="1"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process4" value="1"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process5"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process6"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process7"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process8"></td>
      <td><input type="text" class="small-input" id="shifts_per_day_process9"></td>
    </tr>
    <tr>
      <td>D. 每班总计多少个小时</td>
      <td><input type="text" class="small-input" id="hours_per_shift_process1" value="8"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process2" value="8"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process3" value="8"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process4" value="8"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process5"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process6"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process7"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process8"></td>
      <td><input type="text" class="small-input" id="hours_per_shift_process9"></td>
    </tr>
    <tr>
      <td>E. 计划停机时间 - 就餐, 休息等. (分钟数 / 班)</td>
      <td><input type="text" class="small-input" id="planned_downtime_process1" value="60"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process2" value="60"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process3" value="60"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process4" value="60"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process5"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process6"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process7"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process8"></td>
      <td><input type="text" class="small-input" id="planned_downtime_process9"></td>    </tr>  <tr>
      <td>F. 计划分配到该项目的产能比例 (专线为100%)</td>
      <td><input type="text" class="small-input" value="100%"></td>
      <td><input type="text" class="small-input" value="100%"></td>
      <td><input type="text" class="small-input" value="100%"></td>
      <td><input type="text" class="small-input" value="100%"></td>
      <td><input type="text" class="small-input"></td>
      <td><input type="text" class="small-input"></td>
      <td><input type="text" class="small-input"></td>
      <td><input type="text" class="small-input"></td>
      <td><input type="text" class="small-input"></td>
    </tr>
    <tr>
      <td>G. 净可用时间 (NAT，每周小时数) [B*C*(D-(E/60))*F]</td>
      <td><input type="text" class="small-input" id="net_available_time_process1" value=""></td>
      <td><input type="text" class="small-input" id="net_available_time_process2" value=""></td>
      <td><input type="text" class="small-input" id="net_available_time_process3" value=""></td>
      <td><input type="text" class="small-input" id="net_available_time_process4" value=""></td>
      <td><input type="text" class="small-input" id="net_available_time_process5"></td>
      <td><input type="text" class="small-input" id="net_available_time_process6"></td>
      <td><input type="text" class="small-input" id="net_available_time_process7"></td>
      <td><input type="text" class="small-input" id="net_available_time_process8"></td>
      <td><input type="text" class="small-input" id="net_available_time_process9"></td>
    </tr>
    <tr>
      <td>G1. 每次换型换模计划所需分钟 (into this part #)</td>
      <td><input type="text" class="small-input" id="changeover_time_process1" value=""></td>
      <td><input type="text" class="small-input" id="changeover_time_process2" value=""></td>
      <td><input type="text" class="small-input" id="changeover_time_process3" value=""></td>
      <td><input type="text" class="small-input" id="changeover_time_process4" value="">
      <td><input type="text" class="small-input" id="changeover_time_process5"></td>
      <td><input type="text" class="small-input" id="changeover_time_process6"></td>
      <td><input type="text" class="small-input" id="changeover_time_process7"></td>
      <td><input type="text" class="small-input" id="changeover_time_process8"></td>
      <td><input type="text" class="small-input" id="changeover_time_process9"></td>
    </tr>
    <tr>
      <td>G2. 每周计划换型换模次数 (into this part #)</td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process1" value=""></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process2" value=""></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process3" value=""></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process4" value="">
      <td><input type="text" class="small-input" id="weekly_changeovers_process5"></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process6"></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process7"></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process8"></td>
      <td><input type="text" class="small-input" id="weekly_changeovers_process9"></td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">A5) 每周所需合格品数量</td>
    </tr>
    <tr>
      <td>I. 该段工序的预计报废率(Scrap Rate)</td>
      <td><input type="text" class="small-input" id="scrap_rate_process1" value=""></td>
      <td><input type="text" class="small-input" id="scrap_rate_process2" value=""></td>
      <td><input type="text" class="small-input" id="scrap_rate_process3" value=""></td>
      <td><input type="text" class="small-input" id="scrap_rate_process4" value="">
      <td><input type="text" class="small-input" id="scrap_rate_process5"></td>
      <td><input type="text" class="small-input" id="scrap_rate_process6"></td>
      <td><input type="text" class="small-input" id="scrap_rate_process7"></td>
      <td><input type="text" class="small-input" id="scrap_rate_process8"></td>
      <td><input type="text" class="small-input" id="scrap_rate_process9"></td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">A6) 所需OEE (设备综合效率)</td>
    </tr>
    <tr>
      <td>J. 设备完成一次操作或者出一模所需时间(秒)</td>
      <td><input type="text" class="small-input" id="equipment_cycle_time_process1" value=""></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process2" value=""></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process3" value=""></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process4" value="">
       <td><input type="text" class="small-input" id="equipment_cycle_time_process5"></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process6"></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process7"></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process8"></td>
       <td><input type="text" class="small-input" id="equipment_cycle_time_process9"></td>
     </tr>
     <tr>
       <td>K. 生产节拍（秒/件）</td>
       <td><input type="text" class="small-input" id="production_beat_process1"></td>
       <td><input type="text" class="small-input" id="production_beat_process2"></td>
       <td><input type="text" class="small-input" id="production_beat_process3"></td>
       <td><input type="text" class="small-input" id="production_beat_process4"></td>
       <td><input type="text" class="small-input" id="production_beat_process5"></td>
       <td><input type="text" class="small-input" id="production_beat_process6"></td>
       <td><input type="text" class="small-input" id="production_beat_process7"></td>
       <td><input type="text" class="small-input" id="production_beat_process8"></td>
       <td><input type="text" class="small-input" id="production_beat_process9"></td>
     </tr>
     <tr>
       <td>L. 每个节拍同时使用的模具或设备数量</td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process1" value=""></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process2" value=""></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process3" value=""></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process4" value="">
       <td><input type="text" class="small-input" id="molds_per_cycle_process5"></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process6"></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process7"></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process8"></td>
       <td><input type="text" class="small-input" id="molds_per_cycle_process9"></td>
     </tr>
     <tr>
       <td>M. 模具的行腔数量或者设备每个节拍可以同时生产的数量</td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process1" value=""></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process2" value=""></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process3" value=""></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process4" value="">
       <td><input type="text" class="small-input" id="cavities_per_mold_process5"></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process6"></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process7"></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process8"></td>
       <td><input type="text" class="small-input" id="cavities_per_mold_process9"></td>
     </tr>
     <tr>
       <td>N. 净理想节拍时间(NICT) [K/(LM)]</td>
       <td><span class="small-input" id="nict_process1"></span></td>
       <td><span class="small-input" id="nict_process2"></span></td>
       <td><span class="small-input" id="nict_process3"></span></td>
       <td><span class="small-input" id="nict_process4"></span></td>
       <td><span class="small-input" id="nict_process5"></span></td>
       <td><span class="small-input" id="nict_process6"></span></td>
       <td><span class="small-input" id="nict_process7"></span></td>
       <td><span class="small-input" id="nict_process8"></span></td>
       <td><span class="small-input" id="nict_process9"></span></td>
     </tr>
     <tr>
       <td>P. 理想100% OEE条件下每周生产产品数量 [G×3800/N]</td>
       <td><span class="small-input" id="ideal_weekly_production_process1"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process2"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process3"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process4"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process5"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process6"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process7"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process8"></span></td>
       <td><span class="small-input" id="ideal_weekly_production_process9"></span></td>
     </tr>
     <tr>
       <td>Q. 所需 OEE [J/P]</td>
       <td><span class="small-input" id="required_oee_process1"></span></td>
       <td><span class="small-input" id="required_oee_process2"></span></td>
       <td><span class="small-input" id="required_oee_process3"></span></td>
       <td><span class="small-input" id="required_oee_process4"></span></td>
       <td><span class="small-input" id="required_oee_process5"></span></td>
       <td><span class="small-input" id="required_oee_process6"></span></td>
       <td><span class="small-input" id="required_oee_process7"></span></td>
       <td><span class="small-input" id="required_oee_process8"></span></td>
       <td><span class="small-input" id="required_oee_process9"></span></td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">A7) 共线工序-整体负荷百分比</td>
    </tr>
    <tr>
      <td>U. "Shared Loading Plan"表格输入该工序整体负荷百分比</td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process1" value=""></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process2" value=""></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process3" value=""></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process4" value="">
      <td><input type="text" class="small-input" id="shared_loading_plan_process5"></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process6"></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process7"></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process8"></td>
      <td><input type="text" class="small-input" id="shared_loading_plan_process9"></td>
    </tr>
    <tr>
      <td colspan="10" class="section-title">B. 供应商实际设备综合效率 OEE (Overall Equipment Effectiveness) - 历史数据</td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">B1) 历史数据(参考后面的Historical Mfg Performance表格)</td>
    </tr>
    <tr>
      <td colspan="10">
        <div style="float: left; width: 20%;">
          <p>A 工序描述 (按照价值流的顺序)</p>
          <p>B 每周生产天数</p>
          <p>C 每天的班次数</p>
          <p>D 每班总计多少个小时</p>
          <p>E 计划停机时间 - 就餐, 休息等. (分钟数 / 班)</p>
          <p>F 计划分配到该项目的产能比例 (专线为100%)</p>
          <p>G 净可用时间 (NAT，每周小时数) [B*C*(D-(E/60))*F]</p>
          <p>G1 每次换型换模计划所需分钟 (into this part #)</p>
          <p>G2 每周计划换型换模次数 (into this part #)</p>
        </div>
      </td>
    </tr>
    <tr class="header-row">
      <td>工序描述</td>
      <td>工序1</td>
      <td>工序2</td>
      <td>工序3</td>
      <td>工序4</td>
      <td>工序5</td>
      <td>工序6</td>
      <td>工序7</td>
      <td>工序8</td>
      <td>工序9</td>
    </tr>
    <tr>
      <td>V. 供应商名称</td>
      <td><input type="text" class="small-input" id="supplier_name_process1" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_name_process2" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_name_process3" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_name_process4" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_name_process5"></td>
      <td><input type="text" class="small-input" id="supplier_name_process6"></td>
      <td><input type="text" class="small-input" id="supplier_name_process7"></td>
      <td><input type="text" class="small-input" id="supplier_name_process8"></td>
      <td><input type="text" class="small-input" id="supplier_name_process9"></td>
    </tr>
    <tr>
      <td>W. 供应商地址</td>
      <td><input type="text" class="small-input" id="supplier_address_process1" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_address_process2" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_address_process3" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_address_process4" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_address_process5"></td>
      <td><input type="text" class="small-input" id="supplier_address_process6"></td>
      <td><input type="text" class="small-input" id="supplier_address_process7"></td>
      <td><input type="text" class="small-input" id="supplier_address_process8"></td>
      <td><input type="text" class="small-input" id="supplier_address_process9"></td>
    </tr>
    <tr>
      <td>X. 供应商代码</td>
      <td><input type="text" class="small-input" id="supplier_code_process1" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_code_process2" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_code_process3" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_code_process4" value="10"></td>
      <td><input type="text" class="small-input" id="supplier_code_process5"></td>
      <td><input type="text" class="small-input" id="supplier_code_process6"></td>
      <td><input type="text" class="small-input" id="supplier_code_process7"></td>
      <td><input type="text" class="small-input" id="supplier_code_process8"></td>
      <td><input type="text" class="small-input" id="supplier_code_process9"></td>
    </tr>
    <tr>
      <td>Y. 项目代码</td>
      <td><input type="text" class="small-input" id="project_code_process1" value="10"></td>
      <td><input type="text" class="small-input" id="project_code_process2" value="10"></td>
      <td><input type="text" class="small-input" id="project_code_process3" value="10"></td>
      <td><input type="text" class="small-input" id="project_code_process4" value="10"></td>
      <td><input type="text" class="small-input" id="project_code_process5"></td>
      <td><input type="text" class="small-input" id="project_code_process6"></td>
      <td><input type="text" class="small-input" id="project_code_process7"></td>
      <td><input type="text" class="small-input" id="project_code_process8"></td>
      <td><input type="text" class="small-input" id="project_code_process9"></td>
    </tr>
    <tr>
      <td>Z. 工序名称</td>
      <td><input type="text" class="small-input" id="process_name_process1" value="10"></td>
      <td><input type="text" class="small-input" id="process_name_process2" value="10"></td>
      <td><input type="text" class="small-input" id="process_name_process3" value="10"></td>
      <td><input type="text" class="small-input" id="process_name_process4" value="10"></td>
      <td><input type="text" class="small-input" id="process_name_process5"></td>
      <td><input type="text" class="small-input" id="process_name_process6"></td>
      <td><input type="text" class="small-input" id="process_name_process7"></td>
      <td><input type="text" class="small-input" id="process_name_process8"></td>
      <td><input type="text" class="small-input" id="process_name_process9"></td>
    </tr>
    <tr>
      <td>AA. 历史平均OEE</td>
      <td><input type="text" class="small-input" id="historical_oee_process1" value="10"></td>
      <td><input type="text" class="small-input" id="historical_oee_process2" value="10"></td>
      <td><input type="text" class="small-input" id="historical_oee_process3" value="10"></td>
      <td><input type="text" class="small-input" id="historical_oee_process4" value="10"></td>
      <td><input type="text" class="small-input" id="historical_oee_process5"></td>
      <td><input type="text" class="small-input" id="historical_oee_process6"></td>
      <td><input type="text" class="small-input" id="historical_oee_process7"></td>
      <td><input type="text" class="small-input" id="historical_oee_process8"></td>
      <td><input type="text" class="small-input" id="historical_oee_process9"></td>
    </tr>
    <tr>
      <td>其他需要注明的地方</td>
      <td colspan="9"><textarea></textarea></td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">B2) 该段工序预计每周可生产良品数量</td>
    </tr>
    <tr>
      <td>预计每周良品数量</td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process1" value="10"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process2" value="10"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process3" value="10"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process4" value="10"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process5"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process6"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process7"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process8"></td>
      <td><input type="text" class="small-input" id="estimated_weekly_good_parts_process9"></td>
    </tr>
    <tr>
      <td>所需产能(APW/MPW)</td>
      <td><span class="small-input" id="required_capacity_process1"></span></td>
      <td><span class="small-input" id="required_capacity_process2"></span></td>
      <td><span class="small-input" id="required_capacity_process3"></span></td>
      <td><span class="small-input" id="required_capacity_process4"></span></td>
      <td><span class="small-input" id="required_capacity_process5"></span></td>
      <td><span class="small-input" id="required_capacity_process6"></span></td>
      <td><span class="small-input" id="required_capacity_process7"></span></td>
      <td><span class="small-input" id="required_capacity_process8"></span></td>
      <td><span class="small-input" id="required_capacity_process9"></span></td>
    </tr>
    <tr>
      <td>规划产能</td>
      <td><span class="small-input" id="planned_capacity_process1"></span></td>
      <td><span class="small-input" id="planned_capacity_process2"></span></td>
      <td><span class="small-input" id="planned_capacity_process3"></span></td>
      <td><span class="small-input" id="planned_capacity_process4"></span></td>
      <td><span class="small-input" id="planned_capacity_process5"></span></td>
      <td><span class="small-input" id="planned_capacity_process6"></span></td>
      <td><span class="small-input" id="planned_capacity_process7"></span></td>
      <td><span class="small-input" id="planned_capacity_process8"></span></td>
      <td><span class="small-input" id="planned_capacity_process9"></span></td>
    </tr>
    <tr>
      <td>备注</td>
      <td colspan="9"><textarea></textarea></td>
    </tr>
  </table>



  <table class="capacity-analysis-table">
    <tr>
      <td colspan="10" class="section-title">C. 产能差距分析 - 所需 OEE vs. 实际历史 OEE; 预计每周良品数量</td>
    </tr>
    <tr class="header-row">
      <td></td>
      <td>工序1</td>
      <td>工序2</td>
      <td>工序3</td>
      <td>工序4</td>
      <td>工序5</td>
      <td>工序6</td>
      <td>工序7</td>
      <td>工序8</td>
      <td>工序9</td>
    </tr>
    <tr>
      <td>BB. 所需 OEE</td>
      <td><span class="small-input" id="required_oee_display_process1"></span></td>
      <td><span class="small-input" id="required_oee_display_process2"></span></td>
      <td><span class="small-input" id="required_oee_display_process3"></span></td>
      <td><span class="small-input" id="required_oee_display_process4"></span></td>
      <td><span class="small-input" id="required_oee_display_process5"></span></td>
      <td><span class="small-input" id="required_oee_display_process6"></span></td>
      <td><span class="small-input" id="required_oee_display_process7"></span></td>
      <td><span class="small-input" id="required_oee_display_process8"></span></td>
      <td><span class="small-input" id="required_oee_display_process9"></span></td>
    </tr>
    <tr>
      <td>CC. 实际历史 OEE</td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process1" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process2" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process3" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process4" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process5" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process6" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process7" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process8" value=""></td>
      <td><input type="text" class="small-input" id="actual_historical_oee_process9" value=""></td>
    </tr>
    <tr>
      <td>DD. 产能差距 (APW/MPW)</td>
      <td><span class="small-input" id="capacity_gap_process1"></span></td>
      <td><span class="small-input" id="capacity_gap_process2"></span></td>
      <td><span class="small-input" id="capacity_gap_process3"></span></td>
      <td><span class="small-input" id="capacity_gap_process4"></span></td>
      <td><span class="small-input" id="capacity_gap_process5"></span></td>
      <td><span class="small-input" id="capacity_gap_process6"></span></td>
      <td><span class="small-input" id="capacity_gap_process7"></span></td>
      <td><span class="small-input" id="capacity_gap_process8"></span></td>
      <td><span class="small-input" id="capacity_gap_process9"></span></td>
    </tr>
  </table>

  <div class="signature-area">
    <div class="signature-item">
      <p>供应商签字</p>
      <input type="text" placeholder="">
  </div>
  <script>
    // 这里需要替换为实际的 AI 助手 API 调用代码和凭证
    // 假设这是从阿里云百炼获取的 AI 助手集成代码示例
    // 请根据实际情况替换 API 端点和凭证
    const apiEndpoint = 'YOUR_API_ENDPOINT';
    const apiKey = 'YOUR_API_KEY';
  
    function initAIAssistant() {
      // 初始化 AI 助手的逻辑
      console.log('AI 助手已初始化');
    }
  
    window.addEventListener('load', initAIAssistant);
  </script>

  <button id="chatButton">打开Chatbox</button>
  <div id="chatContainer"></div>
  <table class="ai-analysis-table">
    <tr>
      <td colspan="10" class="section-title">D. AI 分析</td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">D1) OEE 趋势分析与可视化</td>
    </tr>
    <tr>
      <td colspan="10">
        <canvas id="oeeTrendChart"></canvas>
      </td>
    </tr>
    <tr>
      <td colspan="10" class="subsection-title">D2) OEE 异常值检测</td>
    </tr>
    <tr>
      <td colspan="10">
        <div id="oee_anomalies"></div>
      </td>
    </tr>
  </table>
  </body>
  </html>

  <script>
    const API_ENDPOINT = 'YOUR_AI_API_ENDPOINT';
    const API_KEY = 'YOUR_API_KEY';
  
    async function sendMessage(message) {
      // 模拟 AI 回复
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ reply: `您说的是：“${message}”吗？` });
        }, 500);
      });
    }
  
    function createMessageElement(text, isUser) {
      const div = document.createElement('div');
      div.className = `message ${isUser ? 'user' : 'bot'}`;
      div.textContent = text;
      return div;
    }
  
    function openChatbox() {
      const chatBox = document.createElement('div');
      chatBox.className = 'chat-box';
      chatBox.innerHTML = `
        <div class="chat-messages"><div class="message bot">您好！我是您的AI助手，有什么可以帮助您的吗？</div></div>
        <div class="chat-input">
          <input type="text" placeholder="输入消息...">
          <button onclick="sendChatMessage(this)">发送</button>
        </div>
      `;
      document.getElementById('chatContainer').appendChild(chatBox);
    }
  
    async function sendChatMessage(btn) {
      const input = btn.previousElementSibling;
      const message = input.value.trim();
      if (!message) return;
  
      const messagesDiv = btn.parentElement.previousElementSibling;
      messagesDiv.appendChild(createMessageElement(message, true));
      
      input.value = '';
      
      const response = await sendMessage(message);
      if (!response.error) {
        messagesDiv.appendChild(createMessageElement(response.reply, false));
      }
    }

    document.getElementById('generateRandomData').addEventListener('click', function() {
        for (let i = 1; i <= 9; i++) {
            const oeeInput = document.getElementById(`actual_historical_oee_process${i}`);
            if (oeeInput) {
                oeeInput.value = (Math.random() * 100).toFixed(2);
            }
        }
        analyzeOEE(); // 生成数据后重新分析
    });
    document.getElementById('chatButton').addEventListener('click', openChatbox);

    // AI 分析功能  function analyzeOEE() {
        // 获取OEE数据 (从输入框获取)
        const oeeData = [];
        for (let i = 1; i <= 9; i++) {
            const oeeInput = document.getElementById(`actual_historical_oee_process${i}`);
            if (oeeInput && oeeInput.value) {
                oeeData.push(parseFloat(oeeInput.value));
            }
        }

        // 如果没有获取到数据，则不绘制图表并显示提示
        if (oeeData.length === 0) {
            const chartContainer = document.getElementById('oeeTrendChart');
            const parent = chartContainer.parentElement;
            parent.innerHTML = '<p style="text-align: center; color: #888;">请输入OEE历史数据以生成趋势图。</p>';
            document.getElementById('oee_anomalies').innerHTML = ''; // 清空异常值检测结果
            return;
        }

        // 绘制OEE趋势图
        const ctx = document.getElementById('oeeTrendChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: oeeData.map((_, i) => `Process ${i + 1}`),
                datasets: [{
                    label: 'OEE Trend',
                    data: oeeData,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 异常值检测 (简化版)
        const anomalyThreshold = 20; // 假设OEE低于20%为异常
        const anomalies = oeeData.filter(oee => oee < anomalyThreshold);
        const anomalyDetectionDiv = document.getElementById('oee_anomalies');
        if (anomalies.length > 0) {
            anomalyDetectionDiv.innerHTML = `<p style="color: red;">检测到 ${anomalies.length} 个异常OEE值 (低于 ${anomalyThreshold}%): ${anomalies.map(a => a.toFixed(2) + '%').join(', ')}</p>`;
        } else {
            anomalyDetectionDiv.innerHTML = `<p style="color: green;">未检测到异常OEE值。</p>`;
        }

    // 页面加载完成后自动执行AI分析
    window.addEventListener('load', analyzeOEE);
  </script>
</body>
</html>