<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<title>

</title>
<style type = 'text/css'>
<!--
.flxmain_table {table-layout:fixed; border-collapse:collapse;border-spacing: 0}
table.flxmain_table td {overflow:hidden;padding: 0 1.5pt}
.flxmain_bordered_table {table-layout:fixed; border-collapse:collapse;border-spacing: 0;border:1px solid silver}
.flxmain_bordered_table td {overflow:hidden;padding: 0 1.5pt;border:1px solid silver}
 .imagediv {position:absolute;border:none}
 table td.imagecell {vertical-align:top;text-align:left;padding:0}
table td.flxHeading {background-color:#E7E7E7;text-align:center;border: 1px solid black;font-family:helvetica,arial,sans-serif;font-size:10pt}

/* 可编辑输入框样式 */
.editable-input {
  background-color: yellow !important;
  border: 1px solid #000 !important;
  padding: 2px !important;
  font-family: Arial !important;
  font-size: inherit !important;
  width: 100% !important;
  box-sizing: border-box !important;
  outline: none !important;
}

.editable-input:focus {
  background-color: #ffffcc !important;
  border: 2px solid #0066cc !important;
}
 .flx0 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx1 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx2 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx3 {
  background-color:silver;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx4 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx5 {
  background-color:#ffcc99;
  color:black;
  font-size:16pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx6 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx7 {
  background-color:#ffcc99;
  color:black;
  font-size:11pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx8 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx9 {
  background-color:#ffcc99;
  color:blue;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:normal;
 }

 .flx10 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx11 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx12 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx13 {
  background-color:yellow;
  color:black;
  font-size:11pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx14 {
  background-color:#ffcc99;
  color:black;
  font-size:11pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx15 {
  background-color:yellow;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx16 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx17 {
  background-color:yellow;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx18 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx19 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx20 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx21 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx22 {
  background-color:yellow;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx23 {
  background-color:yellow;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx24 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx25 {
  background-color:white;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx26 {
  background-color:#ffcc99;
  color:black;
  font-size:8pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx27 {
  background-color:white;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx28 {
  background-color:gray;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx29 {
  background-color:#ffcc99;
  color:red;
  font-size:16pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx30 {
  background-color:#ffcc99;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx31 {
  background-color:#ffcc99;
  color:red;
  font-size:16pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx32 {
  background-color:#ffcc99;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx33 {
  background-color:red;
  color:black;
  font-size:16pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx34 {
  background-color:red;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx35 {
  background-color:#ffcc99;
  color:red;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx36 {
  background-color:#ffcc99;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx37 {
  background-color:#ffcc99;
  color:black;
  font-size:16pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx38 {
  background-color:#ffcc99;
  color:black;
  font-size:11pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx39 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx40 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx41 {
  background-color:#ffcc99;
  color:black;
  font-size:11pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx42 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx43 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx44 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx45 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx46 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx47 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx48 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx49 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx50 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx51 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx52 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx53 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx54 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx55 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx56 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx57 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx58 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx59 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx60 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx61 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx62 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx63 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx64 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx65 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx66 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx67 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
  cursor: text;
 }

 .flx68 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:normal;
 }

 .flx69 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx70 {
  background-color:red;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx71 {
  background-color:#ed7d31;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx72 {
  background-color:silver;
  color:black;
  font-size:18pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:nowrap;
 }

 .flx73 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx74 {
  background-color:#ffcc99;
  color:black;
  font-size:9pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx75 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx76 {
  background-color:yellow;
  color:black;
  font-size:9pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx77 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx78 {
  background-color:gray;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx79 {
  background-color:#ffcc99;
  color:black;
  font-size:13pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx80 {
  background-color:yellow;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
  cursor: text;
 }

 .flx81 {
  background-color:white;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx82 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx83 {
  background-color:silver;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx84 {
  background-color:silver;
  color:black;
  font-size:9pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx85 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:middle;
  white-space:normal;
 }

 .flx86 {
  background-color:#ffcc99;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:middle;
  white-space:normal;
 }

 .flx87 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx88 {
  background-color:#e3e3e3;
  color:black;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx89 {
  background-color:#e3e3e3;
  color:blue;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx90 {
  background-color:#e3e3e3;
  color:blue;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx91 {
  background-color:white;
  color:black;
  font-size:9pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx92 {
  background-color:#ffcc99;
  color:blue;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:normal;
 }

 .flx93 {
  background-color:#ffcc99;
  color:blue;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx94 {
  background-color:#ffcc99;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:normal;
 }

 .flx95 {
  background-color:#ffcc99;
  color:blue;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx96 {
  background-color:white;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:right;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx97 {
  background-color:#e3e3e3;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:middle;
  white-space:nowrap;
 }

 .flx98 {
  background-color:#ffcc99;
  color:black;
  font-size:12pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx99 {
  background-color:#ffcc99;
  color:blue;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx100 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx101 {
  background-color:yellow;
  color:black;
  font-size:10pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:top;
  white-space:normal;
  cursor: text;
 }

 .flx102 {
  background-color:white;
  color:black;
  font-size:16pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx103 {
  background-color:white;
  color:black;
  font-size:11pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx104 {
  background-color:white;
  color:black;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx105 {
  background-color:white;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:center;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx106 {
  background-color:yellow;
  color:black;
  font-size:12pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
  cursor: text;
 }

 .flx107 {
  background-color:white;
  color:black;
  font-size:14pt;
  font-weight:bold;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

 .flx108 {
  background-color:white;
  color:black;
  font-size:14pt;
  font-weight:normal;
  font-style:normal;
  font-family:Arial;
  text-align:left;
  vertical-align:bottom;
  white-space:nowrap;
 }

-->
</style>
<script type="text/javascript">
// 使黄色填充的单元格可编辑
function makeYellowCellsEditable() {
    // 获取所有黄色填充的单元格
    var yellowCells = document.querySelectorAll('.flx10, .flx13, .flx15, .flx16, .flx17, .flx22, .flx23, .flx44, .flx67, .flx76, .flx80, .flx100, .flx101, .flx106');
    
    yellowCells.forEach(function(cell) {
        // 为每个单元格添加点击事件
        cell.addEventListener('click', function() {
            // 如果单元格已经有输入框，则不重复创建
            if (cell.querySelector('input')) {
                return;
            }
            
            // 获取单元格的原始内容
            var originalContent = cell.innerHTML;
            
            // 创建输入框
            var input = document.createElement('input');
            input.type = 'text';
            input.className = 'editable-input';
            input.value = originalContent.replace(/<[^>]*>/g, '').trim(); // 移除HTML标签
            
            // 设置输入框样式以匹配单元格
            input.style.fontSize = window.getComputedStyle(cell).fontSize;
            input.style.fontWeight = window.getComputedStyle(cell).fontWeight;
            input.style.textAlign = window.getComputedStyle(cell).textAlign;
            
            // 清空单元格内容并添加输入框
            cell.innerHTML = '';
            cell.appendChild(input);
            
            // 聚焦到输入框
            input.focus();
            
            // 选择所有文本
            input.select();
            
            // 处理输入框失去焦点事件
            input.addEventListener('blur', function() {
                var newValue = input.value;
                cell.innerHTML = newValue;
            });
            
            // 处理回车键事件
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    input.blur();
                }
            });
        });
    });
}

// 专门计算G行净可用时间的函数 - 版本2.0
function calculateNATRow() {
    console.log('🧮 === 开始计算G行净可用时间 (版本2.0) ===');

    // 直接通过行号定位到G行
    const rows = document.querySelectorAll('tr');
    console.log(`📊 总共找到 ${rows.length} 行`);

    let gRow = null;
    let bRow = null, cRow = null, dRow = null, eRow = null, fRow = null;

    // 找到各个参数行
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');

        if (cells.length > 1) {
            const label = cells[1].textContent.trim();
            if (label === 'B') {
                bRow = row;
                console.log(`✅ 找到B行在第${i}行`);
            }
            else if (label === 'C') {
                cRow = row;
                console.log(`✅ 找到C行在第${i}行`);
            }
            else if (label === 'D') {
                dRow = row;
                console.log(`✅ 找到D行在第${i}行`);
            }
            else if (label === 'E') {
                eRow = row;
                console.log(`✅ 找到E行在第${i}行`);
            }
            else if (label === 'F') {
                fRow = row;
                console.log(`✅ 找到F行在第${i}行`);
            }
            else if (label === 'G') {
                gRow = row;
                console.log(`✅ 找到G行在第${i}行`);
            }
        }
    }

    if (!gRow || !bRow || !cRow || !dRow || !eRow || !fRow) {
        console.error('❌ 无法找到所需的行');
        console.log(`B行: ${bRow ? '✅' : '❌'}, C行: ${cRow ? '✅' : '❌'}, D行: ${dRow ? '✅' : '❌'}, E行: ${eRow ? '✅' : '❌'}, F行: ${fRow ? '✅' : '❌'}, G行: ${gRow ? '✅' : '❌'}`);
        return;
    }

    console.log('🎯 找到所有必需的行');

    // 获取各行的数据单元格（跳过前面的标签和描述列）
    const bCells = bRow.querySelectorAll('td');
    const cCells = cRow.querySelectorAll('td');
    const dCells = dRow.querySelectorAll('td');
    const eCells = eRow.querySelectorAll('td');
    const fCells = fRow.querySelectorAll('td');
    const gCells = gRow.querySelectorAll('td');

    console.log(`📊 B行单元格数量: ${bCells.length}`);
    console.log(`📊 G行单元格数量: ${gCells.length}`);

    // 显示所有单元格内容来调试
    console.log('🔍 调试：显示B行所有单元格内容');
    for (let i = 0; i < Math.min(bCells.length, 20); i++) {
        console.log(`B行索引${i}: "${bCells[i]?.textContent.trim()}"`);
    }

    console.log('🔍 调试：显示G行所有单元格内容');
    for (let i = 0; i < Math.min(gCells.length, 20); i++) {
        console.log(`G行索引${i}: "${gCells[i]?.textContent.trim()}"`);
    }

    // 根据调试信息修正索引映射
    const processColumns = [
        { name: '工序1-APW', readIndex: 3, writeIndex: 3 },   // B=5 → G=52.50
        { name: '工序1-MPW', readIndex: 4, writeIndex: 4 },   // B=5 → G=63.00
        { name: '工序2-APW', readIndex: 5, writeIndex: 5 },   // B=5 → G=10.50
        { name: '工序2-MPW', readIndex: 6, writeIndex: 6 },   // B=6 → G=63.00 ✅
        { name: '工序3-APW', readIndex: 7, writeIndex: 7 },   // 暂时没有数据
        { name: '工序3-MPW', readIndex: 8, writeIndex: 8 },   // 暂时没有数据
        { name: '工序4-APW', readIndex: 9, writeIndex: 9 },   // 暂时没有数据
        { name: '工序4-MPW', readIndex: 10, writeIndex: 10 }, // 暂时没有数据
        { name: '工序5-APW', readIndex: 11, writeIndex: 11 }, // 暂时没有数据
        { name: '工序5-MPW', readIndex: 12, writeIndex: 12 }  // 暂时没有数据
    ];

    // 计算所有工序列
    for (let i = 0; i < processColumns.length; i++) {
        try {
            const column = processColumns[i];
            const readIndex = column.readIndex;
            const writeIndex = column.writeIndex;

            console.log(`🔍 ${column.name}，读取索引: ${readIndex}，写入索引: ${writeIndex}`);

            // 获取参数值
            const B = parseFloat(bCells[readIndex]?.textContent.trim()) || 0;
            const C = parseFloat(cCells[readIndex]?.textContent.trim()) || 0;
            const D = parseFloat(dCells[readIndex]?.textContent.trim()) || 0;
            const E = parseFloat(eCells[readIndex]?.textContent.trim()) || 0;
            // 解析F行数据（产能分配比例）
            let fValue = fCells[readIndex]?.textContent.trim() || '';
            console.log(`🔍 ${column.name} F行原始值: "${fValue}"`);

            // 移除百分号并转换为小数
            fValue = fValue.replace('%', '');
            const F = parseFloat(fValue) / 100 || 0;
            console.log(`🔍 ${column.name} F行解析后: ${fValue} -> ${F}`);

            console.log(`📋 ${column.name}: B=${B}, C=${C}, D=${D}, E=${E}, F=${F}`);

            // 检查是否有足够的数据进行计算（至少B、C、D、F有值）
            const hasValidData = B > 0 && C > 0 && D > 0 && F > 0;

            if (hasValidData) {
                // 计算净可用时间 G = B*C*(D-(E/60))*F
                const G = B * C * (D - (E / 60)) * F;

                console.log(`🧮 ${column.name}计算结果: G = ${B} * ${C} * (${D} - (${E}/60)) * ${F} = ${G.toFixed(2)}`);

                // 更新G行对应的单元格，并添加视觉效果
                if (gCells[writeIndex]) {
                    const oldValue = gCells[writeIndex].textContent.trim();
                    gCells[writeIndex].textContent = G.toFixed(2);

                    // 添加视觉效果确认更新
                    gCells[writeIndex].style.backgroundColor = 'lightgreen';
                    gCells[writeIndex].style.fontWeight = 'bold';

                    setTimeout(() => {
                        gCells[writeIndex].style.backgroundColor = '';
                        gCells[writeIndex].style.fontWeight = '';
                    }, 800);

                    console.log(`✅ 成功更新${column.name}的净可用时间: ${oldValue} -> ${G.toFixed(2)}`);
                } else {
                    console.log(`❌ 未找到${column.name}的G行单元格，写入索引: ${writeIndex}`);
                }
            } else {
                // 如果没有有效数据，清空G行对应的单元格
                if (gCells[writeIndex]) {
                    const oldValue = gCells[writeIndex].textContent.trim();
                    if (oldValue !== '-' && oldValue !== '') {
                        gCells[writeIndex].textContent = '-';

                        // 添加清空的视觉效果
                        gCells[writeIndex].style.backgroundColor = 'lightcoral';
                        setTimeout(() => {
                            gCells[writeIndex].style.backgroundColor = '';
                        }, 500);

                        console.log(`🧹 清空${column.name}的净可用时间: ${oldValue} -> -`);
                    }
                }
                console.log(`⏭️ ${column.name}数据不完整，清空计算结果`);
            }

        } catch (error) {
            console.error(`❌ 计算${processColumns[i].name}时出错:`, error);
        }
    }
}

// 保留原有的通用计算函数作为备用
function calculateCapacityPlanning() {
    calculateNATRow();
}

// 获取指定工序和行的值
function getProcessValue(processIndex, rowLabel) {
    console.log(`查找工序${processIndex}的${rowLabel}值`);

    // 查找包含行标签的行
    const rows = document.querySelectorAll('tr');
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');

        // 检查是否包含行标签（如 "B", "C", "D" 等）
        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell && cell.textContent.trim() === rowLabel) {
                console.log(`找到标签${rowLabel}在第${i}行第${j}列`);

                // 找到标签后，查找对应工序的数据列
                // 根据表格结构，数据列从标签列后开始
                // 每个工序占2列（colspan=2）
                let dataStartIndex = j + 1;

                // 跳过描述列（通常有colspan）
                while (dataStartIndex < cells.length &&
                       cells[dataStartIndex].getAttribute('colspan') &&
                       parseInt(cells[dataStartIndex].getAttribute('colspan')) > 2) {
                    dataStartIndex++;
                }

                // 计算目标工序的列索引
                let targetIndex = dataStartIndex + processIndex;

                // 考虑colspan的影响
                let currentIndex = dataStartIndex;
                let processCount = 0;

                while (currentIndex < cells.length && processCount <= processIndex) {
                    if (processCount === processIndex) {
                        const targetCell = cells[currentIndex];
                        const value = targetCell ? targetCell.textContent.trim() : '';
                        console.log(`找到${rowLabel}工序${processIndex}值: "${value}"`);
                        return value;
                    }

                    const colspan = cells[currentIndex].getAttribute('colspan');
                    if (colspan && parseInt(colspan) === 2) {
                        processCount++;
                        currentIndex++;
                    } else {
                        currentIndex++;
                    }
                }

                break;
            }
        }
    }

    console.log(`未找到${rowLabel}的值`);
    return '';
}

// 设置指定工序和行的值
function setProcessValue(processIndex, rowLabel, value) {
    console.log(`设置工序${processIndex}的${rowLabel}值为: ${value}`);

    // 查找包含行标签的行
    const rows = document.querySelectorAll('tr');
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');

        // 检查是否包含行标签
        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell && cell.textContent.trim() === rowLabel) {
                console.log(`找到设置标签${rowLabel}在第${i}行第${j}列`);

                // 找到标签后，查找对应工序的数据列
                let dataStartIndex = j + 1;

                // 跳过描述列
                while (dataStartIndex < cells.length &&
                       cells[dataStartIndex].getAttribute('colspan') &&
                       parseInt(cells[dataStartIndex].getAttribute('colspan')) > 2) {
                    dataStartIndex++;
                }

                // 计算目标工序的列索引
                let currentIndex = dataStartIndex;
                let processCount = 0;

                while (currentIndex < cells.length && processCount <= processIndex) {
                    if (processCount === processIndex) {
                        const targetCell = cells[currentIndex];
                        if (targetCell) {
                            targetCell.textContent = value;
                            console.log(`成功设置${rowLabel}工序${processIndex}值为: ${value}`);
                        }
                        return;
                    }

                    const colspan = cells[currentIndex].getAttribute('colspan');
                    if (colspan && parseInt(colspan) === 2) {
                        processCount++;
                        currentIndex++;
                    } else {
                        currentIndex++;
                    }
                }

                break;
            }
        }
    }

    console.log(`未能设置${rowLabel}的值`);
}

// 页面加载完成后初始化功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 页面DOM加载完成');

    makeYellowCellsEditable();

    // 添加自动计算功能
    console.log('🔧 初始化产能规划自动计算功能');

    // 确保F行所有单元格都有百分号
    initializeFRowPercentages();

    // 为可编辑单元格添加计算触发器 - 使用更广泛的选择器
    const editableCells = document.querySelectorAll('td[contenteditable="true"], td.flx10, td.flx13, td.flx15, td.flx16, td.flx17, td.flx22, td.flx23, td.flx44, td.flx67, td.flx76, td.flx80, td.flx100, td.flx101, td.flx106');
    console.log(`📝 找到 ${editableCells.length} 个可编辑单元格`);

    // 额外检查：查找所有黄色背景的单元格（通常是可编辑的）
    const yellowCells = document.querySelectorAll('td[style*="background"], td[bgcolor]');
    console.log(`🟡 找到 ${yellowCells.length} 个有背景色的单元格`);

    // 合并所有可能的可编辑单元格
    const allEditableCells = new Set([...editableCells, ...yellowCells]);
    console.log(`📝 总共找到 ${allEditableCells.size} 个潜在可编辑单元格`);

    // 为所有潜在可编辑单元格添加事件监听器
    allEditableCells.forEach(function(cell, index) {
        console.log(`📌 为第${index + 1}个单元格添加事件监听器`);

        // 检查是否是F行（产能分配比例）的单元格
        const isFRow = cell.closest('tr')?.querySelector('td:nth-child(2)')?.textContent.trim() === 'F';

        // 使用更多的事件类型来确保捕获所有输入
        const events = ['input', 'keyup', 'keydown', 'paste', 'cut'];

        events.forEach(eventType => {
            cell.addEventListener(eventType, function() {
                // 只对相关行进行处理
                const rowLabel = this.closest('tr')?.querySelector('td:nth-child(2)')?.textContent.trim();
                if (['B', 'C', 'D', 'E', 'F'].includes(rowLabel)) {
                    console.log(`⌨️ 检测到${eventType}事件，行${rowLabel}，单元格内容:`, this.textContent);

                    let shouldRecalculate = false;

                    // 如果是F行，处理百分号
                    if (isFRow && (eventType === 'input' || eventType === 'keyup')) {
                        const modified = handlePercentageInput(this);
                        shouldRecalculate = true; // F行输入总是需要重新计算
                    } else if (!isFRow) {
                        shouldRecalculate = true; // 非F行的输入也需要重新计算
                    }

                    // 只在需要时重新计算
                    if (shouldRecalculate) {
                        setTimeout(calculateNATRowIfNeeded, 150);
                    }
                }
            });
        });

        cell.addEventListener('blur', function() {
            // 只对相关行进行处理
            const rowLabel = this.closest('tr')?.querySelector('td:nth-child(2)')?.textContent.trim();
            if (['B', 'C', 'D', 'E', 'F'].includes(rowLabel)) {
                console.log('👁️ 检测到失焦事件，行', rowLabel, '，单元格内容:', this.textContent);

                let shouldRecalculate = false;

                // 如果是F行，确保有百分号
                if (isFRow) {
                    const modified = handlePercentageInput(this);
                    shouldRecalculate = true; // F行失焦总是需要重新计算
                } else {
                    shouldRecalculate = true; // 非F行失焦也需要重新计算
                }

                // 只在需要时重新计算
                if (shouldRecalculate) {
                    setTimeout(calculateNATRowIfNeeded, 100);
                }
            }
        });

        // 添加点击事件用于测试
        cell.addEventListener('click', function() {
            console.log('🖱️ 单元格被点击，内容:', this.textContent);
        });

        // 使单元格可编辑
        if (!cell.hasAttribute('contenteditable')) {
            cell.contentEditable = true;
        }
    });

    // 处理百分比输入的函数
    function handlePercentageInput(cell) {
        let value = cell.textContent.trim();
        console.log(`🔧 处理F行百分号输入: "${value}"`);

        // 移除现有的百分号
        const cleanValue = value.replace('%', '');

        // 如果有数字内容且没有百分号，添加百分号
        if (cleanValue && !isNaN(parseFloat(cleanValue)) && cleanValue !== '-' && !value.includes('%')) {
            const newValue = cleanValue + '%';
            cell.textContent = newValue;
            console.log(`✅ 为F行单元格添加百分号: "${value}" -> "${newValue}"`);
            return true; // 返回true表示进行了修改
        } else {
            console.log(`⏭️ F行单元格不需要添加百分号: "${value}"`);
            return false; // 返回false表示没有修改
        }
    }

    // 存储上次的数据状态，避免重复计算
    let lastDataState = '';

    function shouldRecalculate() {
        // 获取当前所有相关数据的状态
        const rows = document.querySelectorAll('tr');
        let currentState = '';

        ['B', 'C', 'D', 'E', 'F'].forEach(rowLabel => {
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td');

                if (cells.length > 1 && cells[1].textContent.trim() === rowLabel) {
                    // 收集该行的所有数据
                    for (let j = 3; j < Math.min(cells.length, 15); j++) {
                        currentState += cells[j]?.textContent.trim() + '|';
                    }
                    break;
                }
            }
        });

        if (currentState !== lastDataState) {
            lastDataState = currentState;
            return true;
        }
        return false;
    }

    function calculateNATRowIfNeeded() {
        if (shouldRecalculate()) {
            console.log('📊 数据发生变化，开始重新计算');
            calculateNATRow();
        } else {
            console.log('📊 数据未变化，跳过计算');
        }
    }

    // 初始化F行百分号
    function initializeFRowPercentages() {
        console.log('🔧 初始化F行百分号');
        const rows = document.querySelectorAll('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');

            if (cells.length > 1 && cells[1].textContent.trim() === 'F') {
                console.log('📍 找到F行，检查百分号');

                // 检查每个数据单元格
                for (let j = 3; j < Math.min(cells.length, 15); j++) {
                    const cell = cells[j];
                    if (cell && cell.contentEditable === 'true') {
                        handlePercentageInput(cell);
                    }
                }
                break;
            }
        }
    }



    // 添加全局事件监听器作为备用方案（仅用于调试）
    document.addEventListener('input', function(event) {
        const target = event.target;
        if (target.tagName === 'TD' && target.contentEditable === 'true') {
            // 检查是否是相关的计算行（B、C、D、E、F）
            const rowLabel = target.closest('tr')?.querySelector('td:nth-child(2)')?.textContent.trim();
            if (['B', 'C', 'D', 'E', 'F'].includes(rowLabel)) {
                console.log('🌐 全局输入事件捕获（备用），行', rowLabel, '，单元格内容:', target.textContent);
                // 不在这里触发计算，避免重复
            }
        }
    });

    // 添加一个简单的测试按钮
    const testButton = document.createElement('button');
    testButton.textContent = '测试计算';
    testButton.style.cssText = `
        position: fixed;
        top: 50px;
        right: 10px;
        z-index: 1000;
        padding: 10px 15px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    `;
    testButton.addEventListener('click', function() {
        console.log('🔴 测试按钮被点击');
        testDirectUpdate();
    });
    document.body.appendChild(testButton);

    // 创建一个直接更新测试函数
    function testDirectUpdate() {
        console.log('🧪 开始直接更新测试');

        // 重新计算并强制更新所有G行数据
        calculateAndForceUpdate();
    }

    // 强制更新G行数据的函数
    function calculateAndForceUpdate() {
        console.log('🔧 强制更新G行数据');

        // 查找G行
        const rows = document.querySelectorAll('tr');
        let gRow = null;

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');

            if (cells.length > 1 && cells[1].textContent.trim() === 'G') {
                gRow = row;
                console.log(`🎯 找到G行在第${i}行`);
                break;
            }
        }

        if (!gRow) {
            console.log('❌ 未找到G行');
            return;
        }

        // 获取G行的所有数据单元格
        const gCells = gRow.querySelectorAll('td');
        console.log(`📊 G行总共有${gCells.length}个单元格`);

        // 显示所有单元格内容
        gCells.forEach((cell, index) => {
            console.log(`G行单元格${index}: "${cell.textContent.trim()}"`);
        });

        // 查找包含数值的单元格并更新
        const testValues = ['99.99', '88.88', '77.77', '66.66'];
        let updateCount = 0;

        for (let i = 6; i < Math.min(gCells.length, 10); i++) {
            const cell = gCells[i];
            if (cell && updateCount < testValues.length) {
                const oldValue = cell.textContent.trim();
                cell.textContent = testValues[updateCount];

                // 添加视觉效果
                cell.style.backgroundColor = 'yellow';
                cell.style.fontWeight = 'bold';

                console.log(`✅ 更新G行单元格${i}: "${oldValue}" -> "${testValues[updateCount]}"`);

                setTimeout(() => {
                    cell.style.backgroundColor = '';
                    cell.style.fontWeight = '';
                }, 2000);

                updateCount++;
            }
        }

        console.log(`🎉 总共更新了${updateCount}个单元格`);
    }

    // 初始化F行显示百分号
    function initializeFRowPercentages() {
        console.log('🔢 初始化F行百分号显示');

        const rows = document.querySelectorAll('tr');
        let fRow = null;

        // 找到F行
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');

            if (cells.length > 1 && cells[1].textContent.trim() === 'F') {
                fRow = row;
                console.log(`✅ 找到F行在第${i}行`);
                break;
            }
        }

        if (fRow) {
            const fCells = fRow.querySelectorAll('td');
            // 从第3列开始检查数据列
            for (let i = 3; i < fCells.length; i++) {
                const cell = fCells[i];
                if (cell && cell.contentEditable !== 'false') {
                    let value = cell.textContent.trim();
                    // 如果有数字但没有百分号，添加百分号
                    if (value && !value.includes('%') && !isNaN(parseFloat(value)) && value !== '-') {
                        cell.textContent = value + '%';
                        console.log(`✅ 为F行第${i}列添加百分号: ${value} -> ${value}%`);

                        // 触发重新计算
                        setTimeout(calculateNATRowIfNeeded, 100);
                    }
                }
            }
        }
    }

    // 产能需求数据配置
    const requirementData = {
        pa: {
            supplierAPW: 600,
            supplierMPW: 550
        },
        revised: {
            supplierAPW: 0,
            supplierMPW: 0
        }
    };

    // 初始化需求类型选择器
    function initializeRequirementSelector() {
        console.log('📋 初始化需求类型选择器');

        const selector = document.getElementById('requirementType');
        const supplierAPWElement = document.getElementById('supplierAPW');
        const supplierMPWElement = document.getElementById('supplierMPW');

        if (selector && supplierAPWElement && supplierMPWElement) {
            // 设置初始值
            updateRequirementValues('pa');

            // 添加变化事件监听器
            selector.addEventListener('change', function() {
                const selectedType = this.value;
                console.log(`📋 需求类型变更为: ${selectedType}`);
                updateRequirementValues(selectedType);
            });

            console.log('✅ 需求类型选择器初始化完成');
        } else {
            console.log('❌ 未找到需求类型相关元素');
            console.log('selector:', selector);
            console.log('supplierAPWElement:', supplierAPWElement);
            console.log('supplierMPWElement:', supplierMPWElement);
        }
    }

    // 更新需求数值
    function updateRequirementValues(type) {
        const supplierAPWElement = document.getElementById('supplierAPW');
        const supplierMPWElement = document.getElementById('supplierMPW');

        if (supplierAPWElement && supplierMPWElement && requirementData[type]) {
            const data = requirementData[type];

            // 添加动画效果
            supplierAPWElement.style.backgroundColor = 'lightblue';
            supplierMPWElement.style.backgroundColor = 'lightblue';

            // 更新数值
            supplierAPWElement.textContent = data.supplierAPW;
            supplierMPWElement.textContent = data.supplierMPW;

            // 移除动画效果
            setTimeout(() => {
                supplierAPWElement.style.backgroundColor = '';
                supplierMPWElement.style.backgroundColor = '';
            }, 500);

            console.log(`✅ 更新Supplier需求数值: APW=${data.supplierAPW}, MPW=${data.supplierMPW}`);
        } else {
            console.log('❌ 更新数值失败');
            console.log('supplierAPWElement:', supplierAPWElement);
            console.log('supplierMPWElement:', supplierMPWElement);
            console.log('requirementData[type]:', requirementData[type]);
        }
    }

    // 初始计算
    setTimeout(function() {
        console.log('⏰ 页面加载完成，开始初始化');
        initializeRequirementSelector();
        initializeFRowPercentages();
        calculateNATRow();
    }, 1000);

    // 添加手动计算按钮
    const calculateButton = document.createElement('button');
    calculateButton.textContent = '重新计算产能';
    calculateButton.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
        padding: 10px 15px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    `;
    calculateButton.addEventListener('click', calculateNATRow);
    document.body.appendChild(calculateButton);

    // 添加修复F行百分号按钮
    const fixFButton = document.createElement('button');
    fixFButton.textContent = '修复F行百分号';
    fixFButton.style.cssText = `
        position: fixed;
        top: 60px;
        right: 10px;
        z-index: 1000;
        padding: 10px 15px;
        background: #FF9800;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
    `;
    fixFButton.addEventListener('click', function() {
        console.log('🔧 手动修复F行百分号');
        initializeFRowPercentages();
    });
    document.body.appendChild(fixFButton);
});
</script>
</head>
<body>
<table class='flxmain_bordered_table' border='1' cellpadding='0' cellspacing='0' style='width:1507.23pt' summary="Excel Sheet: Capacity Planning (初期产能规划)">
 <col class='flx0' style ='width:3.67pt;'>
 <col class='flx1' style ='width:46.04pt;'>
 <col class='flx0' style ='width:133.22pt;'>
 <col class='flx0' style ='width:93.31pt;'>
 <col class='flx0' span='2' style ='width:63.85pt;'>
 <col class='flx0' span='31' style ='width:35.59pt;'>
  <tr style='display:none'>
    <td style = 'padding:0;width:3.67pt;'></td>
    <td style = 'padding:0;width:46.04pt;'></td>
    <td style = 'padding:0;width:133.22pt;'></td>
    <td style = 'padding:0;width:93.31pt;'></td>
    <td style = 'padding:0;width:63.85pt;'></td>
    <td style = 'padding:0;width:63.85pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
    <td style = 'padding:0;width:35.59pt;'></td>
  </tr>
 <tr  style='height:22.67pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-top:medium solid black;'></td>

  <td class='flx3' style ='border-top:medium solid black;border-bottom:1px none black;' colspan = '11'>A. &nbsp;New Model Required OEE (Overall Equipment Effectiveness) - &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  <td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx4' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:26.22pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px none black;padding-left:8.26pt;' colspan = '3'>A1) 供应商与零件信息</td>
  <td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;border-right:medium solid black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px none black;padding-left:8.26pt;' colspan = '5'>A2) 产能需求信息</td>
  <td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx7' style = 'border-top:medium solid black;'></td>
<td class = 'flx7' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>

  <td class='flx8' style ='border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "Average Production Weekly (APW) is the average weekly quantity of parts specified by Ford to the supplier.  &#x0A;&#x0A;The APW is the quantity of parts the supplier needs to be capable of producing to meet the planned long-term &quot;Average&quot; requirement of the supplier's Ford customer plants.&#x0A;&#x0A;Note: Plant weekly requirements vary">APW</td>
  <td class='flx8' style ='border-top:medium solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "Maximum Production Weekly (MPW) is the maximum weekly quantity of parts specified by Ford to the supplier.  &#x0A;&#x0A;The MPW is the quantity of parts the supplier needs to be capable of producing to meet the planned short-term (up to 90 days) &quot;Peak&quot; requirement of the supplier's Ford customer plants.&#x0A;&#x0A;&#x0A;">MPW</td>
  <td class='flx9' style ='border-top:medium solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1'>&lt;请选择&gt;</td>
  <td class='flx5' style ='border-left:1px none black;border-top:medium solid black;padding-left:8.26pt;' colspan = '4'>A3) 联系人信息</td>
  <td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>
<td class = 'flx6' style = 'border-top:medium solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;text-align:right;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-right:medium solid black;' colspan = '2' rowspan ='1'>供应商名称</td>
  <td class='flx13' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;' colspan = '3' rowspan ='1'>项目代码</td>
  <td class='flx14' colspan = '4' rowspan ='1'>&lt;PA&gt; 节点需求量</td>
  <td class='flx15' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' id="apwValue">600</td>
  <td class='flx15' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' id="mpwValue">550</td>
  <td class='flx17' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'><span class='flx17' style ='height:16.02pt;'>&lt;PA&gt; Requirements</span></td>
  <td class = 'flx6' style = 'border-left:1px none black;'></td>
<td class = 'flx6'></td>
<td class = 'flx6'></td>
<td class = 'flx18'></td>

  <td class='flx8' style ='border-bottom:1px solid black;' colspan = '4' rowspan ='1'>姓名</td>
  <td class='flx8' style ='border-bottom:1px solid black;' colspan = '3' rowspan ='1'>电话</td>
  <td class='flx8' style ='border-bottom:1px solid black;'>邮箱</td>
  <td class = 'flx8' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx8' style = 'border-bottom:1px solid black;'></td>
<td class = 'flx8' style = 'border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;text-align:right;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-right:medium solid black;' colspan = '2' rowspan ='1'>地址 / GSDB代码</td>
  <td class='flx13' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;' colspan = '3' rowspan ='1'>车型年份</td>
  <td class='flx14' style ='border-right:1px solid black;' colspan = '4' rowspan ='1'>更新的需求量</td>
  <td class='flx15' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1'></td>
  <td class='flx15' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1'></td>
  <td class='flx19' style ='border-left:1px none black;border-top:1px none black;border-right:medium solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1'>
    <select id="requirementType" style="width: 100%; height: 100%; border: none; background: transparent; font-size: 12px;">
      <option value="pa">&lt;PA&gt; Requirements</option>
      <option value="revised">Revised Requirements</option>
    </select>
  </td>
  <td class = 'flx6' style = 'border-left:1px none black;'></td>

  <td class='flx21' style ='border-right:1px none black;' colspan = '3'>福特STA工程师</td>
  <td class='flx22' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class = 'flx22' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;text-align:right;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-right:medium solid black;' colspan = '2' rowspan ='1'>零件名称</td>
  <td class='flx11' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx24' style ='border-right:1px solid black;' colspan = '6' rowspan ='1'>APW/MPW来源</td>
  <td class='flx10' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='1'></td>
  <td class = 'flx20' style = 'border-left:1px none black;border-right:medium solid black;'></td>

  <td class='flx21' style ='border-right:1px none black;' colspan = '4'>供应商产能分析工程师</td>
  <td class='flx22' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '3' rowspan ='1'></td>
  <td class = 'flx22' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;text-align:right;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>零件号</td>
  <td class='flx13' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '3' rowspan ='1'></td>
  <td class='flx11' style ='border-left:1px solid black;border-bottom:medium solid black;' colspan = '3' rowspan ='1' title = "Enter date in &quot;dd-mmm-yyyy&quot; format, e.g. 28-Jun-2014&#x0A;&quot;Date of Study&quot;  is the date this CAR form was completed.">日期</td>
  <td class='flx14' style ='border-bottom:medium solid black;' colspan = '5' rowspan ='1'></td>
  <td class='flx26' style ='border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '5' rowspan ='1'></td>
  <td class = 'flx6' style = 'border-left:1px none black;border-bottom:1px none black;'></td>

  <td class='flx21' style ='border-right:1px none black;border-bottom:1px none black;' colspan = '3'>福特采购工程师</td>
  <td class='flx22' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '3' rowspan ='1'></td>
  <td class = 'flx22' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx22' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr class='flx27' style='height:20.39pt;'>
  <td class = 'flx28' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx29' style ='border-left:medium solid black;border-top:1px none black;' colspan = '3'>Capacity Requirements</td>
  <td class = 'flx30' style = 'border-top:medium solid black;'></td>

  <td class='flx31' style ='border-top:medium solid black;' colspan = '9' rowspan ='1'>Supplier to demonstrate APW of</td>
  <td class='flx33' style ='border-top:medium solid black;' colspan = '2' rowspan ='1' id="supplierAPW">600</td>
  <td class='flx29' style ='border-top:medium solid black;' colspan = '12'>parts per week operating no more than 5 days per week</td>
  <td class = 'flx30' style = 'border-top:medium solid black;'></td>
<td class = 'flx30' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>
<td class = 'flx36' style = 'border-top:medium solid black;'></td>

</tr>
 <tr class='flx27' style='height:20.39pt;'>
  <td class = 'flx28' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx29' style = 'border-left:medium solid black;'></td>
<td class = 'flx29'></td>
<td class = 'flx30'></td>
<td class = 'flx30'></td>

  <td class='flx31' colspan = '9' rowspan ='1'>Supplier to demonstrate MPW of</td>
  <td class='flx33' colspan = '2' rowspan ='1' id="supplierMPW">550</td>
  <td class='flx29' colspan = '12'>parts per week operating no more than 6 days per week</td>
  <td class = 'flx30'></td>
<td class = 'flx30'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>
<td class = 'flx36'></td>

</tr>
 <tr class='flx27' style='height:20.39pt;'>
  <td class = 'flx28' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx37' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;' colspan = '4'>Shared Load Required</td>
  <td class='flx38' style ='border-bottom:medium solid black;' colspan = '4'>Shared Load Required</td>
  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

  <td class='flx38' style ='border-bottom:medium solid black;'></td>
  <td class = 'flx30' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx30' style = 'border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx20' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>
<td class = 'flx6' style = 'border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;padding-left:8.26pt;'>A4) &nbsp;</td>
  <td class='flx5' style ='border-top:medium solid black;' colspan = '2'>生产规划 &amp; 净可用时间&nbsp;</td>
  <td class = 'flx12' style = 'border-top:medium solid black;'></td>
<td class = 'flx12' style = 'border-top:medium solid black;border-right:1px none black;'></td>

  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 1</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 2</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 3</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 4</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 5</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 6</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>工序 7</td>
  <td class='flx39' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2'>工序 8</td>
  <td class = 'flx39' style = 'border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx40' style ='border-left:medium solid black;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style ='font-size:11pt;'>&nbsp;</span></td>
  <td class = 'flx5'></td>
<td class = 'flx40'></td>
<td class = 'flx40'></td>
<td class = 'flx40' style = 'border-right:1px none black;'></td>

  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:1px none black;border-bottom:medium solid black;'><span class='flx41' style ='height:16.02pt;'>MPW &nbsp;规划</span></td>
</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>A</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>工序描述 (按照价值流的顺序)</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>注塑</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>装配</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx44' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>&nbsp;</td>
  <td class = 'flx44' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx44' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>B</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>每周生产天数&nbsp;</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>5</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>6</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>5</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>6</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>C</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>每天的班次数</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>3</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>3</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>1</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>1</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>D</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>每班总计多少个小时&nbsp;</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>8</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>8</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>8</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>8</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' contenteditable="true">8</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:26.95pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>E</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>计划停机时间 - 就餐, 休息等. &nbsp;(分钟数 / 班)&nbsp;</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>60</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>60</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>60</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>60</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' contenteditable="true">60</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr class='flx0' style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>F</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1' title = "Where there are 2 or more parts made in the same manufacturing process, an allocation of less than 100% per part is required in this row (F). &#x0A;&#x0A;For shared processes, Allocation % includes changeover time into this part number&#x0A;&#x0A;Where allocation is less than 100% (2 or more parts per process), a Shared Loading Plan must be completed.&#x0A;&#x0A;">计划分配到该项目的产能比例 (专线为100%)</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>50.0%</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>50.0%</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' contenteditable="true">30%</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>30.0%</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' contenteditable="true">30%</td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>G</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>净可用时间 &nbsp;(NAT，每周小时数) [B*C*(D-(E/60))*F]</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>52.50</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>63.00</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>10.50</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>12.60</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:medium solid black;'>-</td>
</tr>
 <tr  style='height:15.29pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>G1</td>
  <td class='flx43' colspan = '4' rowspan ='1'>每次换型换模计划所需分钟 (into this part #)</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;">30</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;">15</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine&#x0A;"></td>
  <td class = 'flx23' style = 'border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' title = "Per tool or machine&#x0A;"></td>
<td class = 'flx23' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>
<td class = 'flx23' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:15.29pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>G2</td>
  <td class='flx43' colspan = '4' rowspan ='1'>每周计划换型换模次数 (into this part #)</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine">2.00</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine">5.00</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine"></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "Per tool or machine"></td>
  <td class = 'flx23' style = 'border-left:medium solid black;border-top:1px none black;border-bottom:medium solid black;' title = "Per tool or machine"></td>
<td class = 'flx23' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx23' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>

</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;padding-left:8.26pt;'>A5)&nbsp;</td>
  <td class='flx5' style ='border-top:medium solid black;' colspan = '2'>每周所需合格品数量</td>
  <td class = 'flx53' style = 'border-top:medium solid black;'></td>
<td class = 'flx53' style = 'border-top:medium solid black;border-right:1px none black;'></td>

  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-bottom:medium solid black;'><span class='flx41' style ='height:23.3pt;'>MPW &nbsp;规划</span></td>
</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>H</td>
  <td class='flx43' colspan = '3' rowspan ='1'>该段工序的预计报废率 (Scrap Rate)</td>
  <td class = 'flx54' style = 'border-right:1px none black;'></td>

  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>3.0%</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>2.5%</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;'></td>
<td class = 'flx23' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>
<td class = 'flx23' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:29.86pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>J</td>
  <td class='flx46' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'><span class='flx46' style ='height:29.86pt;'>为满足下一段工序的需求，该段工序需要生产多少良品<br><span style ='font-weight:normal;'>(考虑每段工序的预计报废率)</span></span></td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 2 equals Process 2 Required Good Parts per Week divided by (100% minus Process 2 scrap rate)">616</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 2 equals Process 2 Required Good Parts per Week divided by (100% minus Process 2 scrap rate)">565</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 3 equals Process 3 Required Good Parts per Week divided by (100% minus Process 3 scrap rate)">600</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 3 equals Process 3 Required Good Parts per Week divided by (100% minus Process 3 scrap rate)">550</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 4 equals Process 4 Required Good Parts per Week divided by (100% minus Process 4 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 4 equals Process 4 Required Good Parts per Week divided by (100% minus Process 4 scrap rate)">-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 5 equals Process 5 Required Good Parts per Week divided by (100% minus Process 5 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 5 equals Process 5 Required Good Parts per Week divided by (100% minus Process 5 scrap rate)">-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 6 equals Process 6 Required Good Parts per Week divided by (100% minus Process 6 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 6 equals Process 6 Required Good Parts per Week divided by (100% minus Process 6 scrap rate)">-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 7 equals Process 7 Required Good Parts per Week divided by (100% minus Process 7 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 7 equals Process 7 Required Good Parts per Week divided by (100% minus Process 7 scrap rate)">-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 8 equals Process 8 Required Good Parts per Week divided by (100% minus Process 8 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 8 equals Process 8 Required Good Parts per Week divided by (100% minus Process 8 scrap rate)">-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1' title = "Material or components required for Process 8 equals Process 8 Required Good Parts per Week divided by (100% minus Process 8 scrap rate)">-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:medium solid black;' title = "Material or components required for Process 8 equals Process 8 Required Good Parts per Week divided by (100% minus Process 8 scrap rate)">-</td>
</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx56' style = 'border-left:medium solid black;'></td>

  <td class='flx43' style ='border-right:medium solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px none black;border-bottom:medium solid black;'>每周平均</td>
  <td class='flx56' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;'>每周最大</td>
  <td class = 'flx56' style = 'border-left:medium solid black;border-top:medium solid black;'></td>
<td class = 'flx56' style = 'border-top:medium solid black;'></td>
<td class = 'flx56' style = 'border-top:medium solid black;'></td>
<td class = 'flx56' style = 'border-top:medium solid black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>
<td class = 'flx56' style = 'border-top:1px none black;'></td>

</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx58' style = 'border-left:medium solid black;'></td>

  <td class='flx59'>注塑</td>
  <td class='flx57' style ='border-right:1px none black;'>需要的来料数量</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px none black;border-bottom:medium solid black;' title = "Material or components required for Process 1 equals Process 1 Required Good Parts per Week divided by (100% minus Process 1 scrap rate)">636</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' title = "Material or components required for Process 1 equals Process 1 Required Good Parts per Week divided by (100% minus Process 1 scrap rate)">583</td>
  <td class = 'flx56' style = 'border-left:1px none black;'></td>
<td class = 'flx56'></td>
<td class = 'flx56'></td>
<td class = 'flx56'></td>

  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class='flx56' colspan = '4' rowspan ='1'></td>
  <td class = 'flx56' style = 'border-right:1px solid black;'></td>
<td class = 'flx56' style = 'border-left:1px solid black;border-right:1px solid black;'></td>
<td class = 'flx56' style = 'border-left:1px solid black;border-right:1px solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx51' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>
<td class = 'flx2' style = 'border-top:1px none black;'></td>

</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;padding-left:8.26pt;' colspan = '3'>A6) 所需OEE&nbsp;<span style ='font-weight:normal;font-size:14pt;'>&nbsp;(设备综合效率)&nbsp;</span></td>
  <td class = 'flx57' style = 'border-top:medium solid black;'></td>
<td class = 'flx57' style = 'border-top:medium solid black;border-right:1px none black;'></td>

  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW &nbsp;规划</td>
  <td class='flx41' style ='border-left:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 规划</td>
  <td class='flx41' style ='border-left:1px solid black;border-bottom:medium solid black;'><span class='flx41' style ='height:23.3pt;'>MPW &nbsp;规划</span></td>
</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>K</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1' title = "For processes with multiple operations, use constraint.&#x0A;">设备完成一次操作或者出一模所需时间 (秒)</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>L</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>每个节拍同时使用的模具或设备数量</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>M</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>模具的行腔数量或者设备每个节拍可以同时生产的数量</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>N</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>净理想节拍时间 &nbsp;(NICT) &nbsp;[K / (L*M)]</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>-</td>
</tr>
 <tr  style='height:19.66pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>P</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1' title = "Assumes no unplanned breakdowns, no changeovers, and maintenance is done outside of planned production time.&#x0A;">理想100% OEE条件下每周生产产品数量 [G x 3600 / N]</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx60' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:1px solid black;'>-</td>
</tr>
 <tr  style='height:28.4pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>Q</td>
  <td class='flx61' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>所需 OEE &nbsp;<span style ='font-weight:normal;'>&nbsp;&nbsp;[J / P]</span></td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-bottom:medium solid black;'>-</td>
</tr>
 <tr  style='height:19.52pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>R</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>返工率 (需要回到该工序进行返工)</td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class='flx23' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx23' style = 'border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;'></td>
<td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;'></td>
<td class = 'flx23' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:32.87pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>S</td>
  <td class='flx63' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'><span class='flx63' style ='height:32.87pt;'>根据换型换模，报废率与返工率的规划，该工序能满足产能需求吗？&nbsp;<span style ='font-size:12pt;'>[J/(100%-H) + (JxR) + (G1x60xG2/N) &lt;= P?]</span></span></td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts">-</td>
  <td class='flx59' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:1px solid black;' title = "If NO, &#x0A;Req'd Good Parts + Scrap + Rework + Parts lost to Changeover &gt; Max Possible Parts&#x0A;">-</td>
</tr>
 <tr  style='height:32.87pt;'>
  <td class = 'flx52' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>T</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'><span class='flx43' style ='height:32.87pt;'>剩余的设备时间开动率以及性能开动率百分比 {P - [(J/(100%-H)) + (JxR) + (G1x60xG2/N)]} / P</span></td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>-</td>
  <td class='flx47' style ='border-left:1px none black;border-top:1px solid black;border-bottom:medium solid black;'>-</td>
</tr>
 <tr class='flx65' style='height:48.65pt;'>
  <td class = 'flx66' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx42' style = 'border-left:medium solid black;'></td>

  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>针对上面的规划，其他需要注明的地方：</td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx67' style = 'border-left:medium solid black;border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx67' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx67' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx52' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx68' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx68' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx68' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx68' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx69' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:medium solid black;border-top:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:medium solid black;'></td>
<td class = 'flx49' style = 'border-top:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;padding-left:8.26pt;' colspan = '3'>A7) 共线工序 - 整体负荷百分比</td>
  <td class = 'flx57'></td>
<td class = 'flx57' style = 'border-right:1px none black;'></td>

  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>注塑</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>装配</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:medium solid black;'>&nbsp;</td>
  <td class = 'flx56' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx56' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:24.13pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>U</td>
  <td class='flx54' style ='border-right:medium solid black;' colspan = '4' rowspan ='1' title = "Where allocation is less than 100%, a Shared  Loading Plan must be completed.">参照 &quot;Shared Loading Plan&quot; 表格输入该工序整体负荷百分比</td>
  <td class='flx70' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx71' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;'>#N/A</td>
</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx48' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx49' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:24.03pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>

  <td class='flx72' style ='border-top:medium solid black;border-bottom:1px none black;' colspan = '14'>B. &nbsp;供应商实际设备综合效率 OEE &nbsp;(Overall Equipment Effectiveness) &nbsp;- 历史数据</td>
  <td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:1px none black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:24.76pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px none black;padding-left:8.26pt;' colspan = '7'>B1) 历史数据 (参考后面的Historical Mfg Performance表格)</td>
  <td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx74' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:24.76pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx75' style = 'border-left:medium solid black;border-top:medium solid black;'></td>

  <td class='flx43' style ='border-top:medium solid black;border-right:medium solid black;' colspan = '4' rowspan ='1'>工序描述</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>注塑</td>
  <td class='flx47' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>装配</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx56' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;'>&nbsp;</td>
  <td class = 'flx56' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>
<td class = 'flx56' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>V</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>供应商名称</td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx76' style = 'border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>W</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>供应商地址</td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx76' style = 'border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx42' style ='border-left:medium solid black;'>X</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>供应商代码 （如果使用兄弟公司数据作参考）</td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:1px none black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px none black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx76' style = 'border-left:medium solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr  style='height:31.31pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx45' style ='border-left:medium solid black;'>Y</td>
  <td class='flx43' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'><span class='flx43' style ='height:31.31pt;'>如果使用兄弟公司数据，请提供项目代码 (~Ford P221)<br>以及工序名称 (~Stamping Press #12)</span></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:1px none black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx76' style ='border-left:medium solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx76' style = 'border-left:medium solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx76' style = 'border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr class='flx77' style='height:18.21pt;'>
  <td class = 'flx78' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx79' style ='border-left:medium solid black;'>Z</td>
  <td class='flx46' style ='border-right:medium solid black;' colspan = '4' rowspan ='1'>历史平均OEE</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='1' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class='flx80' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:1px solid black;' title = "For shared process OEE calculation, Net Available Time must include changeovers">#N/A</td>
  <td class = 'flx80' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>
<td class = 'flx80' style = 'border-top:medium solid black;border-bottom:1px solid black;'></td>

</tr>
 <tr class='flx65' style='height:48.65pt;'>
  <td class = 'flx66' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx45' style = 'border-left:medium solid black;border-bottom:1px none black;'></td>

  <td class='flx63' style ='border-right:medium solid black;border-bottom:1px none black;' colspan = '4' rowspan ='1'><span class='flx63' style ='height:48.65pt;'>其他需要注明的地方<br>(如零件号, 年需求量, 班次安排, etc.)</span></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class='flx67' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx67' style = 'border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx67' style = 'border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx67' style = 'border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr class='flx65' style='height:24.03pt;'>
  <td class = 'flx66' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx5' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;padding-left:8.26pt;' colspan = '5' rowspan ='1' title = "Used to independently evaluate the specific process' potential for producing good parts.  This part estimate does not take into account a blocked condition from a downstream process nor a starved condition from an upstream process.  This part estimate assumes an unlimited supply of parts entering the process and is simply based on the specific process' Demonstrated OEE.  This value does not represent the Overall Process Predicted Good Parts / Week.  For that value, refer to the summary section at the bottom of this tab.">B2)&nbsp;<span style ='font-size:14pt;'>该段工序预计每周可生产良品数量 [P * Z]</span></td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx81' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;'>#N/A</td>
</tr>
 <tr  style='height:4.37pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>
<td class = 'flx82' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx83' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx83' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx83' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx83' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx84' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:24.76pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>

  <td class='flx72' style ='border-top:medium solid black;border-bottom:1px none black;' colspan = '10'>C. &nbsp;产能差距分析 - 所需 OEE vs. 实际历史 OEE; 预计每周良品数量</td>
  <td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>
<td class = 'flx50' style = 'border-top:medium solid black;border-bottom:1px none black;'></td>

</tr>
 <tr  style='height:20.39pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx85' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '5' rowspan ='1'>生产工序描述</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>注塑</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>装配</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>&nbsp;</td>
  <td class='flx59' style ='border-left:medium solid black;border-top:medium solid black;border-bottom:medium solid black;'>&nbsp;</td>
  <td class = 'flx59' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx59' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:21.85pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx85' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '5' rowspan ='1'></td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>MPW 结果</td>
  <td class='flx41' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>APW 结果</td>
  <td class='flx41' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:medium solid black;'><span class='flx41' style ='height:21.85pt;'>MPW 结果</span></td>
</tr>
 <tr  style='height:23.3pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx85' style ='border-left:medium solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '5' rowspan ='1'>实际 OEE&nbsp;<span style ='text-decoration: underline;'>&gt;</span>&nbsp;所需 OEE? (Is Z&nbsp;<span style ='text-decoration: underline;'>&gt;</span>&nbsp;Q?)</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px none black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:medium solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:medium solid black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class='flx87' style ='border-left:1px solid black;border-top:medium solid black;border-bottom:medium solid black;'>#N/A</td>
</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx88' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2'>预计每周良品数量</td>
  <td class='flx89' style ='border-left:1px solid black;border-top:medium solid black;border-right:1px solid black;border-bottom:1px solid black;'>Average<br>平均</td>
  <td class='flx89' style ='border-left:1px solid black;border-top:medium solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>Maximum<br>最大</td>
  <td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;border-left:1px none black;border-top:medium solid black;'>  <img src='data:image/png;base64,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' width='1473' height='366' alt="Chart 5" class = 'imagediv' style='margin-top: 0pt; margin-left: 0pt; z-index:1;width:1104.75pt;height:274.5pt;'  >
</td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx91' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx92' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>所需产能<br>(APW/MPW)</td>
  <td class='flx94' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;'>600</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:medium solid black;border-bottom:1px solid black;' colspan = '2' rowspan ='1'>550</td>
  <td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx91'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx95' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1' title = "These Predicted Good Part / Week APW and MPW values are recorded as PPC (Purchased Part Capacity) in the Ford capacity systems GCP (Vehicle) and MCPV (Powertrain).  &#x0A;&#x0A;If any OEE &gt; 100% in this report, the analysis must be corrected prior to entering the PPC values into the Ford system.  Contact your STA engineer for assistance.&#x0A;&#x0A;Note: Estimated to be the lowest value in row B5 reduced by planned scrap losses (row H) of successive operations.&#x0A;&#x0A;Note 2: The APPC/MPPC values are calculated using the planned scrap rate, not the demonstrated scrap rate; and if the actual scrap / rework rates are higher than the planned rates there is further risk to the capacity.">规划产能</td>
  <td class='flx94' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;'>#N/A</td>
  <td class='flx55' style ='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px none black;' colspan = '2' rowspan ='1'>#N/A</td>
  <td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx96'></td>
<td class = 'flx91'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx95' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx93' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx97' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx97' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx97' style = 'border-top:1px solid black;border-bottom:medium solid black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:16.75pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx75' style ='border-left:medium solid black;border-top:1px none black;'>备注</td>
  <td class = 'flx98' style = 'border-top:1px none black;'></td>

  <td class='flx99' style ='border-top:1px none black;'>&nbsp;&nbsp;&nbsp;&nbsp;产能调查编号:</td>
  <td class = 'flx6' style = 'border-top:1px none black;border-right:1px none black;'></td>
<td class = 'flx100' style = 'border-left:1px solid black;border-top:1px none black;border-right:1px solid black;border-bottom:1px solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class='flx101' style ='border-left:medium solid black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '5' rowspan ='4'></td>
  <td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:32.04pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:1px none black;'></td>

  <td class = 'flx0' style = 'border-left:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:3.64pt;page-break-after: always;'>
  <td class = 'flx2' style = 'border-left:medium solid black;'></td>
<td class = 'flx51' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'vertical-align:top;text-align:left;padding:0;border-top:medium solid black;border-bottom:medium solid black;'>  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAABCAYAAACBr8MpAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAMSURBVChTYxgFDAwAAQUAAannnkUAAAAASUVORK5CYII=' width='65' height='1' alt="Text Box 143" class = 'imagediv' style='margin-top: 3.64pt; margin-left: 23.67pt; z-index:2;width:48.75pt;height:0.75pt;'  >
  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAABCAYAAACBr8MpAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAMSURBVChTYxgFDAwAAQUAAannnkUAAAAASUVORK5CYII=' width='65' height='1' alt="Text Box 144" class = 'imagediv' style='margin-top: 3.64pt; margin-left: 23.67pt; z-index:3;width:48.75pt;height:0.75pt;'  >
  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAABCAYAAAB35kaxAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAANSURBVBhXYxj6gIEBAADFAAFM4iPrAAAAAElFTkSuQmCC' width='49' height='1' alt="Text Box 145" class = 'imagediv' style='margin-top: 3.64pt; margin-left: 35.59pt; z-index:4;width:36.75pt;height:0.75pt;'  >
  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAABCAYAAAC2YQwdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAANSURBVBhXYxhcgIEBAACRAAGw6V7hAAAAAElFTkSuQmCC' width='36' height='1' alt="Text Box 146" class = 'imagediv' style='margin-top: 3.64pt; margin-left: 35.59pt; z-index:5;width:27pt;height:0.75pt;'  >
</td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:20.39pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx102' style ='text-decoration:underline;border-left:medium solid black;border-top:medium solid black;' colspan = '7' rowspan ='1'>供应商签字&nbsp;</td>
  <td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx103' style = 'text-decoration:underline;border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;border-right:1px none black;'></td>

  <td class='flx102' style ='text-decoration:underline;border-left:medium solid black;border-top:medium solid black;' colspan = '4'>FOR STA USE</td>
  <td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:medium solid black;'></td>

</tr>
 <tr  style='height:18.21pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx0' style ='border-left:medium solid black;text-align:right;' colspan = '4' rowspan ='1'></td>
  <td class = 'flx0'></td>

  <td class='flx104' colspan = '7' rowspan ='1'></td>
  <td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>

  <td class='flx105' colspan = '4' rowspan ='1'>Version 5.0</td>
  <td class = 'flx0' style = 'border-right:1px none black;'></td>
<td class = 'flx0' style = 'border-left:medium solid black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;'>  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAAA3CAYAAAAPBmS9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALJSURBVHhe7ZaBzdQwDEZvABZgARZgASZgAjZgA1ZgBWZgCbZgGehDPOmTScud2kP60fck65rEqR3bce9WSimllFJKKaWUUkoppZRSSimllPKf8GqTryFvNyn3Y9w+/Ro9ifeb/Aj5vEm5H+NGop7Gt00ySQi3q9yHMXtakl5vopFMFrdrwnX2apPEjzG+Wv/NJl9+P8/2i8+pyzNz4tqqI6QPyYdN9t6X4Ld+sYdzGbP5zsvAkEYyYSuDzLm+un0zKGf0UzKhPK90EBIL80yJ89omyCvfkFlI7Jk6ufdpSfq+icaAKtLoPOAMIod4t0k6mlV/Rh8d1qxWyCJinXchngFBJ/XYL+g6zzNk4PEPm9wU54xBFoe2cw55SpKoPA14mJwjYUkGMYObQeGAckbfW5FkQA0erHy2EPiV3A/Zqgh44jztEdI3iwYyUU9J0t6t8YBUaJKOTlzLPVfqgzcGvclMSgbPszm21VE4zrGP9yrOa2uOk6O10/hyDSjZPmwLwJrzk9XalfrgGnqTuTdvCV1i1eoySXuirTlOjtZOkU4fyV47mqzWrtQH19CbrPb6bWEtu4Zkkkgk4ym2XfVWto/WTrH6p7In9uAMRLZHWLWiM/orXM8WKe7LosqWp738zq6+yXvMdiqcy3fkWU6TrQCjqwrKyvMAGcS9YORhz+ivQHe1L33l3ZLnVOYfEpPHr8WIDr4gtsY9G95WBP3L2AtSkhVi5WYQnbfCHHtQOKO/R+7nOcf+IUiyY3iOZLb96bP+zYRjl/fl3KVJyuzPNpSkntXlOBON4PSs0jP6exCsVavONpZkEvZ0pm8Ivkz/GM/E5N5Lk5Rt7QgSqB7PM4gEjLV5GHlUn3nt/Q3fhfB8xKN6R4UL6onjvXP9U+6p9ORR/XIBTdILoEl6ATzyzYBH9UsppZRSSimllFJKKaWUUkopf3C7/QQCygc7Rs8+6AAAAABJRU5ErkJggg==' width='105' height='55' alt="Text Box 34" class = 'imagediv' style='margin-top: 8pt; margin-left: 29.2pt; z-index:7;width:78.75pt;height:41.25pt;'  >
</td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:16.02pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx106' style ='text-decoration:underline;border-left:medium solid black;border-bottom:1px solid black;text-align:right;' colspan = '4' rowspan ='2'></td>
  <td class = 'flx104'></td>

  <td class='flx106' style ='border-bottom:1px solid black;' colspan = '7' rowspan ='2'></td>
  <td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;'>  <img src='data:image/png;base64,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' width='136' height='53' alt="Picture 213" class = 'imagediv' style='margin-top: 13.82pt; margin-left: 18.14pt; z-index:6;width:102pt;height:39.75pt;'  >
</td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

<td class='flx17' style='border-left:1px solid black;border-top:1px solid black;border-right:1px solid black;border-bottom:1px solid black;' colspan='2' rowspan='1'>
  <select style="color:#000;font-size:inherit;width:100%;border:none;background:transparent;">
    <option value="PA">&lt;PA&gt; Requirements</option>
    <option value="Revised">Revised Requirements</option>
  </select>
</td>

  <td class='flx17' style ='border-bottom:1px solid black;text-align:right;' colspan = '6' rowspan ='2'></td>
  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:12.38pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class = 'flx104'></td>

  <td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx77'></td>
<td class = 'flx77' style = 'border-right:1px none black;'></td>

  <td class = 'flx0'></td>

  <td class = 'flx0'></td>
<td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;'>  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAYCAYAAACWTY9zAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACLSURBVEhL7c1BDcMwFAPQcIgUHJNCoESLJRAKZRDSWjYA9+Adpv8k32y5lfJrY4yz9365QV/TuL3WsoM+Z3n7DfQ5y9OlB33O8nTpQZ+zPF160OcsT5ce9DnL06UHfc7ydOlBn7M8XXrQ5yxPlx70OcvTpQd9zvL2nPPrBn3O8j5PjhdBv5Ty51q7AcQjEvHW8iyqAAAAAElFTkSuQmCC' width='38' height='24' alt="Check Box 29" class = 'imagediv' style='margin-top: 2.18pt; margin-left: 7.89pt; z-index:9;width:28.5pt;height:18pt;'  >
</td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:16.85pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx107' style ='border-left:medium solid black;border-top:1px solid black;' colspan = '3'>Authorized Representative 姓名 / 职务</td>
  <td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>

  <td class='flx107' style ='border-top:1px none black;'>邮箱</td>
  <td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx77'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

  <td class='flx107' style ='border-left:medium solid black;border-top:1px none black;' colspan = '3'>Site Engineer</td>
  <td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>

  <td class='flx107' style ='border-top:1px solid black;' colspan = '4'>STA LL6 Supervisor</td>
  <td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;'>  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAAAwCAYAAAASA1QFAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKfSURBVHhe7ZgJTQRBEEVXAAYwgAEMoAAFOMABFrCABkzgAjPQD/jJT6Xn6F12diD/JZUw3XV0VfWx4RBCCCGEEEIIIYQQQgghhBDCEdw0uZ2Rqyan4P635NR1V5QD+WzOa5OPBUHn2KTd/xawTmL+9qbwWmzOmiYhb02O4akJMbZIjl3+3oT1/tsmqZgSGqM55K5JRdfA1Elbuu6W7J3rJlO+iPPcRGt9aNLzuzae1i3kl7psDkG1gB4kq/lHBn6gYRqXUKSa/JT/tfZAwdyP5L6J6M0jKvTaeH4aJcTR37tsEklqXk3yhEnIfdRrsed/xJ4iag5xXQRfwLXqxcUPuhR9bTxOqsY1VxuG7eb4gnUVSEiOhWpeBdHCX76+vqEY0pva4WLEno2hcXTAdd2H67J+sTaeX5e+Buw0fvEmzYl2nCene1+iYpCsqE0atdcmqcXBVptG9Jo0Ek/ffrrAT9hum4QOCwWS6+m4eCLuH0bte2NT9Jo0Eq9+O3NzZ4egvgD/RvQOCU+aHScbF94Hwbf0YdReuowvsdSkpXjSY6wyN3d2CKoFCB5rXTOIF82TrtdNj+p/1F66tTi9X4FLTVqKJ70/0SSojVKC/mvL3w6gIIiuRqj+R+390fbGsDbeEN4Z0WvSSLypWLv+Ce6PLqJkSFZjFIZEfUwFgp7/EXv+1jhF5Nub4UXzYqLDxqLYa+OhrzE2Ad/uE9ldk6BXEBL3U+ZC8k7P/4g9eEGrsJEEm6jO04CReL1YbnuRJvHeEHguODtYOtp1JM5Vo3GS05zDnBJ01toL5tCRPrb4qKCn9aKv0z8Sj9NT4+jb3+d/A4nRIN6PsDPYwX7H06ywM/yXEuL/Zgk7we//NCiEEEIIIYQQQgghhBDCOTkcPgEKNutY/amowQAAAABJRU5ErkJggg==' width='105' height='48' alt="Text Box 35" class = 'imagediv' style='margin-top: 12.35pt; margin-left: 29.2pt; z-index:8;width:78.75pt;height:36pt;'  >
</td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>

</tr>
 <tr  style='height:15.1pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx106' style ='border-left:medium solid black;border-bottom:1px solid black;' colspan = '4' rowspan ='3'></td>
  <td class='flx16' style ='border-bottom:1px solid black;' colspan = '1' rowspan ='3'></td>
  <td class='flx104' style ='text-align:right;' colspan = '3' rowspan ='3'></td>
  <td class = 'flx104'></td>

  <td class='flx106' style ='border-bottom:1px solid black;' colspan = '3' rowspan ='3'></td>
  <td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>
<td class = 'flx104' style = 'border-left:medium solid black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx104'></td>
<td class = 'flx104'></td>

</tr>
 <tr  style='height:15.1pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class = 'flx104'></td>

  <td class = 'flx0' style = 'border-right:medium solid black;'></td>
<td class = 'flx0' style = 'border-left:1px none black;'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx0' style = 'border-right:1px none black;'></td>

  <td class='flx10' style ='border-left:medium solid black;border-bottom:1px solid black;' colspan = '6' rowspan ='2'></td>
  <td class = 'flx0'></td>

  <td class='flx10' style ='border-bottom:1px solid black;' colspan = '6' rowspan ='2'></td>
  <td class = 'flx0'></td>
<td class = 'flx0' style = 'vertical-align:top;text-align:left;padding:0;'>  <img src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAqCAYAAAA0yJLWAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACASURBVFhH7dUxCsAwFALQHLU3SW+eVnRswQxCBh/8TXH8o6qqqs50vXdvHPJxa85pH/KsZa0dyLOWpTkP8qxlac6DPGtZmvMgz1qW5jzIs5alOQ/yrGVpzoM8a1ma8yDPWpbmPMizlqU5D/KsZX2+k79DnrWsIx9yVVVV1ZHGeACiORF3gqKL4gAAAABJRU5ErkJggg==' width='37' height='42' alt="Check Box 29" class = 'imagediv' style='margin-top: 6.57pt; margin-left: 7.89pt; z-index:10;width:27.75pt;height:31.5pt;'  >
</td>
<td class = 'flx104'></td>
<td class = 'flx104'></td>

</tr>
 <tr  style='height:16.85pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class = 'flx104'></td>

  <td class = 'flx0' style = 'border-right:medium solid black;'></td>

  <td class='flx108' style ='border-left:1px none black;' colspan = '6'>&nbsp;&copy; 2014 Ford Motor Company</td>
  <td class = 'flx0'></td>

  <td class = 'flx0'></td>
<td class = 'flx0'></td>
<td class = 'flx104'></td>
<td class = 'flx104'></td>

</tr>
 <tr  style='height:17.48pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-right:medium solid black;'></td>

  <td class='flx107' style ='border-left:medium solid black;border-top:1px solid black;border-bottom:medium solid black;' colspan = '2'>（签名）</td>
  <td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>

  <td class='flx107' style ='border-top:1px none black;border-bottom:medium solid black;'>日期</td>
  <td class = 'flx104' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx107' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>

  <td class='flx107' style ='border-top:1px none black;border-right:medium solid black;border-bottom:medium solid black;' colspan = '4' rowspan ='1'>电话号码</td>
  <td class = 'flx0' style = 'border-left:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx77' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-right:1px none black;border-bottom:medium solid black;'></td>

  <td class='flx107' style ='border-left:medium solid black;border-top:1px none black;border-bottom:medium solid black;' colspan = '3'>Signature/Date</td>
  <td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx77' style = 'border-bottom:medium solid black;'></td>

  <td class='flx107' style ='border-top:1px solid black;border-bottom:medium solid black;' colspan = '3'>Signature/Date</td>
  <td class = 'flx0' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx77' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx107' style = 'border-top:1px none black;border-bottom:medium solid black;'></td>
<td class = 'flx104' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx0' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx107' style = 'border-bottom:medium solid black;'></td>
<td class = 'flx104' style = 'border-bottom:medium solid black;'></td>

</tr>
 <tr  style='height:3.64pt;'>
  <td class = 'flx2' style = 'border-left:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx51' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>
<td class = 'flx2' style = 'border-top:medium solid black;border-bottom:medium solid black;'></td>

</tr>
</table>
</body>
</html>
