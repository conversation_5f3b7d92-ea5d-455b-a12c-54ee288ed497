<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A3问题解决工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.9/marked.min.js"></script>
    <style>
        body {
            display: flex; /* 使用flex布局 */
            flex-direction: column; /* 垂直布局 */
            margin: 0; /* 移除body默认外边距 */
            background-color: #f4f4f4;
            color: #333;
            font-family: Arial, sans-serif;
            line-height: 1.6;
            min-height: 100vh; /* 确保body至少占满整个视口高度 */
        }
        body > h1 {
            text-align: center;
            margin: 0 0 20px 0; /* 调整底部外边距 */
            color: #fff;
            font-size: 2em;
            background-color: #0056b3; /* 深蓝色背景 */
            border-radius: 10px; /* 圆角 */
            padding: 10px 20px; /* 内边距 */
            display: inline-block; /* 使背景框适应内容 */
        }
        .main-content {
            display: flex;
            flex-grow: 1;
            margin-top: 5px; /* 移除顶部外边距，与h1的margin-bottom配合 */
        }
        .sidebar {
            width: 220px; /* 侧边栏宽度 */
            background-color: #343a40; /* 深色背景 */
            color: #fff;
            padding: 5px 10px 10px 10px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            flex-shrink: 0; /* 不压缩侧边栏 */
            /* flex-grow: 1; /* 使侧边栏填充剩余垂直空间 */
            overflow-y: auto; /* 允许滚动 */
            border-bottom-left-radius: 10px; /* 左下角圆角 */
            border-bottom-right-radius: 10px; /* 右下角圆角 */
            border-top-left-radius: 10px; /* 左上角圆角 */
            border-top-right-radius: 10px; /* 右上角圆角 */
        }
        .status-filter {
            margin-bottom: 20px;
        }
        .status-filter select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: none;
            background-color: #495057;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
        }
        .nav-item {
            display: flex;
            align-items: center;
            padding: 5px 5px 5px 5px; /* 进一步减小内边距 */
            margin-bottom: 10px;
            background-color: #495057;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .nav-item:hover {
            background-color: #0056b3;
        }
        .nav-item .icon {
            width: 30px;
            height: 30px;
            margin-right: 5px;
            background-color: #6c757d; /* 占位符背景色 */
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            color: #fff;
        }
        .nav-item .text h3 {
            margin: 0;
            font-size: 18px;
            color: white;
        }
        .nav-item .text p {
            margin: 5px 0 0;
            font-size: 12px;
            color: #bbb;
        }
        .nav-item .text {
            flex-grow: 1;
        }
        .a3-container {
            flex-grow: 1; /* 占据剩余空间 */
            width: auto; /* 移除固定宽度，让flex-grow控制 */
            min-height: 297mm;
            margin: 0 0 10px 0; /* 移除水平外边距 */
            border-radius: 10px; /* 添加圆角 */
            background: #fff;
            padding: 10mm 20mm;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .ai-copilot-section {
            width: 250px; /* AI Copilot侧边栏宽度 */
            background-color: #fff; /* 白色背景 */
            padding: 10px 20px; /* 调整上下内边距 */
            box-shadow: -2px 0 5px rgba(0,0,0,0.1); /* 左侧阴影 */
            flex-shrink: 0; /* 不压缩 */
            border-radius: 10px; /* 圆角 */
            margin: 0 0 10px 20px; /* 移除右侧外边距，增加左侧外边距 */
            display: flex;
            flex-direction: column; /* 确保子元素垂直排列 */
        }
        /* Modal Styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
            position: relative;
        }

        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        #projectListContainer {
            margin-bottom: 15px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
            border-radius: 4px;
        }

        #projectListContainer div {
            padding: 8px 0;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }

        #projectListContainer div:last-child {
            border-bottom: none;
        }

        #projectListContainer div:hover {
            background-color: #f0f0f0;
        }

        #newProjectNameInput {
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #confirmProjectSelection,
        #cancelProjectSelection {
            padding: 10px 20px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #confirmProjectSelection {
            background-color: #4CAF50;
            color: white;
        }

        #cancelProjectSelection {
            background-color: #f44336;
            color: white;
        }

        h2, h3 { color: #0056b3; }
        .section { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px; }
        .section:last-child { border-bottom: none; }
        .section-title { font-weight: bold; background-color: #e9ecef; padding: 5px 10px; margin-bottom: 10px; border-left: 5px solid #0056b3; }
        ul { list-style-type: none; padding: 0; }
        ul li { margin-bottom: 5px; }
        textarea { width: 100%; height: 100px; border: 1px solid #ccc; padding: 10px; box-sizing: border-box; }
        .progress-indicator {
            width: 100%;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .progress-indicator-bar {
            height: 100%;
            background-color: #28a745; /* 进度条颜色 */
            border-radius: 5px;
            width: 0%; /* 初始进度为0，通过JS动态设置 */
            transition: width 0.5s ease-in-out;
        }
        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            margin-bottom: 30px;
            position: relative;
        }
        .progress-steps::before {
            content: '';
            position: absolute;
            width: calc(100% - 40px);
            height: 2px;
            background-color: #e0e0e0;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 0;
        }
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 1;
        }
        .progress-step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e0e0e0;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-bottom: 2px;
            transition: background-color 0.3s ease;
        }
        .progress-step-text {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .progress-step.active .progress-step-number {
            background-color: #28a745;
        }
        .progress-step.active .progress-step-text {
            color: #28a745;
        }
        #ai-analyze-button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
            width: 100%;
        }
        #ai-analyze-button:hover {
            background-color: #0056b3;
        }
        .api-key-input {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            width: 100%;
        }
        .api-key-input label,
        .api-key-input input,
        .api-key-input button {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 2px;
        }
        .api-key-input button {
            margin-top: 5px;
        }
        #ai-suggestions-container {
            display: flex;
            flex-direction: column;
            height: 500px; /* Increased height */
            border: 1px solid #ccc;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }

        #ai-suggestions {
            flex-grow: 1;
            padding: 10px;
            overflow-y: auto;
            background-color: #f9f9f9;
            border-bottom: 1px solid #eee;
            font-size: 14px; /* Adjust font size */
        }

        .chat-input-container {
            display: flex;
            padding: 10px;
            background-color: #fff;
            border-top: 1px solid #eee;
        }

        #user-input {
            flex-grow: 1;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 8px;
            margin-right: 10px;
            resize: none;
        }

        #send-button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 8px 15px;
            cursor: pointer;
        }

        #send-button:hover {
            background-color: #0056b3;
        }

        .user-message {
            text-align: right;
            background-color: #dcf8c6;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 5px;
            margin-left: 20%;
            font-size: 14px;
        }

        .ai-message {
            text-align: left;
            background-color: #e0e0e0;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 5px;
            margin-right: 5%;
            font-size: 14px;
        }

        .ai-message p {
            margin-bottom: 5px;
        }

        .ai-message h1, .ai-message h2, .ai-message h3, .ai-message h4, .ai-message h5, .ai-message h6 {
            margin-top: 5px;
            margin-bottom: 2px;
        }

        .ai-message ul, .ai-message ol {
            margin-left: 20px;
            margin-bottom: 2px;
        }

        .ai-message li {
            margin-bottom: 1px;
        }

        .ai-message pre {
            background-color: #eee;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .ai-message code {
            font-family: 'Courier New', Courier, monospace;
            color: #c7254e;
            background-color: #f9f2f4;
            padding: 2px 4px;
            border-radius: 4px;
        }
        .api-key-note {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .ai-suggestions {
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background-color: #f9f9f9;
            flex-grow: 1; /* Allow suggestions to take up remaining space */
            overflow-y: auto; /* Add scroll if suggestions are long */
        }
        .ai-suggestions h4 {
            margin-top: 0;
            color: #555;
            margin-bottom: 10px;
        }
        .ai-suggestions p {
            margin-bottom: 10px;
            color: #333;
        }
        .ai-suggestions p:last-child {
            margin-bottom: 0;
        }
        /* Removed duplicate .nav-item definition */
    </style>
</head>
<body>
    <h1>A3问题解决工具</h1>
    <div class="main-content">
        <div class="sidebar">
        <div class="status-filter">
            <select>
                <option>全部状态</option>
                <option>进行中</option>
                <option>已完成</option>
                <option>已关闭</option>
            </select>
        </div>
        <div class="nav-item">
            <div class="icon">🛡️</div>
            <div class="text">
                <h3>安全类标杆报告</h3>
                <p>查看安全问题解决示例</p>
            </div>
        </div>
        <div class="nav-item">
            <div class="icon">📊</div>
            <div class="text">
                <h3>质量类标杆报告</h3>
                <p>查看质量问题解决示例</p>
            </div>
        </div>
        <div class="nav-item">
            <div class="icon">🛠️</div>
            <div class="text">
                <h3>设备类标杆报告</h3>
                <p>查看设备问题解决示例</p>
            </div>
        </div>
        <div class="nav-item">
            <div class="icon">💼</div>
            <div class="text">
                <h3>业务类标杆报告</h3>
                <p>查看业务问题解决示例</p>
            </div>
        </div>
        </div>
        <div class="a3-container">
            <h2 id="a3-report-title"></h2>
            <div class="progress-steps">
                <div class="progress-step active">
                    <div class="progress-step-number">1</div>
                    <div class="progress-step-text">问题描述</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">2</div>
                    <div class="progress-step-text">现状分析</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">3</div>
                    <div class="progress-step-text">目标设定</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">4</div>
                    <div class="progress-step-text">根因分析</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">5</div>
                    <div class="progress-step-text">对策制定</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">6</div>
                    <div class="progress-step-text">实施计划</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">7</div>
                    <div class="progress-step-text">效果确认</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-number">8</div>
                    <div class="progress-step-text">标准化</div>
                </div>
            </div>
            <div class="section">
                <div class="section-title">1. 问题描述</div>
                <textarea id="problem-description-content" placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">2. 现状分析</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">3. 目标设定</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">4. 根因分析</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">5. 对策制定</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">6. 实施计划</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">7. 效果确认</div>
                <textarea placeholder=""></textarea>
            </div>
            <div class="section">
                <div class="section-title">8. 标准化</div>
                <textarea placeholder=""></textarea>
            </div>
        </div>
        <div class="ai-copilot-section">
            <h2 style="margin-top: 0; margin-bottom: 5px;">AI Copilot建议</h2>

            <button id="ai-analyze-button" style="margin-top: 5px;">AI 智能分析</button>
            <div class="api-key-input">
                <label for="deepseekApiKey">DeepSeek API Key:</label>
                <input type="password" id="deepseekApiKey" placeholder="输入您的 DeepSeek API Key">
                <button id="saveApiKey">保存 API Key</button>
                <p class="api-key-note">注意：您的 API 密钥保存在浏览器本地，仅用于 AI 分析。</p>
            </div>
            <div id="ai-suggestions-container">
                <div id="ai-suggestions" class="chat-box" readonly></div>
                <div class="chat-input-container">
                    <textarea id="userInput" placeholder="输入您的问题..." rows="3"></textarea>
                    <button id="sendButton">发送</button>
                </div>
            </div>
        </div>
            
        </div>
    </div>

    <div id="projectSelectionModal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2>选择或创建项目</h2>
            <div id="projectListContainer"></div>
            <input type="text" id="newProjectNameInput" placeholder="输入新项目名称">
            <button id="confirmProjectSelection">确定</button>
            <button id="cancelProjectSelection">取消</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const aiAnalyzeButton = document.getElementById('ai-analyze-button');
            const deepseekApiKeyInput = document.getElementById('deepseekApiKey');
            const aiSuggestions = document.getElementById('ai-suggestions');


            // Load API Key from local storage if available
            const savedApiKey = localStorage.getItem('deepseekApiKey');
            if (savedApiKey) {
                deepseekApiKeyInput.value = savedApiKey;
            }
          // Save API Key to local storage on input change
                deepseekApiKeyInput.addEventListener('change', () => {
                    const newKey = deepseekApiKeyInput.value.trim();
                    if (newKey !== '') {
                        localStorage.setItem('deepseekApiKey', newKey);
                 } else {
                        // If the input is cleared, do not remove the key from localStorage
                        // This prevents accidental clearing of the stored key
                        // The key will only be updated if a new non-empty value is provided
                    }
                 });

            const userInput = document.getElementById('userInput');
            const sendButton = document.getElementById('sendButton');
            let isAnalyzing = false; // Track if AI analysis is in progress

            sendButton.addEventListener('click', async () => {
                const message = userInput.value.trim();
                if (message) {
                    aiSuggestions.innerHTML += `<div class="user-message">${message}</div>`;
                    userInput.value = '';
                    aiSuggestions.scrollTop = aiSuggestions.scrollHeight;
                    try {
                        const apiKey = deepseekApiKeyInput.value;
                        if (!apiKey) {
                            aiSuggestions.innerHTML += `<div class="ai-message">AI Copilot: 请先输入并保存您的 DeepSeek API Key。</div>`;
                            aiSuggestions.scrollTop = aiSuggestions.scrollHeight;
                            return;
                        }

                        aiSuggestions.innerHTML += `<div class="ai-message">AI Copilot: 正在思考中...</div>`;
                        aiSuggestions.scrollTop = aiSuggestions.scrollHeight;

                        const response = await fetch('https://api.deepseek.com/chat/completions', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${apiKey}`
                            },
                            body: JSON.stringify({
                                model: "deepseek-chat",
                                messages: [
                                    { role: "system", content: "你是一个有帮助的AI助手。" },
                                    { role: "user", content: message }
                                ],
                                stream: false
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`API request failed with status ${response.status}`);
                        }

                        const data = await response.json();
                        if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                            const aiResponseContent = data.choices[0].message.content;
                            console.log('AI Raw Response (for debugging):', aiResponseContent); // Re-add for debugging
                            const aiMessageDiv = document.createElement('div');
                            aiMessageDiv.classList.add('ai-message');
                            const parsedContent = marked.parse(aiResponseContent, { gfm: true, breaks: true });
                            aiMessageDiv.innerHTML = `AI Copilot: ${parsedContent}`;
                            console.log('Parsed HTML Content:', aiMessageDiv.innerHTML); // Add this line for debugging
                            aiSuggestions.appendChild(aiMessageDiv);
                        } else {
                            aiSuggestions.innerHTML += `<div class="ai-message">AI Copilot: 未获取到有效的 AI 建议。</div>`;
                        }
                    } catch (error) {
                        console.error('AI 聊天出错:', error);
                        aiSuggestions.innerHTML += `<div class="ai-message">AI Copilot: 聊天出错：${error.message}。请检查 API Key 或网络连接。</div>`;
                    } finally {
                        aiSuggestions.scrollTop = aiSuggestions.scrollHeight;
                    }
                }
            });

            aiAnalyzeButton.addEventListener('click', async () => {
                if (isAnalyzing) {
                    // If already analyzing, stop the analysis (simulate by changing button text and state)
                    isAnalyzing = false;
                    aiAnalyzeButton.textContent = 'AI 智能分析';
                    aiAnalyzeButton.disabled = false; // Re-enable button
                    aiSuggestions.innerHTML = '<div class="ai-message">分析已停止。</div>';
                    return;
                }

                const problemDescription = document.querySelector('#problem-description-content').value.trim();

                if (!problemDescription) {
                    alert('问题描述为空，请填写问题描述。');
                    return;
                }

                const apiKey = deepseekApiKeyInput.value;
                if (!apiKey) {
                    alert('请先输入并保存您的 DeepSeek API Key。');
                    return;
                }

                isAnalyzing = true;
                aiAnalyzeButton.textContent = '停止分析';
                aiAnalyzeButton.disabled = false; // Keep button enabled to allow stopping
                aiSuggestions.innerHTML = '<div class="ai-message">正在分析中，请稍候...</div>';

                try {
                    const response = await fetch('https://api.deepseek.com/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: "deepseek-chat",
                            messages: [
                                { role: "system", content: "你是一个专业的A3问题解决报告分析助手，请根据用户提供的问题描述，给出A3报告的2.现状分析、3.目标设定、4.根因分析、5.对策制定、6.实施计划、7.效果确认、8.标准化等部分的建议内容。" },
                                { role: "user", content: `问题描述：${problemDescription}` }
                            ],
                            stream: false
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`API request failed with status ${response.status}`);
                    }

                    const data = await response.json();
                    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
                        const aiResponseContent = data.choices[0].message.content;
                        const parsedContent = marked.parse(aiResponseContent, { gfm: true, breaks: true });
                        aiSuggestions.innerHTML = parsedContent;
                    } else {
                        aiSuggestions.innerHTML = '未获取到有效的 AI 建议。';
                    }
                } catch (error) {
                    console.error('AI 分析出错:', error);
                    aiSuggestions.innerHTML = `AI 分析出错：${error.message}。请检查 API Key 或网络连接。`;
                } finally {
                    isAnalyzing = false;
                    aiAnalyzeButton.textContent = 'AI 智能分析';
                    aiAnalyzeButton.disabled = false;
                }
            });

            // Load saved projects from local storage
            let savedProjects = JSON.parse(localStorage.getItem('a3Projects')) || {};
            let currentActiveProject = null; // Track the currently active project

            // Function to update project counts in navigation
            function updateNavProjectCounts() {
                document.querySelectorAll('.nav-item').forEach(item => {
                    const reportType = item.querySelector('h3').textContent;
                    const projectsOfType = savedProjects[reportType] || [];
                    const count = projectsOfType.length;
                    let pTag = item.querySelector('.text p');
                    if (pTag) {
                        // Preserve original text and append count
                        const originalText = pTag.getAttribute('data-original-text') || pTag.textContent;
                        pTag.setAttribute('data-original-text', originalText);
                        pTag.textContent = `${originalText} (${count})`;
                    }
                });
            }

            // Initial update of project counts
            updateNavProjectCounts();

            // Default A3 content templates for different report types
            const defaultA3Content = {
                "安全类标杆报告": {
                    "1. 问题描述": "请描述安全问题。",
                    "2. 现状分析": "请分析安全现状。",
                    "3. 目标设定": "请设定安全目标。",
                    "4. 根因分析": "请分析安全问题的根因。",
                    "5. 对策制定": "请制定安全对策。",
                    "6. 实施计划": "请制定安全实施计划。",
                    "7. 效果确认": "请确认安全效果。",
                    "8. 标准化": "请进行安全标准化。"
                },
                "质量类标杆报告": {
                    "1. 问题描述": "", 
                     "2. 现状分析": "", 
                     "3. 目标设定": "", 
                     "4. 根因分析": "", 
                     "5. 对策制定": "", 
                     "6. 实施计划": "", 
                     "7. 效果确认": "", 
                     "8. 标准化": "" 
                 }, 
                 "设备类标杆报告": { 
                     "1. 问题描述": "", 
                     "2. 现状分析": "", 
                     "3. 目标设定": "", 
                     "4. 根因分析": "", 
                     "5. 对策制定": "", 
                     "6. 实施计划": "", 
                     "7. 效果确认": "", 
                     "8. 标准化": "" 
                 }, 
                 "业务类标杆报告": { 
                     "1. 问题描述": "", 
                     "2. 现状分析": "", 
                     "3. 目标设定": "", 
                     "4. 根因分析": "", 
                     "5. 对策制定": "", 
                     "6. 实施计划": "", 
                     "7. 效果确认": "", 
                     "8. 标准化": ""
                }
            };

            // Function to save current A3 content
            function saveCurrentA3Content() {
                if (currentActiveProject) {
                    const a3Content = {};
                    document.querySelectorAll('.section').forEach(section => {
                        const titleElement = section.querySelector('.section-title');
                        const contentElement = section.querySelector('textarea');
                        if (titleElement && contentElement) {
                            const title = titleElement.textContent.trim();
                            a3Content[title] = contentElement.value.trim();
                        }
                    });
                    currentActiveProject.content = a3Content;
                    localStorage.setItem('a3Projects', JSON.stringify(savedProjects));
                    console.log('Current A3 content saved:', currentActiveProject);
                }
            }

            // Function to load A3 content
            function deleteProject(projectName, reportType) {
                let a3Projects = JSON.parse(localStorage.getItem('a3Projects')) || {};
                if (a3Projects[reportType]) {
                    a3Projects[reportType] = a3Projects[reportType].filter(p => p.name !== projectName);
                    localStorage.setItem('a3Projects', JSON.stringify(a3Projects));
                    alert(`项目 "${projectName}" 已删除。`);
                    updateNavProjectCounts();
                }
            }

            function loadA3Content(project, reportType) {
                if (project) {
                    document.querySelectorAll('.section').forEach(section => {
                        const titleElement = section.querySelector('.section-title');
                        const contentElement = section.querySelector('textarea');
                        if (titleElement && contentElement) {
                            const title = titleElement.textContent.trim();
                            // If project content is empty, load default content based on reportType
                            if (Object.keys(project.content).length === 0 && defaultA3Content[reportType]) {
                                contentElement.value = defaultA3Content[reportType][title] || '';
                            } else {
                                contentElement.value = project.content[title] || '';
                            }
                            // Set placeholder to empty string to remove default hints
                            if (contentElement.placeholder) {
                                contentElement.placeholder = '';
                            }
                        }
                    });
                    document.querySelector('.a3-container h2').textContent = project.name;
                    currentActiveProject = project;
                    console.log('A3 content loaded for project:', project.name);
                }
            }

            // Add event listeners for navigation items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    // Save current project content before switching
                    saveCurrentA3Content();

                    const reportType = item.querySelector('h3').textContent;
                    console.log(`Clicked on: ${reportType}`);

                    const projectsOfType = savedProjects[reportType] || [];
                    let projectName = '';

                    const projectSelectionModal = document.getElementById('projectSelectionModal');
                    const projectListContainer = document.getElementById('projectListContainer');
                    const newProjectNameInput = document.getElementById('newProjectNameInput');
                    const confirmProjectSelection = document.getElementById('confirmProjectSelection');
                    const cancelProjectSelection = document.getElementById('cancelProjectSelection');
                    const closeButton = projectSelectionModal.querySelector('.close-button');

                    projectListContainer.innerHTML = '';
                    if (projectsOfType.length > 0) {
                        projectsOfType.forEach((p, index) => {
                            const projectDiv = document.createElement('div');
                            projectDiv.innerHTML = `<span>${index + 1}. ${p.name}</span><button class="delete-project-button" data-project-name="${p.name}">删除</button>`;
                            projectDiv.dataset.projectName = p.name;
                            projectDiv.addEventListener('click', (event) => {
                                if (event.target.classList.contains('delete-project-button')) {
                                    event.stopPropagation(); // Prevent project selection when clicking delete
                                    const projectNameToDelete = event.target.dataset.projectName;
                                    deleteProject(projectNameToDelete, reportType);
                                    // No need to call displayProjectsInModal here, as the modal will be closed or re-opened
                                    // updateNavProjectCounts() is called inside deleteProject
                                } else {
                                    // Direct selection of the project
                                    projectSelectionModal.style.display = 'none';
                                    loadA3Content(p, reportType); // Directly load the selected project
                                    aiSuggestions.innerHTML = ''; // Clear AI suggestions when loading a project
                                }
                            });
                            projectListContainer.appendChild(projectDiv);
                        });
                    } else {
                        projectListContainer.innerHTML = '<p>没有找到该类型的项目。</p>';
                    }

                    newProjectNameInput.value = ''; // Clear previous input
                    projectSelectionModal.style.display = 'flex'; // Show the modal

                    confirmProjectSelection.onclick = () => {
                        const selectedProjectName = newProjectNameInput.value.trim();
                        if (selectedProjectName) {
                            const existingProject = projectsOfType.find(p => p.name === selectedProjectName);
                            if (existingProject) {
                                alert('项目名称已存在，将加载现有项目。');
                                loadA3Content(existingProject, reportType);
                            } else {
                                const newProject = { name: selectedProjectName, content: {} };
                                if (!savedProjects[reportType]) {
                                    savedProjects[reportType] = [];
                                }
                                savedProjects[reportType].push(newProject);
                                localStorage.setItem('a3Projects', JSON.stringify(savedProjects));
                                loadA3Content(newProject, reportType);
                                console.log('New project created and saved:', selectedProjectName);
                                aiSuggestions.innerHTML = ''; // Clear AI suggestions for new project
                                updateNavProjectCounts(); // Update counts after new project is created
                            }
                        }
                        projectSelectionModal.style.display = 'none';
                    };

                    cancelProjectSelection.onclick = () => {
                        projectSelectionModal.style.display = 'none';
                        // User cancelled, do not load or create any project
                        document.querySelector('.a3-container h2').textContent = `${reportType} - 新建报告`;
                        currentActiveProject = null;
                        aiSuggestions.innerHTML = ''; // Clear AI suggestions
                    };

                    closeButton.onclick = () => {
                        projectSelectionModal.style.display = 'none';
                        // User closed, do not load or create any project
                        document.querySelector('.a3-container h2').textContent = `${reportType} - 新建报告`;
                        currentActiveProject = null;
                        aiSuggestions.innerHTML = ''; // Clear AI suggestions
                    };
            });
                });
            });
    </script>
</body>