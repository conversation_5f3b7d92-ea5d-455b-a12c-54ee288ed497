<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDA 6.3:2023 过程审核工具</title>
    <style>
        /* CSS Styles (保持和您满意的版本一致) */
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            width: 95%;
            max-width: 1600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 10px;
        }


        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 3px solid #00529B;
            background-color: #eef2f7;
            border-radius: 6px 6px 0 0;
        }
        .tab-button {
            padding: 12px 25px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 20px;
            font-weight: 600;
            color: #003366;
            transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            margin-right: 2px;
        }
        .tab-button:hover {
            background-color: #d6e4f0;
            color: #002244;
        }
        .tab-button.active {
    background-color: #00529B;
    color: white;
    border-bottom: 3px solid #003366;
    padding: 12px 25px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    margin-right: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}
        .tab-content {
            display: none;
            padding: 25px;
            border: 1px solid #d1d9e6;
            border-bottom: 1px solid #d1d9e6;
            border-top: none;
            border-radius: 6px;
            background-color: #f8f9fc;
            max-height: calc(100vh - 250px); /* Adjust as needed */
            margin-bottom: -8px;
            overflow-y: auto;
        }
        .tab-content.active {
            display: block;
        }
        h1, h2, h3 {
            color: #003366;
        }
        h2 {
            border-bottom: 2px solid #00529B;
            padding-bottom: 12px;
            margin-top: 2px; /* 进一步缩小与上方元素的距离 */
            margin-bottom: 5px; /* 进一步缩小与下方元素的距离 */
            font-size: 1.8em;
        }
        h3 {
            font-size: 1.5em;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 25px;
            border: 1px solid #d1d9e6;
            border-bottom: 1px solid #d1d9e6;
            border-radius: 6px;
            overflow: hidden;
        }
        th, td {
            border-bottom: 1px solid #d1d9e6;
            padding: 12px 15px;
            text-align: left;
            vertical-align: top;
        }

        @media print {
            /* 隐藏标签页和非审核报告标签页内容 */
            .tabs,
            .tab-content:not(#auditReport):not(#questions) {
                display: none !important;
                height: auto !important;
                min-height: 0 !important;
                box-sizing: border-box !important;
            }

            /* 隐藏按钮 */
            button,
            .print-button {
                display: none !important;
                box-sizing: border-box !important;
            }

            html {
                min-width: 100% !important;
                min-height: 0 !important;
            }
            html, body {
                font-size: 12pt;
                box-sizing: border-box !important;
                min-width: 100% !important;
                overflow-x: hidden !important; /* Added to prevent horizontal overflow */
            }
            body {
                margin: 0 !important;
                padding: 0 !important;
                box-sizing: border-box !important;
            }

            .container {
                margin: 0 !important;
                box-sizing: border-box !important;
                min-width: 100% !important;
            }

            .tab-content {
                display: block !important;
                max-height: none !important;
            }





            
            
            


            #auditReport {
                display: block !important;
                margin: 0;
                height: auto !important;
                min-height: 0 !important;
                min-width: 100% !important;
            }


            /* 确保表格和图片在打印时可见 */
            #auditReport table, #auditReport img {
                max-width: 100% !important;
                height: auto !important;
                box-sizing: border-box !important;
            }
            table {
                page-break-inside: avoid !important;
                table-layout: auto !important;
                width: 100% !important;
                min-width: 100% !important;
            }
            th, td {
                white-space: normal !important;
                box-sizing: border-box !important;
            }

            /* Specific overrides for elements that might be hidden by default or have specific print styles */
            th, td {
                padding: 8px !important;
            }

            .report-table {
                width: 100% !important;
            }

            .report-table th,
            .report-table td {
                word-wrap: break-word !important;
            }

            /* 为report-section添加分页符 */
            .report-section {
                border: none !important;
                box-shadow: none !important;
                page-break-inside: avoid !important;
            }



        }
        td {
            border-left: 1px solid #d1d9e6;
        }
        td:first-child {
            border-left: none;
        }
        #auditPlanTable th, #auditPlanTable td {
            text-align: center;
            vertical-align: middle;
                    box-sizing: border-box;
            padding: 8px 2px;
        }
        #auditPlanTable {
    margin-bottom: 0; /* Adjust this value to extend the border */
        }
        #auditPlanTable th:nth-child(3), #auditPlanTable td:nth-child(3),
        #auditPlanTable th:nth-child(4), #auditPlanTable td:nth-child(4) {
            width: 140px; /* 审核开始日期/到 */
        }
        #auditPlanTable th:nth-child(8), #auditPlanTable td:nth-child(8) {
            width: 100px; /* 过程要素 */
        }
        #auditPlanTable th:nth-child(1), #auditPlanTable td:nth-child(1) {
            width: 60px; /* 操作 */
        }
        #auditPlanTable th:nth-child(2), #auditPlanTable td:nth-child(2) {
            width: 100px; /* 报告编号 */
        }
        #auditPlanTable th:nth-child(5), #auditPlanTable td:nth-child(5) {
            width: 100px; /* 审核类别 */
        }
        #auditPlanTable th:nth-child(6), #auditPlanTable td:nth-child(6) {
            width: 100px; /* 审核目的 */
        }
        #auditPlanTable th:nth-child(7), #auditPlanTable td:nth-child(7) {
            width: 100px; /* 审核范围 */
        }
        #auditPlanTable th:nth-child(9), #auditPlanTable td:nth-child(9) {
            width: 80px; /* 审核组长 */
        }
        #auditPlanTable th:nth-child(10), #auditPlanTable td:nth-child(10) {
            width: 80px; /* 审核员 */
        }
        #auditPlanTable th:nth-child(11), #auditPlanTable td:nth-child(11) {
            width: 80px; /* 备注 */
        }
        #auditPlanTable input[type="text"],
        #auditPlanTable input[type="date"],
        #auditPlanTable textarea {
            width: 90%;
            height: 38px; /* 设置固定高度 */
            min-height: 38px; /* 确保最小高度 */
            line-height: 38px; /* 确保文本垂直居中 */
            border: 1px solid #b0c4de;
            border-radius: 5px;
            font-size: 1em;
            text-align: center;
            box-sizing: border-box;
            margin: 0;
            padding: 0 6px;
        }
        th {
            background-color: #e2eaf3;
            font-weight: 600;
            color: #003366;
            font-size: 1.1em;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        tr:last-child td {
            border-bottom: 1px solid #d1d9e6;
        }
        input[type="text"], input[type="date"], input[type="email"], input[type="tel"], select, textarea {
            width: calc(100% - 24px);
            padding: 10px 12px;
            margin-bottom: 8px;
            border: 1px solid #b0c4de;
            border-radius: 5px;
            box-sizing: border-box;
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            font-size: 1em;
        }
        input[type="text"]:focus, select:focus, textarea:focus {
            border-color: #00529B;
            box-shadow: 0 0 0 0.2rem rgba(0, 82, 155, 0.25);
            outline: none;
        }
        textarea {
            min-height: 80px;
            resize: vertical;
        }
        button, .button-style {
            background-color: #00529B;
            color: white;
            padding: 10px 18px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.2s ease-in-out, transform 0.1s ease;
            margin: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button:hover, .button-style:hover {
            background-color: #003366;
            transform: translateY(-1px);
        }
        button:active, .button-style:active {
            transform: translateY(0px);
        }
        .green-button {
            background-color: #4CAF50;
        }
        .green-button:hover {
            background-color: #45a049;
        }
        .green-button:active {
            background-color: #3e8e41;
        }
        .delete-button {
            background-color: #d9534f;
            white-space: nowrap; /* Prevent text from wrapping */
            width: auto; /* Allow width to adjust to content */
            padding: 8px 12px; /* Adjust padding for horizontal text */
        }
        .delete-button:hover {
            background-color: #c9302c;
        }
        .add-button {
            background-color: #5cb85c;
            margin-left: 0; /* 与表格左侧对齐 */
        }
        .add-button:hover {
            background-color: #4cae4c;
        }
        .actions-toolbar {
            margin-top: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 8px;
            text-align: center;
        }
        .question-block {
            margin-bottom: 25px;
            padding: 0;
            border: 1px solid #c5d9e8;
            border-radius: 6px;
            background-color: #fff;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        }
        .question-header {
            background-color: #00529B;
            color: white;
            padding: 12px 15px;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            font-weight: bold;
            font-size: 1.2em;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .question-header::after {
            content: '▼'; /* Default to expanded */
            font-size: 0.8em;
            transition: transform 0.2s ease-in-out;
        }
        .question-header.collapsed::after {
            transform: rotate(-90deg);
        }
        .question-content {
            padding: 15px;
            border-top: none;
            border-radius: 0 0 6px 6px;
            background-color: #fdfdfe;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
        }
        .question-content.collapsed {
            max-height: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            border-top: 1px solid #c5d9e8;
        }
        .question-text {
            font-weight: 600;
            margin-bottom: 12px;
            color: #003366;
            font-size: 1.1em;
    font-family: 'KaiTi', '楷体', serif;
        }
        /* Removed .requirements-container and .requirements-box as they are no longer used, but kept for reference if needed */
        /*
        .requirements-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 12px;
        }
        .requirements-box {
            flex: 1;
            padding: 12px;
            background-color: #eef2f7;
            border: 1px solid #d6e4f0;
            border-radius: 5px;
            font-size: 0.95em;
        }
        .requirements-box h4 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #003366;
            font-size: 1.05em;
        }
        .requirements-box div {
            white-space: pre-wrap;
        }
        */
        .score-input { width: 90px; display: inline-block; margin-left: 10px; padding: 8px; font-size: 0.95em;}
        .score-label { font-weight: 500; font-size: 1em; }

        .manual-record-section label {
            font-weight: 500;
            font-size: 0.95em;
            color: #003366;
            margin-bottom: 4px;
            display: block;
        }
        .manual-record-section textarea, .manual-record-section input[type="file"] {
            margin-bottom: 8px;
        }
         .manual-record-section input[type="file"] {
            padding: 8px;
            width: auto;
        }
        .manual-record-section span {
            font-size: 0.9em;
            color: #555;
        }


        .rating-a { color: #28a745; font-weight: bold; font-size: 1.8em; }
        .rating-b { color: #fd7e14; font-weight: bold; font-size: 1.8em; }
        .rating-c { color: #dc3545; font-weight: bold; font-size: 1.8em; }
        .rating-invalid {color: #6c757d; font-weight: bold; font-size: 1.8em; }
        .downgrade-reason { color: #c9302c; font-style: italic; margin-top: 8px; font-size: 0.95em; }
        footer {
    text-align: center;
    padding: 0;
    background-color: #2c3e50;
    color: #ffffff;
    font-size: 1.1em;
    font-family: 'KaiTi', '楷体', serif;
    margin: 50px auto 0; /* 调整上边距使其下移，保持水平居中 */
    height: auto; /* 高度自适应 */
    min-height: 50px; /* 最小高度保证布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    width: 95%;
    border-bottom: 2px solid #000;
    box-sizing: border-box; /* 包含边框在高度计算中 */
    position: relative;
    z-index: 2; /* 确保覆盖底层元素 */
}
        @media print {
            footer {
                display: none !important;
            }
        }
        footer a {
            color: #66c2ff;
            text-decoration: none;
        }
        footer a:hover {
            text-decoration: underline;
            color: #99d6ff;
        }
        .checkbox-group label {
            margin-right: 0px; /* 间距由父容器控制 */
            font-weight: normal;
            display: inline-flex;
            align-items: center;
            height: 32px;
            justify-content: flex-start;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 6px;
            vertical-align: middle;
                    box-sizing: border-box;
            transform: scale(1.1);
        }
.checkbox-group {
    display: flex !important;
    flex-wrap: wrap;
    justify-content: center; /* 水平居中 */
    align-items: center;
    gap: 2px 5px !important; /* 进一步缩小行间距到2px，列间距保持5px */
    min-height: 60px;
    height: auto;
    padding: 0 2px;
}
        .p-element-selection-group {
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-start; /* 左对齐 */
            align-items: flex-start; /* 顶部对齐 */
            gap: 20px; /* 调整行间距和列间距 */
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fc;
            border: 1px solid #d1d9e6;
            border-bottom: 1px solid #d1d9e6;
            border-radius: 6px;
        }
        .p-element-selection-group label {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.2s;
            flex: 0 1 auto;
            box-sizing: border-box; /* 包含padding和border在宽度内 */
        }
         .p-element-selection-group label:hover {
            background-color: #e2eaf3;
        }
        #p3ProductCheckboxContainer, #p4ProductCheckboxContainer {
            display: inline-block;
            margin-left: 8px;
            font-size: 0.9em;
        }
        .small-button {
            padding: 4px 8px;
            font-size: 0.8em;
            margin-left: 5px;
        }
        #processProductTable input[type="text"] { width: calc(100% - 50px); margin-right: 5px;}
        #processProductTable button { margin-left: 0;}
        #processProductTable th, #processProductTable td { text-align: center; }
        #processProductTable th:first-child, #processProductTable td:first-child { text-align: left; }

        #actionPlanTable textarea { width: calc(100% - 18px); min-height: 50px; }
        #actionPlanTable input[type="text"], #actionPlanTable input[type="date"], #actionPlanTable select { width: calc(100% - 18px); }

        #actionPlanTable th:nth-child(1), #actionPlanTable td:nth-child(1) { width: 3%; text-align: center; }
        #actionPlanTable th:nth-child(2), #actionPlanTable td:nth-child(2) { width: 10%; }
        #actionPlanTable th:nth-child(3), #actionPlanTable td:nth-child(3) { width: 4%; text-align: center; }
        #actionPlanTable th:nth-child(4), #actionPlanTable td:nth-child(4) { width: 18%; }


        #auditChecklistTable th, #auditChecklistTable td { text-align: center; }
        #auditChecklistTable th:first-child, #auditChecklistTable td:first-child { text-align: center; }
        #auditChecklistTable th:nth-child(2), #auditChecklistTable td:nth-child(2) { text-align: left; }
        #auditChecklistTable td a { cursor: pointer; color: #00529B; text-decoration: underline; }
        #auditChecklistTable td a:hover { color: #003366; }

        /* Styles for Evaluation Summary Table */
        #evaluationSummaryTable { margin-top: 20px; }
        #evaluationSummaryTable th, #evaluationSummaryTable td { padding: 8px 10px; font-size: 0.9em; }
#evaluationSummaryTable th:first-child { width: 60%; word-wrap: break-word; white-space: normal; }
        #evaluationSummaryTable .p-element-header td { background-color: #d0e0f0; font-weight: bold; }
        #evaluationSummaryTable .p6-process-header td { background-color: #e0ecf7; font-style: italic; padding-left: 20px !important; }
        #evaluationSummaryTable .p6-subelement-header td { background-color: #f0f6fc; padding-left: 40px !important; }
        #evaluationSummaryTable .question-row td { padding-left: 20px !important; } /* Adjusted for first cell */
        #evaluationSummaryTable .question-row td:not(:first-child) { padding-left: 20px !important; } /* For P2,P3,P4,P5,P7 questions, subsequent cells */
        #evaluationSummaryTable .p6-question-row td:first-child { padding-left: 60px !important; } /* For P6 questions, first cell */


        @media (max-width: 992px) {
            /* .requirements-container { flex-direction: column; } */ /* No longer needed */
            h2 { font-size: 1.6em; }
            h3 { font-size: 1.3em; }
        }
        @media (max-width: 768px) {
            .tabs { flex-direction: column; }
            .tab-button { width: 100%; text-align: left; border-bottom: 1px solid #d1d9e6; border-radius: 6px; margin-right:0; }
            .tab-button.active { border-bottom: 3px solid #00529B; }
            .container { width: 100%; margin: 0; padding: 10px; border-radius: 6px; box-sizing: border-box;}
            .tab-content { padding: 15px;}
            h2 { font-size: 1.4em; }
            h3 { font-size: 1.2em; }
            .actions-toolbar button, .actions-toolbar .button-style { display: block; width: calc(100% - 10px); margin: 8px auto; }
            #processProductTable input[type="text"] { width: calc(100% - 40px); }
            #evaluationSummaryTable th, #evaluationSummaryTable td { font-size: 0.85em; padding: 6px 8px; }
            #evaluationSummaryTable .p6-process-header td { padding-left: 15px !important; }
            #evaluationSummaryTable .p6-subelement-header td { padding-left: 30px !important; }
            #evaluationSummaryTable .question-row td { padding-left: 15px !important; }
            #evaluationSummaryTable .question-row td:not(:first-child) { padding-left: 15px !important; }
            #evaluationSummaryTable .p6-question-row td:first-child { padding-left: 45px !important; }
        }

        .top-right-actions {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
            gap: 5px; /* Adjust gap based on reference image */
            padding: 0 10px; /* Add some padding */
        }
        .tab-button {
            padding: 0; /* Adjusted padding to align height with action buttons */
            height: 38px; /* Set explicit height to match action buttons */
            font-size: 18px; /* Further increased font size */
            min-width: 90px; /* Adjusted minimum width for buttons */
            white-space: nowrap; /* Prevent text wrapping */
            box-sizing: border-box; /* Include padding and border in the element's total width and height */
            line-height: 38px; /* Ensure text is vertically centered within the 38px height */
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); /* Light gradient for base */
            border: 1px solid #c0c0c0; /* Soft border */
            border-radius: 4px; /* Slightly rounded corners */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.7), inset 0 -1px 0 rgba(0, 0, 0, 0.1); /* Soft outer shadow, top inner highlight, bottom inner shadow */
            transition: all 0.2s ease-in-out;
        }
        .tab-button:hover {
            background: linear-gradient(to bottom, #e0e0e0, #d0d0d0); /* Darker gradient on hover */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.8), inset 0 -1px 0 rgba(0, 0, 0, 0.15); /* More pronounced outer shadow, top inner highlight, bottom inner shadow */
            transform: translateY(-1px); /* Slight lift effect */
        }
        .tab-button.active {
            background: linear-gradient(to bottom, #007bff, #0056b3); /* Gradient for active state */
            color: #fff;
            border-color: #0056b3;
            background: #007bff; /* Pure blue for active state */
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2); /* Stronger inner shadow for pressed effect */
            transform: translateY(1px); /* Move down slightly on active */
        }
        .tab-button.active:hover {
            background: linear-gradient(to bottom, #0056b3, #004085); /* Darker gradient on hover for active */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
            transform: translateY(0); /* No lift on hover for active */
        }

        .tabs {
            display: flex;
            flex-wrap: wrap; /* Allow buttons to wrap to the next line */
            gap: 5px; /* Space between buttons */
            justify-content: flex-start; /* Align buttons to the left */
        }

        .top-right-actions button {
             display: inline-block;
             padding: 8px 10px; /* Add horizontal padding based on reference image */
             margin: 0; /* Ensure no default margin */
             cursor: pointer;
             border: none; /* Remove border */
             outline: none;
             box-shadow: none;
             background-color: #00529B; /* Dark blue background */
             color: #FFFFFF; /* White text */
             font-size: 18px;
             border-radius: 4px; /* Add border-radius based on reference image */
             transition: background-color 0.2s ease-in-out;
        }

        .top-right-actions button.clear-button {
            background-color: #D32F2F; /* Red color */
        }

        .top-right-actions button.clear-button:hover {
            background-color: #B71C1C; /* Darker red on hover */
        }

        .top-right-actions button:hover {
            background-color: #003366; /* Slightly darker blue on hover */
        }

        /* Specific colors based on reference image */
        

        .header-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #2c3e50; /* Dark background color */
            color: #ecf0f1; /* Light text color */
            border-radius: 8px;
        }

        .company-info {
            display: flex;
            align-items: center;
        }

        .page-title {
            flex-grow: 1;
            text-align: center;
            margin: 0;
            font-size: 24px;
            color: #ecf0f1;
        }

        /* Keep existing .top-right-actions and button styles */

    #auditPlanTable {
    margin-bottom: 50px;
}
.footer {
    border-bottom: 2px solid #000; /* Add a bottom border to the footer */
}
</style>
</head>
    <div class="container">
        <div class="header-bar" style="display: flex; align-items: center; justify-content: space-between; height: 48px;">
    <div style="display: flex; align-items: center; flex: 1;">
        <div id="logo-upload-area" style="width: 40px; height: 40px; margin-right: 8px; cursor: pointer; background: transparent; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 6px;" onclick="document.getElementById('logo-upload').click();">
    <img id="logo-preview" src="" alt="" style="max-width: 100%; max-height: 100%; display: none;" />
</div>
<input type="file" id="logo-upload" accept="image/*" style="display: none;">
<script>
document.getElementById('logo-upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(evt) {
            const img = document.getElementById('logo-preview');
            img.src = evt.target.result;
            img.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});
</script>
        <span id="company-name" style="font-size: 18px; font-weight: bold; cursor: pointer; margin-right: 18px;" onclick="editCompanyName()">金马汽车部件有限公司</span>
    </div>
 <div style="flex: 0 0 auto; display: flex; justify-content: center; align-items: center; height: 48px;">
        <span id="main-title" style="font-size: 36px; font-weight: bold; color: #fff; letter-spacing: 1px;">VDA 6.3过程审核工具</span>
    </div>
    <div style="flex: 1;"></div>

</div>
<script>
// 保证点击空白区可上传Logo
// 可选：上传后可在logo-upload-area显示预览
</script>
        <nav class="tabs" style="width: 100%; justify-content: space-between;">
            <div style="display: flex; flex-wrap: wrap;">
                <button class="tab-button active" onclick="showTab('auditPlan')">审核计划</button>
                <button class="tab-button" onclick="showTab('headerData')">基本信息</button>
                <button class="tab-button" onclick="showTab('questions')">提问表</button>
                <button class="tab-button" onclick="showTab('evaluationMatrix')">评价矩阵</button>
                <button class="tab-button" onclick="showTab('actionPlan')">措施计划</button>
                <button class="tab-button" onclick="showTab('auditChecklist')">审核清单</button>
                <button class="tab-button" onclick="showTab('auditReport')">审核报告</button>
                <button class="tab-button" onclick="showTab('instructions')">使用说明</button>
            </div>
            <div style="display: flex; gap: 5px;">
                <button onclick="saveData()" style="height: 38px; line-height: 38px; padding: 0 15px;">保存数据</button>
                <button onclick="clearData()" class="clear-button" style="height: 38px; line-height: 38px; background-color: #D32F2F; padding: 0 15px;">清除数据</button>
                <button onclick="loadData()" style="height: 38px; line-height: 38px; background-color: #FFA500; padding: 0 15px;">加载数据</button>
                <button onclick="exportToExcel()" style="height: 38px; line-height: 38px; padding: 0 15px;">导出CSV</button>
            </div>
        </nav>

        <div id="auditPlan" class="tab-content active">
            <h2>审核计划</h2>
            <div style="display: flex; align-items: center; gap: 10px;">
                 <button type="button" class="add-button" onclick="addAuditPlanRow()">+ 添加审核计划</button>
                 <div id="deleteConfirmPrompt" style="display: none; vertical-align: middle; padding: 4px 10px; border: 1px solid #ccc; background-color: #f0f0f0; border-radius: 3px; font-size: 14px;">
                     <p style="display: inline; margin-right: 10px; font-size: 15px;"><span style="color: #cc0000;">温馨提醒：</span><span style="color: #ff6666;">确定要删除此审核计划记录吗？</span></p>
                      <button id="confirmDeleteBtn" style="padding: 4px 8px; font-size: 13px; background-color: #007bff; color: white; border: none; border-radius: 2px; cursor: pointer;">确定</button>
                      <button id="cancelDeleteBtn" style="padding: 4px 8px; font-size: 13px; background-color: #6c757d; color: white; border: none; border-radius: 2px; cursor: pointer; margin-left: 5px;">取消</button>
                 </div>
             </div>
            <table id="auditPlanTable">
                <thead>
                    <tr>
                        <th style="text-align: center;">操作</th>
                        <th style="text-align: center;">报告编号</th>
                        <th style="text-align: center;">审核开始日期</th>
                        <th style="text-align: center;">审核结束日期</th>
                        <th style="text-align: center;">审核类别</th>
                        <th style="text-align: center;">审核目的</th>
                        <th style="text-align: center;">审核范围</th>
                        <th style="text-align: center;">过程要素</th>
                        <th style="text-align: center;">审核组长</th>
                        <th style="text-align: center;">审核员</th>
                        <th style="text-align: center;">备注</th>
                    </tr>
                </thead>
                <tbody id="auditPlanTableBody">
                    <tr id="noAuditPlanRow">
                        <td colspan="11" style="text-align: center; vertical-align: top; padding-top: 5px; color: #888;">点击上方“+添加审核计划”按钮新增审核计划</td>
                    </tr>
                </tbody>
            </table>
            <button type="button" class="add-button" onclick="syncPElementsWithBasicInfo()">应用选择并更新到基本信息页</button>
        </div>

        <div id="headerData" class="tab-content">
            <h2>审核基本信息</h2>
            <table>
                <tr><td><label for="companyName">公司名称:</label></td><td><input type="text" id="companyName"></td><td><label for="auditDate">审核日期:</label></td><td><input type="text" id="auditDate" placeholder="例如：YYYY-MM-DD" readonly></td></tr>
                <tr><td><label for="auditLocation">审核地点:</label></td><td><input type="text" id="auditLocation"></td><td><label for="auditStandard">审核项目:</label></td><td><input type="text" id="auditStandard" value="项目编号、产品或其他"></td></tr>
                <tr><td><label for="reportNumber">报告编号:</label></td><td><input type="text" id="reportNumber" readonly></td><td><label for="auditLanguage">审核语言:</label></td><td><input type="text" id="auditLanguage" value="中文"></td></tr>
            </table>
            <h2>工序 / 产品矩阵 (最多5工序 x 5产品)</h2>
            <table id="processProductTable">
                <thead><tr><th>工序 <button type="button" class="add-button small-button" onclick="addProcessRow()">+ 工序</button></th><th><button type="button" class="add-button small-button" onclick="addProductColumn()">+ 产品</button></th></tr></thead>
                <tbody></tbody>
            </table>
            <h2>审核员信息 (最多5名)</h2>
            <table id="auditorTable">
                <thead><tr><th>No.</th><th>审核员姓名</th><th>审核员类别</th><th>邮箱</th><th>电话</th><th>操作</th></tr></thead>
                <tbody></tbody>
            </table>
            <button type="button" class="add-button" onclick="addAuditorRowClickHandler()">+ 添加审核员</button>

            <h2>选择审核的过程要素</h2>
            <div class="p-element-selection-group checkbox-group">
                <label><input type="checkbox" name="pElement" value="P2" onchange="handlePElementSelectionChange(this)"> P2 项目管理</label>
                <label><input type="checkbox" name="pElement" value="P3" onchange="handlePElementSelectionChange(this)"> P3 产品和过程开发的策划<span id="p3ProductCheckboxContainer" style="display:none;"><label><input type="checkbox" id="p3ProductSelected" onchange="auditData.header.p3ProductSelected = this.checked;"> 产品</label></span></label>
                <label><input type="checkbox" name="pElement" value="P4" onchange="handlePElementSelectionChange(this)"> P4 产品和过程开发的实现<span id="p4ProductCheckboxContainer" style="display:none;"><label><input type="checkbox" id="p4ProductSelected" onchange="auditData.header.p4ProductSelected = this.checked;"> 产品</label></span></label>
                <label><input type="checkbox" name="pElement" value="P5" onchange="handlePElementSelectionChange(this)"> P5 供方管理</label>
                <label><input type="checkbox" name="pElement" value="P6" onchange="handlePElementSelectionChange(this)"> P6 生产过程分析</label>
                <label><input type="checkbox" name="pElement" value="P7" onchange="handlePElementSelectionChange(this)"> P7 顾客服务</label>
            </div>
            <div style="text-align:left; margin-top:15px; margin-left: -5px;"><button type="button" id="syncQuestionnaireButton" class="button-style green-button" onclick="updateQuestionnaireAndMatrix()">应用选择并更新提问表</button></div>
        </div>

        <div id="questions" class="tab-content">
            <h2>提问表</h2>
            <div style="margin-bottom: 15px; display: flex; align-items: center;">
                <button type="button" onclick="scoreAllVisibleTen()" class="button-style">全部打10分</button>
                <button type="button" onclick="randomlyScoreAllVisible()" class="button-style">随机打分</button>
                <button type="button" onclick="clearAllScores()" class="delete-button">清除所有分数</button><div id="score-clear-message" style="margin-left: 10px; display: none;"><span style="color: red;">温馨提醒：</span><span style="color: orange;">提问不可见无法打分，请点击过程要素项目展开提问表</span></div>
            </div>
            <div style="margin-bottom: 15px;">
                <button type="button" class="button-style" onclick="printQuestions()">打印提问表</button>
            </div>
            <div id="questionnaireContainer"><p style="text-align:center; color: #666;">请先在“基本信息”页选择过程要素并点击“应用选择并更新提问表”。</p></div>
        </div>

        <div id="evaluationMatrix" class="tab-content">
            <h2>评价矩阵</h2>
            <div id="evaluationSummaryTableContainer">
                 <h3>评估表 (详细分数)</h3>
                 <table id="evaluationSummaryTable">
                    <thead>
                        <tr>
                            <th>过程要素 / 工序 / 子要素 / 提问</th>
                            <th>产品得分</th>
                            <th>过程得分</th>
                            <th>最终得分 / 符合率%</th>
                            <th>等级</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td colspan="5" style="text-align:center; vertical-align: top; padding-top: 5px; color:#666;">评分后将在此处显示详细评估结果。</td></tr>
                    </tbody>
                 </table>
            </div>
            <hr style="margin: 30px 0;">
            <div id="evaluationMatrixContainer"><p style="text-align:center; color: #666;">评分后将在此处显示总体评价结果。</p></div>
             <div style="text-align:center; margin-top:20px;"><button type="button" class="button-style" onclick="addAuditToChecklist()">添加到清单</button></div>
        </div>

        <div id="actionPlan" class="tab-content">
            <h2>措施计划</h2>
            <table id="actionPlanTable">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>提问编号</th>
                        <th>分值</th>
                        <th>审核记录</th>
                        <th>发现/不符合项 (请填写)</th>
                        <th>负责人 (请填写)</th>
                        <th>措施 (请填写)</th>
                        <th>计划完成日期 (请填写)</th>
                        <th>状态 (请选择)</th>
                        <th>验证有效性 (请填写)</th>
                    </tr>
                </thead>
                <tbody id="actionPlanTableBody">
                    <tr id="actionPlanPlaceholderRow"><td colspan="10" style="text-align:center; vertical-align: top; padding-top: 5px; color: #666;">评分后，得分非10分的提问将在此处列出。</td></tr>
                </tbody>
            </table>
            <button type="button" class="add-button" onclick="syncPElementsWithBasicInfo()">应用选择并更新到基本信息页</button>
        </div>

        <div id="auditReport" class="tab-content">
            <style>
                .report-header {
                    background-color: #d6e4f0;
                    color: #003366;
                    padding: 10px;
                    text-align: center;
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .report-section {
                    border: 1px solid #ccc;
                    margin-bottom: 15px;
                    border-radius: 15px;
                }
                .report-section-title {
                    background-color: #d6e4f0;
                    color: #003366;
                    padding: 8px;
                    font-weight: bold;
                    border-bottom: 1px solid #ccc; /* 添加底部边框 */
                    border-top-left-radius: 15px; /* 保持顶部圆角与父容器一致 */
                    border-top-right-radius: 15px; /* 保持顶部圆角与父容器一致 */
                }
                .report-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                    gap: 10px;
                    padding: 10px;
                }
                .report-grid-item {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }
                .report-grid-item input[type="text"] {
                    flex-grow: 1;
                    min-width: 0;
                }
                .report-grid-item label {
                    font-weight: bold;
                    margin-right: 5px;
                }
                .report-table-container {
                    padding: 0px;
                    padding-bottom: 0px;
                    border-top: none;
                    border-left: 1px solid #ccc;
                    border-right: 1px solid #ccc;
                    border-bottom: none;
                    border-bottom-left-radius: 15px;
                    border-bottom-right-radius: 15px;
                    margin-top: 0;
                }
                .report-table {
                    width: 100% !important;
                    table-layout: fixed !important;
                    border-collapse: collapse;
                    border-spacing: 0;
                    border: none; /* 移除表格自身的边框 */
                    margin-bottom: 0;
                }
                .report-table tr:first-child th:first-child {
                    border-top-left-radius: 15px;
                }
                .report-table tr:first-child th:last-child {
                    border-top-right-radius: 15px;
                }
                .report-table tr:last-child td:first-child {
                    border-bottom-left-radius: 15px;
                }
                .report-table tr:last-child td:last-child {
                    border-bottom-right-radius: 15px;
                }

                .report-table th, .report-table td {
                    width: auto !important;
                    word-wrap: break-word !important;
                    border-top: none;
                    border-bottom: 1px solid #ccc;
                    border-left: none;
                    border-right: none;
                    padding: 5px 8px;
                    height: 35px;
                    text-align: center;
                    vertical-align: middle;
                    box-sizing: border-box;
                }
                .report-table th {
                    background-color: transparent;
                    color: inherit;
                }


                .full-width-textarea {
                    width: calc(100% - 20px);
                    min-height: 100px;
                    margin: 10px;
                    padding: 5px;

                }
                .signature-section {
                    display: flex;
                    justify-content: space-between;
                    padding: 20px;
                    font-size: 14px;
                }
                .signature-item {
                    text-align: left;
                }
            </style>
            <div class="report-header" style="background-color: #d6e4f0; color: #003366;">VDA 6.3:2023 审核报告</div>

            <div class="report-section">
                <div class="report-grid">
                    <div class="report-grid-item"><label>供方:</label><input type="text"></div>
                    <div class="report-grid-item"><label>供应商代码:</label><input type="text"></div>
                    <div class="report-grid-item"><label>邓白氏码:</label><input type="text"></div>
                    <div class="report-grid-item"><label>日期:</label><input type="text"></div>
                    <div class="report-grid-item"><label>地址:</label><input type="text"></div>
                    <div class="report-grid-item"><label>审核申请:</label><input type="text"></div>
                    <div class="report-grid-item"><label>部门:</label><input type="text"></div>
                    <div class="report-grid-item"><label>班次:</label><input type="text" style="flex-grow: 1; border-radius: 3px;"></div>
                    <div class="report-grid-item"><label>审核缘由:</label><input type="text" style="flex-grow: 1; border-radius: 3px;"></div>
                    <div class="report-grid-item"><label>委托编号:</label><input type="text" style="flex-grow: 1; border-radius: 3px;"></div>
                </div>
            </div>

            <div class="report-section">
                <div class="report-section-title">审核结果</div>
                <div class="report-table-container">
                    <table class="report-table">
                        <thead>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td></td>
                                <td>E<sub>G</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>评价产品组</td>
                                <td>E<sub>PN</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>E<sub>PN</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>E<sub>PN</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>E<sub>PN</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>E<sub>PN</sub></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="font-size: 14px; margin-top: 10px; margin-left: 20px; border-top: 1px solid #ccc; padding-top: 8px;">评级分档: A=EG or EG(Pn)≥90% 具备质量能力; B=80%≤EG or EG(Pn)<90% 有条件的具备质量能力; C=EG or EG(Pn)<80% 不具备质量能力</p>
                </div>
            </div>

            <div class="report-section">
                <div class="report-section-title" style="background-color: #d6e4f0; color: #003366;">声明/要求</div>
                <label for="auditSummary" style="margin-left: 10px;">审核落实情况综述</label>
                <textarea class="full-width-textarea" id="auditSummary"></textarea>
                <label for="majorFindings" style="margin-left: 10px;">审核期间识别的重大发现</label>
                <textarea class="full-width-textarea" id="majorFindings"></textarea>
                <label for="conclusion" style="margin-left: 10px;">结论</label>
                <textarea class="full-width-textarea" id="conclusion"></textarea>
                <label for="furtherActions" style="margin-left: 10px;">进一步的行动</label>
                <textarea class="full-width-textarea" id="furtherActions"></textarea>
            </div>

            <div class="report-section">
                <div class="report-section-title" style="background-color: #d6e4f0; color: #003366;">上次审核结果/证书</div>
                <div class="report-table-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>认证/审核编号:</th>
                                <th>发布日期:</th>
                                <th>实施</th>
                                <th>结果/证书编号:</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                            </tr>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-section">
                <div class="report-section-title" style="background-color: #d6e4f0; color: #003366;">出席人员</div>
                <div class="report-table-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>审核组</th>
                                <th>审核机构:</th>
                                <th>管理人员</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="text-align: center; vertical-align: top; padding-top: 5px;"><span style="white-space: nowrap;">高层管理:</span><input type="text" style="width: calc(100% - 80px); height: 35px; border: 1px solid #ccc; border-radius: 3px; margin-left: 5px; display: inline-block; box-sizing: border-box;"></td>
                            </tr>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="text-align: center; vertical-align: top; padding-top: 5px;"><span style="white-space: nowrap;">工厂管理:</span><input type="text" style="width: calc(100% - 80px); height: 35px; border: 1px solid #ccc; border-radius: 3px; margin-left: 5px; display: inline-block; box-sizing: border-box;"></td>
                            </tr>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="text-align: center; vertical-align: top; padding-top: 5px;"><span style="white-space: nowrap;">质量代表:</span><input type="text" style="width: calc(100% - 80px); height: 35px; border: 1px solid #ccc; border-radius: 3px; margin-left: 5px; display: inline-block; box-sizing: border-box;"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-section">
                <div class="report-section-title" style="background-color: #d6e4f0; color: #003366;">行动方案时间表</div>
                <div class="report-table-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>确认措施</th>
                                <th>有效性检查</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                                <td style="vertical-align: top; padding-top: 5px;"><input type="text" style="width: 100%; height: 35px; border: 1px solid #ccc; border-radius: 3px; box-sizing: border-box;"></td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="font-size: 14px; margin-top: 10px; margin-left: 20px;">审核中未达10分的评价提问请建立纠正措施表，进行原因分析并采取纠正措施。</p>
                </div>
            </div>

            <div class="signature-section">
                <div class="signature-item"><label>审核员:</label><input type="text"></div>
                <div class="signature-item"><label>受审核方经理:</label><input type="text"></div>
                <div class="signature-item"><label>签字 (组织):</label><input type="text"></div>
            </div>
            <div class="signature-section" style="padding-top: 0;">
                <div class="signature-item"><label>证书编号:</label><input type="text"></div>
            </div>
        </div>

        <div id="auditChecklist" class="tab-content">
            <h2>审核清单</h2>
            <table id="auditChecklistTable">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>报告编号</th>
                        <th>审核日期</th>
                        <th>总体评价等级</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="auditChecklistTableBody">
                    <tr><td colspan="5" style="text-align:center; vertical-align: top; padding-top: 5px; color: #666;">点击“评价矩阵”页的“添加到清单”按钮添加审核记录。</td></tr>
                </tbody>
            </table>
            <button type="button" class="add-button" onclick="syncPElementsWithBasicInfo()">应用选择并更新到基本信息页</button>
        </div>


        <div id="instructions" class="tab-content">
            <h2>使用说明</h2>
            <p>本工具为VDA 6.3:2023过程审核的</strong>，旨在帮助审核员熟悉审核流程和工具功能。部分功能相较于完整版有所限制。</p>
            <h3>基本信息页：</h3>
            <ul>
                <li><strong>审核基本信息：</strong> 填写公司名称、审核日期、审核地点、报告编号等。</li>
                <li><strong>工序/产品矩阵：(最多5个工序，最多5个产品)</strong>
                    <ul>
                        <li>点击"+ 工序"添加新工序行 (上限5个)。</li>
                        <li>点击"+ 产品"添加新产品列 (上限5个)。</li>
                        <li>在输入框中填写工序和产品名称。这些名称将用于P6提问表和产品组评价。</li>
                        <li>勾选工序与产品之间的复选框，以表明该产品会流经该工序。此关联是产品组评价P6部分得分的关键。</li>
                        <li>至少需要定义一个工序和一个产品才能进行有效的P6评价和产品组评价。</li>
                    </ul>
                </li>
                <li><strong>审核员信息：(最多5名审核员)</strong> 点击"+ 添加审核员"添加审核员的详细信息 (上限5名)。</li>
                <li><strong>选择审核的过程要素：</strong>
                    <ul>
                        <li>勾选P2至P7中本次审核需要覆盖的过程要素。提问表将仅显示选中的要素。</li>
                        <li><strong>P3 和 P4 的特殊选项：</strong> 若选中P3或P4，其右侧会出现额外的"产品"复选框。
                            <ul>
                                <li>若勾选此"产品"复选框，则P3和P4中除P4.7外的每个提问，都需要分别对"产品开发方面"和"过程开发方面"进行评分。该提问的最终分数将是这两个评分的平均值。</li>
                                <li>若不勾选P3或P4后的"产品"复选框，则P3和P4的提问仅需一个总评分（默认为针对"过程"）。</li>
                                <li>P4.7（在P4中）始终只有一个评分项，针对"过程"，不受P4"产品"复选框影响。</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>完成所有输入和选择后，务必点击页面底部的 <strong>“应用选择并更新提问表”</strong> 按钮。这将根据您的设置生成后续“提问表”页面的内容，并为“评价矩阵”准备计算基础。</li>
            </ul>
            <h3>提问表页：</h3>
            <ul>
                <li>此页面将根据“基本信息”页的选择动态显示对应的过程要素及其提问。</li>
                <li><strong>提问下方不再显示“最低要求”和“实施示例”。</strong></li>
                <li><strong>P6 生产过程分析：</strong> 如果在基本信息页定义了多个工序，P6部分将为每个工序重复生成一套完整的P6提问。请为每个工序的P6问题分别打分。</li>
                <li><strong>评分：</strong> 对每个问题（或P3/P4的"产品"/"过程"子项），从下拉框中选择评分 (10, 8, 6, 4, 0, n.e. - 不评价)。带星号(*)的为重点提问。</li>
                <li><strong>审核记录与附件：</strong> 每个评分项下方提供手动填写审核记录的文本框和添加附件（仅记录文件名）的选项。请填写相关观察、证据或发现。</li>
                <li><strong>展开/折叠：</strong> 每个过程要素的标题（如“P2 项目管理”）均可点击，以展开或折叠其下的问题列表，方便查阅。初始状态为展开。</li>
                <li><strong>2/3规则：</strong> 每个被审核的过程要素（对于P6，指每个工序的P6问题集），至少需要评价其总提问数（不包括标记为n.e.的问题）的2/3（结果向上取整）。如果某个要素（或P6的某个工序）未达到此要求，则该要素（或该工序的P6）的符合度(EPn/En)评价无效，进而导致总体评价(EG)或依赖此工序P6的产品组评价也无效。</li>
                <li><strong>全部打10分按钮 (新增)：</strong> 为当前所有可见的、尚未评分的问题赋予10分。这有助于快速评估最佳情况或初始化评分。</li>
                <li><strong>随机打分按钮：</strong> 为当前所有可见的、尚未评分的问题随机赋予分数。规则：0分最多1个，4分最多3个，6分最多3个，8分最多10个，其余为10分。这有助于快速模拟审核结果。</li>
                <li><strong>清除所有分数按钮：</strong> 清除提问表上所有已填写的评分、手动审核记录和附件名，并将分数重置为空白。</li>
            </ul>
            <h3>评价矩阵页：</h3>
            <ul>
                <li>此页面根据“提问表”中的打分自动计算并显示审核结果。</li>
                <li>**新增评估表 (详细分数):** 此表格位于总体评价之前，详细列出每个P要素、其下每个问题（P3/P4拆分显示产品/过程/平均分）、P6的每个工序及其下每个子要素(EUx)和每个问题的得分/符合率。</li>
                <li><strong>符合度等级：</strong> A (≥90%), B (≥80% 至 <90%), C (<80%)。等级会以颜色（绿、黄、红）和放大字号醒目显示。如果因2/3规则未满足而无效，则显示“无效”。</li>
                <li><strong>降级规则 (VDA 6.3:2023 核心)：</strong>
                    <ul>
                        <li><strong>从 A 自动降级到 B (即使总体符合度 EG ≥ 90%):</strong>
                            <ul>
                                <li>任一过程要素 (P2-P7) 的符合度 EPn < 80%。</li>
                                <li>任一P6工序的符合度 En(P6) < 80%。</li>
                                <li><strong>P6的任一子要素 (EU1-EU6) 的符合度 EUn < 80%。 (已根据VDA 6.3:2023文件更新此条规则)</strong></li>
                                <li>至少一个星号(*)提问评分为4分 (在任何相关要素或P6工序中)。</li>
                                <li>至少一个提问（任何提问，非星号）评分为0分 (在任何相关要素或P6工序中)。</li>
                            </ul>
                        </li>
                        <li><strong>自动降级到 C (即使总体符合度 EG ≥ 80% 或已从A降至B):</strong>
                            <ul>
                                <li>任一过程要素 (P2-P7) 的符合度 EPn < 70%。</li>
                                <li>任一P6工序的符合度 En(P6) < 70%。</li>
                                <li>至少一个星号(*)提问评分为0分 (在任何相关要素或P6工序中)。</li>
                            </ul>
                        </li>
                         <li>**重要逻辑：** 如果一个 A 级审核同时满足导致 B 级的条件和导致 C 级的条件，则直接降级到 C 级。如果仅满足导致 B 级的条件，则降到 B 级。</li>
                    </ul>
                </li>
                <li>如果发生降级，系统会自动列出具体的降级原因。</li>
                <li><strong>产品组评价：</strong> 如果在基本信息页定义了多个产品并将其与工序关联，此处会为每个产品组单独计算符合度 EG(PGn)。其计算包括与该产品组相关的所有选定过程要素的得分，P6部分的得分则精确基于该产品组流经的工序的P6审核结果。同样适用上述降级规则。</li>
            </ul>
             <h3>审核清单页 (新增):</h3>
            <ul>
                <li>此页面展示了历次审核的概要信息清单。</li>
                <li>清单中的数据通过点击“评价矩阵”页的“添加到清单”按钮生成。</li>
                <li>点击清单中某一行的“报告编号”可以加载并查看该次审核的详细数据。</li>
                <li>点击每一行末尾的“删除”按钮可以从清单中移除该条审核记录。</li>
                <li>**重要：** 审核清单数据会保存在浏览器本地存储中。关闭浏览器再打开，清单内容会保留。</li>
            </ul>
            <h3>措施计划页：</h3>
            <ul>
                <li>系统会自动提取“提问表”中所有评分不等于10分（即0, 4, 6, 8分）的问题，作为需要制定改进措施的项目。</li>
                <li>"审核记录"列会自动从提问表中对应问题的“审核记录(手动填写)”部分提取内容。对于P3/P4拆分问题，如果其产品或过程子项分数低于10分，则其对应的审核记录会显示在措施计划中。</li>
                <li>措施计划表格新增了 **No. (序号)** 和 **分值** 列，方便查阅。</li>
                <li>用户需要为每个列出的项目手动填写“发现/不符合项”描述、负责人、“具体措施”、“计划完成日期”和“验证有效性”等信息。“状态”可从下拉框选择。</li>
            </ul>
            <h3>通用功能：</h3>
            <ul>
                <li><strong>保存数据：</strong> 将当前所有页面的输入（包括基本信息、工序产品矩阵、审核员信息、过程要素选择、提问表的所有分数、手动审核记录、附件名以及措施计划中已填写的内容）保存到您浏览器的本地存储(localStorage)中。**审核清单数据也会在添加或删除时自动保存到本地存储。**</li>
                <li><strong>加载数据：</strong> 从浏览器本地存储中加载上次保存的审核数据。加载成功后，所有页面将恢复到上次保存的状态。审核清单也会从其独立的本地存储中加载。</li>
                <li><strong>导出功能：</strong>
                    <ul>
                        <li><strong>Markdown：</strong> 导出一个文本文件，包含审核的主要信息和结果，采用Markdown格式，方便编辑和转换。</li>
                        <li><strong>Excel (CSV)：</strong> 导出一个CSV格式的文件，可以用Excel等表格软件打开。主要包含基本信息、问题列表及评分、手动审核记录、附件名、评价总结等关键信息。</li>
                    </ul>
                </li>
                <li>**自动加载：** 每次打开此HTML文件时，工具会自动尝试从本地存储中加载上次保存的审核数据及审核清单。</li>
            </ul>
            <p style="font-weight:bold; color: #003366;">建议：为确保数据安全，请在完成重要阶段的输入后，及时使用“保存数据”功能。在不同电脑或浏览器间同步数据，请使用导出/导入功能（如此版本未直接提供导入，可考虑复制粘贴关键数据或使用导出文件作为备份）。</p>
        </div>


    </div>

    <footer>
        版权所有 © 吴志明| 电话&微信：13959240478 | <span style="font-family: 'KaiTi', '楷体', serif; font-weight: bold;">弘扬匠心、传递知识、为企业创造价值！</span>
    </footer>

    <script>
    // --- START OF VDA 6.3:2023 Questions Data (Chinese) ---
    // (JavaScript 数据部分保持不变，因为 minReq 和 implEx 只是不在渲染时使用)
    const vdaQuestionsData = {
    "P2": {
        name: "P2: 项目管理",
        questions: [
            { id: "P2.1", text: "是否已建立了项目管理战略（包括项目组织机构）？", isStar: false, minReq: "存在项目管理的过程。\n确定了跨部门的项目组织机构。\n规定了项目团队成员的职责、能力和授权。\n将联络人信息通知顾客和供方。\n项目组织机构和相关升级管理满足顾客的要求。\n明确了升级标准（包括在供方管理中的升级），并且在存在偏差的时候采取措施。\n角色的定义符合开发方法和协作模型（敏捷型或非敏捷型）。\n考虑从正在进行的或以前的具有可比产品范围的项目中获得的经验（特别是经验教训）。\n识别、评价了项目风险，并通过适当的措施降低风险。", implEx: "定义项目经理/技术专家的角色、任务、能力以及责任\n多场所项目的项目接口\n项目组织机构图\n项目组的组成\n资质证明\n顾客对项目管理的特定要求\n如果是敏捷开发方法：定义升级机制（例：项目经理升级至产品负责人）\n定义升级流程中的联络人/决策者\n里程碑评价的记录，包括措施" },
            { id: "P2.2", text: "是否策划了项目实施需要的所有资源，且已经到位，并且报告了变更情况？", isStar: true, minReq: "基于项目协议，在资源策划中考虑了顾客要求。\n建立并落实了针对项目团队成员的资源规划。考虑了员工的工作负荷。\n当发生变更（截止期限、开发范围绩效等）时，那么就必须对资源策划开展评审和（必要时）进行调整。在资源策划中应特别关注关键路径。\n策划并批准了针对人员和设备需要的项目预算。\n项目组织机构发生变更时（与顾客的接口）需进行报告。\n已经策划了与软件相关活动的资源。", implEx: "资源策划的证明（考虑其他顾客项目）\n设备的资源策划（例如：开发试验台架、检验和实验室设施）\n软件开发活动的资源策划和软件项目管理" },
            { id: "P2.3", text: "是否编制了项目计划并与顾客达成一致，且落实了项目管理？", isStar: false, minReq: "项目计划满足顾客的特定要求。\n所有内部及顾客里程碑都应被完整的纳入项目计划。\n应评审项目计划中所定义的里程碑，检查所有计划的事项都已落实，并达到了要求的成熟度水平。\n如果产品特别需要法定授权程序，则项目计划中应包括程序的持续时间。\n项目计划发生变更时，应确保内部沟通。会对顾客产生影响的项目计划变更，则需要与顾客协商沟通。\n需要在升级过程（风险管理）中考虑对总体进度有影响的项目变更。\n来自项目计划的关键路径中应考虑相关的交付项。\n与质量相关的项目活动和采购活动必须是项目计划的组成部分。项目计划中可引用单独的详细计划。\n计划必须包括原型件和试生产件。\n应计划在各个对应时间段内的软件的范围。", implEx: "包含里程碑的项目计划\n有关技术和/或产品组的顾客特定要求\n顾客的项目计划\n顾客的截止日期\n顾客的里程碑\n顾客设定的目标（具体里程碑的测量）\n里程碑评估（评审）\n质量管理计划（例如：VDA MLA或APQP）\n特定国家或地区的认证要求（ECE、SAE、DOT、CCC、INMETRO、KBA等）\n关键生产线的法律法规批准过程（环境要求，或者其他）\nASPICE评估的策划，包括根据顾客规范定义的级别\n含里程碑的软件放行计划" },
            { id: "P2.4", text: "是否策划了与质量相关的项目活动，并监视了其符合情况？", isStar: false, minReq: "质量相关的项目活动满足顾客的特定要求。\n质量相关活动包含产品和过程保证措施。\n计划必须包含产品和过程要求的验证和确认。\n计划同样需要考虑关键零部件和供应项目（内部和外部供方）。\n应定期监控计划的符合性和目标的达成情况。\n已考虑与软件相关的质量相关活动。", implEx: "项目计划\n顾客的里程碑\n与质量计划相关的顾客要求\n质量管理计划（例如VDA MLA或APQP）\n审核和评价计划\n顾客规范\n特定的软件里程碑，例如：代码和模型评审\n创建和确认测试案例\n软件相关的KPI（指标、测试覆盖率、测试自动化水平、错误减少比例等）\n软件的质量批量/放行（SW-Q）\n风险评估（特殊特性、网络安全）" },
            { id: "P2.5", text: "项目的采购活动是否包含在项目计划中？", isStar: false, minReq: "在项目计划中应包含所有类型供方的供方选择和发包目标日期。\n计划中应包括生产线、机器、工具、测量和检验系统以及服务供方 （例如：开发、实验室、维护、软件）。\n计划中应包括发包目标日期、供方里程碑和放行的截止日期，并与整体计划相协调和匹配。", implEx: "项目计划\n里程碑计划\n决定是自制或外购" },
            { id: "P2.6", text: "项目组织是否确保了项目中的变更管理？", isStar: true, minReq: "项目中的变更管理需要满足顾客的特定要求。\n针对变更（供方、内部或者顾客发起的变更），应进行评价。需要时，调整项目计划。评价应包括对产品质量以及项目截止日期的风险评估。\n供方应主动参与到变更管理中。\n确保遵守规定的设计冻结。针对例外情况 顾客和供方应协商并记录。\n所有的变更必须记录。\n变更管理中，应规定顾客、内部和供方的负责人员。", implEx: "变更管理\n过程描述\n进度表\n变更表格\n产品和过程的变更历史\n变更评价\n变更批准\n软件：变更申请、变更管理\n因故障排除、架构和要求变化导致的变更" }
        ]
    },
    "P3": {
        name: "P3: 产品和过程开发的策划",
        questions: [
            { id: "P3.1", text: "是否明确了产品和过程的具体要求？", isStar: false, minReq: "确保明确了包括顾客要求以及法律和法规要求在内的应用在产品（和软件）上的功能性和非功能性要求。\n组织应识别并考虑与产品和过程相关的以往经验的要求。\n必须根据组织自身的要求、顾客要求、法律法规要求、制造技术以及产品目的/用途的基础上识别了特殊特性。\n顾客在供方和/或原材料选择方面的要求应被考虑在内。\n如果是顾客指定的供方（指定供方），应具备接口协议（三方协议）。\n顾客对于文档以及免费和开源软件（FOSS）放行的要求应被考虑在内。", implEx: "产品/生产过程开发：\n对于硬件和软件之间的接口要求（带有集成/嵌入式软件的产品）\n包括要求规范在内的询价及合同文件\n可追溯性方案\n订购和检验要求\n特性清单/参考样件\n包括功能安全特性在内的产品/过程特性\n采购条款\n物流要求（包装、JIT、JIS、托管）\n质量协议，包括QM特定要求\n时间计划\n互联网上的门户网站/信息平台\n经验教训\n环保、回收再利用要求\n能力要求\n对于放行的要求\n产品开发：\n规范、图纸\n软件规范\n生产过程开发：\n对于生产钱、工具、检验设备的要求以及生产和检测工位布局的要求\n关于搬运、包装、储存和标识的要求\n软件识别、软件配置以及确保软件正确安装的要求" },
            { id: "P3.2", text: "根据产品和生产过程的要求，是否对可行性进行了全面评估？", isStar: true, minReq: "采用跨学科程序评估可行性（包括潜在生产场所）。\n所有明确的产品和过程特殊要求(技术、功能、质量、物流、软件等)应针对可行性进行检查。\n在可行性研究中，应考虑物质和人力资源。\n可行性研究的结果应在提交报价前完成。\n应确保外购件的可行性。\n如果顾客要求无法被满足 应告知顾客 顾客可以在合同授予前批准该偏差。\n应具备生产线以及使用过程更新的程序刷写方案（如有要求）。", implEx: "产品/生产过程开发：\n顾客要求和标准\n时间安排，时间框架\n法律、标准、法规、环境影响\n产品责任要求\n可追溯性方案\n建筑，空间\n计算机辅助制造、计算机辅助质量\n产品/过程创新\n跨学科的可行性研究 （例如：销售、开发、采购、生产计划、生产、质量管理策划、物流）\n产品开发：\n实验室/试验设备\n并行软件开发 / 原型开发\n生产过程开发：\n产能监控\n材料到位情况\n制造选择、制造地点\n生产线、工具、生产/检验设备、辅料、实验室设施、运输方式、容器、存储\n变型（或翻译变体）管理，刷写方案" },
            { id: "P3.3", text: "是否详细策划了产品和生产过程开发的活动？", isStar: false, minReq: "在产品和生产过程开发策划时应根据零部件、软件和过程的复杂程度考虑策划的详细程度。\n在开发阶段，为保证产品和生产过程的开发，应使用合适的降低风险的方法以确保产品进入批量生产时满足要求的使用条件（功能 可靠性 安全性）。\n在产品和过程创新的情况下，应有一个应变方案。\n风险分析应是策划的一部分。\n检验策划方案包括对于批量生产、产品审核和再鉴定的要求。（再鉴定[官方翻译再评定]在VDA 6.5都已经改为全尺寸检验了，为什么这里没有改？）。\n时间表应包含所有产品和生产过程开发的信息（包括时间期限和持续时间、符合整体项目计划的里程碑、性能测试、产品和过程批准时间、软件标准等）。\n开发放行的方法和证据应符合顾客要求并且在出现偏差时寻求与顾客的澄清。\n软件工程过程已经被明确规定并且符合顾客要求。\n软件开发进程也应在策划中被考虑，以确保要求的软件功能已经测试并且最终在要求的时间点可用。", implEx: "产品/生产过程开发：\n整体项目计划 \n顾客要求\n顾客时间安排\n交货周期\n包括备选策略和网络安全在内的降低风险的方法（QFD、FMEA、HARA等）\n原型件/试生产策划\n定期检查开发进度状态（评审）\n针对投资（设施和生产线）的项目计划\n针对产品和过程开发全阶段的物流策划，包括包装\n备件方案\n产品开发：\n可靠性试验、功能试验、试生产计划\n开发阶段样件的截止日期\n要求分析\n架构设计\n实施\n测试（例如产品确认）\n敏捷项目管理的组件（产品待办列表、迭代待办列表、增量、就绪定义、完成定义、迭代计划，开发和运营)\n放行计划\n生产过程开发：\n工具截止日期（量产工具制造的零件） \n检验策划、检验设备策划、包括备件管理的维护保养策划" },
            { id: "P3.4", text: "是否计划了采购活动，并监视了其符合情况？", isStar: true, minReq: "选择准则应被定义以确保所选供方具备质量能力。\n对非顾客指定的新供方/新生产场地或新技术已经计划或实施了潜在供方分析。\n授予合同后应根据采购产品/服务的风险等级确定活动策划的范围。\n对于顾客要求在整个供应链中沟通有明确的规则。\n顾客要求也包括图纸要求，零件、软件或零部件规范要求，交付数量要求，截止日期要求，质量协议要求以及适用法规要求。\n针对由顾客指定供方（指定的供方）的协议应基于具体项目进行定义。\n已经确定和计划了采购生产线、机器、工具、测量和检测设备以及服务的相关活动（选择、授予合同、验证和批准）。\n监控了供方活动（例如：授予合同、截止日期、顾客和供方的里程碑）的进度。", implEx: "产品/生产过程开发：\n供方选择准则\nVDA 6.3潜在供方分析和/或针对软件的类似方法\nVDA 6.3 审核策划\n供方管理（供应商开发、传递顾客要求）、PPA程序、失效分析、质量、保修、沟通\n接口协议（根据DIA或接口协议进行服务）\n包括服务供方（例如：开发、实验室、维护、软件）在内的项目供方清单\n获得供货范围内项目和活动的风险分级（VDA MLA）\n第三方软件\n免费和开源软件(FOSS ) 放行的准则" },
            { id: "P3.5", text: "针对产品和生产过程开发的策划，是否考虑了必要的资源？", isStar: false, minReq: "项目所需的资源已经确定并予以记录。\n已规划了针对原型件制造、样件生产、试生产、性能测试以及批量生产实施所需的产能。\n资源策划应定期根据项目的变化加以调整，并且针对潜在的瓶颈采取了措施。\n当引入新技术和新产品时，应计划持续性的员工培训，并且计划持续性的员工培训，并且确保创造了必要的基础设施。\n内部运输的运输方式已经策划，例如包装和特殊运货车，所需数量也已经确定。", implEx: "产品/生产过程开发：\n员工培训策划，顾客服务（0公里和使用现场）以及其它\n资格矩阵\nCAx 设备\n为不同任务配备了有资质的人员\n预算、基础设施（如厂房）、检验设备（软硬件）、实验室设备、机器、生产线\n所有资源的产能策划\n产能测试、节拍生产、2日生产\n软件的提供和分发\n工具链策划（软件开发工具）\n产品开发：\n测试/检验/实验室设施（内部和外部）\n策划和实施测试以及修复程序故障所需的资源\n在硬件上安装软件所需的资源（如：刷写、编码、编程）\n生产过程开发：\n生产地点、工具、生产和检验设备、基础设施" },
            { id: "P3.6", text: "是否策划了针对顾客服务和使用现场失效分析的活动？", isStar: false, minReq: "顾客要求应在整个产品生命周期的供货方案中得以考虑 包括备件的供应。\n供货方案中包括能持续确保批量供货的应急计划。\n已针对交付和供应链策划了0公里和使用现场投诉的分析流程。考虑了顾客对使用现场失效分析的要求。\n投诉过程的接口已得到策划。\n已经建立并维护了一套软件的访问权限控制方案。\n已经进行了对利益相关方的分析。已经明确规定了沟通和升级路径。", implEx: "产品/生产过程开发：\n投资策划\n产品开发：\n顾客投诉分析中心的接口，包括相关软件\n诊断访问\n访问控制列表\n生产过程开发：\n标准检验和负载测试的检验计划\n定义了触发准则\nNTF 过程\n备件供应方案\n应急计划" }
        ]
    },
    "P4": {
        name: "P4: 产品和过程开发的实现",
        questions: [
            { id: "P4.1", text: "是否落实了产品和生产过程开发计划中的活动？", isStar: true, minReq: "已落实开发计划中定义的产品与生产过程开发活动，确保满足运行的状态（功能，可靠性，安全性）。\n在多部门协作的基础上进行风险分析（例如：FMEA，HARA），并根据项目进度不断修订。根据规划实施定义的措施，并对有效性进行检查。\n在相关文件（FMEA等）中定义和识别了特殊特性，并采取了措施确保其符合性。", implEx: "产品/生产过程开发：\n降低风险的方法（例如FMEA、HARA、FTA）\n实验设计（例如DOE、谢宁、田口等）\n防错原则\n产品开发：\n测试计划\n装配测试（官方翻译安装测试）和系统测试\nA、B、C、D样品\n耐久性测试\n环境模拟试验（例如：盐雾试验）\n根据策划进行汽车SPICE评估\n代码及软件发布管理\n可追溯性及应急方案\n产品变型管理（long注：或产品变体管理，此概念可查询VDA 2）\n根据策划进行了需求分析、架构设计、实施了产品开发和测试\n生产过程开发：\n生产控制计划/检验计划" },
            { id: "P4.2", text: "为确保产品和生产过程的实现 人力资源是否到位并且人员具备资质？", isStar: false, minReq: "人力资源配置计划就位。\n员工的任务，能力要求，以及授权已经被定义并分配。此要求也适用于外包服务提供商。针对此要求需要保持适当的证据。\n在产品和生产过程的开发过程中，对于潜在可能出现的瓶颈以及附加要求（翻译成额外要求是不是更好），确保进行需求的定期评估。\n在产品和生产过程实现的所有阶段，都有资质合格的人员就位，并且已经明确对批量生产有关人员的要求。\n考虑了外包过程。", implEx: "产品/生产过程开发：\n顾客要求\n对于相关职位的总体要求\n确定的培训需求\n培训证明\n工作的方法知识\n外语知识\n软件开发：具备资质的[官方翻译：合格的]软件测试人员、集成经理等。\n有资质的敏捷开发人员（开发及运营经理、放行培训经理等）" },
            { id: "P4.3", text: "物质/非物质资源是否到位并且适用 以确保产品和生产过程的实现？", isStar: false, minReq: "确定资源的过程已经完成。\n资源提供指的是建筑物、测量和检验设备、实验室设备、机器、生产线、IT系统和基础设施的可用性及其利用率。\n考虑了外包过程。\n定期评估了产品和生产过程开发期间可能出现的瓶颈以及额外的需求。\n物质/非物质资源可用于产品和生产过程实现的所有阶段 并已明确对批量生产的要求。\n内部运输的运输工具 例如包装和专用载具 已经确定 并且数量充足。", implEx: "产品/生产过程开发：\n顾客要求\n与顾客和供方的技术接口\nERP系统\n支持过程，例如物流和IT\n产品开发：\n产品验证与确认的资源\n测试设定，例如：硬件在环仿真（HIL），测试板，评估板\n开发工具，为软件开发提供的工具链\n生产过程开发：\n设施策划\n设施布局\n生产线和机器的放行，线体许可\n数量和产出时间（官方翻译产量和产出时间）\n运输路径\n运输方式、容器、仓储\n量产启动前的产能（初始库存）" },
            { id: "P4.4", text: "是否具有产品和生产过程开发所要求的能力和放行证明？", isStar: true, minReq: "根据开发计划，所有零部件、总成、软件版本以及外购件/服务都能获得放行并证明其能力。\n测量和检验过程的初始能力已经过验证。\n材料数据已经确认并放行。\n风险分析中的措施（例如：FMEA，HARA）已被包含在产品与生产过程实现中，其有效性已被确认。\n对于集成（嵌入）软件的产品 软件相关方面的要求应考虑VDA 2的要求。\n按约定的日期完成生产过程和产品批准（PPA）。验证了特殊特性的生产过程参数的公差。", implEx: "产品/生产过程开发：\nPPA的结果，特别是符合法律和法规要求的声明（例如：IMDS，REACH，RoHS）\n例如：IMDS、REACH、RoHS\n顾客的开发放行\n生产过程开发：\n过程参数及其公差\n确认的物流方案（例如，通过发运试验验证包装的适用性 [官方翻译：样品运输包装的适用性]）\n能力证明\n测量和检验软件的确认\n产能研究\n生产线和工具的放行\n软件：\n每个版本的使用建议（发布说明） [官方翻译：每一次发布给予使用建议（发布说明）]\n根据顾客要求放行了第三方软件及开源软件(FOSS)\n提供测试结果和测试评价" },
            { id: "P4.5", text: "是否落实了策划的采购活动？", isStar: false, minReq: "策划的采购活动已经完成。这包括风险评估，项目计划，成熟度，放行以及遵守截止日期。\n根据风险分级策划的活动已经完成 已制定了措施并监视了其执行情况。\n根据项目进程，可提供采购产品和服务所必须的能力和放行（生产过程和产品放行）的证明。材料数据已确认并放行。\n已考虑到顾客相关项目的特定要求。\n已建立了面向采购产品/服务的，以确保顾客服务及使用现场失效分析的过程。", implEx: "失效模式及影响分析、危害分析和风险评估\nVDA 6.3 审核、潜在供方分析等\n符合PPA程序的放行、证据与能力测试\n标准检测与负载检测\nNTF过程及其触发标准已定义\n备件供应方案\n应急计划\n接口协议（根据DIA或接口协议提供服务）\n次级供方管理" },
            { id: "P4.6", text: "是否在产品和生产过程开发中确定并落实了制造和检验规范？", isStar: false, minReq: "制造和检验规范包含所有来自于产品和生产过程开发的检验特性（包括特殊特性）。需考虑到所有的组件、总成、分总成，以及物料，包括产品的制造过程。\n考虑了风险分析的结果（例如FMEA、HARA）。\n已具备生产控制计划。其可用于贯穿整个原型样件阶段 （如有顾客要求），试生产阶段和批量生产阶段。\n已确定产品审核、全尺寸检验和功能性试验的范围和相关要素。\n已制定维护规范。\n根据测试等级和顾客要求（例如：V模型），已对所有测试用例进行描述。\n在对应的时间节点，已放行要求的软件功能。", implEx: "产品/生产过程开发：\n风险分析\n产品开发：\n产品审核计划\n再鉴定计划（官方翻译再评定）\n生产过程开发：\n检验指导书\n作业指导书\n反应计划\n生产放行（首、末件，再放行）\n在线检验\n软件：\n确保软件正确安装的要求\n生产测试的放行标准" },
            { id: "P4.7", text: "是否在量产条件下进行了能力测试？", isStar: false, minReq: "在量产条件下进行能力测试，以确认利用所使用的资源，在规定的时间内，根据规范能够生产顾客要求的数量。\n如果能力测试确认未能达到要求，已制定相应措施。\n在确定节拍/产出时间时，应考虑软件在组件中的安装。\n注：依据审核的时机，相关能力测试的某些部分可能仍然处于策划阶段。\n该提问与产品开发无关！", implEx: "生产过程开发：\n批量生产条件：例如：工具、生产线、节拍时间、人员、生产和检验规范、测量与检测设备\n顾客要求\n能力测试、生产节拍\n确定最小数量（生产峰值和约定的灵活性）\n设备和设施的批量生产成熟度（测量报告）\n批量生产的人力配置方案\n包装要求\n软件：\n软件刷写时间包括测试、ROM（只读存储器）编程" },
            { id: "P4.8", text: "是否为确保顾客服务以及使用现场失效分析建立了过程？", isStar: false, minReq: "在过程中已建立了顾客对零部件贯穿产品生命周期的供应要求。\n完成了持续批量供应的策划过程，包括紧急情况的保障措施。\n根据交付范围已建立0公里和使用现场失效的分析过程。已考虑顾客对于使用现场失效分析的要求。\n对所在现场分析能力的要求已与顾客达成一致。\n若使用外部场所进行分析，则应规定接口，且具备所要求设备和分析产能可用性的证明。\n顾客服务中也应考虑新技术和产品。\n指定负责这些过程的人员已经具备资质，基础设施已到位。\n已明确规定使用现场的产品监控过程。\n已建立错误分析和诊断过程（控制单元中嵌入的软件）。\n若同意，已经明确了空中下载（OTA）软件的更新过程。", implEx: "产品/生产过程开发：\n进行标准检测和负载检测的检验设备\n已定义触发准则\n失效分析的检验计划\nNTF 过程\n生产过程开发：\n资质矩阵和培训证明\n检验系统和设备\n与外部分析场所的服务协议\n约定了分析用外包场所的服务协议\n备件供应方案\n应急计划" },
            { id: "P4.9", text: "是否针对从开发到批量生产的项目交接，建立了受控的方法？", isStar: false, minReq: "已建立将工作结果从项目阶段转移到批量生产的过程。\n项目计划中规定的活动已经实施。对于尚待澄清的方面，已经确定了最后期限并任命了负责人。\n成功的内部放行和顾客放行是批量交付放行的先决条件。按时执行来自内部和外部放行的措施。\n策划的人员已到位并具备资质。\n策划的用于批量生产的物质资源已到位。\n已明确规定并引入了保证生产启动的措施。\n对于集成（嵌入）软件的产品，应记录来自于开发阶段的结果（包括中间结果及其文档）。\n工业化进程被保证。", implEx: "产品/生产过程开发：\n顾客要求\nPPA 记录\n包含交接标准和验收报告的交接协议/检查表\n零件历史记录\n关键生产数据，如OEE、ppm、拒收率等。\n来自于正在进行项目的经验\n人力资源（生产工人、工艺工程师、维护人员等）\n物质资源（机器和生产线、建筑物、进出路线、检验设施、载具、包装等）\n变更日志、放行说明\n软件工业化：包括在批量生产中将软件刷写至控制单元，和代码编写 " }
        ]
    },
    "P5": {
        name: "P5: 供方管理",
        questions: [
            { id: "P5.1", text: "是否确保了只和获得批准的供方开展合作？", isStar: true, minReq: "在批量生产中，确保仅使用经过批准的供方。相关的批准标准已经确定。\n在选择供方以及评价其质量能力时 根据部件的风险分级 计划并落实了过程审核。\n已考虑对现有供方的质量绩效进行评价。\n使用适当的措施识别、评价并降低供应链（内部/外部）中的风险。\n如果是顾客指定的供方（指定供方），则应考虑接口协议。", implEx: "确定的供方选择准则\n质量管理协议\n在未满足准则的情况下：为降低风险而采取措施的证据\n评价供方的质量能力，例如：通过KPI（ppm、交付绩效）、升级水平\n供方的自我评估、供方审核结果\nASPICE评估结果\nVDA 6.3过程审核\nVDA 6.3潜在供方分析" },
            { id: "P5.2", text: "是否在供应链中考虑了顾客的要求？", isStar: false, minReq: "顾客要求的沟通是规范和可追溯的。\n在批量生产过程中，变更管理也被考虑在内。\n对于顾客指定的供方（指定供方），必须要有相关接口协议。", implEx: "要求来自：图纸、部件、软件或部件规范、里程碑计划、质量管理协议或其他有效标准\n特殊特性\n再鉴定要求\n有关投诉处理的要求\n法律和法规要求" },
            { id: "P5.3", text: "是否与供方就供货绩效约定了目标，并定期评价目标的达成情况？", isStar: false, minReq: "与所有直接供方签订了有关交付绩效的目标协议并加以落实。\n在规定的期限内，根据定义的准则检查和评价了供方绩效。\n如果未达到约定的目标，则应定义措施并监控包括期限在内的执行情况。\n如果是顾客指定的供方（指定供方），则应考虑接口协议。", implEx: "可测量的目标：交付数量、准时率、故障率、PPM、特殊交货、拒收、投诉的处理时间\n根据质量管理协议的升级准则\n在供方未达到要求的交付绩效情况下的原因和措施（开发计划）的证据" },
            { id: "P5.4", text: "针对采购的产品和服务，是否获得了必要的批准/放行？", isStar: true, minReq: "在批量生产中使用新的或更改的产品/生产过程之前，与顾客商定的所有采购的产品和服务都已放行。\n如果是顾客指定的供方（指向性供方），则应考虑接口协议。", implEx: "关于PPA程序协调的报告\nPPA报告\nPPA程序的参考零件\n极限样件\n供应链中的产品和生产过程变更\n针对小批量和单个需求的放行协议" },
            { id: "P5.5", text: "针对采购的产品和服务，是否确保了约定的质量？", isStar: true, minReq: "为了监控采购的产品和服务的质量，根据检验计划进行检验、记录和评价。\n如果出现偏差，将遵循标准的投诉过程。\n检验和测量设备适用于采购的产品和服务，充足可用且存储适当。检验工位布局合理（例如：环境控制、照明条件、清洁度和防止损坏和污染）。", implEx: "放行的检验程序\n样本大小（例如：跳批抽检）\n极限样件\nPPM评价，8D报告\n改进项目\n符合DIN EN 10204的材料证明\n量具/夹具\n图纸/规范\n订购和包装规范" },
            { id: "P5.6", text: "是否对进货产品进行了适当的交付和储存？", isStar: false, minReq: "材料和运载器具根据其使用状态进行交付和存储 以免损坏或混料。\n对于可能因温度、湿度、振动等损坏并影响最终产品质量的材料，定义了运输和储存条件并提供了证据。\n可疑/隔离材料有清晰的标识并防止未经授权的使用。\nFIFO/FEFO（有效期要求）和批次可追溯性在材料进一步加工时得到保证。该要求同样适用于剩余料。\n仓库管理系统中的物料库存数量与实际库存数量相一致。\n储存条件满足产品要求。", implEx: "包装\n标识（可追溯性/检验状态/使用状态）\n隔离仓库，隔离区域\n与批次相关的使用\n环境条件\n防止损坏/污染/腐蚀\n有序和清洁\n防止混料/错用的预防措施" },
            { id: "P5.7", text: "针对具体的任务，相关人员是否定义了职责并具备了资质？", isStar: false, minReq: "规定了员工在其相关工作领域的职责、任务和权限。\n员工根据其岗位要求进行资质认证。\n根据任务确定资质要求，并相应地计划和实施资格认证（官方翻译：落实资格）。\n要理解之前发生的针对采购的产品和服务的投诉（包括纠正措施）。", implEx: "了解规范、产品特性、客户要求和生产过程\n标准\n法律和法规要求\n包装要求\n质量程序\n岗位描述/任务和职能的描述\n资质矩阵\n供方审核员资质" }
        ]
    },
    "P7": {
        name: "P7: 顾客服务",
        questions: [
            { id: "P7.1", text: "质量管理体系和产品符合性相关的要求是否得到满足？", isStar: false, minReq: "内部和顾客特定的质量体系要求及其进一步开发的要求得到满足。组织内部的过程（包括外包过程）以及供应链都应考虑在内。\n根据顾客要求落实了全尺寸检验和功能性试验。\n满足顾客关于零件回收和再利用的要求。\n有符合必要的国家和国际法规的证明。", implEx: "与顾客的质量协议\n全尺寸检验方案，例如：进行产品审核、功能试验、耐久性试验\n质量管理体系认证\n符合性证明，例如，需通过型式认证、CCC、ECE、DOT、证书、检验报告的零件" },
            { id: "P7.2", text: "是否保障了顾客服务？", isStar: false, minReq: "确保在顾客组织内的各个领域都有合格的联系人。\n确保按顾客的规范进行联络沟通。\n确保对使用现场的产品监控。\n确保根据与顾客的特定协议 登录顾客平台 并及时更新/维护所要求的数据。", implEx: "有关产品使用的知识\n产品问题和有关产品或运输投诉的知识\n满足新的要求\n通报改善措施\n全球顾客服务\n当顾客要求无法被满足时，及时通知顾客\n需要的数据（例如：认证、联系人信息）" },
            { id: "P7.3", text: "是否保障了零件的供应？", isStar: true, minReq: "确保应急预案（包括针对产品/零件持续供货的应急预案）是有效且最新的。相关过程考虑了组织内部（包括外包过程）和供应链。相关应急预案还需要包括非物质产品，比如：软件等。\n已考虑风险及其对顾客的影响。\n组织应有相应的过程确保当发现产品供应出现短缺时，能立即通知顾客。相关信息应包括产品短缺的持续时间、程度、原因和已启动的措施。\n确保满足顾客在产品量产阶段和之后备件供应的要求。", implEx: "应急计划（如替代生产、供方、包装、运输）\n挑选的能力以及响应时间\n利用外部产能\n针对供应短缺进行的沟通\n涵盖引入特殊措施时授权进行决策和升级路径的规定\n零件的隔离\n供方参与备件供应\n提供软件更新" },
            { id: "P7.4", text: "发生投诉时，是否开展了失效分析，并且有效地落实了纠正措施？", isStar: true, minReq: "针对0公里和使用现场投诉，采用符合顾客规范的投诉处理程序。\n必须定义失效分析程序。有必要的人力和物质资源以确保准时处置。遵守与顾客约定的时限。当约定的失效分析时间计划无法满足时 应及时通知顾客。\n当出现使用现场投诉时，需按顾客要求开展使用现场失效分析（例如：VDA 使用现场失效分析和审核标准）。\n针对顾客指定的供方（指定供方），必须要有相关接口协议。", implEx: "处理投诉和使用现场失效分析过程\n内部/外部分析设施（实验室、测试和检测设备、人员）\n使用问题解决方法（8D）\n偏差发生时与顾客的信息流\n知识储备库、经验教训\n质量控制环\n风险分析（如FMEA、危害分析与风险评估）\n获取必要的放行文件（例如，PPA）" },
            { id: "P7.5", text: "针对具体任务，相关人员是否定义了职责并具备了资质？", isStar: false, minReq: "确定每位员工在自己的工作范围内相应的职责、任务和授权。\n培训需求是根据任务具体确定和实施的。\n员工要熟悉产品以及错误执行工作对零件供应和最终产品质量带来的后果。", implEx: "组织机构图和升级程序（官方翻译： 组织机构图和事态升级程序 ）\n了解相关知识的证据：产品、规范、顾客特定要求\n标准/法规（产品责任）\n预期用途\n失效分析\n评价方法（例如，审核、统计）\n质量技术（如，柏拉图、8D方法、因果图、5Why）\n外语知识" }
        ]
    }
};
    const p6Template = {
    name: "P6: 生产过程分析",
    questions: [
        { id: "P6.1.1", text: "是否在开发和批量生产之间进行了项目交接，并确保可靠的生产启动？", isStar: true, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "已根据规定的标准对项目移交至批量生产进行了记录存档。\n整个移交过程的责任有具体的规定并被告知。对未解决的问题有后续跟踪，并按计划实施了必要的措施。\n在首次量产发货之前 完成了生产过程和产品的全面批准/放行（PPA）。\n所需的文件均已到位，基于风险分析采取了确保生产启动的措施。\n所需数量的工装模具以及运输、包装、检验和测量设备均已到位。\n具备失效分析的能力。\n产品软件对应的是最新发布的版本。", implEx: "交接报告\n确定的措施及实施的时间表\n生产放行报告\n所有生产线组件（官方翻译：部件）和工装模具的放行\nPPA文件，包括PPA程序的顾客放行和参考样件\n特殊放行\n已发布的软件版本\n确保生产启动的示例：更高的检验频次、额外的检验、驻厂工程师\n安全投产方案" },
        { id: "P6.1.2", text: "材料是否在约定的时间，按所需的数量/生产批次大小被送至指定的位置？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "材料以约定的质量、正确的数量、正确的包装、在约定的时间被送至约定的地点，并附有正确的文件。可在指定的存储区域/工位获取零件/部件。\n在工作场所，按需提供产品和材料，并根据物流方案考虑订单数量/批量大小。\n明确定义了余料的使用及其可追溯性。\n对来自外包过程（包括分拣服务）重新投入使用的零件进行了规范。", implEx: "材料包括，例如：软件、包装、运载装置、部件和组件、原材料、半成品、交付给顾客的量产包装、操作材料、辅助材料和工艺材料\n适当的运输方式\n定义的存储位置和库存水平\n看板管理、JIT/JIS、FIFO/FEFO\n材料和软件的修订/变更状态\n关于零部件和容器的特殊要求（例如ESD保护、湿度、温度、残留物）" },
        { id: "P6.1.3", text: "是否对材料进行了适当的存储，是否所使用的运输工具/包装设备适合材料的特性？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "始终考虑并满足包装要求。\n在制造和组织内部的运输过程中，以及往返于服务供方的运输过程中，须使用适宜的运输工具，以免产品受损和污染。\n仓储区/加工工位/容器必须达到材料所需的整洁/清洁要求。要定义清洁周期，并加以监控。\n加工工位/装配线上的材料供应必须便于安全操作。\n通过适当的方法监控材料的规定存储时间和有效期。\n生产线和机器的运行及辅助材料，如果对产品/产品质量有直接的影响，要进行相应的监视。\n保护材料、运行和辅助材料免受环境和气候的影响。", implEx: "库存量\n储存条件\n放行的特殊和标准的运输容器\n防止材料损坏\n5S\n不过量填装（储存区域和容器）\n最长和最短存储时间，指定的临时存储时间， FIFO/FEFO" },
        { id: "P6.1.4", text: "材料是否具备必要的标识/记录/放行，并得以适当体现？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "材料的放行状态清晰可辨 定义了捆装/批次/装运容器/零件上的放行标识。\n确保只有放行的材料/零件才被提供给生产/下道工序使用。\n根据定义的追溯性方案，确保从分供方到顾客的整个过程的可追溯性。\n顾客规范以及相关的法律和法规要求的标识被考虑在内。", implEx: "顾客关于标识和可追溯性的规范\n放行的零件/材料的标识（粘贴标签、标识、发料单、 VDA标识、数字矩阵代码(DMC)等）\n批准记录\n可追溯系统\n特殊放行的记录（数量、持续时间、标识类型等）\n库存管理系统" },
        { id: "P6.1.5", text: "是否对批量生产中的产品或生产过程变更进行了跟踪和记录？", isStar: true, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "根据所描述的变更管理落实对产品和生产过程的变更。产品和生产过程的变更与顾客达成一致，根据顾客要求批准和放行（包括软件变更）。执行了PPA批准放行。变更状态的历史是完全可追溯的。\n使用材料/软件的正确、放行的版本。\n完成变更之后，检查风险分析是否需要更新。", implEx: "依据VDA 2中的触发矩阵或顾客规范\n变更的放行要形成文件\n变更前的多学科评价\n变更履历/零件履历（也适用于软件）\nDFMEA和PFMEA\n变更时的操作管理\n试生产\n唯一软件标识，软件完整性（构建号，哈希值）" },
        { id: "P6.2.1", text: "生产控制计划以及生产和检验文件中的要求是否完整，并得到有效落实？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "基于生产控制计划的生产和检验文件是完整的。\n与放行的机器/工具/辅助设备相关的数据在生产控制计划和/或生产和检验文件中注明。\n这些文件可在工位附近取用。\n在生产控制计划中描述了针对过程干扰所需的措施 并予以落实和记录。\n充分陈述了影响产品性能/质量的过程参数。\n规定了过程参数和检验特性的公差。\n过程控制图中的控制限是明确的、可识别的和合理的。\n记录了与过程要求和检验特性相关的偏差和已启动的措施。\n规范返工条件，作为风险分析的一部分进行评估，并在过程中加以保护（零件标识；重复检验等）。", implEx: "检验特性、检验设备、检验方法、检验频率、检验周期和再鉴定\n有关机器/工具/辅助设备的数据（识别编号）、过程参数和公差（压力、温度、时间、速度等）\n作业指导书（包括返工）\n检验指导书" },
        { id: "P6.2.2", text: "是否进行了生产过程的放行？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "对首件/末件和重新放行进行了特定过程的放行检验并记录。\n产品和生产过程的放行是必要的，并由授权的员工根据接收准则执行并记录。记录偏差和已启动的措施。\n在放行时，有必要的参考和极限样件。\n规定了重新放行的触发准则，例如：在生产中断之后。\n如果在收集检验零件后继续生产，则这些产品应是可以获得的，直到放行了检验零件。", implEx: "生产批次放行，包括重新放行\n首件放行/末件放行和重新放行\n工具图/参考件/设置用部件（例如：失效测试件、合格/不合格样件）\n重新放行的可能触发准则：\n生产中断后（例如：两班工作制的夜班、工具更换、材料/批次/产品更换）\n返修，工具更换\n变更设置数据（2016：生产数据修改）\n放行的返工" },
        { id: "P6.2.3", text: "是否在生产中对特殊特性进行了控制？", isStar: true, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "在生产控制计划中标记了顾客指定和组织识别的特殊特性以及定义的过程参数，并进行了系统的监控和记录。\n保持了偏差和纠正措施的记录。对影响产品特性的偏差 要由顾客批准。\n特殊特性的记录是可获得的。这些记录的保存时间和存档方式是明确的并满足顾客要求。", implEx: "图纸\n表明顾客特定的特殊特性的标识 例如：D/TLD、DS、DZ、R、S、F\n过程FMEA\n生产控制计划\nSPC评价\n质量控制图\n能力证明\n检验过程能力证明\n检验结果\n过程参数记录" },
        { id: "P6.2.4", text: "是否对可疑和不合格产品进行了控制？", isStar: true, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "可疑产品和不合格产品被分离、贴标签、记录并安全地从生产过程中移除。\n对这些产品直接标识，或在其容器上标识。\n由授权人员决定可疑产品的后续使用。\n允许返工的范围（包括检验和放行）在作业指导书中描述。\n明确标识隔离仓库和隔离区域。防止意外或未经授权使用隔离产品。", implEx: "表明产品状态的标识\n在生产中定义报废/返工工位\n隔离仓库和隔离区域，清理区域（官方翻译清洗区 ）\n关于拒收、返工和维修的文件\n授权" },
        { id: "P6.2.5", text: "是否能确保材料在流转过程中不发生混合/弄错？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "确保不会发生材料混淆或者使用错误材料、软件或组件的情况。\n采取适当措施 确保尽早发现任何零件混淆或任何使用错误零件/错误安装零件的情况。\n可从标签上清楚看出产品的使用状态。\n明确定义了隔离零件、返工零件和可再利用产品的使用。确保可追溯性。\n必须标识设置标准件、设置用零件和参考件，并防止意外使用。", implEx: "过程FMEA\n防错方法\n生产设施的检查和检验\n批次的可追溯性\nFIFO/FEFO\n先进先出/先到期显出\n看板\n清除无效的标识\n价值流分析\n分选服务\n唯一的软件标识符，软件完整性（构建号，哈希键）" },
        { id: "P6.3.1", text: "员工是否能够完成分配的任务？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "对于每项任务/工作，已规定相应的要求。员工资质应符合要求。如果情况并非如此，则需制定资质认证计划。\n向员工提供的指导、培训和入职介绍以及资质证明都需做好记录。\n提供相关工作所需的特殊资质证明。\n如果过程发生变更，应提供培训/指导并进行记录。\n这些要求同样适用于临时员工。", implEx: "资格证明\n培训计划\n初始培训计划，包括证据\n在职培训\n资质矩阵\n关于产品和已发生失效的知识\n测量设备操作\n控制图的解释\n关于职业安全的培训/指导\n特殊特性的培训\n适用的资质证明（例如：焊接证书、视力检测结果、听力检测结果）" },
        { id: "P6.3.2", text: "员工是否了解监视产品和过程质量的职责和权限？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "描述和落实员工的职责、责任和权限。\n员工知道工作执行不当的后果。理解产品的作用/功能。也清楚它们得不到保证时会发生什么。\n定期向员工通报当前的质量绩效以及顾客投诉。\n这些要求同样适用于临时员工。", implEx: "作业/检验指导书\n岗位描述\n设置放行、首件检验、末件检验\n停止和开始过程的授权\n升级协议\n产品培训\n产品安全/产品责任培训" },
        { id: "P6.3.3", text: "是否具备必要的人力资源？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "所有班次都有员工配置计划。员工配置计划考虑了所需合格员工的数量。\n对于非持续使用的支持区域（如实验室、测量室），应制定相应的规定。\n员工配置计划考虑了顾客订单的波动和员工缺勤情况（如病假、休假、培训）。\n这些要求同样适用于临时员工。", implEx: "班次计划\n资格证明\n资质矩阵\n文件化的缺勤管理规则\n员工配置计划\n指导者（官方翻译 导师 ）" },
        { id: "P6.4.1", text: "生产设备是否适合满足顾客对于产品的具体要求？", isStar: true, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "必须证明，使用现有生产设施能够根据顾客要求落实生产过程，且生产的产品满足顾客规范的要求。\n生产设施、机器和生产线必须有能力满足具体产品和过程特性公差的要求。\n对影响过程的参数和软件进行保护，防止未经授权的访问。\n必须确定所选择的产品和过程特性的过程能力 并须提供能力证明。\n过程能力须满足内部及顾客规范的要求。过程能力至少达到Cpk≥1.33。对于有过程能力要求，却无法提供能力证明的特性，须开展100%检验。", implEx: "机器/过程能力证明\n关键过程参数的监控（如压力、时间、温度）\n备用工具的能力\n上料和取料系统\n量具、夹具等的再现性\n清洁度要求\n技术状态管理，确保按照唯一性标识提供软件，并保障软件的完整性（构建号，哈希键）" },
        { id: "P6.4.2", text: "生产设备和工具的维护保养是否受控？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "根据相应的风险，对所有机器、生产线、设备和工具都规定和实施了预防性和/或预见性维护保养（维护、检查和修理）。\n应记录已经实施的计划和非计划的维护活动 并分析潜在的改进措施。\n为执行必要的维护保养活动提供了所需的资源。\n有效地落实了对停产时间、设备利用率及工具寿命进行分析和优化的过程。\n须确保备件已到位并可用。\n对工具进行了管理，包含如下内容：\n• 工装履历，包括所有变更和工具寿命\n• 运行状态\n• 工具的标识\n这些要求也适用于外部服务提供商。", implEx: "与生产相关的物流设备，如叉车\n维护和服务计划\n全面生产维护（TPM）\n关键过程和瓶颈设备\n制造商提供的技术文件\n对于易损工具制定的预防性更换计划\n工具的运行记录表\n工具运行状态，如运行中、不可运行\n工具标识，如顾客财产、工具编号、索引" },
        { id: "P6.4.3", text: "是否能利用测量和检验设备有效监视质量要求的符合性？", isStar: true, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "使用的测量和检验系统适用于预期目的和实际生产操作 并包含在生产控制计划中。\n所使用的测量及检验系统均有能力证明。\n检验过程中使用的所有系统和装置都进行了标识。并监视了其有效性状态。建立并实施了监视测量和检验系统持续能力的过程。\n针对偏差，开展了针对过程、产品和顾客的风险评估。定义并有效实施了相关措施。\n对测量结果有影响的测量系统及相应的标准和参考物质也采用同样的方法进行监控。", implEx: "测量系统分析\n测量及检验过程能力\n替代测量装置\n嵌入式软件的完整性检查\n校准状态（检验贴纸、条码、刻字等）\n测量和检验用软件的确认\n参考件/参考物质\n监视测试设备" },
        { id: "P6.4.4", text: "生产和检验工位是否满足要求？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "工作场所及环境条件适合于产品和工作内容，以便预防或消除零件的污染、损坏、混料以及错用。\n以上要求同样适用于长期或临时设置的返工、分选及检验工位。\n工位的布局适合于要开展的工作。", implEx: "清洁和整洁，5S\n照明\n噪音污染\n环境控制\n洁净室\n静电防护\n工位布局\n工位周边布置/加工工位上零件的取放(官方翻译：工位周边环境及零件搬运)\n职业健康和安全" },
        { id: "P6.4.5", text: "是否正确地存放了工具、装置和检验设备？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "妥善存放了所有工具、装置和检验设备。\n应确保设备的存放方式，使其不会受损及受到环境影响。\n保障了清洁和整洁。\n控制和记录设备的发放和使用。", implEx: "防止碰撞、污染和环境影响\n5S方法\n规定的存储位置，例如：在地板上做标记\n透明化仓库管理" },
        { id: "P6.5.1", text: "是否针为生产过程设定了目标？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "定义、监控和沟通了有关效果和效率的具体过程的目标。\n设定目标时，考虑了顾客的要求。\n定期将规定的目标与实际结果进行比较并记录。", implEx: "关键生产数据，例如：产量、质量指标、产出时间、缺陷成本、过程有效性数据、生产线和机器利用率\n一次质量合格率，直通率\n减少浪费（例如：拒收和返工，能源和加工材料）" },
        { id: "P6.5.2", text: "是否质量和生产过程数据的收集便于分析？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "定义并记录了可用于证明产品符合性（目标值）的质量及过程参数。对实际数据进行了分析并采取了适当的改进措施。\n记录了过程中的特殊事件。\n所记录的数据应可与产品和过程进行关联，这些数据可获取、清晰、可查阅并按规定进行了存档。满足可追溯性的要求。\n基于质量、成本及服务方面的发现，不断的确定改进的潜力。", implEx: "控制图\n检查表\n失效类型/失效频率\n拒收/返工\n记录了参数变更的过程数据表\n换班/机器日志\n节拍时间/产出时间\n故障信息（例如：停线、断电、程序错误信息）\n输出/可用性\n封存通知/分选措施\n可追溯性" },
        { id: "P6.5.3", text: "如果不能满足产品或生产过程的要求，是否分析了原因，并且验证了纠正措施的有效性？", isStar: true, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "如果不能满足产品或生产过程的要求，必须采取遏制措施以满足要求，直到证明了纠正措施是有效的。员工必须熟悉这些遏制措施。\n采用适宜的方法进行原因分析。\n须对重复失效进行记录，并且相应开展了更详细的原因分析。\n从原因分析导出了纠正措施，并监控其实施且验证了有效性。\n以事件为导向，对生产控制计划及风险分析进行了更新。\n若偏差已影响到已交付产品的特性，必须与顾客沟通。", implEx: "8D方法\n因果图\n田口DOE、谢宁DOE\n5Why方法\n过程能力分析\nDFMEA和PFMEA\n弃权/特殊放行\n追加的尺寸、材料、功能和耐久性测试" },
        { id: "P6.5.4", text: "是否定期开展过程和产品审核？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "基于顾客要求和特定风险 制定并落实了产品和过程审核方案。\n开展的过程和产品审核须适合于识别特定风险和薄弱环节。\n审核中发现偏差时 应分析原因 制定纠正措施 监控措施的实施并进行有效性验证。\n若偏差已影响到发运的产品，必须与顾客沟通。", implEx: "规范\n特殊特性\n计划内的以及事件导向的审核方案\n审核频率\n审核结果、审核报告、措施计划\n审核员资质\n审核范围，例如：P5、P6、P7\n标识、包装\n具体零件和软件的变更状态" },
        { id: "P6.6.1", text: "是否根据需要确定产量/生产批量，并且有序的地运往下一道生产工序？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "使用适宜的运输方式将产品送至指定的存储区/保存地点。\n依据订单的数量/批次大小 将所需要数量的产品转移至指定的存储区/保存地点。\n产品状态（合格品、返工、拒收等）标识清晰（组件、容器）。\n清晰标识变更的状态。\n确保只有合格零件进入下一个生产过程步骤。\n制定了返回剩余产品的管理规则，包括数量的记录以及进一步的处理措施。", implEx: "看板管理\nFIFO/FEFO\n先进先出/先到期显出\nJIT/JIS\nJIT：Just In Time 准时化生产\nJIS：Just in sequence 准时化顺序供应\n仓库管理\n根据顾客需求确定生产数量\n技术清洁度" },
        { id: "P6.6.2", text: "是否对产品进行了适当的存储，所使用的运输/包装设备是否与产品的特性相适应？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "通过适宜的储存和包装，防止产品受到损坏。\n必须了解并落实内部的和顾客的特定包装要求。这也适用于已批准的替换包装。\n储存区域/容器必须满足相关清洁度要求。\n监控了规定的存储期限。\n产品在储存和运输过程中受到保护，防止外界环境和气候带来的影响。\n这些要求适用于生产过程中以及运输过程中的处置。", implEx: "防止受损\n电子零件的ESD保护\n技术清洁度\n清洁、整洁5S\n无过量装填（储存区域和容器）\n监视储存的时间和储存的数量（最长/最短的储存时间，指定的临时储存时间）\n关于包装的清洁规范\n足够数量的包装" },
        { id: "P6.6.3", text: "是否保持了必要的记录和放行？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "产品的放行以及相关证据均有记录。\n特殊放行/弃权要有相关文档记录。该文档记录应包括相关产品的生产时间和/或数量。\n满足关于返工和返修记录的内部和顾客规范的要求。\n必须确保产品的可追溯性。\n满足顾客关于存档的要求。", implEx: "顾客规范\n顾客对存档期限的要求\n存档的要求/法规（例如：电子数据处理、纸张、防火、可读性要求）\n零件履历" },
        { id: "P6.6.4", text: "是否在产品交付时满足了顾客的要求？", isStar: true, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "顾客对最终产品的特定要求（交付可靠性、质量目标、质量绩效等）必须明确。针对相关要求的满足情况需要持续监控、评估和记录。\n当出现偏差时，应分析原因，制定措施并执行，并确保其有效性。\n根据顾客要求对产品进行标识、储存和发运。\n当特殊放行/弃权时 需要按照要求在产品和包装上进行相应的标识。\n遵守内部和顾客的关于返工或返修产品的标识规范。\n要监管对供货产品的处置。\n涉及影响顾客的交付中断时，需要及时告知顾客，并与其协商进一步的程序。", implEx: "与顾客的质量协议\n包装规范\n目标协议\n发货审核\n关于标识的顾客规范（如：VDA 标签）\n针对特殊放行的（断点）标识" }
    ]
};
    // --- END OF VDA 6.3:2023 Questions Data (Chinese) ---

    // Global auditData object
    let auditData = {
        header: {
            companyName: "", auditDate: "", auditLocation: "", reportNumber: "", auditLanguage: "中文",
            processes: [{ name: "工序1", products: [false] }], // Max 3
            products: [{ name: "产品A" }], // Max 3
            auditors: [], // Max 2
            selectedPElements: { P2: true, P3: true, P4: true, P5: true, P6: true, P7: true },
            p3ProductSelected: false, p4ProductSelected: false
        },
        scores: {},
        questionDetails: {}, 
        actionPlanItems: {},
        productGroupEvaluations: [] // Initialize productGroupEvaluations array
    };

    let completedAudits = [];
    const CURRENT_AUDIT_DATA_KEY = "vda63CurrentAuditData_LongShao_v1.4_Trial"; 
    const COMPLETED_AUDITS_KEY = "vda63CompletedAudits_LongShao_v1.4_Trial";


    function showTab(tabId) {
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
        document.getElementById(tabId).classList.add('active');
        document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));

        const clickedButton = event ? event.currentTarget : Array.from(document.querySelectorAll('.tab-button')).find(b => b.getAttribute('onclick').includes(`showTab('${tabId}')`));
        if (clickedButton) {
            clickedButton.classList.add('active');
            clickedButton.dataset.tab = tabId; // Set data-tab attribute
        } else {
            // Fallback for when no matching button is found (e.g., on initial load for a non-existent tab-button)
            // This prevents the TypeError if clickedButton is undefined
            console.warn(`No tab button found for tabId: ${tabId}. Cannot set active class.`);
        }

        if (tabId === 'evaluationMatrix') {
            renderEvaluationSummaryTable(); 
            renderEvaluationMatrix(); 
        }
        if (tabId === 'actionPlan') renderActionPlan();
        if (tabId === 'auditChecklist') renderAuditChecklist();
        if (tabId === 'auditReport') {
            renderAuditReport();
        }
        if (tabId === 'auditPlan') {
            renderAuditPlan();
        }
    }

    function renderAuditReport() {
        const reportTableBody = document.querySelector('#auditReport .report-table tbody');
        if (!reportTableBody) {
            console.error('Audit report table body not found.');
            return;
        }

        // Clear all existing rows except the initial header if it exists
        // Assuming the initial header is part of the static HTML and not dynamically added here.
        // If there's a dynamic header to remove, it needs to be targeted specifically.
        // For now, clear all dynamically added rows.
        reportTableBody.innerHTML = '';

        // If the initial header '评价过程要素, 评价指标, EG%, 评级' is dynamically added, remove it here.
        // Based on the screenshot, this seems to be a static part of the HTML table structure.
        // If it's dynamic, we need to find and remove the specific row/cells.
        // For now, I will assume it's static and the user wants to remove the content *under* it.
        // The previous attempt commented out the '总体评价 (EG)' header, which was incorrect.
        // The user wants to remove the *initial* table header that says '评价过程要素, 评价指标, EG%, 评级'.
        // This implies modifying the static HTML or finding where this header is generated if it's dynamic.
        // Since the provided code snippet for renderAuditReport doesn't show this header being generated,
        // it's likely part of the base HTML structure of the #auditReport table.
        // I will focus on ensuring the content *below* it is correctly rendered and the '总体评价 (EG)' header is restored.

        // Helper function to get rating color
        function getRatingColor(rating) {
            switch (rating) {
                case 'A': return '#28a745'; // Green
                case 'B': return '#ffc107'; // Yellow/Orange
                case 'C': return '#dc3545'; // Red
                default: return 'inherit';
            }
        }

        // Function to determine rating category description
        function getRatingCategoryDescription(egValueStr, egGradeStr) {
            if (egValueStr === 'N/A' || egGradeStr === 'N/A' || egGradeStr === '无效') {
                return '无法评级';
            }
            const egPercentage = parseFloat(egValueStr); // Convert "84%" to 84

            if (egGradeStr === 'A') {
                return 'A：具备质量能力';
            } else if (egGradeStr === 'B') {
                return 'B：有条件的具备质量能力';
            } else if (egGradeStr === 'C') {
                return 'C：不具备质量能力';
            }
            return '无法评级'; // Fallback
        }

        // 1. 总体评价 (EG)
        const overallRatingElement = document.getElementById('overallRating');
        const overallDowngradeReasonElement = document.getElementById('overallDowngradeReason');

        if (overallRatingElement) {
            const overallRatingText = overallRatingElement.textContent.trim();
            const egMatch = overallRatingText.match(/(\d+%)/);
            const gradeMatch = overallRatingText.match(/-\s*(A|B|C|无效)/);
            const egValue = egMatch ? egMatch[1] : 'N/A'; // e.g., "84%"
            const egGrade = gradeMatch ? gradeMatch[1] : 'N/A'; // e.g., "B"

            const ratingCategoryDescription = getRatingCategoryDescription(egValue, egGrade);



            // Add Overall Evaluation (EG) header row
            let overallHeaderRow = reportTableBody.insertRow();
            overallHeaderRow.innerHTML = `
                <th>总体评价 (EG)</th>
                <th>EG%</th>
                <th>评价等级</th>
                <th>主要降级原因</th>
            `;
            overallHeaderRow.style.fontWeight = 'bold';
            overallHeaderRow.style.backgroundColor = '#f2f2f2';

            // Add Overall Evaluation (EG) data row
            let overallDataRow = reportTableBody.insertRow();
            let firstCell = overallDataRow.insertCell();
            firstCell.innerHTML = `<span style="font-family: 'Microsoft YaHei';">评级分档：</span><span id="ratingCategoryText">${ratingCategoryDescription}</span>`;
            firstCell.style.fontWeight = 'bold';
            firstCell.style.textAlign = 'center'; // Align text to the center
            firstCell.style.fontSize = '1.2em'; // Increase font size

            // Set color based on the rating category description
            let categoryColor;
            if (ratingCategoryDescription.includes('A：')) {
                categoryColor = getRatingColor('A');
            } else if (ratingCategoryDescription.includes('B：')) {
                categoryColor = getRatingColor('B');
            } else if (ratingCategoryDescription.includes('C：')) {
                categoryColor = getRatingColor('C');
            } else {
                categoryColor = getRatingColor('N/A'); // Default or '无效' color
            }
            document.getElementById('ratingCategoryText').style.color = categoryColor;

            overallDataRow.insertCell().textContent = egValue;
            let gradeCell = overallDataRow.insertCell();
            gradeCell.textContent = egGrade;
            gradeCell.style.color = getRatingColor(egGrade);
            gradeCell.style.fontWeight = 'bold';

            // Add Overall Downgrade Reason
            let reasonCell = overallDataRow.insertCell();
            if (overallDowngradeReasonElement) {
                const reasonText = overallDowngradeReasonElement.innerHTML.trim();
                if (reasonText) {
                    reasonCell.innerHTML = reasonText;
                    reasonCell.style.color = getRatingColor(egGrade); // Sync color with rating
                    reasonCell.style.fontStyle = 'italic';
                    reasonCell.style.fontSize = '0.95em';
                }
            } else {
                reasonCell.textContent = ''; // Ensure cell is not empty if no reason
            }
        }

        // 2. 各过程要素评价 (EPn)
        const epnTableBody = document.getElementById('epnTable')?.querySelector('tbody');
        if (epnTableBody) {
            // Add EPn header
            let epnHeaderRow = reportTableBody.insertRow();
            let epnHeaderCell = epnHeaderRow.insertCell();
            epnHeaderCell.colSpan = 4;
            epnHeaderCell.innerHTML = `<h3 style="margin: 5px 0;">各过程要素评价 (EPn) - 用于总体评价</h3>`;
            epnHeaderCell.style.textAlign = 'left';

            // Add EPn table headers
            let epnTableHeadersRow = reportTableBody.insertRow();
            epnTableHeadersRow.innerHTML = `
                <th>过程要素</th>
                <th>符合度 (%)</th>
                <th>评价</th>
                <th>主要降级原因</th>
            `;
            epnTableHeadersRow.style.fontWeight = 'bold';
            epnTableHeadersRow.style.backgroundColor = '#f2f2f2';

            epnTableBody.querySelectorAll('tr').forEach(row => {
                const pElement = row.cells[0].textContent;
                const compliance = row.cells[1].textContent;
                const rating = row.cells[2].textContent;
                const downgradeReason = row.cells[3] ? row.cells[3].textContent : ''; // Get downgrade reason

                const newRow = reportTableBody.insertRow();
                newRow.insertCell().textContent = pElement;
                newRow.insertCell().textContent = compliance;
                let ratingCell = newRow.insertCell();
                ratingCell.textContent = rating;
                ratingCell.style.color = getRatingColor(rating);
                ratingCell.style.fontWeight = 'bold';
                let downgradeReasonCell = newRow.insertCell();
                downgradeReasonCell.textContent = downgradeReason;
                // Apply color based on rating for downgrade reason if it's a downgrade
                if (downgradeReason) {
                    downgradeReasonCell.style.color = getRatingColor(rating);
                    downgradeReasonCell.style.fontWeight = 'bold';
                }
            });
        }

        // 3. 产品组评价
        const productGroupEvaluationContainer = document.getElementById('productGroupEvaluationContainer');
        if (productGroupEvaluationContainer) {
            const noProductMessage = productGroupEvaluationContainer.querySelector('p');
            if (!noProductMessage || (!noProductMessage.textContent.includes('未定义有效的产品名称') && !noProductMessage.textContent.includes('所有定义的产品名称均为空'))) {
                // Add Product Group Evaluation header
                let productGroupHeaderRow = reportTableBody.insertRow();
                let productGroupHeaderCell = productGroupHeaderRow.insertCell();
                productGroupHeaderCell.colSpan = 4;
                productGroupHeaderCell.innerHTML = `<h3 style="margin: 5px 0;">产品组评价</h3>`;
                productGroupHeaderCell.style.textAlign = 'left';

                // Add Product Group Evaluation table headers
                let productTableHeadersRow = reportTableBody.insertRow();
                productTableHeadersRow.innerHTML = `
                    <th>产品组</th>
                    <th>符合度 (%)</th>
                    <th>评价</th>
                    <th>主要降级原因</th>
                `;
                productTableHeadersRow.style.fontWeight = 'bold';
                productTableHeadersRow.style.backgroundColor = '#f2f2f2';

                // Use data from auditData.productGroupEvaluations
                if (auditData.productGroupEvaluations && auditData.productGroupEvaluations.length > 0) {
                    auditData.productGroupEvaluations.forEach(productData => {
                        const newRow = reportTableBody.insertRow();
                        newRow.insertCell().textContent = productData.name;
                        newRow.insertCell().textContent = productData.compliance;
                        let gradeCell = newRow.insertCell();
                        gradeCell.textContent = productData.rating;
                        gradeCell.style.color = getRatingColor(productData.rating);
                        gradeCell.style.fontWeight = 'bold';

                        let productReasonCell = newRow.insertCell();
                        if (productData.downgradeReasons) {
                            productReasonCell.innerHTML = productData.downgradeReasons;
                            productReasonCell.style.color = getRatingColor(productData.rating);
                            productReasonCell.style.fontWeight = 'bold';
                            productReasonCell.style.fontStyle = 'italic';
                            productReasonCell.style.fontSize = '0.95em';
                        } else {
                            productReasonCell.textContent = '';
                        }
                    });
                } else {
                    // If no product group evaluations are available, display a message
                    let noDataRow = reportTableBody.insertRow();
                    let noDataCell = noDataRow.insertCell();
                    noDataCell.colSpan = 4;
                    noDataCell.textContent = '未找到产品组评价数据。';
                    noDataCell.style.textAlign = 'center';
                    noDataCell.style.color = '#666';
                }
            }
        }
    }

    function initializeHeaderDataControls() {
        document.getElementById('companyName').value = auditData.header.companyName;
        document.getElementById('auditDate').value = auditData.header.auditDate;
        document.getElementById('auditLocation').value = auditData.header.auditLocation;
        document.getElementById('reportNumber').value = auditData.header.reportNumber;
        document.getElementById('auditLanguage').value = auditData.header.auditLanguage;
        
        rebuildProcessProductTable(); // Will use data possibly limited by applyExperienceVersionLimits
        rebuildAuditorTable(); // Same here

        Object.keys(auditData.header.selectedPElements).forEach(pVal => {
            const cb = document.querySelector(`.p-element-selection-group input[value="${pVal}"]`);
            if (cb) {
                cb.checked = auditData.header.selectedPElements[pVal];
                handlePElementSelectionChange(cb); // Ensure P3/P4 sub-checkboxes are shown/hidden
            }
        });
        if (auditData.header.selectedPElements['P3']) {
             document.getElementById('p3ProductSelected').checked = auditData.header.p3ProductSelected;
        }
        if (auditData.header.selectedPElements['P4']) {
             document.getElementById('p4ProductSelected').checked = auditData.header.p4ProductSelected;
        }
    }

    function updateProcessName(inputElement, processIndex) { auditData.header.processes[processIndex].name = inputElement.value; }
    function updateProductName(inputElement, productIndex) { auditData.header.products[productIndex].name = inputElement.value; }

    function addProcessRow() {
        if (auditData.header.processes.length >= 5) {
            alert("最多只能添加5个工序。");
            return;
        }
        const newName = `新工序 ${auditData.header.processes.length + 1}`;
        auditData.header.processes.push({ name: newName, products: auditData.header.products.map(() => false) });
        rebuildProcessProductTable();
    }

    function deleteProcessRow(button) {
        const rowIndex = Array.from(button.closest('tbody').children).indexOf(button.closest('tr'));
        auditData.header.processes.splice(rowIndex, 1);
        rebuildProcessProductTable();
    }

    function addProductColumn() {
        if (auditData.header.products.length >= 5) {
            alert("最多只能添加5个产品。");
            return;
        }
        const newName = `新产品 ${auditData.header.products.length + 1}`;
        auditData.header.products.push({ name: newName });
        auditData.header.processes.forEach(proc => proc.products.push(false));
        rebuildProcessProductTable();
    }

    function deleteProductColumn(productIndex) {
        auditData.header.products.splice(productIndex, 1);
        auditData.header.processes.forEach(proc => proc.products.splice(productIndex, 1));
        rebuildProcessProductTable();
    }

    function updateProductProcessLink(processIndex, productIndex, isChecked) { auditData.header.processes[processIndex].products[productIndex] = isChecked; }

    function rebuildProcessProductTable() {
        const table = document.getElementById('processProductTable');
        const headerRow = table.querySelector('thead tr');
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        // Clear existing product columns from header, keeping "工序" and "+ 产品" button
        while (headerRow.children.length > 2) { 
            headerRow.removeChild(headerRow.children[1]); // Remove the one after "工序"
        }

        auditData.header.products.forEach((product, prodIdx) => {
            const th = document.createElement('th');
            th.innerHTML = `<input type="text" value="${product.name}" onchange="updateProductName(this, ${prodIdx})"> <button type="button" class="delete-button small-button" onclick="deleteProductColumn(${prodIdx})">-</button>`;
            headerRow.insertBefore(th, headerRow.lastElementChild); // Insert before the "+ 产品" button cell
        });

        auditData.header.processes.forEach((process, procIdx) => {
            const tr = tbody.insertRow();
            const cellProcess = tr.insertCell();
            cellProcess.innerHTML = `<input type="text" value="${process.name}" onchange="updateProcessName(this, ${procIdx})"> <button type="button" class="delete-button small-button" onclick="deleteProcessRow(this)">-</button>`;
            auditData.header.products.forEach((_, prodIdx) => {
                const cellLink = tr.insertCell();
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = process.products[prodIdx] || false;
                checkbox.onchange = () => updateProductProcessLink(procIdx, prodIdx, checkbox.checked);
                cellLink.appendChild(checkbox);
            });
            tr.insertCell(); // Empty cell for alignment with add product button column, if "+产品" button has its own cell
        });
    }
    
    function addAuditorRowClickHandler() {
        const tbody = document.getElementById('auditorTable').getElementsByTagName('tbody')[0];
        if (tbody.rows.length >= 5) {
            alert("最多只能添加5名审核员。");
            return;
        }
        addAuditorRow(); 
    }

    function addAuditorRow(auditor = { name: '', type: '', email: '', phone: '' }) {
        const tbody = document.getElementById('auditorTable').getElementsByTagName('tbody')[0];
        // Check limit again, although addAuditorRowClickHandler should prevent this.
        // This is mainly for when loading data that might exceed the limit before being truncated.
        if (tbody.rows.length >= 5 && !auditor.name) { // Only block new blank rows if at limit
            return;
        }
        const newRow = tbody.insertRow();
        newRow.insertCell().textContent = tbody.rows.length;
        newRow.insertCell().innerHTML = `<input type="text" data-field="name" value="${auditor.name}">`;
        newRow.insertCell().innerHTML = `<input type="text" data-field="type" value="${auditor.type}">`;
        newRow.insertCell().innerHTML = `<input type="email" data-field="email" value="${auditor.email}">`;
        newRow.insertCell().innerHTML = `<input type="tel" data-field="phone" value="${auditor.phone}">`;
        newRow.insertCell().innerHTML = `<button type="button" class="delete-button" onclick="deleteAuditorRow(this)">删除</button>`;
    }

     function rebuildAuditorTable() {
        const tbody = document.getElementById('auditorTable').querySelector('tbody');
        tbody.innerHTML = '';
        (auditData.header.auditors || []).forEach(auditor => addAuditorRow(auditor)); // addAuditorRow now respects limit for blank adds
        for (let i = 0; i < tbody.rows.length; i++) {
            tbody.rows[i].cells[0].textContent = i + 1;
        }
    }

    function deleteAuditorRow(button) {
        const row = button.closest('tr');
        // const rowIndex = Array.from(row.parentNode.children).indexOf(row); // Not needed to update auditData here
        row.parentNode.removeChild(row);
        const tbody = document.getElementById('auditorTable').getElementsByTagName('tbody')[0];
        for (let i = 0; i < tbody.rows.length; i++) {
            tbody.rows[i].cells[0].textContent = i + 1;
        }
    }

    function handlePElementSelectionChange(checkbox) {
        auditData.header.selectedPElements[checkbox.value] = checkbox.checked;
        const p3ProdCbContainer = document.getElementById('p3ProductCheckboxContainer');
        const p4ProdCbContainer = document.getElementById('p4ProductCheckboxContainer');

        if (checkbox.value === 'P3') {
            p3ProdCbContainer.style.display = checkbox.checked ? 'inline-block' : 'none';
            if (!checkbox.checked) {
                document.getElementById('p3ProductSelected').checked = false;
                auditData.header.p3ProductSelected = false;
            }
        }
        if (checkbox.value === 'P4') {
            p4ProdCbContainer.style.display = checkbox.checked ? 'inline-block' : 'none';
            if (!checkbox.checked) {
                document.getElementById('p4ProductSelected').checked = false;
                auditData.header.p4ProductSelected = false;
            }
        }
    }

    function updateQuestionnaireAndMatrix() {
        auditData.header.companyName = document.getElementById('companyName').value;
        auditData.header.auditDate = document.getElementById('auditDate').value;
        auditData.header.auditLocation = document.getElementById('auditLocation').value;
        auditData.header.reportNumber = document.getElementById('reportNumber').value;
        auditData.header.auditLanguage = document.getElementById('auditLanguage').value;
        
        // Auditors are read from the table
        auditData.header.auditors = [];
        const auditorRows = document.getElementById('auditorTable').querySelector('tbody').rows;
        for (let row of auditorRows) {
            auditData.header.auditors.push({
                name: row.cells[1].querySelector('input').value,
                type: row.cells[2].querySelector('input').value,
                email: row.cells[3].querySelector('input').value,
                phone: row.cells[4].querySelector('input').value,
            });
        }
        // Process/Product matrix is already live updated in auditData.header.processes/products
        // P-Element selections are live updated in auditData.header.selectedPElements
        auditData.header.p3ProductSelected = document.getElementById('p3ProductSelected').checked;
        auditData.header.p4ProductSelected = document.getElementById('p4ProductSelected').checked;

        applyExperienceVersionLimits(auditData); // Crucial: ensure limits are applied before render

        renderQuestionnaire();
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();

        // 检查P3/P4产品选择
        const isP3Selected = document.querySelector('input[name="pElement"][value="P3"]').checked;
        const isP4Selected = document.querySelector('input[name="pElement"][value="P4"]').checked;
        const isP3ProductSelected = document.getElementById('p3ProductSelected').checked;
        const isP4ProductSelected = document.getElementById('p4ProductSelected').checked;

        let messageContainer = document.getElementById('questionnaireSyncMessageContainer');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'questionnaireSyncMessageContainer';
            messageContainer.style.cssText = 'display: inline-block; margin-left: 10px; font-size: 1em;'; // 初始不设置颜色
            const syncButton = document.getElementById('syncQuestionnaireButton');
            if (syncButton && syncButton.parentNode) {
                syncButton.parentNode.insertBefore(messageContainer, syncButton.nextSibling);
            }
        }

        if ((isP3Selected && !isP3ProductSelected) || (isP4Selected && !isP4ProductSelected)) {
            messageContainer.innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: orange;">请先选择P3 P4过程要素产品</span>';
            messageContainer.style.color = ''; // 清除之前的颜色设置
            messageContainer.style.opacity = '1';
            setTimeout(() => {
                messageContainer.style.opacity = '0';
            }, 5000);
            return; // 阻止后续操作
        }

        // 显示成功提示信息
        messageContainer.innerHTML = '<span style="color: orange;">温馨提醒：</span>提问表和评价矩阵已根据您的选择更新。';
        messageContainer.style.color = 'green'; // 成功提示为绿色
        messageContainer.style.opacity = '1';
        setTimeout(() => {
            messageContainer.style.opacity = '0';
        }, 5000); // 5秒后消失

    }

    function renderQuestionnaire() {
        const container = document.getElementById('questionnaireContainer');
        container.innerHTML = '';
        let hasVisibleQuestions = false;
        ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].forEach(pKey => {
            if (auditData.header.selectedPElements[pKey]) {
                if (pKey === 'P6') {
                    if (auditData.header.processes.length === 0) {
                        container.innerHTML += '<p style="color:red; text-align:center;">P6被选中，但未定义任何工序。请在“基本信息”页添加工序。</p>';
                        return;
                    }
                    auditData.header.processes.forEach(process => {
                        if (!process.name.trim()) return;
                        const processP6Data = JSON.parse(JSON.stringify(p6Template));
                        processP6Data.name = `P6: ${process.name} (工序)`;
                        container.appendChild(createPElementBlock(processP6Data, `_${process.name}`));
                        hasVisibleQuestions = true;
                    });
                } else {
                    if (vdaQuestionsData[pKey]) {
                        container.appendChild(createPElementBlock(vdaQuestionsData[pKey]));
                        hasVisibleQuestions = true;
                    } else {
                        console.warn(`Data for P-Element ${pKey} not found.`);
                    }
                }
            }
        });
        if (!hasVisibleQuestions) {
            container.innerHTML = '<p style="text-align:center; color: #666;">请先在“基本信息”页选择过程要素并点击“应用选择并更新提问表”。</p>';
        }
    }

    function createPElementBlock(pElementData, processNameSuffix = '') {
        const block = document.createElement('div');
        block.className = 'question-block p-element';
        const pKeyShort = (pElementData.questions && pElementData.questions.length > 0) ? pElementData.questions[0].id.substring(0,2) : '';

        const header = document.createElement('div');
        header.className = 'question-header';
        header.textContent = pElementData.name;
        const contentDiv = document.createElement('div');
        contentDiv.className = 'question-content collapsed'; // 初始为折叠状态
        contentDiv.style.maxHeight = '0px'; // 初始高度为0
        contentDiv.style.overflow = 'hidden';
        contentDiv.style.transition = 'max-height 0.3s ease-out';

        header.onclick = (e) => {
            e.currentTarget.classList.toggle('collapsed');
            contentDiv.classList.toggle('collapsed');
            if (contentDiv.classList.contains('collapsed')) {
                contentDiv.style.maxHeight = '0px';
            } else {
                contentDiv.style.maxHeight = contentDiv.scrollHeight + "px";
            }
        };
        block.appendChild(header);

        (pElementData.questions || []).forEach(q => {
            const qDiv = document.createElement('div');
            qDiv.className = 'question-item';
            qDiv.style.marginBottom = '20px';
            const qIdBase = q.id + processNameSuffix;
            
            qDiv.innerHTML = `
                <p class="question-text">${q.isStar ? '<span style="color:red;font-weight:bold;">*</span> ' : ''}${q.id}${processNameSuffix.replace(/_/g,' ')}: ${q.text}</p>
                <div class="requirements-container" style="margin-top: 10px; padding: 10px; border: 1px solid #eee; border-radius: 5px; background-color: #f9f9f9;">
                    <p style="font-weight: bold; margin-bottom: 5px;">与评价有关的最低要求:</p>
                    <p>${q.minReq || '无'}</p>
                    <p style="font-weight: bold; margin-top: 10px; margin-bottom: 5px;">执行示例:</p>
                    <p>${q.implEx || '无'}</p>
                </div>
            `;

            const scoreDiv = document.createElement('div');
            scoreDiv.style.marginTop = '10px';
            const p3ProductSelected = auditData.header.selectedPElements['P3'] && auditData.header.p3ProductSelected;
            const p4ProductSelected = auditData.header.selectedPElements['P4'] && auditData.header.p4ProductSelected;

            const isSplitP3P4 = (pKeyShort === 'P3' && p3ProductSelected) || (pKeyShort === 'P4' && p4ProductSelected);

            if (isSplitP3P4) {
                 scoreDiv.innerHTML += createScoreDropdownHTML(qIdBase + '_product', '产品评分: ');
                 scoreDiv.innerHTML += createScoreDropdownHTML(qIdBase + '_process', '过程评分: ', true);
            } else {
                 scoreDiv.innerHTML += createScoreDropdownHTML(qIdBase, '评分: ');
            }
            qDiv.appendChild(scoreDiv);

            const manualRecordSectionDiv = document.createElement('div');
            manualRecordSectionDiv.className = 'manual-record-section';
            manualRecordSectionDiv.style.marginTop = '15px';
            manualRecordSectionDiv.style.paddingTop = '10px';
            manualRecordSectionDiv.style.borderTop = '1px dashed #ccc';


            if (isSplitP3P4) {
                manualRecordSectionDiv.innerHTML += `
                    <div>
                        <label for="${qIdBase}_product_manualRecord">产品审核记录 (手动填写):</label>
                        <textarea id="${qIdBase}_product_manualRecord" style="width: calc(100% - 24px); min-height: 40px;" oninput="updateQuestionDetails('${qIdBase}_product', 'manualRecord', this.value)">${auditData.questionDetails[qIdBase+'_product']?.manualRecord || ''}</textarea>
                        <label for="${qIdBase}_product_attachment" style="display:block; margin-top:5px;">产品附件:</label>
                        <input type="file" id="${qIdBase}_product_attachment" onchange="updateQuestionDetails('${qIdBase}_product', 'attachmentName', this.files[0] ? this.files[0].name : '')">
                        <span id="${qIdBase}_product_attachmentNameDisplay" style="margin-left: 10px;">${auditData.questionDetails[qIdBase+'_product']?.attachmentName || ''}</span>
                    </div>
                    <div style="margin-top:10px;">
                        <label for="${qIdBase}_process_manualRecord">过程审核记录 (手动填写):</label>
                        <textarea id="${qIdBase}_process_manualRecord" style="width: calc(100% - 24px); min-height: 40px;" oninput="updateQuestionDetails('${qIdBase}_process', 'manualRecord', this.value)">${auditData.questionDetails[qIdBase+'_process']?.manualRecord || ''}</textarea>
                        <label for="${qIdBase}_process_attachment" style="display:block; margin-top:5px;">过程附件:</label>
                        <input type="file" id="${qIdBase}_process_attachment" onchange="updateQuestionDetails('${qIdBase}_process', 'attachmentName', this.files[0] ? this.files[0].name : '')">
                        <span id="${qIdBase}_process_attachmentNameDisplay" style="margin-left: 10px;">${auditData.questionDetails[qIdBase+'_process']?.attachmentName || ''}</span>
                    </div>
                `;
            } else {
                manualRecordSectionDiv.innerHTML = `
                    <div>
                        <label for="${qIdBase}_manualRecord">审核记录 (手动填写):</label>
                        <textarea id="${qIdBase}_manualRecord" style="width: calc(100% - 24px); min-height: 50px;" oninput="updateQuestionDetails('${qIdBase}', 'manualRecord', this.value)">${auditData.questionDetails[qIdBase]?.manualRecord || ''}</textarea>
                        <label for="${qIdBase}_attachment" style="display:block; margin-top:5px;">添加附件:</label>
                        <input type="file" id="${qIdBase}_attachment" onchange="updateQuestionDetails('${qIdBase}', 'attachmentName', this.files[0] ? this.files[0].name : '')">
                        <span id="${qIdBase}_attachmentNameDisplay" style="margin-left: 10px;">${auditData.questionDetails[qIdBase]?.attachmentName || ''}</span>
                    </div>
                `;
            }
            qDiv.appendChild(manualRecordSectionDiv);

            contentDiv.appendChild(qDiv);
        });
        block.appendChild(contentDiv);
        setTimeout(() => { if (!contentDiv.classList.contains('collapsed')) contentDiv.style.maxHeight = contentDiv.scrollHeight + "px"; }, 0);
        return block;
    }

    function createScoreDropdownHTML(id, labelText, addMargin = false) {
        let html = `<label class="score-label" style="${addMargin ? 'margin-left:20px;' : ''}">${labelText}</label><select id="${id}" class="score-input" onchange="updateScore('${id}', this.value)">`;
        ['', '10', '8', '6', '4', '0', 'n.e.'].forEach(val => {
            const scoreVal = auditData.scores[id] !== undefined ? String(auditData.scores[id]) : '';
            html += `<option value="${val}" ${val === scoreVal ? 'selected' : ''}>${val === '' ? '选择' : val}</option>`;
        });
        html += `</select>`;
        return html;
    }

    function updateScore(questionId, value) {
        const selectElement = document.getElementById(questionId);
        if (value === 'n.e.') {
            auditData.scores[questionId] = 'n.e.';
            selectElement.style.backgroundColor = '#a8e6a8'; // 深绿色
        } else if (value === '') {
            delete auditData.scores[questionId];
            selectElement.style.backgroundColor = ''; // 恢复默认
        } else {
            const score = parseInt(value);
            auditData.scores[questionId] = score;
            if (score === 0) {
                selectElement.style.backgroundColor = '#ff8080'; // 深红色
            } else if (score === 4 || score === 6 || score === 8) {
                selectElement.style.backgroundColor = '#ffcc80'; // 橙色
            } else if (score === 10) {
                selectElement.style.backgroundColor = '#a8e6a8'; // 深绿色
            } else {
                selectElement.style.backgroundColor = ''; // 恢复默认
            }
        }
        renderEvaluationSummaryTable(); 
        renderEvaluationMatrix();
        renderActionPlan();
    }

    function updateQuestionDetails(questionId, field, value) {
        if (!auditData.questionDetails[questionId]) {
            auditData.questionDetails[questionId] = { manualRecord: "", attachmentName: "" };
        }
        auditData.questionDetails[questionId][field] = value;
        if (field === 'attachmentName') {
            const displayElement = document.getElementById(questionId + '_attachmentNameDisplay');
            if (displayElement) displayElement.textContent = value;
        }
    }

    function scoreAllVisibleTen() {
        const allSelects = document.querySelectorAll('#questionnaireContainer select.score-input');
        let visibleSelects = [];
        allSelects.forEach(select => {
            let currentElement = select;
            let isVisible = true;
            while(currentElement && currentElement !== document.body) {
                if (currentElement.classList && currentElement.classList.contains('question-content') && currentElement.classList.contains('collapsed')) {
                    isVisible = false;
                    break;
                }
                if (currentElement.style.display === 'none') {
                    isVisible = false;
                    break;
                }
                currentElement = currentElement.parentElement;
            }
            if (isVisible) {
                visibleSelects.push(select);
            }
        });

        if (visibleSelects.length === 0) {
            document.getElementById('score-clear-message').innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: orange;">提问不可见无法打分，请点击过程要素项目展开提问表</span>';
            document.getElementById('score-clear-message').style.display = 'block';
            setTimeout(() => { document.getElementById('score-clear-message').style.display = 'none'; }, 3000);
            return;
        }

        visibleSelects.forEach(selectElement => {
            if(selectElement.value === '' || selectElement.value === 'n.e.' || (selectElement.value !== '10' && !isNaN(parseInt(selectElement.value)))) {
                 selectElement.value = '10';
                 updateScore(selectElement.id, '10');
            }
        });
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();
        document.getElementById('score-clear-message').style.display = 'none';
        document.getElementById('score-clear-message').innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: green;">当前展开的过程要素项目提问的评分已打10分</span>';
         document.getElementById('score-clear-message').style.display = 'block';
         setTimeout(() => { document.getElementById('score-clear-message').style.display = 'none'; }, 3000);
    }


    function randomlyScoreAllVisible() {
        const allSelects = document.querySelectorAll('#questionnaireContainer select.score-input');
        let visibleSelects = [];
        allSelects.forEach(select => {
            let currentElement = select;
            let isVisible = true;
            while(currentElement && currentElement !== document.body) {
                if (currentElement.classList && currentElement.classList.contains('question-content') && currentElement.classList.contains('collapsed')) {
                    isVisible = false;
                    break;
                }
                if (currentElement.style.display === 'none') {
                    isVisible = false;
                    break;
                }
                currentElement = currentElement.parentElement;
            }
            if (isVisible) {
                visibleSelects.push(select);
            }
        });

        if (visibleSelects.length === 0) {
            document.getElementById('score-clear-message').innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: orange;">提问不可见无法打分，请点击过程要素项目展开提问表</span>';
            document.getElementById('score-clear-message').style.display = 'block';
            setTimeout(() => { document.getElementById('score-clear-message').style.display = 'none'; }, 3000);
            return;
        }

        let counts = { '0': 0, '4': 0, '6': 0, '8': 0 };
        const maxCounts = { '0': 1, '4': 3, '6': 3, '8': 10 }; // Max occurrences for each score

        // Shuffle visible selects to distribute scores more randomly
        for (let i = visibleSelects.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [visibleSelects[i], visibleSelects[j]] = [visibleSelects[j], visibleSelects[i]];
        }

        visibleSelects.forEach(selectElement => {
            let score;
            // Try to assign lower scores first if limits not reached
            if (counts['0'] < maxCounts['0'] && Math.random() < 0.1) { // 10% chance for 0 if available
                score = '0';
            } else if (counts['4'] < maxCounts['4'] && Math.random() < 0.2) { // 20% chance for 4 if available
                score = '4';
            } else if (counts['6'] < maxCounts['6'] && Math.random() < 0.3) { // 30% chance for 6 if available
                score = '6';
            } else if (counts['8'] < maxCounts['8'] && Math.random() < 0.5) { // 50% chance for 8 if available
                score = '8';
            } else {
                score = '10'; // Default to 10
            }

            // If a specific score was chosen but its max count is reached, default to 10
            if (score !== '10' && counts[score] >= (maxCounts[score] || Infinity)) {
                score = '10';
            }
            
            // Increment count for the chosen score
            if (score !== '10') {
                counts[score]++;
            }

            selectElement.value = score;
            updateScore(selectElement.id, score);
        });
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();
        document.getElementById('score-clear-message').innerHTML = '<span style="color: red;">温馨提示：</span><span style="color: green;">当前展开的过程要素项目提问的评分，随机打分完成</span>';
        document.getElementById('score-clear-message').style.display = 'block';
        setTimeout(() => { document.getElementById('score-clear-message').style.display = 'none'; }, 3000);
    }

    function clearAllScores() {
        document.querySelectorAll('#questionnaireContainer select.score-input').forEach(s => { s.value = ''; updateScore(s.id, ''); });
        auditData.scores = {};
        auditData.questionDetails = {}; 
        document.querySelectorAll('.manual-record-section textarea').forEach(ta => ta.value = '');
        document.querySelectorAll('.manual-record-section input[type="file"]').forEach(fi => fi.value = null);
        document.querySelectorAll('.manual-record-section span[id$="_attachmentNameDisplay"]').forEach(span => span.textContent = '');
        
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();
        document.getElementById('score-clear-message').style.display = 'none';
        alert("所有分数、审核记录和附件名已清除。");
    }

    function getQuestionScore(qIdBase, pElementKey, questionDef) {
        let score;
        let isNE = false;
        let productScoreVal, processScoreVal;

        const isP3ProdSelected = auditData.header.selectedPElements['P3'] && auditData.header.p3ProductSelected;
        const isP4ProdSelected = auditData.header.selectedPElements['P4'] && auditData.header.p4ProductSelected;

        const isSplitP3P4 = (pElementKey === 'P3' && isP3ProdSelected) ||
                            (pElementKey === 'P4' && isP4ProdSelected);

        if (isSplitP3P4) {
            productScoreVal = auditData.scores[qIdBase + '_product'];
            processScoreVal = auditData.scores[qIdBase + '_process'];

            const prodIsNE = productScoreVal === 'n.e.';
            const procIsNE = processScoreVal === 'n.e.';
            const prodIsScored = productScoreVal !== undefined && productScoreVal !== '' && !prodIsNE;
            const procIsScored = processScoreVal !== undefined && processScoreVal !== '' && !procIsNE;

            if (prodIsNE || procIsNE) { 
                isNE = true;
            } else if (prodIsScored && procIsScored) {
                score = (Number(productScoreVal) + Number(processScoreVal)) / 2;
            } else { 
                isNE = true; // Treat as not evaluated if one part is missing score
            }
        } else {
            const directScore = auditData.scores[qIdBase];
            if (directScore === 'n.e.') {
                isNE = true;
            } else if (directScore !== undefined && directScore !== '') {
                score = Number(directScore);
            } else { 
                isNE = true; // Treat as not evaluated if score is missing
            }
        }
        return { score: isNE ? undefined : score, isNE, isScored: !isNE && score !== undefined };
    }

    function calculateScopeResults(pKeyList, forProduct = null) {
        let totalAchieved = 0;
        let totalPossible = 0;
        let pElementResults = {};
        let allPElementsInScopeValid = true;
        let individualProcessP6Scores = {};
        let p6SubElementScoresForScope = {}; // To store EUx scores for P6 processes in scope

        pKeyList.forEach(pKey => {
            if (!auditData.header.selectedPElements[pKey]) return;

            let pAch = 0, pPoss = 0, pEvalCount = 0;
            let pStar0 = 0, pStar4 = 0, pAny0 = 0;
            let questionsToConsider;
            let elementTotalQuestions = 0;

            if (pKey === 'P6') {
                questionsToConsider = p6Template.questions;
                elementTotalQuestions = questionsToConsider.length;

                let relevantProcessesForP6Evaluation = [];
                if (forProduct && auditData.header.products.length > 0 && auditData.header.processes.length > 0) {
                    const productIndex = auditData.header.products.findIndex(p => p.name === forProduct.name);
                    if (productIndex !== -1) {
                        relevantProcessesForP6Evaluation = auditData.header.processes.filter(proc => proc.products && proc.products[productIndex]);
                    }
                } else {
                    relevantProcessesForP6Evaluation = auditData.header.processes;
                }

                let aggregatedP6Ach = 0;
                let aggregatedP6Poss = 0;
                let aggregatedP6EvalCount = 0;
                let aggregatedP6Star0 = 0;
                let aggregatedP6Star4 = 0;
                let aggregatedP6Any0 = 0;

                if (relevantProcessesForP6Evaluation && relevantProcessesForP6Evaluation.length > 0) {
                    relevantProcessesForP6Evaluation.forEach(process => {
                        if (!process.name.trim()) return;
                        let procP6Ach = 0, procP6Poss = 0, procP6Eval = 0;
                        let procP6Star0 = 0, procP6Star4 = 0, procP6Any0 = 0;
                        questionsToConsider.forEach(qDef => {
                            const qIdBase = qDef.id + `_${process.name}`;
                            const res = getQuestionScore(qIdBase, 'P6', qDef);
                            if (res.isScored) {
                                procP6Ach += res.score;
                                procP6Poss += 10;
                                procP6Eval++;
                                if (qDef.isStar && res.score === 0) procP6Star0++;
                                if (qDef.isStar && res.score === 4) procP6Star4++;
                                if (res.score === 0) procP6Any0++;
                            }
                        });
                        const minEvalProcP6 = elementTotalQuestions > 0 ? Math.ceil(elementTotalQuestions * 2 / 3) : 0;
                        const isValidProcP6 = procP6Eval >= minEvalProcP6 || elementTotalQuestions === 0;
                        const procP6Value = (procP6Poss > 0 && isValidProcP6) ? Math.round((procP6Ach / procP6Poss) * 100) : 0; 
                        
                        individualProcessP6Scores[process.name] = {
                            value: procP6Value,
                            isValid: isValidProcP6,
                            star0: procP6Star0, star4: procP6Star4, any0: procP6Any0,
                            achieved: procP6Ach, possible: procP6Poss 
                        };
                        // Calculate and store EUx for this P6 process
                        p6SubElementScoresForScope[process.name] = calculateP6ProcessSubElementScores(process.name);

                        if (!isValidProcP6) allPElementsInScopeValid = false;
                    });

                    questionsToConsider.forEach(qDef => {
                        let qScoresSum = 0, qScoresCount = 0;
                        let qHasStar0 = false, qHasStar4 = false, qHasAny0 = false;

                        relevantProcessesForP6Evaluation.forEach(process => {
                            if (!process.name.trim()) return;
                            const qIdBase = qDef.id + `_${process.name}`;
                            const res = getQuestionScore(qIdBase, 'P6', qDef);
                            if (res.isScored) { 
                                qScoresSum += res.score; 
                                qScoresCount++; 
                                if (qDef.isStar && res.score === 0) qHasStar0 = true;
                                if (qDef.isStar && res.score === 4) qHasStar4 = true;
                                if (res.score === 0) qHasAny0 = true;
                            }
                        });

                        if (qScoresCount > 0) {
                            const avgScore = qScoresSum / qScoresCount;
                            aggregatedP6Ach += avgScore;
                            aggregatedP6Poss += 10;
                            aggregatedP6EvalCount++;
                            if (qHasStar0) aggregatedP6Star0++;
                            if (qHasStar4) aggregatedP6Star4++;
                            if (qHasAny0) aggregatedP6Any0++;
                        }
                    });
                    pAch = aggregatedP6Ach;
                    pPoss = aggregatedP6Poss;
                    pEvalCount = aggregatedP6EvalCount;
                    pStar0 = aggregatedP6Star0;
                    pStar4 = aggregatedP6Star4;
                    pAny0 = aggregatedP6Any0;

                } else if (forProduct && (!relevantProcessesForP6Evaluation || relevantProcessesForP6Evaluation.length === 0)) {
                     // If P6 is selected for a product group, but that product is not linked to any process,
                     // P6 should contribute 0 to that product group's score, but be considered "valid" for calculation purposes (doesn't make entire PG invalid alone).
                     pElementResults[pKey] = { value: 0, isValid: true, star0: 0, star4: 0, any0: 0, achieved: 0, possible: 0 }; 
                     return; // Continue to next pKey
                }
                 const minEvalEP6 = elementTotalQuestions > 0 ? Math.ceil(elementTotalQuestions * 2 / 3) : 0;
                 const isValidEP6 = pEvalCount >= minEvalEP6 || elementTotalQuestions === 0; 
                 if (!isValidEP6) allPElementsInScopeValid = false;
                 pElementResults[pKey] = {
                    value: (pPoss > 0 && isValidEP6) ? Math.round((pAch / pPoss) * 100) : 0, 
                    isValid: isValidEP6,
                    star0: pStar0, star4: pStar4, any0: pAny0,
                    achieved: pAch, possible: pPoss
                };
            } else {
                if (!vdaQuestionsData[pKey] || !vdaQuestionsData[pKey].questions) return;
                questionsToConsider = vdaQuestionsData[pKey].questions;
                elementTotalQuestions = questionsToConsider.length;

                questionsToConsider.forEach(qDef => {
                    const res = getQuestionScore(qDef.id, pKey, qDef);
                    if (res.isScored) {
                        pAch += res.score; pPoss += 10; pEvalCount++;
                        if (qDef.isStar && res.score === 0) pStar0++;
                        if (qDef.isStar && res.score === 4) pStar4++;
                        if (res.score === 0) pAny0++;
                    }
                });
                const minEval = elementTotalQuestions > 0 ? Math.ceil(elementTotalQuestions * 2 / 3) : 0;
                const isValidPElement = pEvalCount >= minEval || elementTotalQuestions === 0;
                if (!isValidPElement) allPElementsInScopeValid = false;
                const epnValue = (pPoss > 0 && isValidPElement) ? Math.round((pAch / pPoss) * 100) : 0; 
                pElementResults[pKey] = { value: epnValue, isValid: isValidPElement, star0: pStar0, star4: pStar4, any0: pAny0, achieved: pAch, possible: pPoss };
            }

            if (pElementResults[pKey] && pElementResults[pKey].isValid && pElementResults[pKey].possible > 0) {
                totalAchieved += pElementResults[pKey].achieved;
                totalPossible += pElementResults[pKey].possible;
            }
        });
        const egValue = (totalPossible > 0 && allPElementsInScopeValid) ? Math.round((totalAchieved / totalPossible) * 100) : 0; 
        return { eg: egValue, pElements: pElementResults, allValid: allPElementsInScopeValid, individualProcessP6Scores, p6SubElementScoresByProcess: p6SubElementScoresForScope };
    }

    function applyDowngradingRules(egValue, pElementResults, allPElementsValid, individualProcessP6Scores = {}, p6SubElementScoresByProcess = {}) {
        let finalRating = allPElementsValid ? getRating(egValue) : { text: "无效", class: "rating-invalid" };
        let downgradeReasons = [];

        if (!allPElementsValid) {
            downgradeReasons.push("一个或多个相关过程要素/P6工序未达到最少2/3的提问评价数量。");
            return { rating: finalRating, reasons: downgradeReasons }; 
        }

        let aToBReasonFound = false;
        let bToCReasonFound = false;

        if (finalRating.text === "A") {
            // Check EPn < 80%
            for (const pKey in pElementResults) {
                const res = pElementResults[pKey];
                if (!res.isValid || res.possible === 0) continue;
                if (res.value < 80) { aToBReasonFound = true; downgradeReasons.push(`${pKey}: 符合度 (${res.value}%) < 80%.`); }
                if (res.star4 > 0) { aToBReasonFound = true; downgradeReasons.push(`${pKey}: 至少一个星号提问得4分.`); }
                if (res.any0 > 0 && res.star0 === 0) { aToBReasonFound = true; downgradeReasons.push(`${pKey}: 至少一个普通提问得0分.`); }
            }
            // Check P6 Process Step (En) < 80%
            for (const processName in individualProcessP6Scores) {
                const procP6Res = individualProcessP6Scores[processName];
                if (!procP6Res.isValid) continue;
                if (procP6Res.value < 80) { aToBReasonFound = true; downgradeReasons.push(`工序 ${processName} (P6): 符合度 (${procP6Res.value}%) < 80%.`); }
                if (procP6Res.star4 > 0) { aToBReasonFound = true; downgradeReasons.push(`工序 ${processName} (P6): 至少一个星号提问得4分.`); }
                if (procP6Res.any0 > 0 && procP6Res.star0 === 0) { aToBReasonFound = true; downgradeReasons.push(`工序 ${processName} (P6): 至少一个普通提问得0分.`); }
            }
             // Check P6 Sub-Element (EUx) < 80%
            for (const processName in p6SubElementScoresByProcess) {
                const subElements = p6SubElementScoresByProcess[processName];
                if (subElements) {
                    for (const euKey in subElements) {
                        const euResult = subElements[euKey];
                        if (euResult.isValid && euResult.compliance < 80) {
                            aToBReasonFound = true;
                            downgradeReasons.push(`工序 ${processName} 的P6子要素 ${euKey} (${euResult.name}): 符合度 (${euResult.compliance}%) < 80%.`);
                        }
                    }
                }
            }

            if (aToBReasonFound) {
                finalRating = { text: "B", class: "rating-b" };
            }
        }

        // Check for C downgrade conditions (can happen from A or B)
        // Check EPn < 70% or *-question 0 points
        for (const pKey in pElementResults) {
            const res = pElementResults[pKey];
            if (!res.isValid || res.possible === 0) continue;
            if (res.star0 > 0) { bToCReasonFound = true; downgradeReasons.push(`${pKey}: 至少一个星号提问得0分 (导致C).`); }
            if (res.value < 70) { bToCReasonFound = true; downgradeReasons.push(`${pKey}: 符合度 (${res.value}%) < 70% (导致C).`); }
        }
        // Check P6 Process Step (En) < 70% or *-question 0 points
        for (const processName in individualProcessP6Scores) {
            const procP6Res = individualProcessP6Scores[processName];
            if (!procP6Res.isValid) continue;
            if (procP6Res.star0 > 0) { bToCReasonFound = true; downgradeReasons.push(`工序 ${processName} (P6): 至少一个星号提问得0分 (导致C).`); }
            if (procP6Res.value < 70) { bToCReasonFound = true; downgradeReasons.push(`工序 ${processName} (P6): 符合度 (${procP6Res.value}%) < 70% (导致C).`); }
        }
        // Note: P6 sub-element (EUx) < 70% is not an explicit C downgrade rule per OCR page 17,
        // it's covered if it pulls the En or EP6 down.

        if (bToCReasonFound) { 
            finalRating = { text: "C", class: "rating-c" };
        }
        
        downgradeReasons = [...new Set(downgradeReasons)]; 
        return { rating: finalRating, reasons: downgradeReasons };
    }
    
    function calculateP6ProcessSubElementScores(processName) {
        const p6ProcessSubElementResults = {};
        const p6SubElementDefinitions = [
            { id: 'EU1', sectionKey: 'P6.1', name: 'P6.1 过程输入' },
            { id: 'EU2', sectionKey: 'P6.2', name: 'P6.2 过程流程' },
            { id: 'EU3', sectionKey: 'P6.3', name: 'P6.3 人力资源' },
            { id: 'EU4', sectionKey: 'P6.4', name: 'P6.4 物质资源' },
            { id: 'EU5', sectionKey: 'P6.5', name: 'P6.5 效果和效率' },
            { id: 'EU6', sectionKey: 'P6.6', name: 'P6.6 过程结果（输出）' }
        ];

        p6SubElementDefinitions.forEach(subDef => {
            let euAchieved = 0;
            let euPossible = 0;
            let euEvaluatedCount = 0;
            const questionsInSub = p6Template.questions.filter(q => q.section === subDef.sectionKey);
            const totalQuestionsInSub = questionsInSub.length;

            questionsInSub.forEach(qDef => {
                const qIdForProcess = qDef.id + `_${processName}`;
                const res = getQuestionScore(qIdForProcess, 'P6', qDef);
                if (res.isScored) {
                    euAchieved += res.score;
                    euPossible += 10;
                    euEvaluatedCount++;
                }
            });
            
            const minEvalForEUx = totalQuestionsInSub > 0 ? Math.ceil(totalQuestionsInSub * 2/3) : 0;
            const isEUxValid = euEvaluatedCount >= minEvalForEUx || totalQuestionsInSub === 0;
            
            p6ProcessSubElementResults[subDef.id] = {
                name: subDef.name,
                achieved: euAchieved,
                possible: euPossible,
                compliance: (euPossible > 0 && isEUxValid) ? Math.round((euAchieved / euPossible) * 100) : 0,
                isValid: isEUxValid,
                questions: questionsInSub 
            };
        });
        return p6ProcessSubElementResults;
    }

    function renderEvaluationSummaryTable() {
        const tableBody = document.getElementById('evaluationSummaryTable').querySelector('tbody');
        tableBody.innerHTML = ''; 

        const overallScope = ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].filter(p => auditData.header.selectedPElements[p]);
        const { pElements: overallPeResults, individualProcessP6Scores } = calculateScopeResults(overallScope);

        overallScope.forEach(pKey => {
            if (!auditData.header.selectedPElements[pKey]) return;

            const pElementData = (pKey === 'P6') ? p6Template : vdaQuestionsData[pKey];
            const pElementResult = overallPeResults[pKey];

            const pHeaderRow = tableBody.insertRow();
            pHeaderRow.className = 'p-element-header';
            pHeaderRow.insertCell().textContent = pElementData.name;
            pHeaderRow.insertCell().textContent = ''; 
            pHeaderRow.insertCell().textContent = ''; 
            const complianceCell = pHeaderRow.insertCell();
            const ratingCell = pHeaderRow.insertCell();

            if (pElementResult && pElementResult.isValid) {
                complianceCell.innerHTML = `<b>${pElementResult.achieved.toFixed(2)} / ${pElementResult.possible.toFixed(0)} = ${pElementResult.value}%</b>`;
                const rating = getRating(pElementResult.value);
                ratingCell.innerHTML = `<span class="${rating.class}" style="font-size: 1.2em;">${rating.text}</span>`;
            } else {
                complianceCell.textContent = "无效 (未达2/3)";
                ratingCell.innerHTML = `<span class="rating-invalid" style="font-size: 1.2em;">无效</span>`;
            }


            if (pKey === 'P6') {
                if (!auditData.header.processes || auditData.header.processes.length === 0) {
                    const noProcRow = tableBody.insertRow();
                    noProcRow.className = 'p6-process-header';
                    const cell = noProcRow.insertCell();
                    cell.colSpan = 5;
                    cell.textContent = 'P6: 未定义工序';
                    return;
                }
                auditData.header.processes.forEach(process => {
                    if (!process.name.trim()) return;
                    const p6ProcessResult = individualProcessP6Scores[process.name];
                    const p6ProcSubElementScores = calculateP6ProcessSubElementScores(process.name);

                    const p6ProcHeaderRow = tableBody.insertRow();
                    p6ProcHeaderRow.className = 'p6-process-header';
                    p6ProcHeaderRow.insertCell().textContent = `工序: ${process.name}`;
                    p6ProcHeaderRow.insertCell(); 
                    p6ProcHeaderRow.insertCell();
                    const p6ProcComplianceCell = p6ProcHeaderRow.insertCell();
                    const p6ProcRatingCell = p6ProcHeaderRow.insertCell();
                    if (p6ProcessResult && p6ProcessResult.isValid) {
                        p6ProcComplianceCell.innerHTML = `<b>${p6ProcessResult.achieved.toFixed(2)} / ${p6ProcessResult.possible.toFixed(0)} = ${p6ProcessResult.value}%</b> (En)`;
                        const enRating = getRating(p6ProcessResult.value);
                        p6ProcRatingCell.innerHTML = `<span class="${enRating.class}" style="font-size: 1em;">${enRating.text}</span>`;
                    } else {
                        p6ProcComplianceCell.textContent = "无效 (未达2/3)";
                        p6ProcRatingCell.innerHTML = `<span class="rating-invalid" style="font-size: 1em;">无效</span>`;
                    }


                    Object.values(p6ProcSubElementScores).forEach(euResult => {
                        const euHeaderRow = tableBody.insertRow();
                        euHeaderRow.className = 'p6-subelement-header';
                        euHeaderRow.insertCell().textContent = `${euResult.name} (EUx)`;
                        euHeaderRow.insertCell(); 
                        euHeaderRow.insertCell();
                        const euComplianceCell = euHeaderRow.insertCell();
                        euHeaderRow.insertCell(); 

                        if(euResult.isValid) {
                            euComplianceCell.textContent = `${euResult.achieved.toFixed(2)} / ${euResult.possible.toFixed(0)} = ${euResult.compliance}%`;
                        } else {
                             euComplianceCell.textContent = "无效 (未达2/3)";
                        }


                        euResult.questions.forEach(qDef => {
                            const qRow = tableBody.insertRow();
                            qRow.className = 'p6-question-row';
                            const qIdForProcess = qDef.id + `_${process.name}`;
                            const qScoreResult = getQuestionScore(qIdForProcess, 'P6', qDef);
                            
                            qRow.insertCell().textContent = `${qDef.id}: ${qDef.text.substring(0,30)}... ${qDef.isStar ? '(*)' : ''}`;
                            qRow.insertCell().textContent = ''; 
                            qRow.insertCell().textContent = '';
                            qRow.insertCell().textContent = qScoreResult.isScored ? qScoreResult.score.toFixed(1) : (qScoreResult.isNE ? 'n.e.' : '未评分');
                            qRow.insertCell(); 
                        });
                    });
                });
            } else { 
                pElementData.questions.forEach(qDef => {
                    const qRow = tableBody.insertRow();
                    qRow.className = 'question-row';
                    const qScoreResult = getQuestionScore(qDef.id, pKey, qDef);
                    
                    qRow.insertCell().textContent = `${qDef.id}: ${qDef.text.substring(0,40)}... ${qDef.isStar ? '(*)' : ''}`;
                    
                    const isSplitP3P4 = (pKey === 'P3' && auditData.header.p3ProductSelected) || (pKey === 'P4' && auditData.header.p4ProductSelected) || (pKey === 'P5' && auditData.header.p5ProductSelected);
                    if (isSplitP3P4) {
                        const prodS_val = auditData.scores[qDef.id + '_product'];
                        const procS_val = auditData.scores[qDef.id + '_process'];
                        qRow.insertCell().textContent = (prodS_val !== undefined && prodS_val !== '') ? String(prodS_val) : '-';
                        qRow.insertCell().textContent = (procS_val !== undefined && procS_val !== '') ? String(procS_val) : '-';
                    } else {
                        qRow.insertCell().textContent = ''; 
                        qRow.insertCell().textContent = ''; 
                    }
                    qRow.insertCell().textContent = qScoreResult.isScored ? qScoreResult.score.toFixed(1) : (qScoreResult.isNE ? 'n.e.' : '未评分');
                    qRow.insertCell(); 
                });
            }
        });
         if (tableBody.innerHTML === '') {
            tableBody.innerHTML = `<tr><td colspan="5" style="text-align:center; color:#666;">请先选择过程要素并在提问表打分。</td></tr>`;
        }
    }

    function renderEvaluationMatrix() { 
        const container = document.getElementById('evaluationMatrixContainer');
        container.innerHTML = `
            <h3>总体评价 (EG)</h3><p id="overallRating">待计算...</p><div id="overallDowngradeReason" class="downgrade-reason"></div>
            <h3>各过程要素评价 (EPn) - 用于总体评价</h3><table id="epnTable"><thead><tr><th>过程要素</th><th>符合度 (%)</th><th>评价</th><th>主要降级原因</th></tr></thead><tbody></tbody></table>
            <h3>产品组评价</h3><div id="productGroupEvaluationContainer"></div>`;

        const overallScope = ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].filter(p => auditData.header.selectedPElements[p]);
        const {eg: overallEg, pElements: overallPeResults, allValid: overallAllValid, individualProcessP6Scores: overallIndivP6Scores, p6SubElementScoresByProcess: overallP6SubElementScores} = calculateScopeResults(overallScope);
        const overallEvaluation = applyDowngradingRules(overallEg, overallPeResults, overallAllValid, overallIndivP6Scores, overallP6SubElementScores);

        document.getElementById('overallRating').innerHTML = `${overallAllValid ? overallEg + '%' : ''} - <span class="${overallEvaluation.rating.class}">${overallEvaluation.rating.text}</span>`;
        document.getElementById('overallDowngradeReason').innerHTML = overallEvaluation.reasons.join('<br>');

        const epnTableBody = document.getElementById('epnTable').querySelector('tbody');
        epnTableBody.innerHTML = '';
        for (const pKey in overallPeResults) {
            const res = overallPeResults[pKey];
            const pElementName = (pKey === 'P6' ? p6Template.name : (vdaQuestionsData[pKey] ? vdaQuestionsData[pKey].name : pKey));
            const tr = epnTableBody.insertRow();
            tr.insertCell().textContent = pElementName;
            tr.insertCell().textContent = res.isValid ? `${res.value}%` : "无效 (未达2/3)";
            const rating = res.isValid ? getRating(res.value) : {text: "无效", class:"rating-invalid"};
            tr.insertCell().innerHTML = `<span class="${rating.class}">${rating.text}</span>`;

            let epnDReason = "";
            if (!res.isValid) {
                 epnDReason = "未达到最少评价数量 (2/3)";
            } else { 
                if (res.star0 > 0) epnDReason += "星号提问0分; ";
                if (res.star4 > 0 && rating.text !== 'C') epnDReason += "星号提问4分; ";
                if (res.any0 > 0 && res.star0 === 0 && rating.text !== 'C') epnDReason += "普通提问0分; ";
                if (res.value < 70) epnDReason += `符合度 ${res.value}%<70%; `;
                else if (res.value < 80 && rating.text !== 'A') epnDReason += `符合度 ${res.value}%<80%; `;
            }
            tr.insertCell().textContent = epnDReason.trim().slice(0, -1);
        }

        const pgContainer = document.getElementById('productGroupEvaluationContainer');
        pgContainer.innerHTML = "";
        auditData.productGroupEvaluations = []; // Clear previous product group evaluations
        if (!auditData.header.products || auditData.header.products.length === 0 || auditData.header.products.every(p => !p.name.trim())) {
            pgContainer.innerHTML = "<p><i>未定义有效的产品名称，无法进行产品组评价。</i></p>";
        } else {
            let productContentRendered = false;
            auditData.header.products.forEach(product => {
                if (!product.name.trim()) return;
                productContentRendered = true;
                const productScope = ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].filter(pKey => {
                    if (!auditData.header.selectedPElements[pKey]) return false;
                    if (pKey === 'P6') {
                        const productIndex = auditData.header.products.findIndex(p => p.name === product.name);
                        return productIndex !== -1 && auditData.header.processes.some(proc => proc.products && proc.products[productIndex]);
                    }
                    return true;
                });

                const {eg: productEg, pElements: productPeResults, allValid: productAllValid, individualProcessP6Scores: productIndivP6Scores, p6SubElementScoresByProcess: productP6SubElementScores} = calculateScopeResults(productScope, product);
                const productEvaluation = applyDowngradingRules(productEg, productPeResults, productAllValid, productIndivP6Scores, productP6SubElementScores);

                // Store product group evaluation data in auditData
                    if (!auditData.productGroupEvaluations) {
                        auditData.productGroupEvaluations = [];
                    }
                    auditData.productGroupEvaluations.push({
                        name: product.name,
                        compliance: productAllValid ? productEg + '%' : 'N/A',
                        rating: productEvaluation.rating.text,
                        downgradeReasons: productEvaluation.reasons.join('<br>')
                    });

                    pgContainer.innerHTML += `
                        <h4>产品组: ${product.name}</h4>
                        <p>符合度 (EG<sub>PGn</sub>): ${productAllValid ? productEg + '%' : ''} - <span class="${productEvaluation.rating.class}">${productEvaluation.rating.text}</span></p>
                        <div class="downgrade-reason">${productEvaluation.reasons.join('<br>')}</div><hr style="margin:15px 0;">`;
            });
            if (!productContentRendered) {
                pgContainer.innerHTML = "<p><i>所有定义的产品名称均为空，无法进行产品组评价。</i></p>";
            }
        }
    }


    function getRating(percentage) {
        if (percentage >= 90) return { text: "A", class: "rating-a" };
        if (percentage >= 80) return { text: "B", class: "rating-b" };
        if (percentage < 80 && percentage >=0) return { text: "C", class: "rating-c" };
        return { text: "无效", class: "rating-invalid" };
    }

    function renderActionPlan() {
        const tbody = document.getElementById('actionPlanTableBody');
        const placeholderRow = document.getElementById('actionPlanPlaceholderRow');
        tbody.innerHTML = ''; 
        let hasActionItems = false;
        let actionItemNo = 0;

        const processQuestionForAP = (qDef, pKey, processNameSuffix = '', processDisplayName = '') => {
            const qIdBase = qDef.id + processNameSuffix;
            const qIdDisplayText = qDef.id + (processDisplayName ? ` (${processDisplayName})` : '');

            const isP3P4Split = (pKey === 'P3' && auditData.header.selectedPElements['P3'] && auditData.header.p3ProductSelected) ||
                                (pKey === 'P4' && auditData.header.selectedPElements['P4'] && auditData.header.p4ProductSelected) ||
                                (pKey === 'P5' && auditData.header.selectedPElements['P5'] && auditData.header.p5ProductSelected);

            if (isP3P4Split) {
                ['product', 'process'].forEach(type => {
                    const fullQIdForAP = qIdBase + `_${type}`;
                    const score = auditData.scores[fullQIdForAP];
                    if (score !== undefined && score !== 'n.e.' && Number(score) < 10) {
                        actionItemNo++;
                        const auditRecordText = auditData.questionDetails[fullQIdForAP]?.manualRecord || '';
                        addActionPlanRow(fullQIdForAP, `${qIdDisplayText} (${type === 'product' ? '产品' : '过程'})`, auditRecordText, score, actionItemNo, qDef.isStar);
                        hasActionItems = true;
                    }
                });
            } else {
                const score = auditData.scores[qIdBase];
                if (score !== undefined && score !== 'n.e.' && Number(score) < 10) {
                    actionItemNo++;
                    const auditRecordText = auditData.questionDetails[qIdBase]?.manualRecord || '';
                    addActionPlanRow(qIdBase, qIdDisplayText, auditRecordText, score, actionItemNo, qDef.isStar);
                    hasActionItems = true;
                }
            }
        };

        ['P2','P3','P4','P5','P7'].forEach(pKey => {
            if(auditData.header.selectedPElements[pKey] && vdaQuestionsData[pKey] && vdaQuestionsData[pKey].questions) {
                vdaQuestionsData[pKey].questions.forEach(qDef => processQuestionForAP(qDef, pKey));
            }
        });

        if(auditData.header.selectedPElements['P6'] && p6Template && p6Template.questions) {
            auditData.header.processes.forEach(proc => {
                if (!proc.name.trim()) return;
                p6Template.questions.forEach(qDef => processQuestionForAP(qDef, 'P6', `_${proc.name}`, proc.name));
            });
        }

        if (!hasActionItems) {
             tbody.innerHTML = `<tr id="actionPlanPlaceholderRow"><td colspan="10" style="text-align:center; vertical-align: top; padding-top: 5px; color: #666;">评分后，得分非10分的提问将在此处列出。</td></tr>`;
        }
    }

    function addActionPlanRow(fullQId, qIdText, auditRecordText, score, itemNumber, isStar) {
        if (!auditData.actionPlanItems[fullQId]) {
            auditData.actionPlanItems[fullQId] = { finding: '', responsible: '', action: '', dueDate: '', status: 'open', verification: '' };
        }
        const item = auditData.actionPlanItems[fullQId];
        const tbody = document.getElementById('actionPlanTableBody');
        const placeholder = document.getElementById('actionPlanPlaceholderRow');
        if (tbody.rows.length === 1 && placeholder && tbody.rows[0].id === 'actionPlanPlaceholderRow') {
            tbody.innerHTML = '';
        }

        const tr = tbody.insertRow();
        tr.insertCell().textContent = itemNumber;
        tr.insertCell().textContent = qIdText + (isStar ? ' (*)' : '');
        tr.insertCell().textContent = score;
        tr.insertCell().innerHTML = `<textarea readonly style="min-height:30px; background-color:#f0f0f0; border:none; width:98%; padding:1%;">${auditRecordText}</textarea>`;
        tr.insertCell().innerHTML = `<textarea oninput="updateActionPlanItem('${fullQId}', 'finding', this.value)">${item.finding}</textarea>`;
        tr.insertCell().innerHTML = `<input type="text" value="${item.responsible}" oninput="updateActionPlanItem('${fullQId}', 'responsible', this.value)">`;
        tr.insertCell().innerHTML = `<textarea oninput="updateActionPlanItem('${fullQId}', 'action', this.value)">${item.action}</textarea>`;
        tr.insertCell().innerHTML = `<input type="date" value="${item.dueDate}" oninput="updateActionPlanItem('${fullQId}', 'dueDate', this.value)">`;
        tr.insertCell().innerHTML = `<select onchange="updateActionPlanItem('${fullQId}', 'status', this.value)">
                                        <option value="open" ${item.status === 'open' ? 'selected' : ''}>进行中</option>
                                        <option value="closed" ${item.status === 'closed' ? 'selected' : ''}>已关闭</option>
                                        <option value="verified" ${item.status === 'verified' ? 'selected' : ''}>已验证</option>
                                     </select>`;
        tr.insertCell().innerHTML = `<textarea oninput="updateActionPlanItem('${fullQId}', 'verification', this.value)">${item.verification}</textarea>`;
    }


    function updateActionPlanItem(qId, field, value) {
        if (!auditData.actionPlanItems[qId]) auditData.actionPlanItems[qId] = {};
        auditData.actionPlanItems[qId][field] = value;
        saveData(); // 保存数据
    }

    function addAuditToChecklist() {
        const reportNumber = document.getElementById('reportNumber').value;
        const auditDate = document.getElementById('auditDate').value;
        const overallRatingElement = document.getElementById('overallRating');
        const overallEvaluationTextRaw = overallRatingElement ? overallRatingElement.textContent : '待计算...';
        const overallEvaluationGradeMatch = overallEvaluationTextRaw.match(/(A|B|C|无效)/);
        const overallEvaluationGrade = overallEvaluationGradeMatch ? overallEvaluationGradeMatch[0] : '待计算...';

        if (!reportNumber || !auditDate || overallEvaluationGrade === '待计算...' || overallEvaluationGrade === '无效') {
            alert("请先填写报告编号、审核日期，并确保评价矩阵已计算出有效的总体评价等级。");
            return;
        }
        const auditDataToSave = JSON.parse(JSON.stringify(auditData));
        completedAudits.push({
            reportNumber: reportNumber,
            auditDate: auditDate,
            overallRating: overallEvaluationGrade,
            fullData: auditDataToSave
        });
        saveCompletedAuditsToLocalStorage();
        renderAuditChecklist();
        alert("当前审核概要已添加到清单。");
    }

    function renderAuditChecklist() {
        const tbody = document.getElementById('auditChecklistTableBody');
        tbody.innerHTML = '';
        if (!completedAudits || completedAudits.length === 0) {
             tbody.innerHTML = `<tr><td colspan="5" style="text-align:center; vertical-align: top; padding-top: 5px; color: #666;">点击“评价矩阵”页的“添加到清单”按钮添加审核记录。</td></tr>`;
             return;
        }
        completedAudits.forEach((audit, index) => {
            const tr = tbody.insertRow();
            tr.insertCell().textContent = index + 1;
            const reportNumberCell = tr.insertCell();
            reportNumberCell.innerHTML = `<a href="#" onclick="loadAuditFromChecklist(${index}); return false;">${audit.reportNumber}</a>`;
            tr.insertCell().textContent = audit.auditDate;
            let ratingClass = "rating-invalid";
            if (audit.overallRating === "A") ratingClass = "rating-a";
            else if (audit.overallRating === "B") ratingClass = "rating-b";
            else if (audit.overallRating === "C") ratingClass = "rating-c";
            tr.insertCell().innerHTML = `<span class="${ratingClass}" style="font-size:1.2em;">${audit.overallRating}</span>`;
            tr.insertCell().innerHTML = `<button type="button" class="delete-button small-button" onclick="deleteAuditFromChecklist(${index})">删除</button>`;
        });
    }

    function deleteAuditFromChecklist(index) {
        if (confirm(`确定要删除报告编号为 ${completedAudits[index].reportNumber} 的审核记录吗？`)) {
            completedAudits.splice(index, 1);
            saveCompletedAuditsToLocalStorage();
            renderAuditChecklist();
        }
    }

    function loadAuditFromChecklist(index) {
        const selectedAuditFullData = completedAudits[index].fullData;
        if (confirm(`确定要加载报告编号为 ${completedAudits[index].reportNumber} 的审核记录吗？ 这将覆盖当前数据。`)) {
            auditData = JSON.parse(JSON.stringify(selectedAuditFullData));
            if (!auditData.questionDetails) auditData.questionDetails = {}; 
            
            applyExperienceVersionLimits(auditData); // Apply limits after loading from checklist item

            initializeHeaderDataControls();
            renderQuestionnaire(); 
            renderEvaluationSummaryTable();
            renderEvaluationMatrix();
            renderActionPlan();
            showTab('headerData');
            alert(`已加载报告编号为 ${completedAudits[index].reportNumber} 的审核记录。`);
        }
    }

    function saveCompletedAuditsToLocalStorage() {
        try {
            localStorage.setItem(COMPLETED_AUDITS_KEY, JSON.stringify(completedAudits));
        } catch (e) {
            console.error("SAVE COMPLETED ERROR: Failed to save completed audits:", e);
        }
    }

    function loadCompletedAuditsFromLocalStorage() {
        const savedData = localStorage.getItem(COMPLETED_AUDITS_KEY);
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                if (Array.isArray(parsedData)) {
                    completedAudits = parsedData.map(audit => { 
                        if (audit.fullData && !audit.fullData.questionDetails) {
                            audit.fullData.questionDetails = {};
                        }
                        if (audit.fullData) { // Apply limits to each loaded completed audit
                            applyExperienceVersionLimits(audit.fullData);
                        }
                        return audit;
                    });
                } else { completedAudits = []; }
            } catch (e) { completedAudits = []; console.error("Error parsing completed audits:", e); }
        } else { completedAudits = []; }
    }

    function applyExperienceVersionLimits(dataObject) {
        if (!dataObject || !dataObject.header) return;

        const MAX_PRODUCTS = 3;
        const MAX_PROCESSES = 3;
        const MAX_AUDITORS = 2;

        if (dataObject.header.products && dataObject.header.products.length > MAX_PRODUCTS) {
            dataObject.header.products = dataObject.header.products.slice(0, MAX_PRODUCTS);
            console.warn(`产品数量已截断为最多 ${MAX_PRODUCTS} 个。`);
        }

        if (dataObject.processes && dataObject.processes.length > MAX_PROCESSES) {
            dataObject.processes = dataObject.processes.slice(0, MAX_PROCESSES);
            console.warn(`工序数量已截断为最多 ${MAX_PROCESSES} 个。`);
        }
        
        if (dataObject.header.processes && dataObject.header.products) {
            dataObject.header.processes.forEach(proc => {
                if (proc.products && proc.products.length > dataObject.header.products.length) {
                    proc.products = proc.products.slice(0, dataObject.header.products.length);
                }
            });
        }
        
        if (dataObject.header.auditors && dataObject.header.auditors.length > MAX_AUDITORS) {
            dataObject.header.auditors = dataObject.header.auditors.slice(0, MAX_AUDITORS);
            console.warn(`审核员数量已截断为最多 ${MAX_AUDITORS} 个。`);
        }
    }


    function saveData() {
        updateQuestionnaireAndMatrix(); // This now calls applyExperienceVersionLimits internally
        try {
            localStorage.setItem(CURRENT_AUDIT_DATA_KEY, JSON.stringify(auditData));
            alert('数据已成功保存！');
        } catch (e) {
            console.error("SAVE CURRENT AUDIT ERROR: ", e);
            alert('保存当前审核数据失败! 可能存储已满或浏览器设置问题。');
        }
    }

    function loadData() {
        const savedAuditData = localStorage.getItem(CURRENT_AUDIT_DATA_KEY);
        let dataLoadedSuccessfully = false;
        if (savedAuditData) {
            try {
                const parsedData = JSON.parse(savedAuditData);
                if (parsedData && typeof parsedData.header === 'object' && typeof parsedData.scores === 'object' && typeof parsedData.actionPlanItems === 'object') {
                    auditData = parsedData;
                    
                    // Ensure default structures if missing after load (especially for older saved data)
                    if (!auditData.header.processes) auditData.header.processes = [{ name: "工序1", products: (auditData.header.products || [{name:"产品A"}]).map(()=>false) }];
                    if (!auditData.header.products) auditData.header.products = [{ name: "产品A" }];
                    if (!auditData.header.auditors) auditData.header.auditors = [];
                    if (!auditData.header.selectedPElements) {
                         auditData.header.selectedPElements = { P2: false, P3: false, P4: false, P5: false, P6: false, P7: false };
                         auditData.header.p3ProductSelected = false;
                         auditData.header.p4ProductSelected = false;
                    }
                    if (!auditData.questionDetails) auditData.questionDetails = {}; 

                    applyExperienceVersionLimits(auditData); // Apply limits AFTER loading
                    dataLoadedSuccessfully = true;
                }
            } catch (e) { console.error("LOAD CURRENT AUDIT ERROR:", e); }
        }

        if (dataLoadedSuccessfully) {
            alert('数据已成功加载！');
            initializeHeaderDataControls(); // This will use the (potentially) limited data
            renderQuestionnaire();
            renderEvaluationSummaryTable();
            renderEvaluationMatrix();
            renderActionPlan();
        } else {
            alert('加载数据失败！没有找到保存的数据或数据已损坏。');
            initializeDefaultAuditDataAndRenderUIOnly(); // Defaults should be compliant
        }
        return dataLoadedSuccessfully;
    }

    function initializeDefaultAuditDataAndRenderUIOnly() {
        // Default data is already compliant with 3x3 matrix and 2 auditors
        auditData = {
            header: {
                companyName: "", auditDate: "", auditLocation: "", reportNumber: "", auditLanguage: "中文",
                processes: [{ name: "工序1", products: [false] }], 
                products: [{ name: "产品A" }], 
                auditors: [], 
                selectedPElements: { P2: false, P3: false, P4: false, P5: false, P6: false, P7: false },
                p3ProductSelected: false, p4ProductSelected: false
            },
            scores: {},
            questionDetails: {},
            actionPlanItems: {}
        };
        // No need to call applyExperienceVersionLimits here as defaults are compliant
        initializeHeaderDataControls();
        renderQuestionnaire();
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();
    }

    
    // Function to add a new row to the Audit Plan table
    function addAuditPlanRow() {
        const tbody = document.getElementById('auditPlanTableBody');
        // 移除提示行（如果存在）
        const noAuditPlanRow = document.getElementById('noAuditPlanRow');
        if (noAuditPlanRow) {
            noAuditPlanRow.remove(); // 使用.remove()方法更简洁
        }
        const newRow = tbody.insertRow();



        // Cell 0: 操作 (删除按钮)
        const actionCell = newRow.insertCell(0);
        actionCell.innerHTML = '<button type="button" class="delete-button" onclick="deleteAuditPlanRow(this)">删除</button>';

        // Cell 1: 报告编号
        const reportNumberCell = newRow.insertCell(1);
        reportNumberCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()" class="report-number-input">';

        // Cell 2: 审核开始日期
        const dateFromCell = newRow.insertCell(2);
        dateFromCell.innerHTML = '<input type="date" value="" onchange="updateAuditPlanData(); generateReportNumber(this);">';

        // Cell 3: 审核结束日期
        const dateToCell = newRow.insertCell(3);
        dateToCell.innerHTML = '<input type="date" value="" onchange="updateAuditPlanData()">';

        // Cell 4: 审核类别
        const categoryCell = newRow.insertCell(4);
        categoryCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()">'; // Or a select dropdown if categories are fixed

        // Cell 5: 审核目的
        const purposeCell = newRow.insertCell(5);
        purposeCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()">';

        // Cell 6: 审核范围
        const scopeCell = newRow.insertCell(6);
        scopeCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()">';

        // Cell 7: 过程要素 (Checkboxes)
        const pElementsCell = newRow.insertCell(7);
        pElementsCell.innerHTML = `
            <div class="checkbox-group">
                <label><input type="checkbox" value="P2" onchange="updateAuditPlanData()"> P2</label>
                <label><input type="checkbox" value="P3" onchange="updateAuditPlanData()"> P3</label>
                <label><input type="checkbox" value="P4" onchange="updateAuditPlanData()"> P4</label>
                <label><input type="checkbox" value="P5" onchange="updateAuditPlanData()"> P5</label>
                <label><input type="checkbox" value="P6" onchange="updateAuditPlanData()"> P6</label>
                <label><input type="checkbox" value="P7" onchange="updateAuditPlanData()"> P7</label>
            </div>`;

        // Cell 8: 审核组长
        const leadAuditorCell = newRow.insertCell(8);
        leadAuditorCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()">';

        // Cell 9: 审核员
        const auditorsCell = newRow.insertCell(9);
        auditorsCell.innerHTML = '<input type="text" value="" oninput="updateAuditPlanData()">';

        // Cell 10: 备注
        const notesCell = newRow.insertCell(10);
        notesCell.innerHTML = '<textarea oninput="updateAuditPlanData()"></textarea>';





        // Add a placeholder data structure for this row to auditData
        auditData.auditPlans.push({
            reportNumber: '',
            dateFrom: '',
            dateTo: '',
            category: '',
            purpose: '',
            scope: '',
            pElements: { P2: false, P3: false, P4: false, P5: false, P6: false, P7: false },
            leadAuditor: '',
            auditors: '',
            notes: ''
        });

        // Save data after adding a row
        // saveData(); // 移除频繁保存，改为手动保存或在特定事件触发
    }

    function generateReportNumber(dateInput) {
        const dateValue = dateInput.value;
        if (dateValue) {
            const dateObj = new Date(dateValue);
            const year = dateObj.getFullYear();
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
            // Find the report number input in the same row
            const row = dateInput.closest('tr');
            const reportNumberInput = row.querySelector('.report-number-input');

            if (!reportNumberInput) {
                alert('Error: Report number input not found in row!');
                return;
            }

            // Generate report number based on year, month, and sequential number
            // 暂时不更新localStorage，只生成临时编号
            // 实际的报告编号生成和保存将在审核计划保存时进行
            const yearMonth = `${year}${month}`;
            // 获取当前月份已有的报告编号，并找到最大的序列号
            let maxSequence = 0;
            
            // 1. 检查已保存的审核记录
            completedAudits.forEach(audit => {
                if (audit.reportNumber && audit.reportNumber.startsWith(yearMonth)) {
                    const sequence = parseInt(audit.reportNumber.substring(6));
                    if (!isNaN(sequence) && sequence > maxSequence) {
                        maxSequence = sequence;
                    }
                }
            });

            // 2. 检查当前表格中已有的报告编号 (auditData.auditPlans)
            // 这涵盖了新添加的行，这些行可能尚未保存到 completedAudits 中
            auditData.auditPlans.forEach(plan => {
                if (plan.reportNumber && plan.reportNumber.startsWith(yearMonth)) {
                    const sequence = parseInt(plan.reportNumber.substring(6));
                    if (!isNaN(sequence) && sequence > maxSequence) {
                        maxSequence = sequence;
                    }
                }
            });

            const nextSequence = maxSequence + 1;
            const newReportNo = `${yearMonth}${nextSequence.toString().padStart(3, '0')}`;
            reportNumberInput.value = newReportNo;
            
            // Update auditData.auditPlans with the new report number
            const rowIndex = row.rowIndex - 1; // Adjust for tbody index
            if (auditData.auditPlans[rowIndex]) {
                auditData.auditPlans[rowIndex].reportNumber = newReportNo;
            }
            // saveData(); // 移除自动保存，由用户手动点击保存按钮触发
        } else {
            // If date is cleared, clear report number
            const row = dateInput.closest('tr');
            const reportNumberInput = row.querySelector('.report-number-input');
            if (reportNumberInput) {
                reportNumberInput.value = '';
                const rowIndex = row.rowIndex - 1;
                if (auditData.auditPlans[rowIndex]) {
                    auditData.auditPlans[rowIndex].reportNumber = '';
                }
                // saveData(); // 移除自动保存，由用户手动点击保存按钮触发
            }
        }
    }

    // 新增函数：根据审核计划同步基本信息页的P要素选择
    function syncPElementsWithBasicInfo() {
        const basicInfoPElements = document.querySelectorAll('#headerData .p-element-selection-group input[name="pElement"]');
        const p3ProductCheckbox = document.getElementById('p3ProductSelected');
        const p4ProductCheckbox = document.getElementById('p4ProductSelected');

        // 重置基本信息页的P要素选择状态
        basicInfoPElements.forEach(checkbox => {
            checkbox.checked = false;
        });
        if (p3ProductCheckbox) p3ProductCheckbox.checked = false;
        if (p4ProductCheckbox) p4ProductCheckbox.checked = false;

        // 根据所有审核计划的P要素选择来更新基本信息页
        auditData.auditPlans.forEach(plan => {
            for (const pElement in plan.pElements) {
                if (plan.pElements[pElement]) {
                    const basicInfoCheckbox = document.querySelector(`#headerData .p-element-selection-group input[value="${pElement}"]`);
                    if (basicInfoCheckbox) {
                        basicInfoCheckbox.checked = true;
                    }
                }
            }
            // 同步P3/P4的产品复选框
            if (plan.pElements['P3'] && plan.p3ProductSelected && p3ProductCheckbox) {
                p3ProductCheckbox.checked = true;
            }
            if (plan.pElements['P4'] && plan.p4ProductSelected && p4ProductCheckbox) {
                p4ProductCheckbox.checked = true;
            }
        });

        // 触发基本信息页P要素的onchange事件，以更新相关UI（如P3/P4的产品复选框显示）
        basicInfoPElements.forEach(checkbox => {
            // 只有当checkbox被选中时才触发onchange，避免不必要的UI更新
            if (checkbox.checked) {
                // 手动调用handlePElementSelectionChange，并传入当前checkbox
                handlePElementSelectionChange(checkbox);
            }
        });

        // 确保P3/P4的产品复选框容器的显示状态正确
        const p3Container = document.getElementById('p3ProductCheckboxContainer');
        const p4Container = document.getElementById('p4ProductCheckboxContainer');
        if (p3Container) p3Container.style.display = document.querySelector('#headerData .p-element-selection-group input[value="P3"]').checked ? 'inline-block' : 'none';
        if (p4Container) p4Container.style.display = document.querySelector('#headerData .p-element-selection-group input[value="P4"]').checked ? 'inline-block' : 'none';

        // 同步报告编号到基本信息页
        if (auditData.auditPlans && auditData.auditPlans.length > 0) {
            const firstPlan = auditData.auditPlans[0];
            const reportNumberInput = document.getElementById('reportNumber');
            if (reportNumberInput && firstPlan.reportNumber) {
                reportNumberInput.value = firstPlan.reportNumber;
            }
        }

        // 同步审核日期到基本信息页
        // 同步审核日期到基本信息页
        if (auditData.auditPlans && auditData.auditPlans.length > 0) {
            const auditDateInput = document.getElementById('auditDate');
            const firstPlan = auditData.auditPlans[0];
            console.log('Debug - firstPlan:', firstPlan); // 调试日志
            console.log('Debug - dateFrom:', firstPlan.dateFrom, 'dateTo:', firstPlan.dateTo); // 调试日志
            if (auditDateInput) {
                let dateRange = '';
                if (firstPlan.dateFrom) {
                    dateRange += firstPlan.dateFrom;
                }
                if (firstPlan.dateTo) {
                    if (dateRange) dateRange += ' ~ ';
                    dateRange += firstPlan.dateTo;
                }
                console.log('Debug - dateRange:', dateRange); // 调试日志
                auditDateInput.value = dateRange;
            }
        }

        // 检查是否有审核计划或过程要素被选中
        const hasAuditPlans = auditData.auditPlans && auditData.auditPlans.length > 0;
        const hasSelectedPElements = hasAuditPlans && auditData.auditPlans.some(plan => {
            return Object.values(plan.pElements).some(isSelected => isSelected);
        });

        let messageContainer = document.getElementById('syncMessageContainer');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'syncMessageContainer';
            messageContainer.style.cssText = 'display: inline-block; margin-left: 10px; font-size: 1em;'; // 初始不设置颜色，根据内容动态设置
            const syncButton = document.querySelector('#auditPlan .add-button[onclick="syncPElementsWithBasicInfo()"]');
            if (syncButton && syncButton.parentNode) {
                syncButton.parentNode.insertBefore(messageContainer, syncButton.nextSibling);
            }
        }

        if (!hasAuditPlans) {
            messageContainer.innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: orange;">请先添加审核计划。</span>';
            messageContainer.style.color = ''; // 清除之前的颜色设置
            messageContainer.style.opacity = '1';
            setTimeout(() => {
                messageContainer.style.opacity = '0';
            }, 5000); // 5秒后消失
            return; // 不执行后续的同步逻辑
        } else if (!hasSelectedPElements) {
            messageContainer.innerHTML = '<span style="color: red;">温馨提醒：</span><span style="color: orange;">请选择要评价的过程要素。</span>';
            messageContainer.style.color = ''; // 清除之前的颜色设置
            messageContainer.style.opacity = '1';
            setTimeout(() => {
                messageContainer.style.opacity = '0';
            }, 5000); // 5秒后消失
            return; // 不执行后续的同步逻辑
        } else {
            messageContainer.innerHTML = '<span style="color: orange;">温馨提醒：</span>基本信息已经根据您的选择更新。';
            messageContainer.style.color = 'green'; // 成功提示为绿色
            messageContainer.style.opacity = '1';
            setTimeout(() => {
                messageContainer.style.opacity = '0';
            }, 5000); // 5秒后消失
        }

    }

    // Function to delete a row from the Audit Plan table
    function deleteAuditPlanRow(button) {
        const row = button.parentNode.parentNode;
        const tbody = row.parentNode;
        const rowIndex = row.rowIndex - 1; // Adjust for the header row

        // 确保在删除前进行确认
        if (confirm('确定要删除此审核计划记录吗？')) {
            tbody.removeChild(row);
            auditData.auditPlans.splice(rowIndex, 1); // 从数据数组中移除对应的审核计划
            // 如果所有行都被删除，显示提示信息
            if (tbody.rows.length === 0) {
                    const noAuditPlanRow = document.getElementById('noAuditPlanRow');
                    if (!noAuditPlanRow) {
                        const newRow = tbody.insertRow();
                        newRow.id = 'noAuditPlanRow';
                        const cell = newRow.insertCell(0);
                        cell.colSpan = 12;
                        cell.style.textAlign = 'center';
                        cell.style.color = '#888';
                        cell.innerText = '点击上方“+添加审核计划”按钮新增审核计划';
                        tbody.appendChild(newRow);
                    }
                }
                deleteConfirmPrompt.style.display = 'none';
            };

        }
        

    // Function to update auditData when input fields change
    function updateAuditPlanData() {
        const tbody = document.getElementById('auditPlanTableBody');
        // 如果auditData.auditPlans不为空，则移除提示行（如果存在）
        if (auditData.auditPlans && auditData.auditPlans.length > 0) {
            const noAuditPlanRow = document.getElementById('noAuditPlanRow');
            if (noAuditPlanRow) {
                noAuditPlanRow.remove(); // 使用.remove()方法更简洁
            }
        }
        auditData.auditPlans = []; // Clear existing data

        for (let i = 0; i < tbody.rows.length; i++) {
            const row = tbody.rows[i];
            const pElementsCheckboxes = row.cells[7].querySelectorAll('input[type="checkbox"]');
            const selectedPElements = {};
            pElementsCheckboxes.forEach(checkbox => {
                selectedPElements[checkbox.value] = checkbox.checked;
            });

            auditData.auditPlans.push({
                reportNumber: row.cells[1].querySelector('input').value,
                dateFrom: row.cells[2].querySelector('input').value,
                dateTo: row.cells[3].querySelector('input').value,
                category: row.cells[4].querySelector('input').value,
                purpose: row.cells[5].querySelector('input').value,
                scope: row.cells[6].querySelector('input').value,
                pElements: selectedPElements,
                leadAuditor: row.cells[8].querySelector('input').value,
                auditors: row.cells[9].querySelector('input').value,
                notes: row.cells[10].querySelector('textarea').value
            });
        }
        // Save data after updating
        // saveData(); // 移除频繁保存，改为手动保存或在特定事件触发
    }

    // Function to render the audit plan table from auditData
    function renderAuditPlan() {
        const tbody = document.getElementById('auditPlanTableBody');
        tbody.innerHTML = ''; // Clear existing rows

        if (!auditData.auditPlans || auditData.auditPlans.length === 0) {
            // 如果没有审核计划，确保提示行存在
            let noAuditPlanRow = document.getElementById('noAuditPlanRow');
            if (!noAuditPlanRow) {
                const newRow = tbody.insertRow();
                newRow.id = 'noAuditPlanRow';
                const cell = newRow.insertCell(0);
                cell.colSpan = 11;
                cell.style.textAlign = 'center';
                cell.style.color = '#888';
                cell.innerText = '点击上方“+添加审核计划”按钮新增审核计划';
            }
            return;
        }

        auditData.auditPlans.forEach((plan, index) => {
            const newRow = tbody.insertRow();



            // Cell 0: 操作 (Delete button)
            const deleteCell = newRow.insertCell(0);
            deleteCell.innerHTML = '<button type="button" class="delete-button small-button" onclick="deleteAuditPlanRow(this)">删除</button>';

            // Cell 1: 报告编号
            const reportNumberCell = newRow.insertCell(1);
            reportNumberCell.innerHTML = `<input type="text" value="${plan.reportNumber}" oninput="updateAuditPlanData()">`;

            // Cell 2: 审核开始日期
            const dateFromCell = newRow.insertCell(2);
            dateFromCell.innerHTML = `<input type="date" value="${plan.dateFrom}" onchange="updateAuditPlanData()">`;

            // Cell 3: 审核结束日期
            const dateToCell = newRow.insertCell(3);
            dateToCell.innerHTML = `<input type="date" value="${plan.dateTo}" onchange="updateAuditPlanData()">`;

            // Cell 4: 审核类别
            const categoryCell = newRow.insertCell(4);
            categoryCell.innerHTML = `<input type="text" value="${plan.category}" oninput="updateAuditPlanData()">`;

            // Cell 5: 审核目的
            const purposeCell = newRow.insertCell(5);
            purposeCell.innerHTML = `<input type="text" value="${plan.purpose}" oninput="updateAuditPlanData()">`;

            // Cell 6: 审核范围
            const scopeCell = newRow.insertCell(6);
            scopeCell.innerHTML = `<input type="text" value="${plan.scope}" oninput="updateAuditPlanData()">`;

            // Cell 7: 过程要素 (Checkboxes)
            const pElementsCell = newRow.insertCell(7);
            let pElementsHtml = '<div class="checkbox-group">';
            ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].forEach(pKey => {
                const checked = plan.pElements && plan.pElements[pKey] ? 'checked' : '';
                pElementsHtml += `<label><input type="checkbox" value="${pKey}" ${checked} onchange="updateAuditPlanData()"> ${pKey}</label>`;
            });
            pElementsHtml += '</div>';
            pElementsCell.innerHTML = pElementsHtml;

            // Cell 8: 审核组长
            const leadAuditorCell = newRow.insertCell(8);
            leadAuditorCell.innerHTML = `<input type="text" value="${plan.leadAuditor}" oninput="updateAuditPlanData()">`;

            // Cell 9: 审核员
            const auditorsCell = newRow.insertCell(9);
            auditorsCell.innerHTML = `<input type="text" value="${plan.auditors}" oninput="updateAuditPlanData()">`;

            // Cell 10: 备注
            const notesCell = newRow.insertCell(10);
            notesCell.innerHTML = `<textarea oninput="updateAuditPlanData()">${plan.notes}</textarea>`;


        });
    }

    // Modify showTab function to render audit plan when its tab is active


    // Modify loadData to handle auditPlans
    function loadData() {
        const savedAuditData = localStorage.getItem(CURRENT_AUDIT_DATA_KEY);
        let dataLoadedSuccessfully = false;
        if (savedAuditData) {
            try {
                const parsedData = JSON.parse(savedAuditData);
                if (parsedData && typeof parsedData.header === 'object' && typeof parsedData.scores === 'object' && typeof parsedData.actionPlanItems === 'object') {
                    auditData = parsedData;

                    // Ensure default structures if missing after load (especially for older saved data)
                    if (!auditData.header.processes) auditData.header.processes = [{ name: "工序1", products: (auditData.header.products || [{name:"产品A"}]).map(()=>false) }];
                    if (!auditData.header.products) auditData.header.products = [{ name: "产品A" }];
                    if (!auditData.header.auditors) auditData.header.auditors = [];
                    if (!auditData.header.selectedPElements) {
                         auditData.header.selectedPElements = { P2: false, P3: false, P4: false, P5: false, P6: false, P7: false };
                         auditData.header.p3ProductSelected = false;
                         auditData.header.p4ProductSelected = false;
                    }
                    if (!auditData.questionDetails) auditData.questionDetails = {};
                    if (!auditData.auditPlans) auditData.auditPlans = []; // Ensure auditPlans exists after load

                    applyExperienceVersionLimits(auditData); // Apply limits AFTER loading
                    dataLoadedSuccessfully = true;
                }
            } catch (e) { console.error("LOAD CURRENT AUDIT ERROR:", e); }
        }

        if (dataLoadedSuccessfully) {
            initializeHeaderDataControls(); // This will use the (potentially) limited data
            renderQuestionnaire();
            renderEvaluationSummaryTable();
            renderEvaluationMatrix();
            renderActionPlan();
            renderAuditPlan(); // Render audit plan after loading
        } else {
            initializeDefaultAuditDataAndRenderUIOnly(); // Defaults should be compliant
        }
        return dataLoadedSuccessfully;
    }

    // Add auditPlans array to the default auditData structure
    function initializeDefaultAuditDataAndRenderUIOnly() {
        // Default data is already compliant with 3x3 matrix and 2 auditors
        auditData = {
            header: {
                companyName: "", auditDate: "", auditLocation: "", reportNumber: "", auditLanguage: "中文",
                processes: [{ name: "工序1", products: [false] }],
                products: [{ name: "产品A" }],
                auditors: [],
                selectedPElements: { P2: false, P3: false, P4: false, P5: false, P6: false, P7: false },
                p3ProductSelected: false, p4ProductSelected: false
            },
            scores: {},
            questionDetails: {},
            actionPlanItems: {},
            auditPlans: [] // Add auditPlans array here
        };
        // No need to call applyExperienceVersionLimits here as defaults are compliant
        initializeHeaderDataControls();
        renderQuestionnaire();
        renderEvaluationSummaryTable();
        renderEvaluationMatrix();
        renderActionPlan();
        renderAuditPlan(); // Render audit plan on initialization
    }

    // Modify exportToMarkdown to include Audit Plan data
    function exportToMarkdown() {
        let mdContent = `# VDA 6.3 审核报告 - ${auditData.header.companyName || 'N/A'} \n\n`;
        mdContent += `## 1. 审核基本信息\n`;
        mdContent += `- **公司名称:** ${auditData.header.companyName || ''}\n`;
        mdContent += `- **审核日期:** ${auditData.header.auditDate || ''}\n`;
        mdContent += `- **审核地点:** ${auditData.header.auditLocation || ''}\n`;
        mdContent += `- **报告编号:** ${auditData.header.reportNumber || ''}\n`;
        mdContent += `- **审核标准:** ${document.getElementById('auditStandard').value}\n`;
        mdContent += `- **审核语言:** ${auditData.header.auditLanguage || ''}\n\n`;

        mdContent += `### 工序/产品矩阵 (最多5工序 x 5产品)\n`;
        if (auditData.header.processes && auditData.header.products) {
            auditData.header.processes.forEach(proc => {
                mdContent += `- **工序: ${proc.name}**\n`;
                (proc.products || []).forEach((linked, prodIdx) => {
                    if (linked && auditData.header.products[prodIdx]) mdContent += `  - 相关产品: ${auditData.header.products[prodIdx].name}\n`;
                });
            });
        }
        mdContent += `\n`;

        mdContent += `### 审核员信息 (最多5名)\n`;
        (auditData.header.auditors || []).forEach((aud, idx) => {
            mdContent += `- **审核员 ${idx+1}:** ${aud.name} (${aud.type}), 邮箱: ${aud.email}, 电话: ${aud.phone}\n`;
        });
        mdContent += `\n`;

        mdContent += `## 2. 提问表与评分\n`;
        for (const pKey of ['P2', 'P3', 'P4', 'P5', 'P6', 'P7']) {
            if (!auditData.header.selectedPElements[pKey]) continue;
            const pElementData = (pKey === 'P6') ? p6Template : vdaQuestionsData[pKey];
            if (!pElementData || !pElementData.questions) continue;

            mdContent += `### ${pElementData.name}\n`;

            const processPElement = (qDef, processSuffix = '', displayProcessName = '') => {
                const qIdBase = qDef.id + processSuffix;
                const qIdDisplay = qDef.id + (displayProcessName ? ` (${displayProcessName})` : '');
                const isSplit = (pKey === 'P3' && auditData.header.p3ProductSelected) || (pKey === 'P4' && auditData.header.p4ProductSelected && qDef.id !== 'P4.7');

                if (isSplit) {
                    ['product', 'process'].forEach(type => {
                        const fullId = qIdBase + `_${type}`;
                        const score = auditData.scores[fullId];
                        const details = auditData.questionDetails[fullId] || {};
                        if (score !== undefined && score !== '') {
                             mdContent += `- **${qIdDisplay} (${type === 'product' ? '产品' : '过程'})**: ${qDef.text.substring(0, Math.min(qDef.text.length, 50))}...\n`;
                             mdContent += `  - 评分: ${score}\n`;
                             if(details.manualRecord) mdContent += `  - 审核记录: ${details.manualRecord}\n`;
                             if(details.attachmentName) mdContent += `  - 附件: ${details.attachmentName}\n`;
                        }
                    });
                } else {
                    const score = auditData.scores[qIdBase];
                    const details = auditData.questionDetails[qIdBase] || {};
                     if (score !== undefined && score !== '') {
                        mdContent += `- **${qIdDisplay}**: ${qDef.text.substring(0, Math.min(qDef.text.length, 50))}...\n`;
                        mdContent += `  - 评分: ${score}\n`;
                        if(details.manualRecord) mdContent += `  - 审核记录: ${details.manualRecord}\n`;
                        if(details.attachmentName) mdContent += `  - 附件: ${details.attachmentName}\n`;
                    }
                }
            };

            if (pKey === 'P6') {
                auditData.header.processes.forEach(process => {
                    if (!process.name.trim()) return;
                    mdContent += `#### 工序: ${process.name}\n`;
                    pElementData.questions.forEach(qDef => processPElement(qDef, `_${process.name}`, process.name));
                });
            } else {
                 pElementData.questions.forEach(qDef => processPElement(qDef));
            }
            mdContent += `\n`;
        }
        mdContent += `\n`;

        mdContent += `## 3. 评价矩阵总结\n`;
        mdContent += `- **总体评价 (EG):** ${document.getElementById('overallRating')?.textContent || 'N/A'}\n`;
        mdContent += `- **降级原因:** ${document.getElementById('overallDowngradeReason')?.innerHTML.replace(/<br>/g, '; ') || '无'}\n\n`;

        mdContent += `### 各过程要素评价 (EPn)\n`;
        const epnRows = document.getElementById('epnTable')?.querySelectorAll('tbody tr');
        if (epnRows) epnRows.forEach(row => {
            mdContent += `- ${row.cells[0].textContent}: ${row.cells[1].textContent} - ${row.cells[2].textContent} ${row.cells[3].textContent ? '('+row.cells[3].textContent+')' : ''}\n`;
        });
        mdContent += `\n`;

        mdContent += `### 产品组评价\n`;
        const pgDiv = document.getElementById('productGroupEvaluationContainer');
        if(pgDiv && auditData.header.products.length > 0 && auditData.header.products.some(p => p.name && p.name.trim() !== '')){
             const productHeaders = pgDiv.querySelectorAll('h4');
             productHeaders.forEach(h4 => {
                const productName = h4.textContent.replace('产品组: ','').trim();
                if (productName) {
                    const pElement = h4.nextElementSibling;
                    const reasonDiv = pElement.nextElementSibling;
                    mdContent += `- **产品组: ${productName}** ${pElement.textContent}\n`;
                    if(reasonDiv && reasonDiv.classList.contains('downgrade-reason') && reasonDiv.innerHTML.trim() !== ''){
                        mdContent += `  - 降级原因: ${reasonDiv.innerHTML.replace(/<br>/g, '; ')}\n`;
                    }
                }
             });
        } else {
             mdContent += `- 未进行产品组评价或未定义有效的产品名称。\n`;
        }
        mdContent += `\n`;

         mdContent += `## 4. 审核清单\n`;
        if (completedAudits.length > 0) {
            mdContent += "| No. | 报告编号 | 审核日期 | 总体评价等级 |\n";
            mdContent += "|---|---|---|---|\n";
            completedAudits.forEach((audit, index) => {
                mdContent += `| ${index + 1} | ${audit.reportNumber} | ${audit.auditDate} | ${audit.overallRating.replace(/<[^>]*>?/gm, '')} |\n`;
            });
        } else {
            mdContent += "无已添加的审核记录。\n";
        }
        mdContent += `\n`;

        mdContent += `## 5. 措施计划\n`;
        mdContent += "| No. | 提问编号 | 分值 | 审核记录 | 发现/不符合项 | 负责人 | 措施 | 计划完成日期 | 状态 | 验证有效性 |\n";
        mdContent += "|---|---|---|---|---|---|---|---|---|---|\n";
        const apRows = document.getElementById('actionPlanTableBody')?.querySelectorAll('tr');
        if (apRows) apRows.forEach(row => {
            if (row.cells.length < 10 || (row.cells[0].colSpan && row.cells[0].colSpan === 10) ) return;
            mdContent += `| ${row.cells[0].textContent} ` +
                         `| ${row.cells[1].textContent} ` +
                         `| ${row.cells[2].textContent} ` +
                         `| ${row.cells[3].querySelector('textarea')?.value || row.cells[3].textContent} ` +
                         `| ${row.cells[4].querySelector('textarea')?.value || ''} ` +
                         `| ${row.cells[5].querySelector('input')?.value || ''} ` +
                         `| ${row.cells[6].querySelector('textarea')?.value || ''} ` +
                         `| ${row.cells[7].querySelector('input')?.value || ''} ` +
                         `| ${row.cells[8].querySelector('select')?.value || ''} ` +
                         `| ${row.cells[9].querySelector('textarea')?.value || ''} |\n`;
        });
        const blob = new Blob([mdContent], { type: 'text/markdown;charset=utf-8,' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `VDA6.3审核报告-${auditData.header.companyName || '未命名'}.md`;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    function clearData() {
        if (confirm('确定要清除所有当前审核数据吗？此操作不可逆！')) {
            localStorage.removeItem(CURRENT_AUDIT_DATA_KEY);
            initializeDefaultAuditDataAndRenderUIOnly(); // Reset UI and data to default
            alert('当前审核数据已清除。');
        }
    }

    function exportToExcel() {
        let csvRows = [];
        const universalBOM = "\uFEFF";
        csvRows.push(["VDA 6.3 审核报告"]);
        csvRows.push(["公司名称", auditData.header.companyName]);
        csvRows.push(["审核日期", auditData.header.auditDate]);
        csvRows.push(["审核地点", auditData.header.auditLocation]);
        csvRows.push(["报告编号", auditData.header.reportNumber]);
        csvRows.push([]);

        csvRows.push(["工序 / 产品矩阵 (最多5工序 x 5产品)"]);
        let headerProductMatrix = ["工序"];
        auditData.header.products.forEach(p => headerProductMatrix.push(p.name));
        csvRows.push(headerProductMatrix);
        auditData.header.processes.forEach(proc => {
            let row = [proc.name];
            (proc.products || []).forEach(linked => row.push(linked ? "X" : ""));
            csvRows.push(row);
        });
        csvRows.push([]);

        csvRows.push(["提问表与评分"]);
        csvRows.push(["过程要素", "提问编号", "星号", "问题描述", "产品评分", "过程评分", "最终得分", "审核记录(产品/单一)", "附件(产品/单一)", "审核记录(过程)", "附件(过程)"]);

         ['P2', 'P3', 'P4', 'P5', 'P6', 'P7'].forEach(pKey => {
            if (auditData.header.selectedPElements[pKey]) {
                const pElementData = (pKey === 'P6') ? p6Template : vdaQuestionsData[pKey];
                const questions = pElementData.questions;

                if (pKey === 'P6') {
                     auditData.header.processes.forEach(process => {
                         if (!process.name.trim()) return;
                        csvRows.push([`${pElementData.name} (${process.name})`]);
                        questions.forEach(q => {
                            const qIdBase = q.id + `_${process.name}`;
                            const { score, isNE, isScored } = getQuestionScore(qIdBase, pKey, q);
                            const details = auditData.questionDetails[qIdBase] || {};
                            csvRows.push(["", q.id, q.isStar ? "*" : "", q.text, "", "", isScored ? score.toFixed(2) : (isNE ? "n.e." : ""), details.manualRecord || "", details.attachmentName || "", "", ""]);
                        });
                    });
                } else {
                    if (vdaQuestionsData[pKey]) {
                        csvRows.push([pElementData.name]);
                        questions.forEach(q => {
                            const qIdBase = q.id;
                            const isSplit = (pKey === 'P3' && auditData.header.selectedPElements['P3'] && auditData.header.p3ProductSelected) ||
                                            (pKey === 'P4' && auditData.header.selectedPElements['P4'] && auditData.header.p4ProductSelected && q.id !== 'P4.7');
                            const res = getQuestionScore(qIdBase, pKey, q);
                            let prodScoreText = "", procScoreText = "", totalScoreText = "";
                            let prodManualRec = "", prodAttachName = "", procManualRec = "", procAttachName = "";

                            if(isSplit){
                                const sProd = auditData.scores[qIdBase + '_product'];
                                const sProc = auditData.scores[qIdBase + '_process'];
                                prodScoreText = sProd !== undefined && sProd !== '' ? String(sProd) : "";
                                procScoreText = sProc !== undefined && sProc !== '' ? String(sProc) : "";
                                const prodDetails = auditData.questionDetails[qIdBase + '_product'] || {};
                                const procDetails = auditData.questionDetails[qIdBase + '_process'] || {};
                                prodManualRec = prodDetails.manualRecord || "";
                                prodAttachName = prodDetails.attachmentName || "";
                                procManualRec = procDetails.manualRecord || "";
                                procAttachName = procDetails.attachmentName || "";
                            } else {
                                const details = auditData.questionDetails[qIdBase] || {};
                                prodManualRec = details.manualRecord || ""; 
                                prodAttachName = details.attachmentName || "";
                            }
                            totalScoreText = res.isScored ? res.score.toFixed(2) : (res.isNE ? "n.e." : "");
                            csvRows.push(["", q.id, q.isStar ? "*" : "", q.text, prodScoreText, isSplit ? procScoreText : "", totalScoreText, prodManualRec, prodAttachName, procManualRec, procAttachName]);
                        });
                    }
                }
            }
        });
        csvRows.push([]);

        csvRows.push(["评价总结"]);
        csvRows.push(["总体评价 (EG)", document.getElementById('overallRating')?.textContent || 'N/A']);
        csvRows.push(["降级原因", document.getElementById('overallDowngradeReason')?.innerHTML.replace(/<br>/g, '; ') || '无']);
        csvRows.push([]);

        csvRows.push(["各过程要素评价 (EPn)"]);
        csvRows.push(["过程要素", "符合度 (%)", "评价", "主要降级原因"]);
        const epnRowsExcel = document.getElementById('epnTable')?.querySelectorAll('tbody tr');
        if (epnRowsExcel) epnRowsExcel.forEach(row => csvRows.push(Array.from(row.cells).map(cell => cell.textContent)));
        csvRows.push([]);

        csvRows.push(["产品组评价"]);
        const pgDivExcel = document.getElementById('productGroupEvaluationContainer');
        if (pgDivExcel && auditData.header.products.length > 0 && auditData.header.products.some(p => p.name && p.name.trim() !== '')) {
             const productHeadersExcel = pgDivExcel.querySelectorAll('h4');
             productHeadersExcel.forEach(h4 => {
                const productNameExcel = h4.textContent.replace('产品组: ','').trim();
                 if (productNameExcel) {
                    const pElementExcel = h4.nextElementSibling;
                    const reasonDivExcel = pElementExcel.nextElementSibling;
                    csvRows.push([`产品组: ${productNameExcel}`, pElementExcel.textContent, (reasonDivExcel && reasonDivExcel.classList.contains('downgrade-reason')) ? reasonDivExcel.innerHTML.replace(/<br>/g, '; ') : '']);
                }
            });
        } else {
            csvRows.push(["未进行产品组评价或未定义有效的产品名称。"]);
        }
        csvRows.push([]);


        csvRows.push(["审核清单"]);
        if (completedAudits.length > 0) {
            csvRows.push(["No.", "报告编号", "审核日期", "总体评价等级"]);
            completedAudits.forEach((audit, index) => {
                 csvRows.push([index + 1, audit.reportNumber, audit.auditDate, audit.overallRating.replace(/<.*?>/g, '')]);
            });
        } else {
             csvRows.push(["无已添加的审核记录。"]);
        }
        csvRows.push([]);


        csvRows.push(["措施计划"]);
        csvRows.push(["No.", "提问编号", "分值", "审核记录", "发现/不符合项", "负责人", "措施", "计划完成日期", "状态", "验证有效性"]);
        const apRowsExcel = document.getElementById('actionPlanTableBody')?.querySelectorAll('tr');
        if (apRowsExcel) apRowsExcel.forEach(row => {
            if (row.cells.length < 10 || (row.cells[0].colSpan && row.cells[0].colSpan === 10)) return; 
            csvRows.push([
                row.cells[0].textContent,
                row.cells[1].textContent,
                row.cells[2].textContent,
                row.cells[3].querySelector('textarea')?.value || row.cells[3].textContent,
                row.cells[4].querySelector('textarea')?.value || '',
                row.cells[5].querySelector('input')?.value || '',
                row.cells[6].querySelector('textarea')?.value || '',
                row.cells[7].querySelector('input')?.value || '',
                row.cells[8].querySelector('select')?.value || '',
                row.cells[9].querySelector('textarea')?.value || ''
            ]);
        });

        let csvContent = universalBOM + csvRows.map(e => e.map(cell => `"${String(cell || '').replace(/"/g, '""')}"`).join(",")).join("\n");
        const encodedUri = "data:text/csv;charset=utf-8," + encodeURIComponent(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `VDA6.3审核报告-${auditData.header.companyName || '未命名'}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }


    window.onload = () => {
        loadCompletedAuditsFromLocalStorage(); // Loads and applies limits to completed audits
        const currentDataWasLoaded = loadData(); // Loads and applies limits to current audit data

        // Load company logo from localStorage
        let savedLogo = localStorage.getItem('companyLogo');
        if (!savedLogo) {
            savedLogo = localStorage.getItem('companyLogo_v4'); // Try loading from v4 key for compatibility
        }
        if (savedLogo) {
            const logoPreview = document.getElementById('logo-preview');
            if (logoPreview) {
                logoPreview.src = savedLogo;
                logoPreview.style.display = 'block'; // Ensure the image is visible
            }
        }

        showTab('auditPlan'); // Activate auditPlan tab by default
        renderAuditChecklist(); // Render (possibly limited) checklist

        let messages = [];
        if (currentDataWasLoaded) {
            messages.push("已加载上次保存的审核数据。");
        } else {
            if (localStorage.getItem(CURRENT_AUDIT_DATA_KEY)) {
                 messages.push("加载已存当前审核数据失败 (可能已损坏)，已使用默认设置。");
            } else {
                 messages.push("未找到已保存的当前审核数据，已使用默认设置。");
            }
        }

        if (completedAudits && completedAudits.length > 0) {
            messages.push(`审核清单已加载 (${completedAudits.length} 条记录，已应用)。`);
        } else {
             if (localStorage.getItem(COMPLETED_AUDITS_KEY) && localStorage.getItem(COMPLETED_AUDITS_KEY) !== "[]" && localStorage.getItem(COMPLETED_AUDITS_KEY) !== null ) {
                 messages.push("审核清单加载失败或为空。");
             } else {
                 messages.push("未找到已保存的审核清单记录。");
             }
        }
        if (messages.length > 0 && !(messages.length === 2 && messages[0].includes("默认设置") && messages[1].includes("未找到已保存的审核清单记录"))) {
            console.log(messages.join("\n"));
        }
        
        // These were called in loadData or initializeDefaultAuditDataAndRenderUIOnly.
        // If not, ensure they are called after data (and limits) are set.
        // rebuildProcessProductTable(); 
        // initializeHeaderDataControls(); 
        // updateQuestionnaireAndMatrix(); // This is important to render everything based on current (limited) state
    };
    
    // Initial setup when script loads, before window.onload (mainly for default state if no load)
    // Ensure default data is already compliant.
    // applyExperienceVersionLimits(auditData); // Apply to the initial default auditData
    rebuildProcessProductTable();
    rebuildAuditorTable();

        function editCompanyName() {
            const currentName = document.getElementById('company-name').innerText;
            const newName = prompt('请输入新的公司名称:', currentName);
            if (newName !== null && newName.trim() !== '') {
                document.getElementById('company-name').innerText = newName.trim();
            }
        }

        document.getElementById('logo-upload').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoPreview = document.getElementById('logo-preview');
                    if (logoPreview) {
                        logoPreview.src = e.target.result;
                        logoPreview.style.display = 'block'; // Ensure the image is visible
                    }
                    localStorage.setItem('companyLogo', e.target.result); // Save logo to localStorage
                    localStorage.setItem('companyLogo_v4', e.target.result); // Also save to v4 key for compatibility
                }
                reader.readAsDataURL(file);
            }
        });

    function printQuestions() {
        const allTabs = document.querySelectorAll('.tab-content');
        const tabsNav = document.querySelector('.tabs');
        const printButton = document.querySelector('.print-button');
        const allButtons = document.querySelectorAll('button');

        // Hide all tabs except 'questions'
        allTabs.forEach(tab => {
            if (tab.id !== 'questions') {
                tab.style.display = 'none';
            } else {
                tab.style.display = 'block'; // Ensure questions tab is visible
            }
        });

        // Hide tab navigation and all buttons
        if (tabsNav) tabsNav.style.display = 'none';
        allButtons.forEach(button => {
            button.style.display = 'none';
        });
        if (printButton) printButton.style.display = 'none';

        // Adjust body and container for printing
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        const container = document.querySelector('.container');
        if (container) {
            container.style.margin = '0';
            container.style.width = '100%';
            container.style.maxWidth = 'none';
            container.style.boxShadow = 'none';
            container.style.borderRadius = '0';
            container.style.padding = '0';
        }

        window.print();

        // Restore original styles after printing
        allTabs.forEach(tab => {
            tab.style.display = ''; // Revert to default display
        });
        if (tabsNav) tabsNav.style.display = '';
        allButtons.forEach(button => {
            button.style.display = '';
        });
        if (printButton) printButton.style.display = '';

        if (container) {
            container.style.margin = '';
            container.style.width = '';
            container.style.maxWidth = '';
            container.style.boxShadow = '';
            container.style.borderRadius = '';
            container.style.padding = '';
        }
        document.body.style.margin = '';
        document.body.style.padding = '';

        // Re-activate the currently active tab
        const currentActiveTabButton = document.querySelector('.tab-button.active');
        const currentActiveTabId = currentActiveTabButton ? currentActiveTabButton.dataset.tab : 'auditReport'; // Default to auditReport if no active tab
        showTab(currentActiveTabId);
    }
    </script>
<div style="text-align: center; margin-top: 20px; margin-bottom: 20px;">
        
    </div>
</body>
</html>