<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可持续价值流图4.0</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</head>
<body>
    <h1>可持续价值流图4.0</h1>
    <p>欢迎来到可持续价值流图4.0的网页文档。本页面旨在帮助您理解和应用价值流图（Value Stream Mapping, VSM）来分析和优化您的流程。</p>

    <h2>什么是价值流图（VSM）？</h2>
    <p>价值流图是一种精益管理工具，用于可视化、分析和改进产品或服务从开始到交付给客户的整个流程。它通过绘制当前状态图来识别浪费（如过量生产、等待、运输、过度加工、库存、不必要的动作和缺陷），然后设计未来状态图以消除浪费并提高效率。</p>

    <h2>价值流步骤描述与数据输入</h2>
    <div id="valueStreamSteps"></div>
    <button type="button" onclick="addValueStreamStep()">添加价值流步骤</button>

    <script>
        let stepCount = 0;

        function addValueStreamStep() {
            stepCount++;
            const container = document.getElementById('valueStreamSteps');
            const stepDiv = document.createElement('div');
            stepDiv.className = 'value-stream-step';
            stepDiv.innerHTML = `
                <h3>步骤 ${stepCount}</h3>
                <label for="step_name_${stepCount}">步骤名称:</label><br>
                <input type="text" id="step_name_${stepCount}" name="step_name_${stepCount}"><br><br>
                
                <label for="step_description_${stepCount}">步骤描述:</label><br>
                <textarea id="step_description_${stepCount}" name="step_description_${stepCount}" rows="4" cols="50"></textarea><br><br>
                
                <h4>流程数据</h4>
                <label for="cycle_time_${stepCount}">周期时间 (秒):</label><br>
                <input type="number" id="cycle_time_${stepCount}" name="cycle_time_${stepCount}"><br><br>
                
                <label for="lead_time_${stepCount}">提前期 (天):</label><br>
                <input type="number" id="lead_time_${stepCount}" name="lead_time_${stepCount}"><br><br>
                
                <label for="inventory_${stepCount}">库存量:</label><br>
                <input type="number" id="inventory_${stepCount}" name="inventory_${stepCount}"><br><br>

                <h4>可持续性指标</h4>
                <label for="energy_consumption_${stepCount}">能耗 (kWh):</label><br>
                <input type="number" id="energy_consumption_${stepCount}" name="energy_consumption_${stepCount}"><br><br>

                <label for="water_usage_${stepCount}">水耗 (升):</label><br>
                <input type="number" id="water_usage_${stepCount}" name="water_usage_${stepCount}"><br><br>

                <label for="waste_generation_${stepCount}">废弃物产生量 (kg):</label><br>
                <input type="number" id="waste_generation_${stepCount}" name="waste_generation_${stepCount}"><br><br>

                <label for="co2_emissions_${stepCount}">CO2排放量 (kg):</label><br>
                <input type="number" id="co2_emissions_${stepCount}" name="co2_emissions_${stepCount}"><br><br>
                <hr>
            `;
            container.appendChild(stepDiv);
        }

        // Add an initial step when the page loads
        window.onload = addValueStreamStep;
    </script>

    <h2>数据输入与分析</h2>
    <form>
        <h3>流程步骤信息</h3>
        <label for="process_step">流程步骤名称:</label><br>
        <input type="text" id="process_step" name="process_step"><br><br>
        
        <label for="cycle_time">周期时间 (秒):</label><br>
        <input type="number" id="cycle_time" name="cycle_time"><br><br>
        
        <label for="lead_time">提前期 (天):</label><br>
        <input type="number" id="lead_time" name="lead_time"><br><br>
        
        <label for="inventory">库存量:</label><br>
        <input type="number" id="inventory" name="inventory"><br><br>

        <h3>可持续性指标</h3>
        <label for="energy_consumption">能耗 (kWh):</label><br>
        <input type="number" id="energy_consumption" name="energy_consumption"><br><br>

        <label for="water_usage">水耗 (升):</label><br>
        <input type="number" id="water_usage" name="water_usage"><br><br>

        <label for="waste_generation">废弃物产生量 (kg):</label><br>
        <input type="number" id="waste_generation" name="waste_generation"><br><br>

        <label for="co2_emissions">CO2排放量 (kg):</label><br>
        <input type="number" id="co2_emissions" name="co2_emissions"><br><br>
        
        <input type="submit" value="提交数据">
    </form>
</body>
</html>