<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计量控制图与正态性分析生成器 v2.9.3 (完整修复版)</title>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- Chart.js Annotation Plugin -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.4.0/dist/chartjs-plugin-annotation.min.js"></script>
    <!-- SheetJS (js-xlsx) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- jStat (用于正态分布计算) -->
    <script src="https://cdn.jsdelivr.net/npm/jstat@latest/dist/jstat.min.js"></script>
    <!-- FileSaver.js (用于保存文件) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <!-- html-docx-js (用于导出Word) -->
    <script src="https://unpkg.com/html-docx-js/dist/html-docx.js"></script>
    <!-- html2pdf.js (用于导出PDF) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --danger-color: #e74c3c;
            --light-bg: #f4f7f6;
            --white-bg: #fff;
            --text-dark: #333;
            --text-light: #555;
            --border-color: #e0e0e0;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; background-color: var(--light-bg); color: var(--text-dark); display: flex; flex-direction: column; min-height: 100vh; }
        body.pdf-export-mode footer { position: static !important; margin-top: 30px; display: block !important; }
        body.pdf-export-mode .container-main { box-shadow: none; border: 1px solid #eee; }

        .app-nav { background-color: var(--primary-color); padding: 12px 0; margin-bottom: 20px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .app-nav a { color: var(--white-bg); margin: 0 18px; text-decoration: none; font-size: 1.15em; padding: 8px 15px; border-radius: 5px; transition: background-color 0.2s ease-in-out; }
        .app-nav a:hover, .app-nav a.active { background-color: #2980b9; }

        .page-container { flex: 1; max-width: 1050px; margin: 0 auto; width: 100%; padding: 0 15px; box-sizing: border-box; }
        .page-section { display: none; }
        .page-section.active { display: block; }

        .container-main { background-color: var(--white-bg); padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1, h2 { color: #2c3e50; padding-bottom: 10px; margin-bottom: 20px; page-break-after: avoid; text-align:center; }
        h1 { font-size: 1.8em; }
        h2 { font-size: 1.5em; margin-top: 0;}
        .section { margin-bottom: 30px; padding: 3px 20px 20px 20px; border: 1px solid var(--border-color); border-radius: 5px; background-color: #fdfdfd; page-break-inside: avoid; }

        .meta-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }

        .form-group { margin-bottom: 15px; display: flex; flex-wrap: wrap; align-items: center; }
        .form-group label { min-width: 150px; font-weight: bold; margin-right: 10px; margin-bottom: 5px; color: var(--text-light); }
        .form-group input[type="number"], .form-group input[type="text"], .form-group input[type="date"], .form-group textarea, .form-group select { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; min-width: 180px; font-size: 0.95rem; }
        .form-group textarea { height: 100px; resize: vertical; }
        .input-group-row { display: flex; gap: 15px; margin-bottom: 15px; align-items: center;}
        .input-group-row label { margin-bottom: 0; min-width: 180px; font-weight: bold; color: var(--text-light); }
        .input-group-row input[type="number"] { padding: 8px; border: 1px solid #ced4da; border-radius: 4px; width: 180px; }
        .form-group input[type="radio"], .form-group input[type="checkbox"] { margin-right: 5px; }
        .form-group .radio-group label, .form-group .checkbox-group label { font-weight: normal; margin-right: 15px; display: inline-flex; align-items: center; cursor: pointer; }
        .form-group .radio-group, .form-group .checkbox-group{ display: flex; flex-wrap: wrap; align-items: center; gap: 5px 15px; }
        #nelsonRules { max-width: calc(100% - 160px); }

        .button-row { margin-top: 20px; }
        button { background-color: var(--primary-color); color: white; padding: 12px 25px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; transition: background-color 0.3s ease; margin-right: 10px; margin-top: 10px; }
        button:hover { background-color: #2980b9; }
        button:disabled { background-color: #bdc3c7; cursor: not-allowed; }

        #resultsSection, #chartsSection, #dataTableSection { margin-top: 30px; }
        .export-buttons button { background-color: var(--secondary-color); font-size: 15px; padding: 10px 20px;}
        .export-buttons button:hover { background-color: #27ae60; }
        .export-buttons button:disabled { background-color: #bdc3c7; cursor: not-allowed; }

        table { width: 100%; border-collapse: collapse; margin-top: 15px; font-size: 0.9em; page-break-inside: auto; }
        thead { display: table-header-group; } tbody { display: table-row-group; } tr { page-break-inside: avoid; page-break-after: auto; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; vertical-align: top; word-wrap: break-word; }
        #dataTable td:not(:first-child) { text-align: right; }
        #analysisHistoryTable td { text-align: left; }
        #analysisHistoryTable td:first-child,
        #analysisHistoryTable td:nth-child(2),
        #analysisHistoryTable td:last-child {
            text-align: center;
        }
        #analysisHistoryTable td a { color: var(--primary-color); text-decoration: underline; cursor: pointer;}
        #analysisHistoryTable td a:hover { color: #2980b9; }
        .delete-btn {
            background-color: var(--danger-color);
            color: white;
            padding: 5px 10px;
            font-size: 0.9em;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin: 0;
        }
        .delete-btn:hover {
            background-color: #c0392b;
        }

        th { background-color: #ecf0f1; color: #34495e; text-align:center; font-weight: 600;}
        tr:nth-child(even) { background-color: #f9f9f9; }
        .chart-container { position: relative; width: 100%; margin-bottom: 30px; margin-left: auto; margin-right: auto; page-break-inside: avoid; }
        canvas { border: 1px solid #eee; border-radius: 4px; display: block; width: 100% !important; height: 400px !important; }

        .message-area { font-weight: bold; margin-top: 10px; padding: 12px; border-radius: 4px; display: none; text-align: center; }
        .message-area.error { color: #721c24; background-color: #f8d7da; border: 1px solid #f5c6cb; display: block; }
        .message-area.success { color: #155724; background-color: #d4edda; border: 1px solid #c3e6cb; display: block; }
        .message-area.info { color: #0c5460; background-color: #d1ecf1; border: 1px solid #bee5eb; display: block; }
        .highlight { color: var(--danger-color); font-weight: bold; }

        .results-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; }
        .result-item { background-color: #f8f9fa; padding: 12px; border: 1px solid #e9ecef; border-radius: 4px; word-wrap: break-word; page-break-inside: avoid; }
        .result-item strong { color: #0056b3; display: block; margin-bottom: 3px; }
        .result-item.full-width { grid-column: 1 / -1; }
        .result-item code { background-color: #e9ecef; padding: 2px 4px; border-radius: 3px; font-size: 0.85em; display: block; margin-top: 5px; white-space: pre-wrap; word-break: break-all; }

        footer { text-align: center; margin-top: auto; padding: 20px; font-size: 0.9em; color: var(--text-light); border-top: 1px solid #eee; background-color: #f8f9fa; width: 100%; box-sizing: border-box;}
        @media (max-width: 768px) {
            .app-nav a { font-size: 1em; margin: 0 8px; padding: 6px 10px;}
            .page-container{ padding: 0 10px;}
            .form-group { flex-direction: column; align-items: flex-start; }
            .form-group label { min-width: auto; margin-bottom: 8px; }
            .form-group input[type="number"], .form-group input[type="text"], .form-group input[type="date"], .form-group textarea, .form-group select { width: 100%; }
            .input-group-row { flex-direction: column; align-items: flex-start; gap: 5px; }
            .input-group-row input[type="number"] { width: 100%; }
            .results-grid { grid-template-columns: 1fr; }
            #nelsonRules { max-width: 100%; }
            canvas { height: 300px !important; }
            footer { font-size: 0.8em; padding: 15px; }
        }
        .word-export-table { border-collapse: collapse; width: 100%; margin-bottom: 15px; font-family: SimSun, sans-serif; }
        .word-export-table th, .word-export-table td { border: 1px solid #999; padding: 5px; text-align: left; }
        .word-export-table th { background-color: #f2f2f2; font-weight: bold;}
    </style>
</head>
<body>
    <nav class="app-nav">
        <a href="#" id="nav-tool" class="active">数据输入与参数设置</a>
        <a href="#" id="nav-list">分析清单</a>
    </nav>

    <div class="page-container">
        <!-- 数据输入与参数设置页面 -->
        <div id="tool-section" class="page-section active">
            <div class="container-main" id="mainContainer">
                                                <div style="background-color: #2c3e50; border-radius: 5px; padding-top: 15px; padding-left: 20px; padding-right: 20px; padding-bottom: 5px; margin-bottom: 10px;">
                    <h1 style="color: #FFFFFF; margin-top: 0; margin-bottom: 0; line-height: 1;">计量控制图与正态性分析</h1>
                </div>
                <div class="section" id="inputSection">
                    <h2>数据输入与参数设置</h2>

                    <div class="meta-info-grid">
                        <div class="form-group"> <label for="metaMeasuredProduct">测量产品:</label> <input type="text" id="metaMeasuredProduct" placeholder="例如: 零件XYZ"> </div>
                        <div class="form-group"> <label for="metaProductCharacteristic">产品特性:</label> <input type="text" id="metaProductCharacteristic" placeholder="例如: 直径"> </div>
                        <div class="form-group"> <label for="metaMeasuringInstrument">测量仪器、仪器编号:</label> <input type="text" id="metaMeasuringInstrument" placeholder="例如: 千分尺 #123"> </div>
                        <div class="form-group"> <label for="metaMeasurementPersonnel">测量人员:</label> <input type="text" id="metaMeasurementPersonnel" placeholder="例如: 张三"> </div>
                        <div class="form-group"> <label for="metaDataSource">数据来源:</label> <input type="text" id="metaDataSource" placeholder="例如: 生产线A, 设备 #S001"> </div>
                        <div class="form-group"> <label for="metaAnalysisPersonnel">分析人员:</label> <input type="text" id="metaAnalysisPersonnel" placeholder="例如: 李四"> </div>
                        <div class="form-group"> <label for="metaAnalysisDate">分析日期:</label> <input type="date" id="metaAnalysisDate"> </div>
                    </div>
                    <hr style="margin: 25px 0;">

                    <div class="form-group"> <label>数据录入方式:</label> <div class="radio-group"> <label><input type="radio" name="dataInputMethod" value="random" checked onchange="toggleInputFields()"> 随机生成</label> <label><input type="radio" name="dataInputMethod" value="paste" onchange="toggleInputFields()"> 粘贴数据</label> </div> </div>
                    <div id="randomDataFields"> <div class="form-group"> <label for="numPoints">数据点总数:</label> <input type="number" id="numPoints" value="100" min="10"> </div> <div class="form-group"> <label for="dataMin">最小值:</label> <input type="number" id="dataMin" value="9.8" step="any"> </div> <div class="form-group"> <label for="dataMax">最大值:</label> <input type="number" id="dataMax" value="10.2" step="any"> </div> <div class="form-group"> <label for="decimalPlaces">小数位数:</label> <input type="number" id="decimalPlaces" value="2" min="0" max="10"> </div> </div>
                    <div id="pasteDataField" style="display: none;"> <div class="form-group"> <label for="pastedData">粘贴数据:</label> <textarea id="pastedData" placeholder="在此粘贴数据，用逗号、空格、制表符或换行符分隔。7.6, 8.1, 8.8, 9.1, 9.2, 9.3, 9.4, 9.8, 10.1, 10.2, 10.3, 10.4, 10.7, 10.8, 11.1, 11.4, 11.9, 12.1, 12.3, 13.3"></textarea> </div> <div class="form-group"> <label></label> <small>示例: 10.1, 10.2, 9.9, 10.0 ... 或每行/每列一个数据</small> </div> </div>
                    <div class="input-group-row"> <label for="subgroupSize">子组大小 (n):</label> <input type="number" id="subgroupSize" value="5" min="1"> <small style="margin-left: 10px;">(1: I-MR; 2-8: X̄-R; ≥9: X̄-S)</small> </div>
                    <div class="input-group-row"> <label for="lslInput">规格下限 (LSL, 可选):</label> <input type="number" id="lslInput" step="any" placeholder="例如: 7.0"> </div>
                    <div class="input-group-row"> <label for="uslInput">规格上限 (USL, 可选):</label> <input type="number" id="uslInput" step="any" placeholder="例如: 13.0"> </div>
                    <div class="input-group-row"> <label for="target">目标值 (Target, 可选):</label> <input type="number" id="target" step="any" placeholder="用于 Cpm"> <small style="margin-left: 10px;">(留空则为规格中心)</small> </div>
                    <div class="form-group"> <label>判异准则:</label> <div class="checkbox-group" id="nelsonRules"> <label title="1点落在A区之外 (±3σ)"><input type="checkbox" value="1" checked> 准则 1</label> <label title="连续9点落在中心线同一侧"><input type="checkbox" value="2"> 准则 2</label> <label title="连续6点递增或递减"><input type="checkbox" value="3"> 准则 3</label> <label title="连续14点上下交错"><input type="checkbox" value="4"> 准则 4</label> <label title="连续3点中有2点落在A区或之外(±2σ, 中心线同侧)"><input type="checkbox" value="5"> 准则 5</label> <label title="连续5点中有4点落在B区或之外(±1σ, 中心线同侧)"><input type="checkbox" value="6"> 准则 6</label> <label title="连续15点落在C区内(±1σ, 中心线两侧)"><input type="checkbox" value="7"> 准则 7</label> <label title="连续8点落在中心线两侧，但无一在C区内"><input type="checkbox" value="8"> 准则 8</label> </div> </div>
                    <div class="button-row">
                        <button id="saveButton" onclick="saveSettings()">保存当前输入</button>
                        <button id="generateButton" onclick="processDataAndGenerate()">生成图表与分析 (并存入清单)</button>
                    </div>
                    <div id="errorMessage" class="message-area error" style="display:none;"></div>
                    <div id="statusMessage" class="message-area success" style="display:none;"></div>
                </div>
                <div class="section export-buttons" id="exportSection" style="display: none;"> <button id="exportExcelButton" onclick="exportToExcel()" disabled style="background-color: #4CAF50;">导出Excel</button> <button id="exportWordButton" onclick="exportToWord()" disabled style="background-color: #FF9800;">导出Word</button> <button id="exportPDFButton" onclick="exportToPDF()" disabled style="background-color: #E91E63;">导出PDF</button> <button id="exportMarkdownButton" onclick="exportToMarkdown()" disabled style="background-color: #81D4FA;">导出Markdown</button> <small id="exportNote" style="display: block; margin-top: 10px;"></small> </div>
                <div class="section" id="dataTableSection" style="display: none;"> <h2>原始数据与子组统计</h2> <div style="max-height: 400px; overflow-y: auto;"> <table id="dataTable"></table> </div> </div>
                <div class="section" id="resultsSection" style="display: none;"> <h2>统计结果摘要</h2> <div id="resultsSummary" class="results-grid"> </div> </div>
                <div class="section" id="chartsSection" style="display: none;"> <h2>控制图与正态性分析</h2> <div class="chart-container"> <canvas id="controlChart1"></canvas> </div> <div class="chart-container"> <canvas id="controlChart2"></canvas> </div> <div class="chart-container"> <canvas id="histogramChart"></canvas> </div> <div class="chart-container"> <canvas id="qqPlotChart"></canvas> </div>  </div>
            </div>
            </div>
        </div>

        <!-- 分析清单页面 -->
        <div id="list-section" class="page-section">
            <div class="container-main">
                <h1>分析清单</h1>
                <div class="section">
                    <table id="analysisHistoryTable">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>分析日期</th>
                                <th>分析人员</th>
                                <th>数据来源</th>
                                <th>测量产品</th>
                                <th>测量仪器</th>
                                <th>测量人员</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="analysisHistoryTableBody">
                            <!-- 历史记录将通过JS动态填充 -->
                        </tbody>
                    </table>
                    <div id="historyMessageArea" class="message-area info" style="margin-top:15px; display:none;"></div>
                </div>
            </div>
        </div>
    </div> <!-- End Page Container -->

    <footer> 版权所有 © 吴志明 | 电话&微信：13959240478 |  弘扬匠心、传递知识、为企业创造价值！|本工具的8个判异准则仅针对均值图/单值图适用，极差图/移动极差图/标准差图仅适用判异准则1。 </footer>

    <script>
        let chartInstances = {};
        let spcResults = {};
        let subgroupData = [];
        let rawData = [];

        const SETTINGS_KEY = 'spcGeneratorSettings_longshao_v4_meta_full_v2_final';
        const HISTORY_KEY = 'spcAnalysisHistory_longshao_v1_full_v2_final';

        // --- DOM Elements ---
        const navToolLink = getElement('nav-tool');
        const navListLink = getElement('nav-list');
        const toolSection = getElement('tool-section');
        const listSection = getElement('list-section');
        const analysisHistoryTableBody = getElement('analysisHistoryTableBody');
        const historyMessageArea = getElement('historyMessageArea');
        const metaMeasuredProductInput = getElement('metaMeasuredProduct');
        const metaProductCharacteristicInput = getElement('metaProductCharacteristic');
        const metaMeasuringInstrumentInput = getElement('metaMeasuringInstrument');
        const metaMeasurementPersonnelInput = getElement('metaMeasurementPersonnel');
        const metaDataSourceInput = getElement('metaDataSource');
        const metaAnalysisPersonnelInput = getElement('metaAnalysisPersonnel');
        const metaAnalysisDateInput = getElement('metaAnalysisDate');

        const spcConstants = {R:{d2:[null,null,1.128,1.693,2.059,2.326,2.534,2.704,2.847,2.97,3.078,3.173,3.258,3.336,3.407,3.472,3.532,3.588,3.64,3.689,3.735,3.778,3.819,3.858,3.895,3.931],A2:[null,null,1.88,1.023,0.729,0.577,0.483,0.419,0.373,0.337,0.308,0.285,0.266,0.249,0.235,0.223,0.212,0.203,0.194,0.187,0.18,0.173,0.167,0.162,0.157,0.153],D3:[null,null,0,0,0,0,0.076,0.136,0.184,0.223,0.256,0.285,0.308,0.329,0.348,0.364,0.379,0.392,0.404,0.414,0.425,0.434,0.443,0.452,0.459,0.466],D4:[null,null,3.267,2.574,2.282,2.114,2.004,1.924,1.864,1.816,1.777,1.744,1.717,1.692,1.671,1.652,1.636,1.622,1.61,1.599,1.589,1.581,1.572,1.565,1.558,1.552]},S:{c4:[null,null,0.7979,0.8862,0.9213,0.94,0.9515,0.9594,0.965,0.9693,0.9727,0.9754,0.9776,0.9794,0.981,0.9823,0.9835,0.9845,0.9854,0.9862,0.9869,0.9876,0.9882,0.9887,0.9892,0.9896],A3:[null,null,2.659,1.954,1.628,1.427,1.287,1.182,1.099,1.032,0.975,0.927,0.886,0.85,0.817,0.789,0.763,0.739,0.718,0.698,0.68,0.663,0.647,0.633,0.619,0.606],B3:[null,null,0,0,0,0.03,0.118,0.185,0.239,0.284,0.321,0.354,0.382,0.406,0.428,0.448,0.466,0.482,0.497,0.51,0.523,0.534,0.545,0.555,0.565,0.574],B4:[null,null,3.267,2.568,2.266,2.089,1.97,1.882,1.815,1.761,1.716,1.679,1.646,1.618,1.594,1.572,1.552,1.534,1.518,1.503,1.49,1.477,1.466,1.455,1.445,1.435]},I:{d2:1.128,E2:2.66}};

        // --- Helper Functions ---
        function getConstant(type, constant, n) { if (spcConstants[type] && spcConstants[type][constant] && n >= 0 && spcConstants[type][constant].length > n && spcConstants[type][constant][n] !== null && spcConstants[type][constant][n] !== undefined) { return spcConstants[type][constant][n]; } console.warn(`SPC constant ${type}.${constant} for n=${n} not found or out of bounds.`); return undefined; }
        function getElement(id) { return document.getElementById(id); }
        function getValue(id) { const el = getElement(id); return el ? el.value : ''; }
        function setValue(id, val) { const el = getElement(id); if(el) el.value = val; else console.warn("Element not found for setValue:", id); }
        function getNumber(id) { const value = getValue(id); return value === '' ? NaN : parseFloat(value); }
        function getInt(id) { return parseInt(getValue(id), 10); }
        function showError(message) { console.error("Error displayed:", message); const el = getElement('errorMessage'); if(el) { el.textContent = message; el.style.display = 'block';}}
        function clearError() { const el = getElement('errorMessage'); if(el) { el.textContent = ''; el.style.display = 'none';}}
        function showStatus(message, duration = 3000, areaId = 'statusMessage', type='success') { const statusEl = getElement(areaId); if(!statusEl) return; statusEl.textContent = message; statusEl.className = `message-area ${type}`; statusEl.style.display = 'block'; if (duration > 0) { setTimeout(() => { statusEl.textContent = ''; statusEl.style.display = 'none'; }, duration); } }
        function disableButton(id) { const el = getElement(id); if(el) el.disabled = true; }
        function enableButton(id) { const el = getElement(id); if(el) el.disabled = false; }
        function enableExportButtons() { enableButton('exportExcelButton'); enableButton('exportWordButton'); enableButton('exportPDFButton'); enableButton('exportMarkdownButton'); getElement('exportSection').style.display = 'block'; getElement('exportNote').textContent = '提示: Word/Markdown 主要为文本和表格, PDF 包含图表。'; }
        function disableExportButtons(showStatusMsg = false) { disableButton('exportExcelButton'); disableButton('exportWordButton'); disableButton('exportPDFButton'); disableButton('exportMarkdownButton'); if (!showStatusMsg) { const noteEl = getElement('exportNote'); if(noteEl) noteEl.textContent = ''; } const exportSectionEl = getElement('exportSection'); if(exportSectionEl) { if (getElement('resultsSection').style.display !== 'none') { exportSectionEl.style.display = 'block'; } else { exportSectionEl.style.display = 'none'; } } }
        function formatNum(value, decimals) { if (typeof value === 'number' && isFinite(value)) { const factor = Math.pow(10, decimals); return (Math.round(value * factor) / factor).toFixed(decimals); } return 'N/A';  }
        function formatPPM(value) { if (typeof value === 'number' && isFinite(value)) { return Math.round(value).toLocaleString(); } return 'N/A'; }
        function escapeHtml(unsafe) { if (typeof unsafe !== 'string') { return unsafe; } return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;"); }
        function calculateMean(arr) { if (!arr || arr.length === 0) return NaN; return arr.reduce((a, b) => a + b, 0) / arr.length; }
        function calculateStdDev(arr, isSample = true) { if (!arr || arr.length < 2) return NaN; const mean = calculateMean(arr); if(isNaN(mean)) return NaN; return Math.sqrt(arr.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / (arr.length - (isSample ? 1 : 0))); }
        function calculateRange(arr) { if (!arr || arr.length === 0) return NaN; return Math.max(...arr) - Math.min(...arr); }
        function standardNormalInv(p) { if (p <= 0) return -Infinity; if (p >= 1) return Infinity; return jStat.normal.inv(p, 0, 1); }
        function getQuantile(sortedData, q) { if (!sortedData || sortedData.length === 0) return NaN; const dataCopy = sortedData; const pos = (dataCopy.length - 1) * q; const base = Math.floor(pos); const rest = pos - base; if (dataCopy.length === 1) return dataCopy[0]; if (base + 1 < dataCopy.length) { return dataCopy[base] + rest * (dataCopy[base + 1] - dataCopy[base]); } else { return dataCopy[base]; } }
        function getSelectedRuleDescriptions() { const rules = []; const checkboxes = getElement('nelsonRules').querySelectorAll('input[type="checkbox"]:checked'); checkboxes.forEach(cb => { rules.push({ value: parseInt(cb.value), text: cb.parentElement.title }); }); return rules; }
        function getSelectedRules() { return getSelectedRuleDescriptions().map(r => r.value); }
        function toggleInputFields() { const method = document.querySelector('input[name="dataInputMethod"]:checked').value; getElement('randomDataFields').style.display = (method === 'random') ? 'block' : 'none'; getElement('pasteDataField').style.display = (method === 'paste') ? 'block' : 'none'; }

        // --- Navigation Logic ---
        function showPage(pageId) {
            document.querySelectorAll('.page-section').forEach(section => section.classList.remove('active'));
            getElement(pageId).classList.add('active');
            document.querySelectorAll('.app-nav a').forEach(link => link.classList.remove('active'));
            if (pageId === 'tool-section') navToolLink.classList.add('active');
            else if (pageId === 'list-section') {
                navListLink.classList.add('active');
                populateAnalysisHistoryTable();
            }
        }
        navToolLink.addEventListener('click', (e) => { e.preventDefault(); showPage('tool-section'); });
        navListLink.addEventListener('click', (e) => { e.preventDefault(); showPage('list-section'); });

        // --- Settings Save/Load (Includes Meta Info) ---
        function saveSettings() {
            clearError();
            try {
                const settings = {
                    metaMeasuredProduct: getValue('metaMeasuredProduct'),
                    metaProductCharacteristic: getValue('metaProductCharacteristic'),
                    metaMeasuringInstrument: getValue('metaMeasuringInstrument'),
                    metaMeasurementPersonnel: getValue('metaMeasurementPersonnel'),
                    metaDataSource: getValue('metaDataSource'),
                    metaAnalysisPersonnel: getValue('metaAnalysisPersonnel'),
                    metaAnalysisDate: getValue('metaAnalysisDate'),
                    dataInputMethod: document.querySelector('input[name="dataInputMethod"]:checked').value,
                    numPoints: getValue('numPoints'), dataMin: getValue('dataMin'), dataMax: getValue('dataMax'),
                    decimalPlaces: getValue('decimalPlaces'), pastedData: getValue('pastedData'),
                    subgroupSize: getValue('subgroupSize'),
                    usl: getValue('uslInput'), lsl: getValue('lslInput'), target: getValue('target'),
                    nelsonRules: []
                };
                getElement('nelsonRules').querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    settings.nelsonRules.push({ value: cb.value, checked: cb.checked });
                });
                localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
                showStatus('当前输入已保存。下次打开将自动加载。');
            } catch (e) {
                console.error("保存设置失败:", e);
                showError("保存设置失败：" + e.message);
            }
        }

        function loadSettings(settingsObject = null) {
            try {
                const settingsToLoad = settingsObject || JSON.parse(localStorage.getItem(SETTINGS_KEY));
                if (settingsToLoad) {
                    setValue('metaMeasuredProduct', settingsToLoad.metaMeasuredProduct || '');
                    setValue('metaProductCharacteristic', settingsToLoad.metaProductCharacteristic || '');
                    setValue('metaMeasuringInstrument', settingsToLoad.metaMeasuringInstrument || '');
                    setValue('metaMeasurementPersonnel', settingsToLoad.metaMeasurementPersonnel || '');
                    setValue('metaDataSource', settingsToLoad.metaDataSource || '');
                    setValue('metaAnalysisPersonnel', settingsToLoad.metaAnalysisPersonnel || '');
                    setValue('metaAnalysisDate', settingsToLoad.metaAnalysisDate || '');
                    const radioBtn = document.querySelector(`input[name="dataInputMethod"][value="${settingsToLoad.dataInputMethod || 'random'}"]`);
                    if (radioBtn) radioBtn.checked = true;
                    toggleInputFields();
                    setValue('numPoints', settingsToLoad.numPoints || '100');
                    setValue('dataMin', settingsToLoad.dataMin || '9.8');
                    setValue('dataMax', settingsToLoad.dataMax || '10.2');
                    setValue('decimalPlaces', settingsToLoad.decimalPlaces || '2');
                    setValue('pastedData', settingsToLoad.pastedData || '');
                    setValue('subgroupSize', settingsToLoad.subgroupSize || '5');
                    setValue('uslInput', settingsToLoad.usl || '');
                    setValue('lslInput', settingsToLoad.lsl || '');
                    setValue('target', settingsToLoad.target || '');

                    const ruleMap = {};
                    if (settingsToLoad.nelsonRules) {
                        settingsToLoad.nelsonRules.forEach(rule => { ruleMap[rule.value] = rule.checked; });
                    }
                    getElement('nelsonRules').querySelectorAll('input[type="checkbox"]').forEach(cb => {
                        cb.checked = (settingsToLoad.nelsonRules ? (ruleMap[cb.value] === undefined ? (cb.value === "1") : ruleMap[cb.value]) : (cb.value === "1"));
                    });

                    if (!settingsObject) {
                       showStatus('已加载上次保存的输入。', 3000);
                    }
                }
            } catch (e) {
                console.error("加载设置失败:", e);
                if (!settingsObject) showError("加载保存的输入失败: " + e.message);
            }
        }


        // --- Analysis History Functions ---
        function getAnalysisHistory() {
            const historyString = localStorage.getItem(HISTORY_KEY);
            return historyString ? JSON.parse(historyString) : [];
        }

        function saveFullHistory(historyArray) {
            try {
                localStorage.setItem(HISTORY_KEY, JSON.stringify(historyArray));
            } catch (e) {
                if (e.name === 'QuotaExceededError' || (e.message && e.message.toLowerCase().includes('quota'))) {
                    showStatus('存储空间已满，无法保存更多历史记录。请考虑清理浏览器数据或导出重要分析。', 5000, 'historyMessageArea', 'error');
                    console.error("Error saving full history (quota exceeded):", e);
                } else {
                    showStatus('保存历史记录时发生未知错误。', 5000, 'historyMessageArea', 'error');
                    console.error("Error saving full history:", e);
                }
                throw e;
            }
        }

        function addAnalysisToHistory(currentAnalysisState) {
            let history = getAnalysisHistory();
            const nextNo = history.reduce((max, item) => Math.max(max, item.no || 0), 0) + 1;
            currentAnalysisState.no = nextNo;

            history.unshift(currentAnalysisState);

            try {
                saveFullHistory(history);
                showStatus(`分析 #${String(nextNo).padStart(4, '0')} 已添加到清单。`, 3000, 'statusMessage', 'success');
            } catch (e) {
                if (e.name === 'QuotaExceededError' && history.length > 1) {
                    console.warn("Quota exceeded, attempting to remove oldest history item.");
                    history.shift(); 
                    history.pop(); 
                    history.unshift(currentAnalysisState); 
                    try {
                        saveFullHistory(history);
                        showStatus(`分析 #${String(nextNo).padStart(4, '0')} 已添加。存储空间已满，最旧记录已移除。`, 5000, 'historyMessageArea', 'warning');
                    } catch (e2) {
                        showStatus(`无法将此分析添加到历史清单，存储空间已满且无法移除旧记录。`, 5000, 'historyMessageArea', 'error');
                        console.error("Error adding to history (quota exceeded, even after trying to prune):", e2);
                    }
                } else if (e.name === 'QuotaExceededError') {
                     showStatus(`无法将此分析添加到历史清单，存储空间已满。`, 5000, 'historyMessageArea', 'error');
                }
            }
        }

        function populateAnalysisHistoryTable() {
            clearError();
            showStatus('', 0, 'historyMessageArea', 'info');
            const history = getAnalysisHistory();
            analysisHistoryTableBody.innerHTML = '';

            if (history.length === 0) {
                const row = analysisHistoryTableBody.insertRow();
                const cell = row.insertCell();
                cell.colSpan = 8; // Adjusted for new "操作" column
                cell.textContent = '暂无历史记录。';
                cell.style.textAlign = 'center';
                return;
            }

            history.forEach(entry => {
                const row = analysisHistoryTableBody.insertRow();
                const noCell = row.insertCell();
                const link = document.createElement('a');
                link.href = "#";
                link.textContent = String(entry.no).padStart(4, '0');
                link.onclick = (e) => {
                    e.preventDefault();
                    loadHistoricalAnalysis(entry.no);
                };
                noCell.appendChild(link);

                row.insertCell().textContent = entry.metaAnalysisDate || '---';
                row.insertCell().textContent = entry.metaAnalysisPersonnel || '---';
                row.insertCell().textContent = entry.metaDataSource || '---';
                row.insertCell().textContent = entry.metaMeasuredProduct || '---';
                row.insertCell().textContent = entry.metaMeasuringInstrument || '---';
                row.insertCell().textContent = entry.metaMeasurementPersonnel || '---';

                const actionCell = row.insertCell();
                const deleteButton = document.createElement('button');
                deleteButton.textContent = '删除';
                deleteButton.className = 'delete-btn';
                deleteButton.onclick = () => deleteHistoryEntry(entry.no);
                actionCell.appendChild(deleteButton);
            });
        }

        function deleteHistoryEntry(entryNoToDelete) {
            if (confirm(`您确定要删除编号为 ${String(entryNoToDelete).padStart(4, '0')} 的历史记录吗？此操作不可恢复。`)) {
                let history = getAnalysisHistory();
                const initialLength = history.length;
                history = history.filter(item => item.no !== entryNoToDelete);

                if (history.length < initialLength) {
                    try {
                        saveFullHistory(history);
                        populateAnalysisHistoryTable();
                        showStatus(`历史记录 #${String(entryNoToDelete).padStart(4, '0')} 已成功删除。`, 3000, 'historyMessageArea', 'success');
                    } catch(e) {
                        showStatus(`删除历史记录 #${String(entryNoToDelete).padStart(4, '0')} 后保存失败: ${e.message}`, 5000, 'historyMessageArea', 'error');
                        console.error("Error saving history after deletion:", e);
                    }
                } else {
                    showStatus(`未找到编号为 ${String(entryNoToDelete).padStart(4, '0')} 的历史记录进行删除。`, 3000, 'historyMessageArea', 'error');
                }
            }
        }

        function loadHistoricalAnalysis(entryNo) {
            const history = getAnalysisHistory();
            const reportToLoad = history.find(item => item.no === entryNo);

            if (!reportToLoad) {
                showStatus(`未找到编号为 ${String(entryNo).padStart(4, '0')} 的历史分析报告。`, 5000, 'historyMessageArea', 'error');
                return;
            }
            loadSettings(reportToLoad);
            showPage('tool-section');
            setTimeout(() => {
                processDataAndGenerate(true);
                showStatus(`已加载历史分析报告 #${String(entryNo).padStart(4, '0')}。`, 4000);
            }, 100);
        }

        // --- Core Data Processing and Charting Functions (from v2.8, with minor adjustments) ---
        function generateRandomData(count, min, max, decimals) { const data = []; const factor = Math.pow(10, decimals); for (let i = 0; i < count; i++) { const randomValue = Math.random() * (max - min) + min; data.push(Math.round(randomValue * factor) / factor); } return data; }
        function parsePastedData(text) { let cleanedString = text.replace(/\r\n|\r|\n/g, ' '); cleanedString = cleanedString.replace(/[,;\t]/g, ' '); cleanedString = cleanedString.replace(/\s+/g, ' '); cleanedString = cleanedString.trim(); let parts; if (cleanedString === "") { parts = []; } else { parts = cleanedString.split(' '); } const data = []; const invalidEntries = []; parts.forEach(item => { const trimmedItem = item.trim(); if (trimmedItem === '') return; const num = parseFloat(trimmedItem); if (!isNaN(num) && isFinite(num)) { data.push(num); } else { invalidEntries.push(trimmedItem); } }); if (invalidEntries.length > 0) { showError(`无法解析以下条目: ${invalidEntries.join(', ')}`); return null; } return data; }
        function structureData(data, n_subgroup_size) { subgroupData = []; rawData = [...data]; const numSubgroups = Math.floor(data.length / n_subgroup_size); if (numSubgroups === 0 && n_subgroup_size > 1 && data.length > 0) { showError(`数据点 (${data.length}) 不足以形成大小为 ${n_subgroup_size} 的子组.`); return null; } if (numSubgroups === 0 && data.length === 0) { showError(`没有有效数据点.`); return null;} if (n_subgroup_size === 1) { for(let i=0; i<data.length; i++){ subgroupData.push({ id: i + 1, values: [data[i]], mean: data[i], range: (i > 0) ? Math.abs(data[i] - data[i-1]) : NaN, stddev: NaN }); } return subgroupData; } else { let k = 0; for (let i = 0; i < numSubgroups; i++) { const subgroupValues = data.slice(k, k + n_subgroup_size); subgroupData.push({ id: i + 1, values: subgroupValues, mean: calculateMean(subgroupValues), range: calculateRange(subgroupValues), stddev: calculateStdDev(subgroupValues, true) }); k += n_subgroup_size; } if (data.length % n_subgroup_size !== 0) { console.warn(`警告: ${data.length % n_subgroup_size} 个数据点未被使用.`); } return subgroupData; } }
        function displayDataTable(subgroups, n_subgroup_size) { const table = getElement('dataTable'); table.innerHTML = ''; const thead = table.createTHead(); const headerRow = thead.insertRow(); const dp = spcResults.decimalPlaces !== undefined ? spcResults.decimalPlaces : 2; const dpPlus1 = dp + 1; const dpPlus2 = dp + 2; if (n_subgroup_size === 1) { headerRow.insertCell().textContent = '观测值 (i)'; headerRow.insertCell().textContent = '数值 (X)'; headerRow.insertCell().textContent = '移动极差 (MR)'; } else { headerRow.insertCell().textContent = '子组 (k)'; for(let i=1; i<=n_subgroup_size; i++){ headerRow.insertCell().textContent = `X${i}`; } headerRow.insertCell().textContent = '均值 (X̄)'; headerRow.insertCell().textContent = '极差 (R)'; if (spcResults.chartType === 'Xbar-S') { headerRow.insertCell().textContent = '标准差 (s)'; } } const tbody = table.createTBody(); subgroups.forEach((sg) => { const row = tbody.insertRow(); row.insertCell().textContent = sg.id; if (n_subgroup_size === 1) { row.insertCell().textContent = formatNum(sg.values[0], dp); row.insertCell().textContent = formatNum(sg.range, dpPlus1); } else { sg.values.forEach(val => { row.insertCell().textContent = formatNum(val, dp); }); row.insertCell().textContent = formatNum(sg.mean, dpPlus1); row.insertCell().textContent = formatNum(sg.range, dpPlus1); if (spcResults.chartType === 'Xbar-S') { row.insertCell().textContent = formatNum(sg.stddev, dpPlus2); } } }); getElement('dataTableSection').style.display = 'block';}
        function erfForAD(x) { const sign = (x >= 0) ? 1 : -1; x = Math.abs(x); const a1=0.254829592, a2=-0.284496736, a3=1.421413741, a4=-1.453152027, a5=1.061405429, p=0.3275911; const t = 1.0 / (1.0 + p * x); return sign * (1.0 - (((((a5*t + a4)*t) + a3)*t + a2)*t + a1)*t*Math.exp(-x*x)); }
        function normsdistEquivalent(z) { if (z > 8.0) return 1.0; if (z < -8.0) return 0.0; let cdf = 0.5 * (1.0 + erfForAD(z / Math.sqrt(2.0))); const epsilon = 1e-16; return Math.max(epsilon, Math.min(1.0 - epsilon, cdf)); }
        function calculateExcelAndersonDarling(allIndividualData) {
            const results = { aSquaredExcel: NaN, aSquaredAdjusted: NaN, pValue: NaN, pValueString: "N/A", interpretation: "", errorMessage: null };
            const n_ad = allIndividualData.length;
            if (n_ad < 2) { results.errorMessage = "至少需要2个数据点进行AD检验。"; return results; }
            const mean_ad = calculateMean(allIndividualData);
            const stdDev_ad = calculateStdDev(allIndividualData, true);
            if (stdDev_ad === 0 || isNaN(stdDev_ad)) { results.errorMessage = "数据标准差为零或无效，无法计算AD统计量。"; return results; }
            const sorted_ad_Data = [...allIndividualData].sort((a, b) => a - b);
            const zScoresForSortedADData = sorted_ad_Data.map(x => (x - mean_ad) / stdDev_ad);
            const f_of_z_forSortedADData = zScoresForSortedADData.map(z => normsdistEquivalent(z));
            const ln_f_of_z_ad = []; const ln_1_minus_f_of_z_ad = [];
            for (let k=0; k < f_of_z_forSortedADData.length; k++){ let fz_val = f_of_z_forSortedADData[k]; fz_val = Math.max(1e-300, Math.min(1.0 - 1e-16, fz_val)); ln_f_of_z_ad.push(Math.log(fz_val)); ln_1_minus_f_of_z_ad.push(Math.log(1.0 - fz_val)); }
            let sumOfTermsH_ad = 0;
            for (let i = 0; i < n_ad; i++) { const rank_ad = i + 1; const current_lnFZi_ad = ln_f_of_z_ad[i]; const corresponding_ln1mFZi_from_G_ad = ln_1_minus_f_of_z_ad[n_ad - rank_ad]; const term_H_i_ad = (2 * rank_ad - 1) * (current_lnFZi_ad + corresponding_ln1mFZi_from_G_ad); sumOfTermsH_ad += term_H_i_ad; }
            results.aSquaredExcel = -n_ad - (sumOfTermsH_ad / n_ad);
            results.aSquaredAdjusted = results.aSquaredExcel * (1.0 + (0.75 / n_ad) + (2.25 / (n_ad * n_ad)));
            let pValue_raw_ad; const adjA2_ad = results.aSquaredAdjusted;
            if (adjA2_ad >= 13.0) { pValue_raw_ad = 0.0; }
            else if (adjA2_ad >= 1.06) { pValue_raw_ad = Math.exp(1.2937 - 5.709 * adjA2_ad + 0.0186 * Math.pow(adjA2_ad, 2)); }
            else if (adjA2_ad >= 0.70) { pValue_raw_ad = Math.exp(0.9177 - 4.279 * adjA2_ad - 1.380 * Math.pow(adjA2_ad, 2)); }
            else if (adjA2_ad >= 0.40) { pValue_raw_ad = Math.exp(0.2000 - 2.500 * adjA2_ad - 3.200 * Math.pow(adjA2_ad, 2)); }
            else if (adjA2_ad >= 0.25) { pValue_raw_ad = 1.0 - Math.exp(-8.318 + 42.796 * adjA2_ad - 59.938 * Math.pow(adjA2_ad, 2));}
            else { pValue_raw_ad = 1.0 - Math.exp(-13.436 + 101.14 * adjA2_ad - 223.73 * Math.pow(adjA2_ad, 2));}
            results.pValue = Math.max(0.0, Math.min(1.0, pValue_raw_ad));
            if (isNaN(results.pValue) || !isFinite(results.pValue)) { results.pValueString = "计算错误"; }
            else if (adjA2_ad >= 13.0) { results.pValueString = "0.000000"; }
            else if (results.pValue < 0.0000005 && results.pValue !== 0) { results.pValueString = "<0.000001"; }
            else { results.pValueString = results.pValue.toFixed(6); }
            let numericPForInterp = (results.pValueString.startsWith("<")) ? 0.0000001 : parseFloat(results.pValueString);
            if (isNaN(numericPForInterp) || results.pValueString === "计算错误") results.interpretation = "P 值计算出现问题。";
            else if (numericPForInterp <= 0.05) { results.interpretation = `P 值 (${results.pValueString}) ≤ 0.05。数据显著偏离正态分布。`; }
            else { results.interpretation = `P 值 (${results.pValueString}) > 0.05。没有足够证据表明数据偏离正态分布。`; }
            return results;
        }
        function applyNelsonRules(subgroups, limits, center, sigma_hat, n_subgroup_size_rule, chartType, activeRules) {
             const violations = { chart1: [], chart2: [] }; if (activeRules.length === 0 || subgroups.length === 0) { const numPoints = subgroups.length; violations.chart1 = Array(numPoints).fill(null).map(() => []); violations.chart2 = Array(numPoints).fill(null).map(() => []); return violations;}
             const chart1Points = subgroups.map(sg => sg.mean); let chart2Points = []; if (chartType === 'I-MR') { chart2Points = subgroups.map(sg => sg.range).slice(1); } else if (chartType === 'Xbar-R') { chart2Points = subgroups.map(sg => sg.range); } else if (chartType === 'Xbar-S') { chart2Points = subgroups.map(sg => sg.stddev); }
             const chart1Limits = (chartType === 'I-MR') ? limits.I : limits.Xbar;
             const chart2Limits = (chartType === 'I-MR') ? limits.MR : (chartType === 'Xbar-R' ? limits.R : limits.S);
             function checkData(points, cl, ucl, lcl, sigma_eff, checkChartType, zoneLimits = null, rulesToCheck) { const numPoints = points.length; const chartViolations = Array(numPoints).fill(null).map(() => []); if (isNaN(cl)) { console.warn(`中心线无效 (${cl}) for ${checkChartType}，跳过判异。`); return chartViolations; } const len = points.length; const sigma = sigma_eff; let cl1s_upper = NaN, cl2s_upper = NaN, cl1s_lower = NaN, cl2s_lower = NaN; let zonesNeeded = rulesToCheck.some(r => [2, 5, 6, 7, 8].includes(r)); let zonesValid = zonesNeeded && !isNaN(sigma) && sigma > 0 && (checkChartType === 'Xbar' || checkChartType === 'I'); if (zonesValid) { cl1s_upper = (zoneLimits && !isNaN(zoneLimits.UCL_1Sigma)) ? zoneLimits.UCL_1Sigma : cl + sigma; cl2s_upper = (zoneLimits && !isNaN(zoneLimits.UCL_2Sigma)) ? zoneLimits.UCL_2Sigma : cl + 2 * sigma; cl1s_lower = (zoneLimits && !isNaN(zoneLimits.LCL_1Sigma)) ? zoneLimits.LCL_1Sigma : cl - sigma; cl2s_lower = (zoneLimits && !isNaN(zoneLimits.LCL_2Sigma)) ? zoneLimits.LCL_2Sigma : cl - 2 * sigma; }  else if (zonesNeeded && (checkChartType === 'Xbar' || checkChartType === 'I')) { console.warn(`Sigma (${sigma}) 无效或为零 for ${checkChartType}, 依赖区域的判异准则将跳过。`); } let applicableRules = [...rulesToCheck]; if (checkChartType === 'R' || checkChartType === 'MR' || checkChartType === 'S') { applicableRules = rulesToCheck.filter(r => r === 1); }  else if (!zonesValid) { applicableRules = rulesToCheck.filter(rule => ![2, 5, 6, 7, 8].includes(rule)); } if (applicableRules.length === 0) return chartViolations;  for (let i = 0; i < len; i++) { const p = points[i]; if (isNaN(p)) continue;  if (applicableRules.includes(1)) { if ((!isNaN(ucl) && p > ucl) || (!isNaN(lcl) && p < lcl)) { if (!chartViolations[i].includes(1)) chartViolations[i].push(1); } } if (zonesValid && (checkChartType === 'Xbar' || checkChartType === 'I')) { if (applicableRules.includes(2) && i >= 8) { const last9 = points.slice(i - 8, i + 1); if (last9.filter(pt => !isNaN(pt)).length === 9) { if (last9.every(pt => pt > cl) || last9.every(pt => pt < cl)) { if (!chartViolations[i].includes(2)) chartViolations[i].push(2); } } } if (applicableRules.includes(3) && i >= 5) { const last6 = points.slice(i - 5, i + 1); if (last6.filter(pt => !isNaN(pt)).length === 6) { let inc = true; let dec = true; for (let j = 1; j < 6; j++) { if (last6[j] <= last6[j-1]) inc = false; if (last6[j] >= last6[j-1]) dec = false; } if (inc || dec) { if (!chartViolations[i].includes(3)) chartViolations[i].push(3); } } } if (applicableRules.includes(4) && i >= 13) { const last14 = points.slice(i - 13, i + 1); if(last14.filter(pt => !isNaN(pt)).length === 14) { let alt = true; let lastDir = 0; for (let j = 1; j < 14; j++) { const currentDir = Math.sign(last14[j] - last14[j-1]); if (currentDir === 0 || (lastDir !== 0 && currentDir === lastDir)) { alt = false; break; } if(currentDir !== 0) lastDir = currentDir; } if (alt) { if (!chartViolations[i].includes(4)) chartViolations[i].push(4); } } } if (applicableRules.includes(5) && i >= 2) { const last3 = points.slice(i - 2, i + 1).filter(pt=>!isNaN(pt)); if (last3.length === 3) { let countUpA = last3.filter(pt => pt >= cl2s_upper || (!isNaN(ucl) && pt > ucl)).length; let countDownA = last3.filter(pt => pt <= cl2s_lower || (!isNaN(lcl) && pt < lcl)).length; if (countUpA >= 2 || countDownA >= 2) { if (!chartViolations[i].includes(5)) chartViolations[i].push(5); } } } if (applicableRules.includes(6) && i >= 4) { const last5 = points.slice(i - 4, i + 1).filter(pt=>!isNaN(pt)); if (last5.length === 5) { let countUpB = last5.filter(pt => pt >= cl1s_upper || (!isNaN(ucl) && pt > ucl)).length; let countDownB = last5.filter(pt => pt <= cl1s_lower || (!isNaN(lcl) && pt < lcl)).length; if (countUpB >= 4 || countDownB >= 4) { if (!chartViolations[i].includes(6)) chartViolations[i].push(6); } } } if (applicableRules.includes(7) && i >= 14) { const last15 = points.slice(i - 14, i + 1).filter(pt=>!isNaN(pt)); if (last15.length === 15) { if (last15.every(pt => pt < cl1s_upper && pt > cl1s_lower)) { if (!chartViolations[i].includes(7)) chartViolations[i].push(7); } } } if (applicableRules.includes(8) && i >= 7) { const last8 = points.slice(i - 7, i + 1).filter(pt=>!isNaN(pt)); if (last8.length === 8) { if (last8.every(pt => pt > cl1s_upper || pt < cl1s_lower)) { if (!chartViolations[i].includes(8)) chartViolations[i].push(8); } } } } } return chartViolations; }
             let sigma_xbar_eff = NaN; if (!isNaN(sigma_hat)) { sigma_xbar_eff = (n_subgroup_size_rule > 1 && !isNaN(n_subgroup_size_rule)) ? sigma_hat / Math.sqrt(n_subgroup_size_rule) : sigma_hat; } const chart1CheckType = (chartType === 'I-MR') ? 'I' : 'Xbar'; const chart1ZoneLimits = (chartType === 'I-MR') ? limits.I : limits.Xbar; violations.chart1 = checkData(chart1Points, chart1Limits.CL, chart1Limits.UCL, chart1Limits.LCL, sigma_xbar_eff, chart1CheckType, chart1ZoneLimits, activeRules); let chart2CheckType = ''; if (chartType === 'I-MR') chart2CheckType = 'MR'; else if (chartType === 'Xbar-R') chart2CheckType = 'R'; else if (chartType === 'Xbar-S') chart2CheckType = 'S'; const chart2ViolationsRaw = checkData(chart2Points, chart2Limits.CL, chart2Limits.UCL, chart2Limits.LCL, NaN, chart2CheckType, null, activeRules); if (chartType === 'I-MR') { violations.chart2 = Array(subgroups.length).fill(null).map(() => []); chart2ViolationsRaw.forEach((v, index) => { if (index + 1 < violations.chart2.length) { violations.chart2[index + 1] = v; } }); } else { violations.chart2 = chart2ViolationsRaw; } return violations;
        }
        function calculateSPC(data_spc, n_subgroup_size_param, usl, lsl, targetValue) {
            spcResults = {};
            spcResults.n_subgroup_size = n_subgroup_size_param;
            spcResults.usl = usl; spcResults.lsl = lsl; spcResults.target = targetValue; spcResults.selectedRules = getSelectedRuleDescriptions();
            if (!structureData(data_spc, n_subgroup_size_param)) { return null; }
            const N_total_spc = rawData.length; const numSubgroups_spc = subgroupData.length;
            if (n_subgroup_size_param === 1 && N_total_spc < 2) { showError(`数据点 (${N_total_spc}) 不足，I-MR分析至少需要2个点。`); return null; }
            if (n_subgroup_size_param > 1 && numSubgroups_spc < 2) { showError(`子组数量 (${numSubgroups_spc}) 不足，X-Bar分析至少需要2个子组。`); return null; }
            if(N_total_spc > 0) { let maxDecimals = 0; rawData.forEach(val => { const valStr = String(val); if (valStr.includes('.')) { maxDecimals = Math.max(maxDecimals, valStr.split('.')[1].length); } }); const inputDecimals = getInt('decimalPlaces'); spcResults.decimalPlaces = !isNaN(inputDecimals) ? inputDecimals : (maxDecimals > 0 ? maxDecimals : 2); }
            else { spcResults.decimalPlaces = getInt('decimalPlaces') || 2; }
            const allMeans = subgroupData.map(sg => sg.mean).filter(m => !isNaN(m)); const allRanges = subgroupData.map(sg => sg.range).filter(r => !isNaN(r)); const allStdDevs = subgroupData.map(sg => sg.stddev).filter(s => !isNaN(s));
            spcResults.xbarbar = calculateMean(allMeans); spcResults.rbar = (n_subgroup_size_param > 1 && allRanges.length > 0) ? calculateMean(allRanges) : NaN;
            spcResults.mrbar = (n_subgroup_size_param === 1 && allRanges.length > 0) ? calculateMean(allRanges) : NaN;
            spcResults.sbar = (allStdDevs.length > 0) ? calculateMean(allStdDevs) : NaN;
            spcResults.overallStdDev = calculateStdDev(rawData, true);
            let sigmaHat; let d2 = NaN, c4 = NaN, d3_const = NaN, d4_const = NaN, a2_const = NaN, a3_const = NaN, b3_const = NaN, b4_const = NaN, e2_const = NaN;
            if (n_subgroup_size_param === 1) { spcResults.chartType = 'I-MR'; d2 = spcConstants.I.d2; e2_const = spcConstants.I.E2; d3_const = getConstant('R', 'D3', 2); d4_const = getConstant('R', 'D4', 2); if (d2 && !isNaN(spcResults.mrbar) && spcResults.mrbar > 0) sigmaHat = spcResults.mrbar / d2; else if (d2 && !isNaN(spcResults.mrbar) && spcResults.mrbar === 0) sigmaHat = 0; else { sigmaHat = NaN; } }
            else if (n_subgroup_size_param >= 2 && n_subgroup_size_param <= 8) { spcResults.chartType = 'Xbar-R'; d2 = getConstant('R', 'd2', n_subgroup_size_param); a2_const = getConstant('R', 'A2', n_subgroup_size_param); d3_const = getConstant('R', 'D3', n_subgroup_size_param); d4_const = getConstant('R', 'D4', n_subgroup_size_param); if (d2 === undefined || a2_const === undefined || d3_const === undefined || d4_const === undefined) { showError(`缺少 n=${n_subgroup_size_param} 的 X̄-R 常数。`); return null; } if (d2 && !isNaN(spcResults.rbar) && spcResults.rbar > 0) sigmaHat = spcResults.rbar / d2; else if (d2 && !isNaN(spcResults.rbar) && spcResults.rbar === 0) sigmaHat = 0; else { sigmaHat = NaN; } }
            else { spcResults.chartType = 'Xbar-S'; c4 = getConstant('S', 'c4', n_subgroup_size_param); a3_const = getConstant('S', 'A3', n_subgroup_size_param); b3_const = getConstant('S', 'B3', n_subgroup_size_param); b4_const = getConstant('S', 'B4', n_subgroup_size_param); if (c4 === undefined || a3_const === undefined || b3_const === undefined || b4_const === undefined) { showError(`缺少 n=${n_subgroup_size_param} 的 X̄-S 常数。`); return null; } if (c4 && !isNaN(spcResults.sbar) && spcResults.sbar > 0) sigmaHat = spcResults.sbar / c4; else if (c4 && !isNaN(spcResults.sbar) && spcResults.sbar === 0) sigmaHat = 0; else { sigmaHat = NaN; } }
            spcResults.sigmaHat = sigmaHat; spcResults.controlLimits = {};
            const calcLimit = (center, factor, variation) => { if (isNaN(center) || isNaN(factor) || isNaN(variation)) return NaN; return center + factor * variation; }; const calcLimitLower = (center, factor, variation) => { if (isNaN(center) || isNaN(factor) || isNaN(variation)) return NaN; return center - factor * variation; };
            if (spcResults.chartType === 'I-MR') { spcResults.controlLimits.I = { UCL: calcLimit(spcResults.xbarbar, e2_const, spcResults.mrbar), CL: spcResults.xbarbar, LCL: calcLimitLower(spcResults.xbarbar, e2_const, spcResults.mrbar) }; spcResults.controlLimits.MR = { UCL: calcLimit(0, d4_const, spcResults.mrbar), CL: spcResults.mrbar, LCL: Math.max(0, calcLimit(0, d3_const, spcResults.mrbar ?? 0)) }; if (!isNaN(sigmaHat) && sigmaHat > 0) { spcResults.controlLimits.I.UCL_2Sigma = spcResults.xbarbar + 2 * sigmaHat; spcResults.controlLimits.I.UCL_1Sigma = spcResults.xbarbar + 1 * sigmaHat; spcResults.controlLimits.I.LCL_1Sigma = spcResults.xbarbar - 1 * sigmaHat; spcResults.controlLimits.I.LCL_2Sigma = spcResults.xbarbar - 2 * sigmaHat;} }
            else if (spcResults.chartType === 'Xbar-R') { spcResults.controlLimits.Xbar = { UCL: calcLimit(spcResults.xbarbar, a2_const, spcResults.rbar), CL: spcResults.xbarbar, LCL: calcLimitLower(spcResults.xbarbar, a2_const, spcResults.rbar) }; spcResults.controlLimits.R = { UCL: calcLimit(0, d4_const, spcResults.rbar), CL: spcResults.rbar, LCL: Math.max(0, calcLimit(0, d3_const, spcResults.rbar ?? 0)) }; if (!isNaN(sigmaHat) && sigmaHat > 0 && n_subgroup_size_param > 0) { const sigmaXbar = sigmaHat / Math.sqrt(n_subgroup_size_param); spcResults.controlLimits.Xbar.UCL_2Sigma = spcResults.xbarbar + 2 * sigmaXbar; spcResults.controlLimits.Xbar.UCL_1Sigma = spcResults.xbarbar + 1 * sigmaXbar; spcResults.controlLimits.Xbar.LCL_1Sigma = spcResults.xbarbar - 1 * sigmaXbar; spcResults.controlLimits.Xbar.LCL_2Sigma = spcResults.xbarbar - 2 * sigmaXbar;} }
            else { spcResults.controlLimits.Xbar = { UCL: calcLimit(spcResults.xbarbar, a3_const, spcResults.sbar), CL: spcResults.xbarbar, LCL: calcLimitLower(spcResults.xbarbar, a3_const, spcResults.sbar) }; spcResults.controlLimits.S = { UCL: calcLimit(0, b4_const, spcResults.sbar), CL: spcResults.sbar, LCL: Math.max(0, calcLimit(0, b3_const, spcResults.sbar ?? 0)) }; if (!isNaN(sigmaHat) && sigmaHat > 0 && n_subgroup_size_param > 0) { const sigmaXbar = sigmaHat / Math.sqrt(n_subgroup_size_param); spcResults.controlLimits.Xbar.UCL_2Sigma = spcResults.xbarbar + 2 * sigmaXbar; spcResults.controlLimits.Xbar.UCL_1Sigma = spcResults.xbarbar + 1 * sigmaXbar; spcResults.controlLimits.Xbar.LCL_1Sigma = spcResults.xbarbar - 1 * sigmaXbar; spcResults.controlLimits.Xbar.LCL_2Sigma = spcResults.xbarbar - 2 * sigmaXbar;} }
            spcResults.capability = {};
            if (!isNaN(usl) && !isNaN(lsl)) { const specRange = usl - lsl; if (specRange <= 0) { spcResults.capability.error = "规格范围无效 (USL <= LSL)"; }  else if (isNaN(spcResults.xbarbar)) { spcResults.capability.error = "无法计算整体均值 (X̄̄)，无法进行能力分析"; } else { let T_cap = !isNaN(spcResults.target) ? spcResults.target : (usl + lsl) / 2; spcResults.capability.T_used = T_cap;  if (!isNaN(sigmaHat) && sigmaHat > 0) { spcResults.capability.Cp = specRange / (6 * sigmaHat); const cpu = (usl - spcResults.xbarbar) / (3 * sigmaHat); const cpl = (spcResults.xbarbar - lsl) / (3 * sigmaHat); spcResults.capability.Cpk = Math.min(cpu, cpl); const zUpperWithin = (usl - spcResults.xbarbar) / sigmaHat; const zLowerWithin = (lsl - spcResults.xbarbar) / sigmaHat; spcResults.capability.ppmWithin = ((1 - jStat.normal.cdf(zUpperWithin, 0, 1)) + jStat.normal.cdf(zLowerWithin, 0, 1)) * 1e6; } else if (sigmaHat === 0) { spcResults.capability.Cp = Infinity; spcResults.capability.Cpk = (spcResults.xbarbar >= lsl && spcResults.xbarbar <= usl) ? Infinity : -Infinity; spcResults.capability.ppmWithin = (spcResults.xbarbar >= lsl && spcResults.xbarbar <= usl) ? 0 : 1e6; spcResults.capability.warning_within = "组内标准差(σ̂)为零"; } else { spcResults.capability.Cp = NaN; spcResults.capability.Cpk = NaN; spcResults.capability.ppmWithin = NaN; spcResults.capability.warning_within = "无法计算基于组内标准差(σ̂)的能力指数 (σ̂无效)"; } if (!isNaN(spcResults.overallStdDev) && spcResults.overallStdDev > 0) { spcResults.capability.Pp = specRange / (6 * spcResults.overallStdDev); const ppu = (usl - spcResults.xbarbar) / (3 * spcResults.overallStdDev); const ppl = (spcResults.xbarbar - lsl) / (3 * spcResults.overallStdDev); spcResults.capability.Ppk = Math.min(ppu, ppl); const zUpperOverall = (usl - spcResults.xbarbar) / spcResults.overallStdDev; const zLowerOverall = (lsl - spcResults.xbarbar) / spcResults.overallStdDev; spcResults.capability.ppmOverall = ((1 - jStat.normal.cdf(zUpperOverall, 0, 1)) + jStat.normal.cdf(zLowerOverall, 0, 1)) * 1e6; } else if (spcResults.overallStdDev === 0) { spcResults.capability.Pp = Infinity; spcResults.capability.Ppk = (spcResults.xbarbar >= lsl && spcResults.xbarbar <= usl) ? Infinity : -Infinity; spcResults.capability.ppmOverall = (spcResults.xbarbar >= lsl && spcResults.xbarbar <= usl) ? 0 : 1e6; spcResults.capability.warning_overall = "整体标准差(s)为零"; } else { spcResults.capability.Pp = NaN; spcResults.capability.Ppk = NaN; spcResults.capability.ppmOverall = NaN; spcResults.capability.warning_overall = "无法计算基于整体标准差(s)的能力指数 (s无效)"; } if (N_total_spc >= 2 && isFinite(T_cap)) { const sumSqDevT = rawData.reduce((acc, xi) => acc + Math.pow(xi - T_cap, 2), 0); const variance_Cpm = (N_total_spc > 1) ? sumSqDevT / (N_total_spc - 1) : NaN; if (!isNaN(variance_Cpm) && variance_Cpm >= 0) { const sigma_Cpm = Math.sqrt(variance_Cpm); spcResults.capability.sigma_Cpm = sigma_Cpm; if (sigma_Cpm > 0) { spcResults.capability.Cpm = specRange / (6 * sigma_Cpm); }  else { spcResults.capability.Cpm = Infinity; spcResults.capability.warning_cpm = "数据点均等于目标值 T (σ̂_cpm = 0)"; } } else { spcResults.capability.Cpm = NaN; spcResults.capability.sigma_Cpm = NaN; spcResults.capability.warning_cpm = "无法计算 σ̂_cpm (方差无效)"; } } else { spcResults.capability.Cpm = NaN; spcResults.capability.sigma_Cpm = NaN; if (!isFinite(T_cap)) spcResults.capability.warning_cpm = "目标值 T 无效"; else if (N_total_spc < 2) spcResults.capability.warning_cpm = "计算 σ̂_cpm 需要至少2个数据点"; } } }
            else { spcResults.capability.error = "未提供有效的规格界限 (USL/LSL)"; }
            spcResults.ruleViolations = applyNelsonRules( subgroupData, spcResults.controlLimits, spcResults.xbarbar, spcResults.sigmaHat, n_subgroup_size_param, spcResults.chartType, getSelectedRules() );
            return spcResults;
        }
        function displayResults(results) { const summaryDiv = getElement('resultsSummary'); summaryDiv.innerHTML = ''; const dp = results.decimalPlaces !== undefined ? results.decimalPlaces : 2; const dp_stats = dp + 2; const dp_cap = 3; const dp_ppm = 0; function addResultItem(label, value, unit = '', formula = '', decimals = dp_stats, details = '') { const item = document.createElement('div'); item.classList.add('result-item'); let displayValue; if (label.includes("PPM")) displayValue = formatPPM(value); else if (label.includes("Cp") || label.includes("Pp") || label.includes("Cpk") || label.includes("Ppk") || label.includes("Cpm")) displayValue = formatNum(value, dp_cap); else if (label.includes("子组大小") || label.includes("子组数量") || label.includes("数据点总数")) displayValue = formatNum(value, 0); else if (label === 'σ̂_cpm') displayValue = formatNum(value, dp_cap + 2); else if (label === '过程标准差估计 (σ̂)' || label === '整体标准差 (s)') displayValue = formatNum(value, dp_stats + 1); else displayValue = formatNum(value, decimals); item.innerHTML = `<strong>${label}:</strong> ${displayValue} ${unit}`; if (formula || details) { item.title = `计算: ${formula} ${details}`; item.innerHTML += `<br><code title="${escapeHtml(`计算: ${formula} ${details}`)}">${formula}${details ? ' ('+details+')' : ''}</code>`; } summaryDiv.appendChild(item); } addResultItem('子组大小 (n)', results.n_subgroup_size, '', '', 0); addResultItem('子组数量 (k)', subgroupData.length, '', '', 0); addResultItem('数据点总数 (N)', rawData.length, '', '', 0); addResultItem('整体均值 (X̄̄)', results.xbarbar, '', 'ΣX̄ / k', dp_stats); if (results.chartType === 'I-MR') { addResultItem('平均移动极差 (MR̄)', results.mrbar, '', 'Σ|Xᵢ - Xᵢ₋₁| / (N-1)', dp_stats); addResultItem('过程标准差估计 (σ̂)', results.sigmaHat, '', `MR̄ / d₂ [d₂=${spcConstants.I.d2}]`); } else if (results.chartType === 'Xbar-R') { addResultItem('平均极差 (R̄)', results.rbar, '', 'ΣR / k', dp_stats); const d2_val = getConstant('R', 'd2', results.n_subgroup_size) || 'N/A'; addResultItem('过程标准差估计 (σ̂)', results.sigmaHat, '', `R̄ / d₂ [d₂=${d2_val}]`); } else { addResultItem('平均标准差 (s̄)', results.sbar, '', 'Σs / k', dp_stats + 1); const c4_val = getConstant('S', 'c4', results.n_subgroup_size) || 'N/A'; addResultItem('过程标准差估计 (σ̂)', results.sigmaHat, '', `s̄ / c₄ [c₄=${c4_val}]`); } addResultItem('整体标准差 (s)', results.overallStdDev, '', 'Overall Sample Std Dev (N-1)'); const rulesItem = document.createElement('div'); rulesItem.classList.add('result-item', 'full-width'); let rulesHtml = '<strong>启用的判异准则:</strong> '; if (results.selectedRules && results.selectedRules.length > 0) { rulesHtml += results.selectedRules.map(r => `<span title="${escapeHtml(r.text)}">规则 ${r.value}</span>`).join(', '); } else { rulesHtml += '无'; } rulesItem.innerHTML = rulesHtml; summaryDiv.appendChild(rulesItem); const capItem = document.createElement('div'); capItem.classList.add('result-item', 'full-width'); capItem.style.backgroundColor = '#e8f4fd'; let capHtml = '<strong>过程能力分析:</strong>'; if (results.capability.error) { capHtml += `<br><span style="color: #c0392b;">错误: ${escapeHtml(results.capability.error)}</span>`; } else { capHtml += `<br>USL: ${formatNum(results.usl, dp_stats)} | LSL: ${formatNum(results.lsl, dp_stats)} | T: ${formatNum(results.capability.T_used, dp_stats)} ${(isNaN(results.target) ? '(中心值)' : '')}`; const capGrid = document.createElement('div'); capGrid.style.display = 'grid'; capGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(200px, 1fr))'; capGrid.style.gap = '5px 10px'; capGrid.style.marginTop = '10px'; function addCap(label, value, formula = '', details = '', warning = null) { const div = document.createElement('div'); let displayVal = ''; let cssClass = ''; let effectiveDetails = details; if (label.includes("PPM")) displayVal = formatPPM(value); else if (label === 'σ̂_cpm') displayVal = formatNum(value, dp_cap + 2); else displayVal = formatNum(value, dp_cap); if (warning) { displayVal = 'N/A'; cssClass = 'text-muted'; effectiveDetails = warning; } else if(displayVal === 'Infinity' || displayVal === '-Infinity') { cssClass = 'text-muted'; effectiveDetails = warning || details; } div.innerHTML = `<span class="${cssClass}">${label}: <strong>${displayVal}</strong></span>`; if (formula || effectiveDetails) { const titleText = `计算: ${formula} ${effectiveDetails ? '('+effectiveDetails+')' : ''}`; div.title = titleText; div.innerHTML += `<br><code title="${escapeHtml(titleText)}">${formula} ${effectiveDetails ? '('+effectiveDetails+')' : ''}</code>`; } capGrid.appendChild(div); } addCap('Cp', results.capability.Cp, '(USL-LSL)/(6σ̂)', '', results.capability.warning_within); addCap('Cpk', results.capability.Cpk, 'min(Cpu,Cpl)', 'Cpu/l based on σ̂', results.capability.warning_within); addCap('Pp', results.capability.Pp, '(USL-LSL)/(6s)', '', results.capability.warning_overall); addCap('Ppk', results.capability.Ppk, 'min(Ppu,Ppl)', 'Ppu/l based on s', results.capability.warning_overall); addCap('σ̂_cpm', results.capability.sigma_Cpm, 'sqrt[ Σ(xᵢ-T)²/(N-1) ]', 'Std Dev from Target', results.capability.warning_cpm); addCap('Cpm', results.capability.Cpm, '(USL-LSL)/(6σ̂_cpm)', '', results.capability.warning_cpm); addCap('PPM (组内 σ̂)', results.capability.ppmWithin, '(1-Φ(Zup)+Φ(Zlow))*1E6', 'Z using σ̂', results.capability.warning_within); addCap('PPM (整体 s)', results.capability.ppmOverall, '(1-Φ(Zup)+Φ(Zlow))*1E6', 'Z using s', results.capability.warning_overall); capItem.appendChild(capGrid); } summaryDiv.appendChild(capItem); const adDisplayItem = document.createElement('div');  adDisplayItem.classList.add('result-item', 'full-width');  adDisplayItem.style.backgroundColor = '#e6ffe6';  let adDisplayHtml = '<strong>正态性检验 (安德森-达林, Excel方法复现):</strong>'; if (results.andersonDarling && results.andersonDarling.errorMessage) { adDisplayHtml += `<br><span style="color: #c0392b;">错误: ${escapeHtml(results.andersonDarling.errorMessage)}</span>`; }  else if (results.andersonDarling) { const ad = results.andersonDarling; const dp_ad_export = 6; adDisplayHtml += `<br>AD (Excel A²): <strong>${formatNum(ad.aSquaredExcel, dp_ad_export)}</strong>`; adDisplayHtml += ` | AD* (调整后): <strong>${formatNum(ad.aSquaredAdjusted, dp_ad_export)}</strong>`; adDisplayHtml += ` | P 值: <strong>${ad.pValueString}</strong>`; adDisplayHtml += `<br><em>${escapeHtml(ad.interpretation)}</em>`; }  else { adDisplayHtml += "<br>AD检验未执行或无结果。"; } adDisplayItem.innerHTML = adDisplayHtml; summaryDiv.appendChild(adDisplayItem); getElement('resultsSection').style.display = 'block'; enableExportButtons(); }
        function destroyChart(chartId) { if (chartInstances[chartId]) { chartInstances[chartId].destroy(); delete chartInstances[chartId]; } }
        function createControlChart(canvasId, title, dataPoints, labels, cl, ucl, lcl, violations = [], zoneLimits = null) { destroyChart(canvasId); const ctx = getElement(canvasId).getContext('2d'); const chartData = labels.map((label, index) => { const dataIndex = index; return (dataIndex >= 0 && dataIndex < dataPoints.length) ? dataPoints[dataIndex] : NaN; }); const validDataPoints = chartData.filter(p => !isNaN(p)); let minY = isNaN(lcl) ? (validDataPoints.length > 0 ? Math.min(...validDataPoints) : 0) : lcl; let maxY = isNaN(ucl) ? (validDataPoints.length > 0 ? Math.max(...validDataPoints) : 1) : ucl; const isVariationChart = title.includes('R Chart') || title.includes('MR Chart') || title.includes('S Chart'); if (!isVariationChart && zoneLimits) { const zoneValues = [zoneLimits.UCL_2Sigma, zoneLimits.LCL_2Sigma, zoneLimits.UCL_1Sigma, zoneLimits.LCL_1Sigma].filter(v => !isNaN(v)); if (zoneValues.length > 0) { minY = Math.min(minY, ...zoneValues); maxY = Math.max(maxY, ...zoneValues); } } if (validDataPoints.length > 0) { minY = Math.min(minY, ...validDataPoints); maxY = Math.max(maxY, ...validDataPoints); } let suggestedMinY, suggestedMaxY; if (isFinite(minY) && isFinite(maxY)) { if (maxY > minY) { const rangeY = maxY - minY; const paddingY = rangeY * 0.15; suggestedMinY = minY - paddingY; suggestedMaxY = maxY + paddingY; } else { const paddingY = Math.abs(maxY * 0.1) || 0.5; suggestedMinY = minY - paddingY; suggestedMaxY = maxY + paddingY; } } else { suggestedMinY = undefined; suggestedMaxY = undefined; } if (!isNaN(lcl) && isFinite(lcl) && suggestedMinY !== undefined && suggestedMinY > lcl) { const rangeCheck = (isFinite(maxY) && isFinite(minY)) ? maxY - minY : 1; const lclPadding = Math.abs(rangeCheck) * 0.05 || 0.1; suggestedMinY = lcl - lclPadding; } if (isVariationChart && lcl >= 0) { suggestedMinY = Math.max(0, suggestedMinY ?? 0); } if (title.includes('MR Chart') && lcl === 0 && maxY > 0) { suggestedMinY = - (maxY * 0.05); } const datasets = [ { label: '数据点', data: chartData, borderColor: '#3498db', backgroundColor: '#3498db', tension: 0.1, type: 'line', pointRadius: 4, pointHoverRadius: 6, borderWidth: 2, order: 3, spanGaps: false }, { label: `CL (${formatNum(cl, 4)})`, data: Array(labels.length).fill(cl), borderColor: '#2ecc71', borderDash: [5, 5], borderWidth: 1.5, pointRadius: 0, fill: false, type: 'line', order: 0, hidden: isNaN(cl) }, { label: `UCL (${formatNum(ucl, 4)})`, data: Array(labels.length).fill(ucl), borderColor: '#e74c3c', borderWidth: 2, pointRadius: 0, fill: false, type: 'line', order: 1, hidden: isNaN(ucl) }, { label: `LCL (${formatNum(lcl, 4)})`, data: Array(labels.length).fill(lcl), borderColor: '#e74c3c', borderWidth: 2, pointRadius: 0, fill: false, type: 'line', order: 1, hidden: isNaN(lcl) } ]; const zoneLineColor = '#f39c12'; const zoneLineWidth = 1; const zoneLineDash = [3, 3]; let showZones = !isVariationChart && zoneLimits && !isNaN(zoneLimits.UCL_1Sigma); if (showZones && zoneLimits && !isNaN(zoneLimits.UCL_2Sigma)) datasets.push({ label: '+2σ', data: Array(labels.length).fill(zoneLimits.UCL_2Sigma), borderColor: zoneLineColor, borderDash: zoneLineDash, borderWidth: zoneLineWidth, pointRadius: 0, fill: false, type: 'line', order: 0 }); if (showZones && zoneLimits && !isNaN(zoneLimits.LCL_2Sigma)) datasets.push({ label: '-2σ', data: Array(labels.length).fill(zoneLimits.LCL_2Sigma), borderColor: zoneLineColor, borderDash: zoneLineDash, borderWidth: zoneLineWidth, pointRadius: 0, fill: false, type: 'line', order: 0 }); if (showZones && zoneLimits && !isNaN(zoneLimits.UCL_1Sigma)) datasets.push({ label: '+1σ', data: Array(labels.length).fill(zoneLimits.UCL_1Sigma), borderColor: zoneLineColor, borderDash: zoneLineDash, borderWidth: zoneLineWidth, pointRadius: 0, fill: false, type: 'line', order: 0 }); if (showZones && zoneLimits && !isNaN(zoneLimits.LCL_1Sigma)) datasets.push({ label: '-1σ', data: Array(labels.length).fill(zoneLimits.LCL_1Sigma), borderColor: zoneLineColor, borderDash: zoneLineDash, borderWidth: zoneLineWidth, pointRadius: 0, fill: false, type: 'line', order: 0 }); const violationPointsData = []; const violationTooltips = {}; violations.forEach((rules, subgroupIndex) => { if (rules && rules.length > 0) { let chartIndex = -1; if (title.includes('MR Chart')) { if (subgroupIndex > 0) { chartIndex = subgroupIndex - 1; } } else { chartIndex = subgroupIndex; } if (chartIndex >= 0 && chartIndex < labels.length && chartIndex < chartData.length) { const pointValue = chartData[chartIndex]; const pointLabel = labels[chartIndex]; if (!isNaN(pointValue)) { violationPointsData.push({ x: pointLabel, y: pointValue }); violationTooltips[pointLabel] = `点 ${subgroupIndex + 1}: 违反规则 ${rules.join(', ')}`; } } } }); if (violationPointsData.length > 0) { datasets.push({ label: '判异点', data: violationPointsData, pointBackgroundColor: '#e74c3c', pointBorderColor: '#c0392b', pointRadius: 6, pointHoverRadius: 8, type: 'scatter', showLine: false, order: 4 }); } chartInstances[canvasId] = new Chart(ctx, { type: 'line', data: { labels: labels, datasets: datasets.filter(ds => !ds.hidden) }, options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: title, font: { size: 18 } }, legend: { position: 'top', align: 'center', labels: { usePointStyle: true, boxWidth: 8, padding: 15 } }, tooltip: { mode: 'index', intersect: false, callbacks: { label: function (context) { const datasetLabel = context.dataset.label || ''; const yValue = context.parsed.y; let pointLabel = context.label; if (datasetLabel === '判异点' && context.dataset.type === 'scatter') { return violationTooltips[pointLabel] || `判异点: Y=${formatNum(yValue, 4)}`; } let outputLabel = datasetLabel.replace(/\s*\(.*\)/, ''); if (yValue !== null && yValue !== undefined && !isNaN(yValue)) { outputLabel += `: ${formatNum(yValue, 4)}`; if (datasetLabel === '数据点' && violationTooltips[pointLabel]) { outputLabel += ` (${violationTooltips[pointLabel]})`; } } return outputLabel; } } } }, scales: { x: { title: { display: true, text: (spcResults.n_subgroup_size === 1) ? '观测值序号' : '子组序号' }, grid: { display: false } }, y: { title: { display: true, text: '数值' }, beginAtZero: false, suggestedMin: suggestedMinY, suggestedMax: suggestedMaxY, grid: { color: '#e9e9e9' } } }, interaction: { mode: 'index', intersect: false }, hover: { mode: 'nearest', intersect: true }, animation: { duration: 0 } } }); }
        function createNormalProbabilityPlot(canvasId, title, data) {
            destroyChart(canvasId);
            const ctx = getElement(canvasId).getContext('2d');
            if (!data || data.length < 3) { ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height); ctx.textAlign = 'center'; ctx.font = '16px Arial'; ctx.fillText("数据点过少，无法生成正态概率图。", ctx.canvas.width / 2, 20); return; }
            const sortedDataForPlot = [...data].sort((a, b) => a - b);
            const n_plot_local = sortedDataForPlot.length;
            const plotDataPoints = [];
            for (let i = 0; i < n_plot_local; i++) { const p = (i + 1 - 0.375) / (n_plot_local + 0.25); const normalScore = standardNormalInv(p); if (isFinite(normalScore)) { plotDataPoints.push({ x: sortedDataForPlot[i], y: normalScore }); } }
            if (plotDataPoints.length < 2) { ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height); ctx.textAlign = 'center'; ctx.font = '16px Arial';ctx.fillText("计算正态得分后有效点过少。", ctx.canvas.width / 2, 20); return; }
            let referenceLineData = []; let visualPlotXMin, visualPlotXMax, suggestedMinY_ns_plot, suggestedMaxY_ns_plot;
            const dataForXAxis = plotDataPoints.map(p => p.x);
            let plotXMin_data = Math.min(...dataForXAxis); let plotXMax_data = Math.max(...dataForXAxis);
            const dataRangeX = plotXMax_data - plotXMin_data; let paddingX = dataRangeX * 0.10;
            if (paddingX === 0 && dataForXAxis.length > 0) paddingX = Math.abs(dataForXAxis[0] * 0.15) || 0.5; else if (paddingX === 0) paddingX = 0.5;
            visualPlotXMin = plotXMin_data - paddingX; visualPlotXMax = plotXMax_data + paddingX;
            visualPlotXMin = isFinite(visualPlotXMin) ? visualPlotXMin : (plotDataPoints.length > 0 ? plotDataPoints[0].x -1 : -5);
            visualPlotXMax = isFinite(visualPlotXMax) ? visualPlotXMax : (plotDataPoints.length > 0 ? plotDataPoints[plotDataPoints.length-1].x +1 : 5);
            if (visualPlotXMin >= visualPlotXMax && visualPlotXMax !== undefined) { visualPlotXMax = visualPlotXMin + 1; }
            if (plotDataPoints.length >= 2) { const q1_data = getQuantile(dataForXAxis, 0.25); const q3_data = getQuantile(dataForXAxis, 0.75); const z_q1_ref = standardNormalInv(0.25); const z_q3_ref = standardNormalInv(0.75); let slope_ref, intercept_ref; if (q3_data - q1_data !== 0) { slope_ref = (z_q3_ref - z_q1_ref) / (q3_data - q1_data); intercept_ref = z_q1_ref - slope_ref * q1_data; } else { slope_ref = 0; intercept_ref = (z_q1_ref + z_q3_ref) / 2; } referenceLineData = [ { x: visualPlotXMin, y: slope_ref * visualPlotXMin + intercept_ref }, { x: visualPlotXMax, y: slope_ref * visualPlotXMax + intercept_ref } ]; }
            const normalScoresAll = plotDataPoints.map(p => p.y); let minY_ns_local = Math.min(...normalScoresAll); let maxY_ns_local = Math.max(...normalScoresAll); if (referenceLineData.length === 2) { minY_ns_local = Math.min(minY_ns_local, referenceLineData[0].y, referenceLineData[1].y); maxY_ns_local = Math.max(maxY_ns_local, referenceLineData[0].y, referenceLineData[1].y); } if (isFinite(minY_ns_local) && isFinite(maxY_ns_local)) { if (maxY_ns_local > minY_ns_local) { const rangeY_ns = maxY_ns_local - minY_ns_local; const paddingY_ns = rangeY_ns * 0.10; suggestedMinY_ns_plot = minY_ns_local - paddingY_ns; suggestedMaxY_ns_plot = maxY_ns_local + paddingY_ns; } else { const paddingY_ns = Math.abs(maxY_ns_local * 0.1) || 0.5; suggestedMinY_ns_plot = minY_ns_local - paddingY_ns; suggestedMaxY_ns_plot = maxY_ns_local + paddingY_ns; } }
             chartInstances[canvasId] = new Chart(ctx, { type: 'scatter', data: { datasets: [ { label: '数据点 (观测值, 正态得分)', data: plotDataPoints, backgroundColor: '#3498db', borderColor: '#2980b9', showLine: false, pointRadius: 4, order: 1 }, { label: '拟合参考线', data: referenceLineData, borderColor: '#e74c3c', borderWidth: 2, pointRadius: 0, fill: false, type: 'line', order: 0, tension:0 } ] },
             options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: title, font: { size: 18 } }, legend: { position: 'top', align: 'center', labels: { usePointStyle: true, boxWidth: 8, padding: 15 } }, tooltip: { mode: 'nearest', intersect: true, callbacks: { label: function(context) { if(context.dataset.label === '数据点 (观测值, 正态得分)'){ const xVal = context.parsed.x; const yVal = context.parsed.y; return `观测值: ${formatNum(xVal, (spcResults && spcResults.decimalPlaces !== undefined) ? spcResults.decimalPlaces+1 : 3)}, 正态得分: ${formatNum(yVal, 3)}`; } return null; } } } }, scales: { x: { type: 'linear', position: 'bottom', title: { display: true, text: '观测数据值 (Data)' }, min: visualPlotXMin, max: visualPlotXMax, grid: { display: false } }, y: { type: 'linear', title: { display: true, text: '期望正态分位数 (Normal Score)' }, suggestedMin: suggestedMinY_ns_plot, suggestedMax: suggestedMaxY_ns_plot, grid: { color: '#e9e9e9' } } }, interaction: { mode: 'nearest', intersect: true, } } });
        }
        function createHistogram(canvasId, title, data, mean, stdDev, lsl, usl) {
           destroyChart(canvasId);
           const ctx = getElement(canvasId).getContext('2d');
           if (!data || data.length === 0) { console.warn("Histogram: No data provided."); ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height); ctx.textAlign = 'center'; ctx.font = '16px Arial'; ctx.fillText("无数据，无法生成直方图。", ctx.canvas.width / 2, ctx.canvas.height / 2); return; }
           const curveValid = !isNaN(mean) && !isNaN(stdDev) && stdDev > 0;
           const N_hist = data.length;
           let numBins;
           if (N_hist < 50) { numBins = Math.max(5, Math.min(10, Math.floor(Math.sqrt(N_hist)) + 2 )); }
           else if (N_hist >= 50 && N_hist <= 100) { numBins = 10; }
           else if (N_hist >= 101 && N_hist <= 200) { numBins = 12; }
           else if (N_hist >= 201 && N_hist <= 280) { numBins = 14; }
           else { numBins = 16; }
           numBins = Math.max(1, numBins);
           const dataMin_hist = Math.min(...data);
           const dataMax_hist = Math.max(...data);
           let dataRange_hist = dataMax_hist - dataMin_hist;
           let binWidth_hist; let actualDataMinForBins = dataMin_hist;
           if (dataRange_hist === 0) { binWidth_hist = Math.abs(dataMin_hist * 0.2) || 1; if (binWidth_hist <= 0) binWidth_hist = 1; numBins = 1; actualDataMinForBins = dataMin_hist - binWidth_hist / 2; }
           else { binWidth_hist = dataRange_hist / numBins; if (binWidth_hist <= 0) { binWidth_hist = (dataMax_hist - dataMin_hist > 0) ? (dataMax_hist - dataMin_hist) / Math.max(1, Math.floor(numBins/2)) : 1; if(binWidth_hist <=0) binWidth_hist = 1; numBins = Math.ceil((dataMax_hist - dataMin_hist) / binWidth_hist); if (numBins === 0 && data.length > 0) numBins = 1;}}
           if (numBins === 0 && data.length > 0) numBins = 1;
           const bins = Array(numBins).fill(0); const binMidPoints = []; const binEdges = [];
           let currentEdge = actualDataMinForBins;
           if (dataRange_hist === 0 && numBins === 1) { binEdges.push(actualDataMinForBins); binEdges.push(actualDataMinForBins + binWidth_hist); binMidPoints.push(actualDataMinForBins + binWidth_hist/2); }
           else { for (let i = 0; i < numBins; i++) { binEdges.push(currentEdge); binMidPoints.push(currentEdge + binWidth_hist / 2); currentEdge += binWidth_hist; } if (binEdges.length > 0 && currentEdge < dataMax_hist && binWidth_hist > 0) { binEdges.push(currentEdge); } else if (binEdges.length > 0 && binEdges[binEdges.length-1] < dataMax_hist && binWidth_hist > 0) { binEdges.push(binEdges[binEdges.length-1] + binWidth_hist); } else if (binEdges.length === 0 && data.length > 0) { binEdges.push(dataMin_hist - binWidth_hist/2); binEdges.push(dataMax_hist + binWidth_hist/2); binMidPoints.push(dataMin_hist); } }
           if (binEdges.length === numBins && numBins > 0) { binEdges.push(binEdges[numBins-1] + binWidth_hist); }
           data.forEach(value => { let binIndex = -1; for (let i = 0; i < numBins; i++) { if (value >= binEdges[i] && (value < binEdges[i+1] || (i === numBins - 1 && value <= binEdges[i+1]))) { binIndex = i; break; } } if (binIndex !== -1 && binIndex < numBins && bins[binIndex] !== undefined) { bins[binIndex]++; } });
           let xAxisMin = dataMin_hist; let xAxisMax = dataMax_hist;
           if (curveValid) { const curveSpan = 3.5 * stdDev; xAxisMin = Math.min(xAxisMin, mean - curveSpan); xAxisMax = Math.max(xAxisMax, mean + curveSpan); }
           if (!isNaN(lsl)) xAxisMin = Math.min(xAxisMin, lsl); if (!isNaN(usl)) xAxisMax = Math.max(xAxisMax, usl);
           const overallRangeDisplay = xAxisMax - xAxisMin; const padding = overallRangeDisplay === 0 ? 0.5 : overallRangeDisplay * 0.05;
           xAxisMin -= padding; xAxisMax += padding;
           if (xAxisMin >= xAxisMax && xAxisMax !== undefined) { xAxisMax = xAxisMin + 1; }
           const datasets = [{ type: 'bar', label: '频数', data: bins.map((count, i) => ({x: binMidPoints[i], y: count})), backgroundColor: 'rgba(54, 162, 235, 0.6)', borderColor: 'rgba(54, 162, 235, 1)', borderWidth: 1, barThickness: binWidth_hist > 0 ? (getElement(canvasId).width / ((xAxisMax - xAxisMin) / binWidth_hist)) * 0.9 : 30, order: 1 }];
           if (curveValid) { const curvePoints = []; const numCurvePoints = 100; const step = (xAxisMax - xAxisMin) / numCurvePoints; for (let i = 0; i <= numCurvePoints; i++) { const x_curve = xAxisMin + i * step; const pdfValue = jStat.normal.pdf(x_curve, mean, stdDev); curvePoints.push({ x: x_curve, y: pdfValue * N_hist * (binWidth_hist > 0 ? binWidth_hist : 1) }); } datasets.push({ type: 'line', label: '正态分布曲线', data: curvePoints, borderColor: '#e74c3c', backgroundColor: 'transparent', borderWidth: 2, pointRadius: 0, yAxisID: 'y', tension: 0.1, order: 0 }); }
           else { title = title.replace('(带正态曲线)', '(无正态曲线)'); }
           const annotation_elements = []; const specLabelFontSize = 10; const specLabelColor = 'darkgreen'; const specLineColor = 'green'; const dp_spec_hist = (spcResults && spcResults.decimalPlaces !== undefined) ? spcResults.decimalPlaces + 1 : 3;
           if (!isNaN(lsl)) { annotation_elements.push({ type: 'line', scaleID:'x', value: lsl, borderColor: specLineColor, borderWidth: 2, borderDash: [6, 6], label: { content: `LSL: ${formatNum(lsl, dp_spec_hist)}`, display: true, position: "start", rotation: -90, yAdjust: -20, font: {size: specLabelFontSize, weight:'bold'}, color: specLabelColor, backgroundColor: 'rgba(255,255,255,0.7)'} }); }
           if (!isNaN(usl)) { annotation_elements.push({ type: 'line', scaleID:'x', value: usl, borderColor: specLineColor, borderWidth: 2, borderDash: [6, 6], label: { content: `USL: ${formatNum(usl, dp_spec_hist)}`, display: true, position: "start", rotation: -90, yAdjust: -20, font: {size: specLabelFontSize, weight:'bold'}, color: specLabelColor, backgroundColor: 'rgba(255,255,255,0.7)'} }); }
           chartInstances[canvasId] = new Chart(ctx, { data: { datasets: datasets }, options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: title, font: { size: 18 } }, legend: { position: 'top', align: 'center', labels: { usePointStyle: true, boxWidth: 8, padding: 15 } }, tooltip: { mode: 'index', intersect: false }, annotation: { drawTime: 'afterDatasetsDraw', annotations: annotation_elements } }, scales: { x: { type: 'linear', title: { display: true, text: '数据值' }, min: xAxisMin, max: xAxisMax,  grid: { display: false } }, y: { title: { display: true, text: '频数 / 曲线高度' }, beginAtZero: true, grid: { color: '#e9e9e9' } } } } });
        }

        // --- Main Processing Function ---
        function processDataAndGenerate(isHistoricalLoad = false) {
            console.log("processDataAndGenerate called. Historical load:", isHistoricalLoad);
            try {
                clearError();
                disableButton('generateButton');
                disableExportButtons(true);
                showStatus('正在处理数据和生成图表...', 0, 'statusMessage', 'info');
                getElement('dataTableSection').style.display = 'none';
                getElement('resultsSection').style.display = 'none';
                getElement('chartsSection').style.display = 'none';
                getElement('exportSection').style.display = 'none';
                Object.keys(chartInstances).forEach(id => destroyChart(id));

                const dataInputMethod = document.querySelector('input[name="dataInputMethod"]:checked').value;
                const n_sg_local = getInt('subgroupSize');
                const usl_local = getNumber('uslInput');
                const lsl_local = getNumber('lslInput');
                const target_local = getNumber('target');

                if (isNaN(n_sg_local) || n_sg_local < 1) { showError("子组大小必须是大于等于 1 的整数。"); throw new Error("Invalid subgroup size.");}
                let inputDataArr = [];
                if (dataInputMethod === 'random') {
                    const numPoints = getInt('numPoints');
                    const min = getNumber('dataMin');
                    const max = getNumber('dataMax');
                    const decimals = getInt('decimalPlaces');
                    if (isNaN(numPoints) || numPoints < 1 || isNaN(min) || isNaN(max) || isNaN(decimals) || decimals < 0) { showError("随机数据参数无效。"); throw new Error("Invalid random data params.");}
                    if (min >= max) { showError("最小值必须小于最大值。"); throw new Error("Min >= Max for random data."); }
                    const minRequiredPoints = (n_sg_local > 1) ? Math.max(n_sg_local * 2, 2) : 2;
                    if (numPoints < minRequiredPoints) { showError(`数据点总数 (${numPoints}) 不足，至少需要 ${minRequiredPoints}。`); throw new Error("Not enough random points.");}
                    inputDataArr = generateRandomData(numPoints, min, max, decimals);
                } else {
                    const pastedText = getValue('pastedData');
                    if (!pastedText.trim()) { showError("请粘贴有效的数据。"); throw new Error("Pasted data is empty.");}
                    inputDataArr = parsePastedData(pastedText);
                    if (inputDataArr === null) { throw new Error("Pasted data parsing failed.");}
                     const minRequiredPoints = (n_sg_local > 1) ? Math.max(n_sg_local * 2, 2) : 2;
                    if (inputDataArr.length < minRequiredPoints) { showError(`数据点数量 (${inputDataArr.length}) 不足，至少需要 ${minRequiredPoints}。`); throw new Error("Not enough pasted points.");}
                }
                if (!isNaN(usl_local) && !isNaN(lsl_local) && lsl_local >= usl_local) { showError("规格下限 (LSL) 必须小于规格上限 (USL)。"); throw new Error("LSL >= USL."); }

                const results = calculateSPC(inputDataArr, n_sg_local, usl_local, lsl_local, target_local);

                if (results) {
                    if (rawData && rawData.length >= 2) {
                        const adResults = calculateExcelAndersonDarling(rawData);
                        results.andersonDarling = adResults;
                    } else {
                        results.andersonDarling = { errorMessage: "原始数据不足或无效 ("+ (rawData ? rawData.length : 0) +" points), 无法进行AD检验。", pValueString:"N/A", interpretation:"" };
                    }
                    displayDataTable(subgroupData, n_sg_local);
                    displayResults(results);

                    getElement('chartsSection').style.display = 'block';
                    const chartLabels = subgroupData.map(sg => sg.id.toString());
                    const chart1Data = subgroupData.map(sg => sg.mean);
                    const chart1Limits = (results.chartType === 'I-MR') ? results.controlLimits.I : results.controlLimits.Xbar;
                    const chart1Title = (results.chartType === 'I-MR') ? '单值控制图 (I Chart)' : '均值控制图 (X̄ Chart)';
                    createControlChart('controlChart1', chart1Title, chart1Data, chartLabels, chart1Limits.CL, chart1Limits.UCL, chart1Limits.LCL, results.ruleViolations.chart1, chart1Limits);

                    let chart2Data, chart2Limits, chart2Title, chart2Labels, chart2Violations;
                    if (results.chartType === 'I-MR') { chart2Data = subgroupData.map(sg => sg.range).slice(1); chart2Labels = chartLabels.slice(1); chart2Limits = results.controlLimits.MR; chart2Title = '移动极差图 (MR Chart)'; chart2Violations = results.ruleViolations.chart2; createControlChart('controlChart2', chart2Title, chart2Data, chart2Labels, chart2Limits.CL, chart2Limits.UCL, chart2Limits.LCL, chart2Violations); }
                    else if (results.chartType === 'Xbar-R') { chart2Data = subgroupData.map(sg => sg.range); chart2Labels = chartLabels; chart2Limits = results.controlLimits.R; chart2Title = '极差控制图 (R Chart)'; chart2Violations = results.ruleViolations.chart2; createControlChart('controlChart2', chart2Title, chart2Data, chart2Labels, chart2Limits.CL, chart2Limits.UCL, chart2Limits.LCL, chart2Violations); }
                    else { chart2Data = subgroupData.map(sg => sg.stddev); chart2Labels = chartLabels; chart2Limits = results.controlLimits.S; chart2Title = '标准差控制图 (S Chart)'; chart2Violations = results.ruleViolations.chart2; createControlChart('controlChart2', chart2Title, chart2Data, chart2Labels, chart2Limits.CL, chart2Limits.UCL, chart2Limits.LCL, chart2Violations); }

                    createHistogram('histogramChart', '数据分布直方图 (带正态曲线)', rawData, results.xbarbar, results.overallStdDev, lsl_local, usl_local);
                    createNormalProbabilityPlot('qqPlotChart', '正态概率图', rawData);

                    if (!isHistoricalLoad) {
                        const currentState = {
                            metaMeasuredProduct: getValue('metaMeasuredProduct'),
                            metaProductCharacteristic: getValue('metaProductCharacteristic'),
                            metaMeasuringInstrument: getValue('metaMeasuringInstrument'),
                            metaMeasurementPersonnel: getValue('metaMeasurementPersonnel'),
                            metaDataSource: getValue('metaDataSource'),
                            metaAnalysisPersonnel: getValue('metaAnalysisPersonnel'),
                            metaAnalysisDate: getValue('metaAnalysisDate'),
                            dataInputMethod: document.querySelector('input[name="dataInputMethod"]:checked').value,
                            numPoints: getValue('numPoints'), dataMin: getValue('dataMin'), dataMax: getValue('dataMax'),
                            decimalPlaces: getValue('decimalPlaces'), pastedData: getValue('pastedData'),
                            subgroupSize: getValue('subgroupSize'),
                            usl: getValue('uslInput'), lsl: getValue('lslInput'), target: getValue('target'),
                            nelsonRules: []
                        };
                        getElement('nelsonRules').querySelectorAll('input[type="checkbox"]').forEach(cb => {
                            currentState.nelsonRules.push({ value: cb.value, checked: cb.checked });
                        });
                        addAnalysisToHistory(currentState);
                    }
                    showStatus('图表与分析已生成!', 3000);
                } else {
                    console.error("calculateSPC returned null or falsy.");
                    showStatus('计算失败，请检查输入或错误信息。', 5000, 'statusMessage', 'error');
                }
            } catch (e) {
                showError(`发生意外错误: ${e.message}. 请检查控制台。`);
                console.error("Unhandled error in processDataAndGenerate:", e, e.stack);
                showStatus('发生错误!', 5000, 'statusMessage', 'error');
            } finally {
                enableButton('generateButton');
            }
        }

        // --- Export Functions ---
        function getResultsAsText() {
            if (!spcResults || Object.keys(spcResults).length === 0) return "请先生成分析结果。";
            let text = "SPC 与正态性分析报告\n==========================\n\n";
            text += `测量产品: ${getValue('metaMeasuredProduct') || 'N/A'}\n`;
            text += `产品特性: ${getValue('metaProductCharacteristic') || 'N/A'}\n`;
            text += `测量仪器、仪器编号: ${getValue('metaMeasuringInstrument') || 'N/A'}\n`;
            text += `测量人员: ${getValue('metaMeasurementPersonnel') || 'N/A'}\n`;
            text += `数据来源: ${getValue('metaDataSource') || 'N/A'}\n`;
            text += `分析人员: ${getValue('metaAnalysisPersonnel') || 'N/A'}\n`;
            text += `分析日期: ${getValue('metaAnalysisDate') || new Date().toLocaleDateString()}\n\n`;
            text += `子组大小 (n): ${spcResults.n_subgroup_size}\n`;
            text += `子组数量 (k): ${subgroupData.length}\n`;
            text += `数据点总数 (N): ${rawData.length}\n`;
            text += `USL: ${formatNum(spcResults.usl, spcResults.decimalPlaces+2)}, LSL: ${formatNum(spcResults.lsl, spcResults.decimalPlaces+2)}, Target: ${formatNum(spcResults.target, spcResults.decimalPlaces+2)}\n\n`;
            text += "统计摘要:\n";
            text += `  整体均值 (X̄̄): ${formatNum(spcResults.xbarbar, spcResults.decimalPlaces+2)}\n`;
            if (spcResults.chartType === 'I-MR') { text += `  平均移动极差 (MR̄): ${formatNum(spcResults.mrbar, spcResults.decimalPlaces+2)}\n`; }
            else if (spcResults.chartType === 'Xbar-R') { text += `  平均极差 (R̄): ${formatNum(spcResults.rbar, spcResults.decimalPlaces+2)}\n`; }
            else { text += `  平均标准差 (s̄): ${formatNum(spcResults.sbar, spcResults.decimalPlaces+3)}\n`; }
            text += `  过程标准差估计 (σ̂): ${formatNum(spcResults.sigmaHat, spcResults.decimalPlaces+3)}\n`;
            text += `  整体标准差 (s): ${formatNum(spcResults.overallStdDev, spcResults.decimalPlaces+3)}\n\n`;
            if (spcResults.andersonDarling) {
                const ad = spcResults.andersonDarling;
                text += "正态性检验 (AD Excel 方法):\n";
                if (ad.errorMessage) { text += `  错误: ${ad.errorMessage}\n`; }
                else { text += `  AD (Excel A²): ${formatNum(ad.aSquaredExcel, 6)}\n`; text += `  AD* (调整后): ${formatNum(ad.aSquaredAdjusted, 6)}\n`; text += `  P 值: ${ad.pValueString}\n`; text += `  解释: ${ad.interpretation}\n`; }
                text += "\n";
            }
            return text;
        }

        function getTableData(tableId) { const table = document.getElementById(tableId); if (!table) return { headers: [], rows: [] }; const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent); const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => Array.from(tr.querySelectorAll('td')).map(td => td.textContent) ); return { headers, rows }; }

        function exportToExcel() {
            if (!spcResults || !subgroupData || subgroupData.length === 0) { alert("没有可导出的数据。"); return; }
            disableExportButtons(true); showStatus('正在生成 Excel 文件...');
            try {
                const wb = XLSX.utils.book_new();
                const dp_excel = spcResults.decimalPlaces ?? 2;
                const dp_stats_excel = dp_excel + 2;
                const dp_cap_excel = 3;

                const metaInfoData = [
                    ["项目", "内容"],
                    ["测量产品", getValue('metaMeasuredProduct') || 'N/A'],
                    ["产品特性", getValue('metaProductCharacteristic') || 'N/A'],
                    ["测量仪器、仪器编号", getValue('metaMeasuringInstrument') || 'N/A'],
                    ["测量人员", getValue('metaMeasurementPersonnel') || 'N/A'],
                    ["数据来源", getValue('metaDataSource') || 'N/A'],
                    ["分析人员", getValue('metaAnalysisPersonnel') || 'N/A'],
                    ["分析日期", getValue('metaAnalysisDate') || new Date().toLocaleDateString()]
                ];
                const wsMeta = XLSX.utils.aoa_to_sheet(metaInfoData);
                wsMeta['!cols'] = [{wch: 25}, {wch: 40}];
                XLSX.utils.book_append_sheet(wb, wsMeta, "分析元信息");

                const summaryData = [
                    ["参数", "值", "备注"],
                    ["子组大小 (n)", spcResults.n_subgroup_size],
                    ["子组数量 (k)", subgroupData.length],
                    ["数据点总数 (N)", rawData.length],
                    ["规格上限 (USL)", formatNum(spcResults.usl, dp_stats_excel)],
                    ["规格下限 (LSL)", formatNum(spcResults.lsl, dp_stats_excel)],
                    ["目标值 (Target)", formatNum(spcResults.target, dp_stats_excel)],
                    ["计算用目标值 (T)", `${formatNum(spcResults.capability.T_used, dp_stats_excel)} ${isNaN(spcResults.target) ? '(默认规格中心)' : ''}`],
                    [],
                    ["统计量", "值", "公式/备注"],
                    ["整体均值 (X̄̄)", formatNum(spcResults.xbarbar, dp_stats_excel), "ΣX̄ / k"],
                ];
                if (spcResults.chartType === 'I-MR') {
                    summaryData.push(["平均移动极差 (MR̄)", formatNum(spcResults.mrbar, dp_stats_excel), "Σ|Xᵢ - Xᵢ₋₁| / (N-1)"]);
                    summaryData.push(["过程标准差估计 (σ̂)", formatNum(spcResults.sigmaHat, dp_stats_excel + 1), `MR̄ / d₂ (d₂=${spcConstants.I.d2})`]);
                } else if (spcResults.chartType === 'Xbar-R') {
                    summaryData.push(["平均极差 (R̄)", formatNum(spcResults.rbar, dp_stats_excel), "ΣR / k"]);
                    summaryData.push(["过程标准差估计 (σ̂)", formatNum(spcResults.sigmaHat, dp_stats_excel + 1), `R̄ / d₂ (d₂=${getConstant('R', 'd2', spcResults.n_subgroup_size)})`]);
                } else {
                    summaryData.push(["平均标准差 (s̄)", formatNum(spcResults.sbar, dp_stats_excel + 1), "Σs / k"]);
                    summaryData.push(["过程标准差估计 (σ̂)", formatNum(spcResults.sigmaHat, dp_stats_excel + 1), `s̄ / c₄ (c₄=${getConstant('S', 'c4', spcResults.n_subgroup_size)})`]);
                }
                summaryData.push(["整体标准差 (s)", formatNum(spcResults.overallStdDev, dp_stats_excel + 1), "Overall Sample Std Dev (N-1)"]);
                summaryData.push([]);

                if (spcResults.andersonDarling) {
                    const ad = spcResults.andersonDarling;
                    summaryData.push(["正态性检验 (AD Excel)", "", ""]);
                    if (ad.errorMessage) { summaryData.push(["AD 错误", ad.errorMessage, ""]); }
                    else { summaryData.push(["AD (Excel A²)", formatNum(ad.aSquaredExcel, 6), ""], ["AD* (调整后)", formatNum(ad.aSquaredAdjusted, 6), ""], ["P 值 (AD)", ad.pValueString, ""], ["AD 解释", ad.interpretation, ""]);}
                }
                summaryData.push([]);

                const chart1Name_excel = spcResults.chartType === 'I-MR' ? 'I Chart' : 'X̄ Chart';
                const chart2Name_excel = spcResults.chartType === 'I-MR' ? 'MR Chart' : (spcResults.chartType === 'Xbar-R' ? 'R Chart' : 'S Chart');
                const cl1_excel = (spcResults.chartType === 'I-MR') ? spcResults.controlLimits.I : spcResults.controlLimits.Xbar;
                const cl2_excel = (spcResults.chartType === 'I-MR') ? spcResults.controlLimits.MR : (spcResults.chartType === 'Xbar-R' ? spcResults.controlLimits.R : spcResults.controlLimits.S);
                summaryData.push([`${chart1Name_excel} 控制限`, "值"], ["UCL", formatNum(cl1_excel.UCL, dp_stats_excel + 1)], ["CL", formatNum(cl1_excel.CL, dp_stats_excel)], ["LCL", formatNum(cl1_excel.LCL, dp_stats_excel + 1)], []);
                summaryData.push([`${chart2Name_excel} 控制限`, "值"], ["UCL", formatNum(cl2_excel.UCL, dp_stats_excel + 1)], ["CL", formatNum(cl2_excel.CL, dp_stats_excel)], ["LCL", formatNum(cl2_excel.LCL, dp_stats_excel + 1)], []);

                summaryData.push(["过程能力指数", "值", "备注"]);
                if (spcResults.capability.error) { summaryData.push(["能力分析错误", spcResults.capability.error, ""]); }
                else { const capFmt = (val, warn) => warn ? 'N/A' : formatNum(val, dp_cap_excel); const ppmFmt = (val, warn) => warn ? 'N/A' : formatPPM(val); summaryData.push(["Cp", capFmt(spcResults.capability.Cp, spcResults.capability.warning_within), spcResults.capability.warning_within || '(USL-LSL)/(6σ̂)'], ["Cpk", capFmt(spcResults.capability.Cpk, spcResults.capability.warning_within), spcResults.capability.warning_within || 'min(Cpu,Cpl), based on σ̂'], ["Pp", capFmt(spcResults.capability.Pp, spcResults.capability.warning_overall), spcResults.capability.warning_overall || '(USL-LSL)/(6s)'], ["Ppk", capFmt(spcResults.capability.Ppk, spcResults.capability.warning_overall), spcResults.capability.warning_overall || 'min(Ppu,Ppl), based on s'], ["σ̂_cpm", formatNum(spcResults.capability.sigma_Cpm, dp_cap_excel + 2), spcResults.capability.warning_cpm || 'Std Dev from Target'], ["Cpm", capFmt(spcResults.capability.Cpm, spcResults.capability.warning_cpm), spcResults.capability.warning_cpm || '(USL-LSL)/(6σ̂_cpm)'], ["PPM (基于 σ̂)", ppmFmt(spcResults.capability.ppmWithin, spcResults.capability.warning_within), spcResults.capability.warning_within || ''], ["PPM (基于 s)", ppmFmt(spcResults.capability.ppmOverall, spcResults.capability.warning_overall), spcResults.capability.warning_overall || '']); }

                const wsSummary = XLSX.utils.aoa_to_sheet(summaryData);
                wsSummary['!cols'] = [{wch: 25}, {wch: 15}, {wch: 30}];
                XLSX.utils.book_append_sheet(wb, wsSummary, "结果摘要");

                const dataTableExport = getTableData('dataTable');
                const wsDataTable = XLSX.utils.aoa_to_sheet([dataTableExport.headers, ...dataTableExport.rows]);
                XLSX.utils.book_append_sheet(wb, wsDataTable, "详细数据");

                const excelFileName = `SPC_Analysis_${getValue('metaAnalysisDate') || new Date().toISOString().slice(0,10).replace(/-/g,'')}.xlsx`;
                XLSX.writeFile(wb, excelFileName);
                showStatus('Excel 文件已生成!');

            } catch (error) { console.error("导出 Excel 时出错:", error); alert("导出 Excel 文件失败。"); showError('导出 Excel 文件失败。'); }
            finally { enableExportButtons(); }
        }
        function exportToMarkdown() {
            if (!spcResults || !subgroupData || subgroupData.length === 0) { alert("没有可导出的数据。"); return; }
            disableExportButtons(true); showStatus('正在生成 Markdown 文件...');
            try {
                let mdContent = `# SPC 与正态性分析报告\n\n`;
                const n_md = spcResults.n_subgroup_size; const dp_md = spcResults.decimalPlaces ?? 2; const dp_stats_md = dp_md + 2; const dp_cap_md = 3;

                mdContent += `## 1. 分析元信息\n\n`;
                mdContent += `*   **测量产品:** ${getValue('metaMeasuredProduct') || 'N/A'}\n`;
                mdContent += `*   **产品特性:** ${getValue('metaProductCharacteristic') || 'N/A'}\n`;
                mdContent += `*   **测量仪器、仪器编号:** ${getValue('metaMeasuringInstrument') || 'N/A'}\n`;
                mdContent += `*   **测量人员:** ${getValue('metaMeasurementPersonnel') || 'N/A'}\n`;
                mdContent += `*   **数据来源:** ${getValue('metaDataSource') || 'N/A'}\n`;
                mdContent += `*   **分析人员:** ${getValue('metaAnalysisPersonnel') || 'N/A'}\n`;
                mdContent += `*   **分析日期:** ${getValue('metaAnalysisDate') || new Date().toLocaleDateString()}\n\n`;

                mdContent += `## 2. 基本信息与参数\n\n`; mdContent += `*   **子组大小 (n):** ${n_md}\n`; mdContent += `*   **子组数量 (k):** ${subgroupData.length}\n`; mdContent += `*   **数据点总数 (N):** ${rawData.length}\n`; mdContent += `*   **规格上限 (USL):** ${formatNum(spcResults.usl, dp_stats_md)}\n`; mdContent += `*   **规格下限 (LSL):** ${formatNum(spcResults.lsl, dp_stats_md)}\n`; mdContent += `*   **目标值 (Target):** ${formatNum(spcResults.target, dp_stats_md)}\n`; mdContent += `*   **计算用目标值 (T):** ${formatNum(spcResults.capability.T_used, dp_stats_md)} ${isNaN(spcResults.target) ? '(默认规格中心)' : ''}\n\n`;
                mdContent += `## 3. 统计结果摘要\n\n`; mdContent += `| 统计量 | 值 | 备注 |\n`; mdContent += `| :--- | :--- | :--- |\n`; mdContent += `| 整体均值 (X̄̄) | ${formatNum(spcResults.xbarbar, dp_stats_md)} | ΣX̄ / k |\n`; if (spcResults.chartType === 'I-MR') { mdContent += `| 平均移动极差 (MR̄) | ${formatNum(spcResults.mrbar, dp_stats_md)} | Σ|Xᵢ - Xᵢ₋₁| / (N-1) |\n`; mdContent += `| σ̂ | ${formatNum(spcResults.sigmaHat, dp_stats_md + 1)} | MR̄ / d₂ |\n`; } else if (spcResults.chartType === 'Xbar-R') { mdContent += `| 平均极差 (R̄) | ${formatNum(spcResults.rbar, dp_stats_md)} | ΣR / k |\n`; mdContent += `| σ̂ | ${formatNum(spcResults.sigmaHat, dp_stats_md + 1)} | R̄ / d₂ |\n`; } else { mdContent += `| 平均标准差 (s̄) | ${formatNum(spcResults.sbar, dp_stats_md + 1)} | Σs / k |\n`; mdContent += `| σ̂ | ${formatNum(spcResults.sigmaHat, dp_stats_md + 1)} | s̄ / c₄ |\n`; } mdContent += `| 整体标准差 (s) | ${formatNum(spcResults.overallStdDev, dp_stats_md + 1)} | Overall Sample Std Dev (N-1) |\n`;
                if (spcResults.andersonDarling) { const ad = spcResults.andersonDarling; mdContent += `| **正态性 (AD Excel)** |  |  |\n`; if (ad.errorMessage) { mdContent += `| AD 错误 | ${ad.errorMessage.replace(/\|/g, '\\|')} |  |\n`; } else { mdContent += `| AD (Excel A²) | ${formatNum(ad.aSquaredExcel, 6)} |  |\n`; mdContent += `| AD* | ${formatNum(ad.aSquaredAdjusted, 6)} |  |\n`; mdContent += `| P 值 (AD) | ${ad.pValueString.replace(/\|/g, '\\|')} |  |\n`; mdContent += `| AD 解释 | ${ad.interpretation.replace(/\|/g, '\\|')} |  |\n`; } } mdContent += '\n';
                mdContent += `## 4. 控制限\n\n`; const chart1Name_md = spcResults.chartType === 'I-MR' ? 'I Chart' : 'X̄ Chart'; const chart2Name_md = spcResults.chartType === 'I-MR' ? 'MR Chart' : (spcResults.chartType === 'Xbar-R' ? 'R Chart' : 'S Chart'); const cl1_md = (spcResults.chartType === 'I-MR') ? spcResults.controlLimits.I : spcResults.controlLimits.Xbar; const cl2_md = (spcResults.chartType === 'I-MR') ? spcResults.controlLimits.MR : (spcResults.chartType === 'Xbar-R' ? spcResults.controlLimits.R : spcResults.controlLimits.S); mdContent += `### ${chart1Name_md}\n\n| 控制限 | 值 |\n| :----- | :--- |\n`; mdContent += `| UCL | ${formatNum(cl1_md.UCL, dp_stats_md + 1)} |\n| CL  | ${formatNum(cl1_md.CL, dp_stats_md)} |\n| LCL | ${formatNum(cl1_md.LCL, dp_stats_md + 1)} |\n\n`; mdContent += `### ${chart2Name_md}\n\n| 控制限 | 值 |\n| :----- | :--- |\n`; mdContent += `| UCL | ${formatNum(cl2_md.UCL, dp_stats_md + 1)} |\n| CL  | ${formatNum(cl2_md.CL, dp_stats_md)} |\n| LCL | ${formatNum(cl2_md.LCL, dp_stats_md + 1)} |\n\n`;
                mdContent += `## 5. 过程能力指数\n\n`; if (spcResults.capability.error) { mdContent += `**错误:** ${spcResults.capability.error}\n\n`; } else { const capFormat_md = (val, warn) => warn ? 'N/A' : formatNum(val, dp_cap_md); const ppmFormat_md = (val, warn) => warn ? 'N/A' : formatPPM(val); mdContent += `| 指数 | 值 | 备注 |\n`; mdContent += `| :--- | :--- | :--- |\n`; mdContent += `| Cp | ${capFormat_md(spcResults.capability.Cp, spcResults.capability.warning_within)} | ${spcResults.capability.warning_within || '(USL-LSL)/(6σ̂)'} |\n`; mdContent += `| Cpk | ${capFormat_md(spcResults.capability.Cpk, spcResults.capability.warning_within)} | ${spcResults.capability.warning_within || 'min(Cpu,Cpl), based on σ̂'} |\n`; mdContent += `| Pp | ${capFormat_md(spcResults.capability.Pp, spcResults.capability.warning_overall)} | ${spcResults.capability.warning_overall || '(USL-LSL)/(6s)'} |\n`; mdContent += `| Ppk | ${capFormat_md(spcResults.capability.Ppk, spcResults.capability.warning_overall)} | ${spcResults.capability.warning_overall || 'min(Ppu,Ppl), based on s'} |\n`; mdContent += `| σ̂_cpm | ${formatNum(spcResults.capability.sigma_Cpm, dp_cap_md + 2)} | ${spcResults.capability.warning_cpm || 'Std Dev from Target'} |\n`; mdContent += `| Cpm | ${capFormat_md(spcResults.capability.Cpm, spcResults.capability.warning_cpm)} | ${spcResults.capability.warning_cpm || '(USL-LSL)/(6σ̂_cpm)'} |\n`; mdContent += `| PPM (基于 σ̂) | ${ppmFormat_md(spcResults.capability.ppmWithin, spcResults.capability.warning_within)} | ${spcResults.capability.warning_within || ''} |\n`; mdContent += `| PPM (基于 s) | ${ppmFormat_md(spcResults.capability.ppmOverall, spcResults.capability.warning_overall)} | ${spcResults.capability.warning_overall || ''} |\n\n`; }

                mdContent += `## 6. 详细数据与子组统计\n\n`;
                const dataTableForMD = getTableData('dataTable');
                if (dataTableForMD.headers.length > 0) {
                    mdContent += `| ${dataTableForMD.headers.join(" | ")} |\n`;
                    mdContent += `| ${dataTableForMD.headers.map(() => "---").join(" | ")} |\n`;
                    dataTableForMD.rows.forEach(row => {
                        mdContent += `| ${row.map(cell => String(cell).replace(/\|/g, '\\|')).join(" | ")} |\n`;
                    });
                    mdContent += '\n';
                }

                const blob = new Blob([mdContent], { type: 'text/markdown;charset=utf-8' });
                const mdFileName = `SPC_Analysis_${getValue('metaAnalysisDate') || new Date().toISOString().slice(0,10).replace(/-/g,'')}.md`;
                saveAs(blob, mdFileName);
                showStatus('Markdown 文件已生成!');
            } catch (error) { console.error("导出 Markdown 时出错:", error); alert("导出 Markdown 文件失败。"); showError('导出 Markdown 文件失败。'); } finally { enableExportButtons(); }
        }
        async function exportToPDF() {
             if (!spcResults || !subgroupData || subgroupData.length === 0) { alert("没有可导出的数据。"); return; }
             disableExportButtons(true); showStatus('正在生成 PDF 文件 (横板)... (可能需要一点时间)', 0, 'statusMessage', 'info');
             const elementToExport = getElement('mainContainer'); // Use mainContainer which wraps tool-section's content
             const bodyEl = document.body;

             bodyEl.classList.add('pdf-export-mode'); window.scrollTo(0, 0);
             const opt = { margin: [10, 5, 10, 5], filename: `SPC_Analysis_${getValue('metaAnalysisDate') || new Date().toISOString().slice(0,10).replace(/-/g,'')}_Landscape.pdf`, image: { type: 'jpeg', quality: 0.92 }, html2canvas:  { scale: 2, logging: false, useCORS: true, scrollX: 0, scrollY: -window.scrollY, windowWidth: document.documentElement.scrollWidth, windowHeight: document.documentElement.scrollHeight  }, jsPDF: { unit: 'mm', format: 'a4', orientation: 'landscape' }, pagebreak: { mode: ['css', 'avoid-all'], before: '.section h2, .results-grid + .result-item.full-width, #resultsSummary > div:nth-last-child(1)' } };
             html2pdf().from(elementToExport).set(opt).save()
                 .catch((error) => { console.error("导出 PDF 时出错:", error); alert("导出 PDF 文件失败。"); showError('导出 PDF 文件失败。'); })
                 .finally(() => { bodyEl.classList.remove('pdf-export-mode'); enableExportButtons(); if (!getElement('errorMessage').textContent) { showStatus('PDF 文件已生成!'); } });
        }
        function exportToWord() {
            if (!spcResults || !subgroupData || subgroupData.length === 0) { alert("没有可导出的数据。"); return; }
            disableExportButtons(true); showStatus('正在生成 Word 文件...');

            try {
                let htmlContent = `
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head><meta charset="UTF-8"><title>SPC 分析报告</title>
                    <style>
                        body { font-family: SimSun, sans-serif; font-size: 10.5pt; margin: 20px; }
                        h1, h2, h3 { font-family: SimHei, sans-serif; margin-bottom: 0.5em; page-break-after: avoid; }
                        h1 { font-size: 16pt; text-align: center; margin-bottom: 1em; }
                        h2 { font-size: 14pt; margin-top: 1.5em; border-bottom: 1px solid #ccc; padding-bottom: 0.2em;}
                        h3 { font-size: 12pt; margin-top: 1em; }
                        table { border-collapse: collapse; width: 95%; margin-bottom: 1em; font-size: 9pt; page-break-inside: avoid; margin-left:auto; margin-right:auto;}
                        th, td { border: 1px solid black; padding: 4px; text-align: left; vertical-align: top; }
                        th { background-color: #f2f2f2; font-weight: bold; text-align: center;}
                        .meta-table td:first-child { font-weight: bold; width: 180px; background-color: #f2f2f2;}
                        .results-grid-export { margin-top:10px; } /* Not directly used from here, but kept for consistency */
                        .result-item-export { border: 1px solid #ccc; padding: 5px; margin-bottom: 5px; background-color: #f9f9f9;}
                        .result-item-export strong { color: #0056b3; }
                        .result-item-export code { font-size: 0.9em; color: #555; display:block; margin-top:2px; }
                        .chart-image-container { text-align: center; page-break-inside: avoid; margin-top:10px; margin-bottom:20px; }
                        .chart-image { max-width: 550px; height: auto; display: block; margin: 0 auto; border: 1px solid #ddd;}
                    </style>
                    </head>
                    <body>
                        <h1>计量控制图与正态性分析报告</h1>

                        <h2>1. 分析元信息</h2>
                        <table class="meta-table">
                            <tr><td>测量产品:</td><td>${escapeHtml(getValue('metaMeasuredProduct')) || 'N/A'}</td></tr>
                            <tr><td>产品特性:</td><td>${escapeHtml(getValue('metaProductCharacteristic')) || 'N/A'}</td></tr>
                            <tr><td>测量仪器、仪器编号:</td><td>${escapeHtml(getValue('metaMeasuringInstrument')) || 'N/A'}</td></tr>
                            <tr><td>测量人员:</td><td>${escapeHtml(getValue('metaMeasurementPersonnel')) || 'N/A'}</td></tr>
                            <tr><td>数据来源:</td><td>${escapeHtml(getValue('metaDataSource')) || 'N/A'}</td></tr>
                            <tr><td>分析人员:</td><td>${escapeHtml(getValue('metaAnalysisPersonnel')) || 'N/A'}</td></tr>
                            <tr><td>分析日期:</td><td>${getValue('metaAnalysisDate') || new Date().toLocaleDateString()}</td></tr>
                        </table>`;

                htmlContent += `<h2>2. 数据输入与参数概要</h2>`;
                const dataInputMethodWord = document.querySelector('input[name="dataInputMethod"]:checked').value;
                htmlContent += `<p><strong>数据录入方式:</strong> ${dataInputMethodWord === 'random' ? '随机生成' : '粘贴数据'}</p>`;
                if (dataInputMethodWord === 'random') {
                    htmlContent += `<p>数据点总数: ${getValue('numPoints')}, 最小值: ${getValue('dataMin')}, 最大值: ${getValue('dataMax')}, 小数位数: ${getValue('decimalPlaces')}</p>`;
                }
                htmlContent += `<p><strong>子组大小 (n):</strong> ${getValue('subgroupSize')}</p>`;
                htmlContent += `<p><strong>规格:</strong> LSL: ${getValue('lslInput') || 'N/A'}, USL: ${getValue('uslInput') || 'N/A'}, Target: ${getValue('target') || 'N/A'}</p>`;
                const selectedRulesDesc = getSelectedRuleDescriptions().map(r => `规则 ${r.value} (${r.text.split('(')[0].trim()})`).join(', ');
                htmlContent += `<p><strong>判异准则:</strong> ${selectedRulesDesc || '无'}</p>`;

                htmlContent += `<h2>3. 统计结果摘要</h2>`;
                const resultsSummaryEl = getElement('resultsSummary');
                if (resultsSummaryEl) {
                    htmlContent += `<table class="word-export-table"><tr><th>指标</th><th>值</th><th>备注/公式</th></tr>`;
                    resultsSummaryEl.querySelectorAll('.result-item:not(.full-width)').forEach(item => {
                        const label = item.querySelector('strong') ? item.querySelector('strong').innerText.replace(':', '') : 'N/A';
                        const valueWithUnit = item.innerHTML.split('</strong>')[1] ? item.innerHTML.split('</strong>')[1].split('<br>')[0].trim() : 'N/A';
                        const formulaEl = item.querySelector('code');
                        const formula = formulaEl ? formulaEl.innerText : '';
                        htmlContent += `<tr><td>${escapeHtml(label)}</td><td>${escapeHtml(valueWithUnit)}</td><td>${escapeHtml(formula)}</td></tr>`;
                    });
                     resultsSummaryEl.querySelectorAll('.result-item.full-width').forEach(item => {
                        const label = item.querySelector('strong') ? item.querySelector('strong').innerText.replace(':', '') : '';
                        let content = item.innerHTML.split('</strong>')[1] ? item.innerHTML.split('</strong>')[1].trim() : item.innerText;
                        content = content.replace(/<br\s*\/?>/gi, '; ').replace(/<\/?code.*?>/gi, '').replace(/<span.*?>/gi, '').replace(/<\/span>/gi, '');
                        htmlContent += `<tr><td><strong>${escapeHtml(label)}</strong></td><td colspan="2">${escapeHtml(content)}</td></tr>`;
                    });
                    htmlContent += `</table>`;
                }

                htmlContent += `<h2>4. 原始数据与子组统计</h2>`;
                const dataTableEl = getElement('dataTable');
                if (dataTableEl && dataTableEl.outerHTML) {
                     htmlContent += dataTableEl.outerHTML.replace(/<table/g, '<table class="word-export-table"');
                }

                htmlContent += `<h2>5. 控制图与正态性分析</h2>`;
                ['controlChart1', 'controlChart2', 'histogramChart', 'qqPlotChart'].forEach(id => {
                    const canvas = getElement(id);
                    if (chartInstances[id] && canvas) {
                        const chartContainer = canvas.closest('.chart-container');
                        let chartTitle = id.replace('Chart',' Chart'); // Default title
                        if(chartContainer) {
                            const titleElement = chartContainer.previousElementSibling;
                             if (titleElement && (titleElement.tagName === 'H3' || titleElement.tagName === 'H2')) {
                                chartTitle = titleElement.innerText;
                             }
                        }
                        htmlContent += `<h3>${escapeHtml(chartTitle)}</h3>`;
                        try {
                            const imgDataUrl = canvas.toDataURL('image/png', 1.0);
                            htmlContent += `<div class="chart-image-container"><img src="${imgDataUrl}" class="chart-image" alt="${escapeHtml(chartTitle)}"></div>`;
                        } catch (e) {
                             htmlContent += `<p style="color:red;">无法导出 ${escapeHtml(chartTitle)} 图像。</p>`;
                             console.error("Error converting chart to image for Word:", e);
                        }
                    }
                });

                htmlContent += `</body></html>`;

                const converted = htmlDocx.asBlob(htmlContent, {
                    orientation: 'landscape',
                    margins: { top: 720, right: 720, bottom: 720, left: 720 }
                });
                saveAs(converted, `SPC_Analysis_${getValue('metaAnalysisDate') || new Date().toISOString().slice(0,10).replace(/-/g,'')}.docx`);
                showStatus('Word 文件已生成!');

            } catch (error) {
                console.error("导出 Word 时出错:", error);
                alert("导出 Word 文件失败。请检查控制台以获取更多信息。");
                showError('导出 Word 文件失败: ' + error.message);
            } finally {
                enableExportButtons();
            }
        }


        // --- Initial Setup ---
        document.addEventListener('DOMContentLoaded', function() {
            try {
                showPage('tool-section');
                loadSettings();
                if (!metaAnalysisDateInput.value) {
                    metaAnalysisDateInput.valueAsDate = new Date();
                }
                toggleInputFields();
            } catch (e) {
                console.error("Error during initial DOM setup:", e);
                showError("页面初始化失败：" + e.message);
            }
            disableExportButtons();
            const exportSection = getElement('exportSection');
            if(exportSection) exportSection.style.display = 'none';
        });
    </script>
</body>
</html>