<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APQP项目管理系统（第三版）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            width: 100%;
            padding: 24px 1vw 24px 1vw;
            box-sizing: border-box;
        }

        .header {
            background: linear-gradient(135deg, #0a1a3c 0%, #1a2950 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #fff;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.15);
        }

        .header p {
            color: #fff;
            font-size: 1.1em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.10);
        }

        .project-info {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .project-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .form-group input, .form-group select, .form-group textarea {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .phases-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .phase-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 2px 12px #0001;
            padding: 24px 24px 18px 24px;
            min-width: 350px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 10px;
        }

        .phase-number {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 38px;
            height: 38px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .phase-title {
            font-size: 1.18em;
            font-weight: bold;
            color: #2c3e50;
            flex: 1;
            text-align: left;
            margin-bottom: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .phase-status {
            margin-left: 10px;
            padding: 5px 14px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: bold;
            white-space: nowrap;
            align-self: flex-end;
        }

        .status-pending {
            background: #f1c40f;
            color: #fff;
        }

        .status-in-progress {
            background: #3498db;
            color: #fff;
        }

        .status-completed {
            background: #27ae60;
            color: #fff;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        /* 项目计划模板样式 */
        .project-template-section {
            margin: 32px auto;
            max-width: 1200px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
            padding: 24px;
        }
        
        .quick-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .quick-nav .btn {
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-nav .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .gantt-section {
            margin-bottom: 24px;
        }
        
        .gantt-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
        }
        
        .gantt-header {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 16px;
            margin-bottom: 16px;
            font-weight: bold;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .chart-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
        }
        
        .manager-load-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
        }
        
        .task-management-section {
            margin: 32px auto;
            max-width: 1200px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
            padding: 24px;
        }
        
        .task-filters {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .task-filters .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .task-filters .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .task-filters .btn:hover {
            background: #f8f9fa;
        }
        
        .task-filters .btn.active:hover {
            background: #0056b3;
        }
        
        .milestone-section {
            margin: 32px auto;
            max-width: 1200px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
            padding: 24px;
        }
        
        .milestone-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .milestone-stat {
            text-align: center;
            padding: 16px;
            border-radius: 8px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .task-list {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border-left: 4px solid #667eea;
            min-height: 40px;
            gap: 8px;
        }

        .task-checkbox {
            margin-right: 14px;
            transform: scale(1.15);
            flex-shrink: 0;
        }

        .task-text {
            flex: 1;
            font-size: 15px;
            text-align: left;
            word-break: break-word;
            white-space: normal;
            line-height: 1.4;
            color: #333;
        }

        .task-completed {
            text-decoration: line-through;
            opacity: 0.6;
        }

        .documents-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .document-card {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: border-color 0.3s;
        }

        .document-card:hover {
            border-color: #667eea;
        }

        .document-icon {
            font-size: 2em;
            margin-bottom: 10px;
            color: #667eea;
        }

        .document-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .document-status {
            font-size: 12px;
            color: #7f8c8d;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .phases-container {
                grid-template-columns: 1fr;
            }
            
            .project-form {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 900px) {
            .phases-container { grid-template-columns: 1fr; }
            .phase-card { min-width: 0; }
        }

        .header-flex {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 24px;
        }
        .company-logo-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 18px;
        }
        .company-logo-img {
            width: 54px;
            height: 54px;
            object-fit: contain;
            border-radius: 10px;
            background: #fff;
            box-shadow: 0 2px 8px #0002;
            margin-bottom: 6px;
        }
        .company-name-input {
            width: 110px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 6px;
            padding: 3px 6px;
            font-size: 15px;
            margin-bottom: 2px;
        }
        .footer {
            width: 100%;
            text-align: center;
            color: #fff;
            background: linear-gradient(90deg, #1a2950 0%, #0a1a3c 100%);
            padding: 18px 0 12px 0;
            font-size: 1em;
            letter-spacing: 1px;
            margin-top: 40px;
            border-radius: 0 0 16px 16px;
        }
        .company-bar {
            display: flex;
            align-items: center;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px #0001;
            padding: 12px 24px 12px 18px;
            margin-bottom: 22px;
            width: fit-content;
            min-width: 320px;
            max-width: 420px;
        }
        .company-bar .company-logo-img {
            width: 48px;
            height: 48px;
            object-fit: contain;
            border-radius: 8px;
            background: #f4f6fa;
            box-shadow: 0 1px 4px #0001;
            margin-right: 16px;
        }
        .company-bar .logo-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: #e5eaf3;
            border-radius: 8px;
            color: #888;
            font-size: 13px;
            margin-right: 16px;
        }
        .company-bar .company-name-input {
            font-size: 1.18em;
            font-weight: bold;
            border: none;
            border-bottom: 2px solid #3B6CB7;
            outline: none;
            background: transparent;
            color: #2c3e50;
            min-width: 120px;
            max-width: 220px;
            padding: 2px 6px;
        }
        .company-bar .company-name-input::placeholder {
            color: #aaa;
        }
        .topbar {
            width: 100%;
            background: #3b4a5e;
            border-radius: 16px 16px 16px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 36px 18px 28px;
            margin-bottom: 32px;
            box-sizing: border-box;
            box-shadow: 0 2px 12px #0002;
        }
        .topbar-left {
            display: flex;
            align-items: center;
            gap: 18px;
        }
        .topbar-logo {
            width: 54px;
            height: 54px;
            object-fit: contain;
            border-radius: 8px;
            background: #fff2;
            box-shadow: 0 1px 4px #0002;
            margin-right: 10px;
        }
        .topbar-logo-placeholder {
            width: 54px;
            height: 54px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff2;
            border-radius: 8px;
            color: #fff;
            font-size: 13px;
            margin-right: 10px;
            cursor: pointer;
        }
        .topbar-company-name {
            font-size: 1.6em;
            font-weight: bold;
            color: #fff;
            border: none;
            background: transparent;
            outline: none;
            min-width: 120px;
            max-width: 320px;
            padding: 2px 8px;
            letter-spacing: 1px;
        }
        .topbar-company-name::placeholder { color: #e0e0e0; }
        .topbar-title {
            font-size: 2.1em;
            font-weight: bold;
            color: #fff;
            letter-spacing: 2px;
            text-align: right;
            flex: 1;
        }
        .footerbar {
            width: 100%;
            background: #3b4a5e;
            border-radius: 0 0 16px 16px;
            color: #fff;
            text-align: center;
            padding: 18px 0 12px 0;
            font-size: 1.1em;
            margin-top: 40px;
            letter-spacing: 1px;
        }
        @media (max-width: 700px) {
            .topbar { flex-direction: column; align-items: flex-start; padding: 14px 8vw 14px 8vw; }
            .topbar-title { text-align: left; font-size: 1.3em; margin-top: 10px; }
        }
        
        /* 综合看板样式 */
        .dashboard-container {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .quick-nav .nav-item {
            transition: all 0.3s ease;
        }
        
        .quick-nav .nav-item:hover {
            background: #667eea;
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .quick-nav .nav-item:hover span {
            color: #fff;
        }
        
        .status-card {
            transition: all 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .chart-section {
            transition: all 0.3s ease;
        }
        
        .chart-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }



    </style>
</head>
<body>
    <!-- 8D工具风格顶部横幅：公司LOGO、公司名称、系统名称 -->
    <div id="function-buttons" style="width: calc(1500px - 40px); max-width: calc(100vw - 40px); margin-left: auto; margin-right: auto; position: relative; margin-bottom: 18px; margin-top: 40px; background-color: #374757; padding: 12px; border-radius: 8px; box-sizing: border-box; min-height: 60px;">
        <!-- 左侧：公司LOGO和名称 -->
        <div style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); display: flex; align-items: center; gap: 12px;">
            <div id="company-logo-container"
                 style="width: 50px; height: 50px; border: none; border-radius: 8px; display: flex; align-items: center; justify-content: center; background: transparent; cursor: pointer; overflow: hidden;"
                 onclick="document.getElementById('company-logo-upload').click()">
                <img id="company-logo" src="" alt="公司LOGO"
                     style="max-width: 100%; max-height: 100%; display: none; background: transparent;" />
                <input type="file" id="company-logo-upload" accept="image/*" style="display: none;" onchange="uploadCompanyLogo(this)">
            </div>
            <span id="company-name" style="color: #fff; font-size: 1.3em; font-weight: bold; min-width: 120px; cursor: pointer;" onclick="editCompanyName()">公司名称</span>
        </div>
        <!-- 中间：系统标题（绝对居中） -->
        <div style="text-align: center; width: 100%;">
            <span style="color: #fff; font-size: 2.0em; font-weight: bold; letter-spacing: 1px;">APQP项目管理系统（第三版）</span>
        </div>
    </div>
    <!-- 项目综合看板 -->
    <div class="dashboard-container" style="margin-bottom: 36px;">
        <div style="background: linear-gradient(90deg, #1c4b8d 0%, #3082f2 100%); border-radius: 12px 12px 0 0; padding: 18px 0 10px 0; text-align: center;">
            <span style="color: #fff; font-size: 2.1em; font-weight: bold; letter-spacing: 2px;">项目综合看板</span>
        </div>
        <!-- 仪表盘布局（完全贴合图片） -->
        <div style="background: #f7f9fb; border-radius: 0 0 12px 12px; padding: 32px 24px 24px 24px; box-shadow: 0 2px 12px #0001;">
            <!-- 快捷入口卡片 -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 60px; margin-bottom: 28px; background: #fff; border-radius: 16px; box-shadow: 0 2px 12px #0002; padding: 28px 0 18px 0;">
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="项目管理" onclick="showJiandaoyunNotification('跳转到项目管理模块')">
                    <img src="https://cdn.jsdelivr.net/gh/wzmsoft/static/icons/project-mgr-blue.svg" style="width:38px;height:38px;margin-bottom:8px;" alt="项目管理" />
                    <span style="font-size: 1.1em; color: #222;">项目管理</span>
                </div>
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="项目立项" onclick="showJiandaoyunNotification('跳转到项目立项模块')">
                    <img src="https://cdn.jsdelivr.net/gh/wzmsoft/static/icons/project-create-yellow.svg" style="width:38px;height:38px;margin-bottom:8px;" alt="项目立项" />
                    <span style="font-size: 1.1em; color: #222;">项目立项</span>
                </div>
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="项目计划" onclick="showJiandaoyunNotification('跳转到项目计划模块')">
                    <img src="https://cdn.jsdelivr.net/gh/wzmsoft/static/icons/project-plan-orange.svg" style="width:38px;height:38px;margin-bottom:8px;" alt="项目计划" />
                    <span style="font-size: 1.1em; color: #222;">项目计划</span>
                </div>
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="项目合同" onclick="showJiandaoyunNotification('跳转到项目合同模块')">
                    <img src="https://cdn.jsdelivr.net/gh/wzmsoft/static/icons/project-contract-yellow.svg" style="width:38px;height:38px;margin-bottom:8px;" alt="项目合同" />
                    <span style="font-size: 1.1em; color: #222;">项目合同</span>
                </div>
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="项目结项申请" onclick="showJiandaoyunNotification('跳转到项目结项申请模块')">
                    <img src="https://cdn.jsdelivr.net/gh/wzmsoft/static/icons/project-finish-green.svg" style="width:38px;height:38px;margin-bottom:8px;" alt="项目结项申请" />
                    <span style="font-size: 1.1em; color: #222;">项目结项申请</span>
                </div>
                <div class="nav-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer; min-width: 90px;" title="查看全部" onclick="showJiandaoyunNotification('显示所有项目列表')">
                    <div style="width:38px;height:38px;border-radius:50%;background:#f5f6fa;display:flex;align-items:center;justify-content:center;margin-bottom:8px;font-size:2em;color:#bbb;">…</div>
                    <span style="font-size: 1.1em; color: #222;">查看全部</span>
                </div>
            </div>
            <!-- 统计卡片区，两行两列 -->
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); grid-template-rows: repeat(2, 1fr); gap: 24px; margin-bottom: 24px;">
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 22px 0 10px 0; text-align: center; position:relative;">
                    <div style="color: #ff9800; font-size: 2.1em; font-weight: bold;">8<span style="font-size:0.7em;">个</span></div>
                    <div style="color: #222; font-size: 1.1em; margin-bottom: 8px;">已立项项目</div>
                    <div style="position:absolute;left:0;bottom:0;width:100%;height:5px;background:linear-gradient(90deg,#ff9800 60%,#fff0 100%);border-radius:0 0 12px 12px;"></div>
                </div>
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 22px 0 10px 0; text-align: center; position:relative;">
                    <div style="color: #7c4dff; font-size: 2.1em; font-weight: bold;">4<span style="font-size:0.7em;">个</span></div>
                    <div style="color: #222; font-size: 1.1em; margin-bottom: 8px;">未开始项目</div>
                    <div style="position:absolute;left:0;bottom:0;width:100%;height:5px;background:linear-gradient(90deg,#7c4dff 60%,#fff0 100%);border-radius:0 0 12px 12px;"></div>
                </div>
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 22px 0 10px 0; text-align: center; position:relative;">
                    <div style="color: #3082f2; font-size: 2.1em; font-weight: bold;">5<span style="font-size:0.7em;">个</span></div>
                    <div style="color: #222; font-size: 1.1em; margin-bottom: 8px;">进行中项目数</div>
                    <div style="position:absolute;left:0;bottom:0;width:100%;height:5px;background:linear-gradient(90deg,#3082f2 60%,#fff0 100%);border-radius:0 0 12px 12px;"></div>
                </div>
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 22px 0 10px 0; text-align: center; position:relative;">
                    <div style="color: #00c853; font-size: 2.1em; font-weight: bold;">0<span style="font-size:0.7em;">个</span></div>
                    <div style="color: #222; font-size: 1.1em; margin-bottom: 8px;">已结项项目</div>
                    <div style="position:absolute;left:0;bottom:0;width:100%;height:5px;background:linear-gradient(90deg,#00c853 60%,#fff0 100%);border-radius:0 0 12px 12px;"></div>
                </div>
            </div>
            <!-- 图表和经理负载三栏分布 -->
            <div style="display: grid; grid-template-columns: 2fr 2fr 1.2fr; gap: 24px; align-items: stretch;">
                <!-- 图表区 -->
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 18px 0 0 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                    <div style="font-size: 1.1em; color: #222; margin-bottom: 4px;">项目类型分布</div>
                    <canvas id="projectTypeChart" width="200" height="140" style="margin: 0 auto;"></canvas>
                </div>
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 18px 0 0 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                    <div style="font-size: 1.1em; color: #222; margin-bottom: 4px;">项目来源</div>
                    <canvas id="projectSourceChart" width="200" height="140" style="margin: 0 auto;"></canvas>
                </div>
                <!-- 经理负载区 -->
                <div style="background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 18px 18px; display: flex; flex-direction: column; height: 100%; min-width: 180px; max-width: 240px;">
                    <div style="font-size: 1.1em; color: #222; margin-bottom: 8px;">项目经理负载</div>
                    <div id="managerList" style="margin-bottom: 18px;"></div>
                    <div style="border-top: 1px solid #f0f0f0; margin: 8px 0 10px 0;"></div>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="color: #3082f2; font-size: 1.1em; font-weight: bold;">计数</div>
                        <div style="background: #f5b445; color: #fff; font-size: 1.3em; font-weight: bold; border-radius: 50%; width: 38px; height: 38px; display: flex; align-items: center; justify-content: center;">10</div>
                    </div>
                    <div style="color: #888; font-size: 1em; text-align: right; margin-top: 8px;">13959240478</div>
                </div>
            </div>
        </div>
    </div>
    <!-- 原有内容 -->
    <div class="container">
        <!-- 项目信息 -->
        <div class="project-info" id="project-info">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">📋 项目基本信息</h2>
            <div class="project-form">
                <div class="form-group">
                    <label>项目名称</label>
                    <input type="text" id="projectName" placeholder="输入项目名称">
                </div>
                <div class="form-group">
                    <label>客户名称</label>
                    <input type="text" id="customerName" placeholder="输入客户名称">
                </div>
                <div class="form-group">
                    <label>项目编号</label>
                    <input type="text" id="projectNumber" placeholder="输入项目编号">
                </div>
                <div class="form-group">
                    <label>项目经理</label>
                    <input type="text" id="projectManager" placeholder="输入项目经理姓名">
                </div>
                <div class="form-group">
                    <label>开始日期</label>
                    <input type="date" id="startDate">
                </div>
                <div class="form-group">
                    <label>计划完成日期</label>
                    <input type="date" id="endDate">
                </div>
                <div class="form-group">
                    <label>产品类型</label>
                    <select id="productType">
                        <option value="">选择产品类型</option>
                        <option value="发动机">发动机</option>
                        <option value="变速箱">变速箱</option>
                        <option value="底盘">底盘</option>
                        <option value="车身">车身</option>
                        <option value="电子系统">电子系统</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>项目状态</label>
                    <select id="projectStatus">
                        <option value="planning">规划中</option>
                        <option value="active">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="on-hold">暂停</option>
                    </select>
                </div>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="saveProjectInfo()">💾 保存项目信息</button>
                <button class="btn btn-secondary" onclick="loadProjectInfo()">📂 加载项目</button>
                <button class="btn btn-success" onclick="exportProject()">📤 导出项目</button>
                <button class="btn btn-warning" onclick="showProjectTemplates()">📋 项目模板</button>
                <button class="btn btn-danger" onclick="createNewProject()">🆕 新建项目</button>
                <button class="btn btn-info" onclick="saveCurrentAsTemplate()">⭐ 保存为模板</button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid" id="project-completion">
            <div class="stat-card">
                <div class="stat-number" id="totalTasks">0</div>
                <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedTasks">0</div>
                <div class="stat-label">已完成任务</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completionRate">0%</div>
                <div class="stat-label">完成率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="daysRemaining">0</div>
                <div class="stat-label">剩余天数</div>
            </div>
        </div>



        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        // ====== 综合看板功能 ======
        
        // 导航功能
        function scrollToSection(sectionId) {
            let element = document.getElementById(sectionId);
            if (!element) {
                // 如果没有找到ID，尝试通过data-section属性查找
                element = document.querySelector(`[data-section="${sectionId}"]`);
            }
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }
        
        function showAllProjects() {
            alert('查看全部项目功能 - 这里可以跳转到项目列表页面');
        }
        

        
        // 初始化综合看板
        function initDashboard() {
            // 初始化项目综合看板
            console.log('项目综合看板已初始化');
        }
        
        // 初始化简道云项目计划模板
        function initJiandaoyunProjectTemplate() {
            // 初始化项目类型分布图表
            const ctx = document.getElementById('projectTypeChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['生产制造', '产品研发', '售后运维', '产品设计'],
                        datasets: [{
                            data: [40, 30, 20, 10],
                            backgroundColor: ['#9192ab', '#3082f2', '#2b5ab3', '#f5b445'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        cutout: '60%'
                    }
                });
            }

            // 初始化项目来源柱状图
            const ctx2 = document.getElementById('projectSourceChart');
            if (ctx2) {
                new Chart(ctx2, {
                    type: 'bar',
                    data: {
                        labels: ['广告营销', '客户介绍', '朋友介绍', '销售自拓'],
                        datasets: [{
                            label: '项目数量',
                            data: [1, 5, 2, 2],
                            backgroundColor: ['#a9c8fc', '#3082f2', '#40d99d', '#ff6b6b'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 5
                            }
                        }
                    }
                });
            }

            // 绑定快速入口点击事件
            const entryItems = document.querySelectorAll('.jiandaoyun-project-template .node-wrapper');
            entryItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.getAttribute('title');
                    handleJiandaoyunEntryClick(title);
                });
                
                // 添加悬停效果
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(28, 75, 141, 0.1)';
                    this.style.transform = 'translateY(-2px)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.transform = 'translateY(0)';
                });
            });

            // 绑定筛选器点击事件
            const filterItems = document.querySelectorAll('.jiandaoyun-project-template .dash-card-item');
            filterItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他选中状态
                    filterItems.forEach(fi => {
                        const wrapper = fi.querySelector('.node-wrapper');
                        wrapper.style.background = '#fff';
                        wrapper.style.color = '#1f2d3d';
                        wrapper.style.border = '1px solid #e0e0e0';
                    });
                    
                    // 设置当前选中状态
                    const wrapper = this.querySelector('.node-wrapper');
                    wrapper.style.background = '#1c4b8d';
                    wrapper.style.color = '#fff';
                    wrapper.style.border = '1px solid #1c4b8d';
                    
                    // 执行筛选
                    const status = wrapper.textContent;
                    filterJiandaoyunTasksByStatus(status);
                });
            });
        }

        // 处理简道云快速入口点击
        function handleJiandaoyunEntryClick(title) {
            switch(title) {
                case '项目管理':
                    showJiandaoyunNotification('跳转到项目管理模块');
                    break;
                case '项目立项':
                    showJiandaoyunNotification('跳转到项目立项模块');
                    break;
                case '项目计划':
                    showJiandaoyunNotification('跳转到项目计划模块');
                    break;
                case '项目合同':
                    showJiandaoyunNotification('跳转到项目合同模块');
                    break;
                case '项目结项申请':
                    showJiandaoyunNotification('跳转到项目结项申请模块');
                    break;
                case '查看全部':
                    showJiandaoyunNotification('显示所有项目列表');
                    break;
            }
        }

        // 按状态筛选简道云任务
        function filterJiandaoyunTasksByStatus(status) {
            showJiandaoyunNotification(`筛选任务状态: ${status}`);
            // 这里可以添加实际的筛选逻辑
        }

        // 显示简道云通知
        function showJiandaoyunNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1c4b8d;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // 渲染甘特图
        function renderGanttChart() {
            const ganttData = [
                { name: '子任务1.2', start: '2024-05-06', end: '2024-05-20', progress: 60 },
                { name: '起重设备生产子任务2.2', start: '2024-03-30', end: '2024-04-21', progress: 100 },
                { name: '方案设计', start: '2024-04-22', end: '2024-05-11', progress: 80 },
                { name: '物资采购', start: '2024-04-30', end: '2024-05-12', progress: 40 },
                { name: '采购任务4.1', start: '2024-04-21', end: '2024-04-25', progress: 100 },
                { name: '零件采购', start: '2024-04-28', end: '2024-05-03', progress: 70 },
                { name: '生产制造', start: '2024-05-01', end: '2024-05-19', progress: 30 },
                { name: '设备调试', start: '2024-05-14', end: '2024-05-26', progress: 0 },
                { name: '项目结项', start: '2024-05-17', end: '2024-06-09', progress: 0 }
            ];
            
            const ganttBody = document.getElementById('ganttBody');
            ganttBody.innerHTML = '';
            
            ganttData.forEach(task => {
                const taskElement = document.createElement('div');
                taskElement.style.display = 'grid';
                taskElement.style.gridTemplateColumns = '200px 1fr';
                taskElement.style.gap = '16px';
                taskElement.style.marginBottom = '8px';
                taskElement.style.alignItems = 'center';
                
                const startDate = new Date(task.start);
                const endDate = new Date(task.end);
                const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);
                const progressDays = (totalDays * task.progress) / 100;
                
                taskElement.innerHTML = `
                    <div style="font-weight:500;">${task.name}</div>
                    <div style="position:relative;height:20px;background:#e9ecef;border-radius:10px;overflow:hidden;">
                        <div style="position:absolute;left:0;top:0;height:100%;width:${task.progress}%;background:linear-gradient(90deg,#007bff,#0056b3);border-radius:10px;"></div>
                        <div style="position:absolute;left:0;top:0;height:100%;width:100%;display:flex;align-items:center;justify-content:center;font-size:12px;color:#666;">
                            ${task.progress}%
            </div>
            </div>
                `;
                
                ganttBody.appendChild(taskElement);
            });
        }
        
        // 渲染任务列表
        function renderTaskList() {
            const tasks = [
                { id: 1, name: '子任务1.2', status: 'in-progress', assignee: '张三', priority: 'high', startDate: '2024-05-06', endDate: '2024-05-20' },
                { id: 2, name: '起重设备生产子任务2.2', status: 'completed', assignee: '李四', priority: 'medium', startDate: '2024-03-30', endDate: '2024-04-21' },
                { id: 3, name: '方案设计', status: 'in-progress', assignee: '王五', priority: 'high', startDate: '2024-04-22', endDate: '2024-05-11' },
                { id: 4, name: '物资采购', status: 'in-progress', assignee: '赵六', priority: 'medium', startDate: '2024-04-30', endDate: '2024-05-12' },
                { id: 5, name: '采购任务4.1', status: 'completed', assignee: '钱七', priority: 'low', startDate: '2024-04-21', endDate: '2024-04-25' },
                { id: 6, name: '零件采购', status: 'in-progress', assignee: '孙八', priority: 'medium', startDate: '2024-04-28', endDate: '2024-05-03' },
                { id: 7, name: '生产制造', status: 'in-progress', assignee: '周九', priority: 'high', startDate: '2024-05-01', endDate: '2024-05-19' },
                { id: 8, name: '设备调试', status: 'not-started', assignee: '吴十', priority: 'high', startDate: '2024-05-14', endDate: '2024-05-26' },
                { id: 9, name: '项目结项', status: 'not-started', assignee: '郑十一', priority: 'medium', startDate: '2024-05-17', endDate: '2024-06-09' }
            ];
            
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = '';
            
            tasks.forEach(task => {
                const taskElement = document.createElement('div');
                taskElement.className = 'task-item';
                taskElement.style.cssText = 'background:#f8f9fa;border-radius:8px;padding:16px;margin-bottom:12px;border-left:4px solid #007bff;';
                
                const statusColors = {
                    'not-started': '#ffc107',
                    'in-progress': '#17a2b8',
                    'completed': '#28a745'
                };
                
                const statusText = {
                    'not-started': '未开始',
                    'in-progress': '进行中',
                    'completed': '已完成'
                };
                
                const priorityColors = {
                    'low': '#28a745',
                    'medium': '#ffc107',
                    'high': '#dc3545'
                };
                
                const priorityText = {
                    'low': '低',
                    'medium': '中',
                    'high': '高'
                };
                
                taskElement.innerHTML = `
                    <div style="display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:12px;">
                        <div style="flex:1;min-width:200px;">
                            <h4 style="margin:0 0 8px 0;font-size:16px;">${task.name}</h4>
                            <div style="display:flex;gap:16px;flex-wrap:wrap;">
                                <span style="background:${statusColors[task.status]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;">
                                    ${statusText[task.status]}
                                </span>
                                <span style="background:${priorityColors[task.priority]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;">
                                    优先级: ${priorityText[task.priority]}
                                </span>
                                <span style="color:#666;font-size:12px;">负责人: ${task.assignee}</span>
          </div>
        </div>
                        <div style="text-align:right;color:#666;font-size:12px;">
                            <div>${task.startDate} ~ ${task.endDate}</div>
                        </div>
                    </div>
                `;
                
                taskList.appendChild(taskElement);
            });
        }
        
        // 渲染里程碑列表
        function renderMilestoneList() {
            const milestones = [
                { id: 1, name: '项目启动', date: '2024-03-30', status: 'completed', description: '项目正式启动，团队组建完成' },
                { id: 2, name: '需求确认', date: '2024-04-15', status: 'completed', description: '客户需求确认，技术方案制定' },
                { id: 3, name: '设计完成', date: '2024-05-11', status: 'in-progress', description: '产品设计和技术设计完成' },
                { id: 4, name: '生产完成', date: '2024-05-19', status: 'not-started', description: '产品生产制造完成' },
                { id: 5, name: '项目交付', date: '2024-06-09', status: 'not-started', description: '项目最终交付客户' }
            ];
            
            const milestoneList = document.getElementById('milestoneList');
            milestoneList.innerHTML = '';
            
            milestones.forEach(milestone => {
                const milestoneElement = document.createElement('div');
                milestoneElement.className = 'milestone-item';
                milestoneElement.style.cssText = 'background:#f8f9fa;border-radius:8px;padding:16px;margin-bottom:12px;border-left:4px solid #28a745;';
                
                const statusColors = {
                    'not-started': '#6c757d',
                    'in-progress': '#17a2b8',
                    'completed': '#28a745'
                };
                
                const statusText = {
                    'not-started': '未开始',
                    'in-progress': '进行中',
                    'completed': '已完成'
                };
                
                milestoneElement.innerHTML = `
                    <div style="display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:12px;">
                        <div style="flex:1;min-width:200px;">
                            <h4 style="margin:0 0 8px 0;font-size:16px;">${milestone.name}</h4>
                            <p style="margin:0;color:#666;font-size:14px;">${milestone.description}</p>
              </div>
                        <div style="text-align:right;">
                            <div style="background:${statusColors[milestone.status]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;margin-bottom:4px;">
                                ${statusText[milestone.status]}
              </div>
                            <div style="color:#666;font-size:12px;">${milestone.date}</div>
            </div>
                    </div>
                `;
                
                milestoneList.appendChild(milestoneElement);
            });
        }
        
        // 渲染项目经理列表
        function renderManagerList() {
            const managers = [
                { name: '张三', projectCount: 3, workload: 80 },
                { name: '李四', projectCount: 2, workload: 60 },
                { name: '王五', projectCount: 4, workload: 90 },
                { name: '赵六', projectCount: 1, workload: 30 }
            ];
            
            const managerList = document.getElementById('managerList');
            managerList.innerHTML = '';
            
            managers.forEach(manager => {
                const managerElement = document.createElement('div');
                managerElement.style.cssText = 'display:flex;justify-content:space-between;align-items:center;padding:12px;background:white;border-radius:6px;margin-bottom:8px;';
                
                const workloadColor = manager.workload > 80 ? '#dc3545' : manager.workload > 60 ? '#ffc107' : '#28a745';
                
                managerElement.innerHTML = `
                    <div>
                        <div style="font-weight:bold;">${manager.name}</div>
                        <div style="color:#666;font-size:12px;">负责项目: ${manager.projectCount}个</div>
            </div>
                    <div style="text-align:right;">
                        <div style="color:${workloadColor};font-weight:bold;">${manager.workload}%</div>
                        <div style="width:60px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                            <div style="width:${manager.workload}%;height:100%;background:${workloadColor};"></div>
                        </div>
                    </div>
                `;
                
                managerList.appendChild(managerElement);
            });
        }
        
        // 任务筛选功能
        function filterTasks(status) {
            const buttons = document.querySelectorAll('.task-filters .btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以添加实际的筛选逻辑
            console.log('筛选任务状态:', status);
        }
        
        // 显示添加任务模态框
        function showAddTaskModal() {
            document.getElementById('addTaskModal').style.display = 'block';
        }
        
        // 关闭添加任务模态框
        function closeAddTaskModal() {
            document.getElementById('addTaskModal').style.display = 'none';
        }
        
        // 添加任务表单提交
        document.addEventListener('DOMContentLoaded', function() {
            const addTaskForm = document.getElementById('addTaskForm');
            if (addTaskForm) {
                addTaskForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const taskData = {
                        name: document.getElementById('taskName').value,
                        description: document.getElementById('taskDescription').value,
                        startDate: document.getElementById('taskStartDate').value,
                        endDate: document.getElementById('taskEndDate').value,
                        assignee: document.getElementById('taskAssignee').value,
                        priority: document.getElementById('taskPriority').value
                    };
                    
                    console.log('新任务数据:', taskData);
                    
                    // 这里可以添加保存任务的逻辑
                    alert('任务添加成功！');
                    closeAddTaskModal();
                    addTaskForm.reset();
                });
            }
        });

        </script>

        <!-- 输入区（可编辑） -->
        <div class="input-section" id="project-approval" style="background: #fff; border-radius: 12px; box-shadow: 0 2px 12px #0001; padding: 22px 24px 18px 24px; margin-bottom: 30px;">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                <h2 style="color: #2c3e50; font-size: 1.25em; margin: 0;">输入</h2>
                <button class="btn btn-success" onclick="addInputItem()">+ 添加输入项</button>
            </div>
            <ul id="inputList" style="list-style: decimal inside; padding-left: 0; margin: 0;">
            </ul>
        </div>

        <!-- APQP五个阶段 -->
        <div class="phases-container" id="phasesContainer" data-section="project-planning">
            <!-- 阶段内容将通过JavaScript动态生成 -->
        </div>

        <!-- 文档管理 -->
        <div class="documents-section" id="project-contract">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">📁 文档管理</h2>
            <div style="text-align: center; margin-bottom: 20px;">
                <button class="btn" onclick="uploadDocument()">📤 上传文档</button>
                <button class="btn btn-secondary" onclick="viewAllDocuments()">📂 查看所有文档</button>
            </div>
            <div class="documents-grid" id="documentsGrid">
                <!-- 文档卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="phaseModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">阶段详情</h2>
            <div id="modalContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 项目模板选择模态框 -->
    <div id="templateModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <span class="close" onclick="closeTemplateModal()">&times;</span>
            <h2>📋 选择项目模板</h2>
            <div style="margin-bottom: 20px;">
                <p>选择一个预设的项目模板来快速启动您的APQP项目：</p>
            </div>
            <div id="templateGrid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; max-height: 500px; overflow-y: auto;">
                <!-- 模板卡片将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 任务表单模态框 -->
    <div id="taskFormModal" class="modal">
        <div class="modal-content" style="max-width: 900px; max-height: 90vh; overflow-y: auto;">
            <span class="close" onclick="closeTaskFormModal()">&times;</span>
            <h2 id="taskFormTitle">任务表单</h2>
            <div id="taskFormContent">
                <!-- 表单内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 8D工具风格底部版权栏 -->
    <div id="copyright-info"
     style="width: calc(1500px - 40px); max-width: calc(100vw - 40px); margin: 32px auto 18px auto; background-color: #374757; border-radius: 8px; color: #fff; font-size: 1em; letter-spacing: 1px; text-align: center; padding: 12px; box-sizing: border-box;">
    版权所有 © 吴志明 | 电话&微信：13959240478 |
    <span style="font-family: 'KaiTi', '楷体', serif; font-weight: bold;">
        弘扬匠心、传递知识、为企业创造价值！
    </span>
</div>

    <script>
        // APQP最新版五大阶段及任务（带详细描述）
        const apqpPhases = [
            {
                name: "策划和定义项目",
                tasks: [
                    {
                        title: "设计目标",
                        description: "明确产品的设计目标，包括性能要求、功能规格、技术参数等。制定详细的设计目标文档，确保设计团队对产品要求有清晰的理解。",
                        formTemplate: {
                            type: "design_objectives",
                            fields: [
                                { name: "product_name", label: "产品名称", type: "text", required: true },
                                { name: "customer_requirements", label: "客户要求", type: "textarea", required: true },
                                { name: "performance_targets", label: "性能目标", type: "textarea", required: true },
                                { name: "technical_specs", label: "技术规格", type: "textarea", required: true },
                                { name: "design_constraints", label: "设计约束", type: "textarea" },
                                { name: "target_cost", label: "目标成本", type: "number" },
                                { name: "target_weight", label: "目标重量", type: "text" },
                                { name: "target_volume", label: "目标体积", type: "text" },
                                { name: "reliability_target", label: "可靠性目标", type: "text" },
                                { name: "safety_requirements", label: "安全要求", type: "textarea" },
                                { name: "regulatory_requirements", label: "法规要求", type: "textarea" },
                                { name: "design_team", label: "设计团队", type: "text" },
                                { name: "completion_date", label: "完成日期", type: "date" },
                                { name: "approval_status", label: "审批状态", type: "select", options: ["待审批", "已审批", "需修改"] }
                            ]
                        }
                    },
                    {
                        title: "可靠性和质量目标",
                        description: "设定产品的可靠性指标和质量目标，包括MTBF、失效率、质量等级等。制定质量保证计划，确保产品满足客户的质量要求。",
                        formTemplate: {
                            type: "reliability_quality_objectives",
                            fields: [
                                { name: "product_name", label: "产品名称", type: "text", required: true },
                                { name: "mtbf_target", label: "MTBF目标（小时）", type: "number", required: true },
                                { name: "failure_rate_target", label: "失效率目标（%/1000小时）", type: "number" },
                                { name: "quality_level", label: "质量等级", type: "select", options: ["A级", "B级", "C级"], required: true },
                                { name: "ppm_target", label: "PPM目标", type: "number" },
                                { name: "warranty_period", label: "保修期（月）", type: "number" },
                                { name: "reliability_test_plan", label: "可靠性测试计划", type: "textarea", required: true },
                                { name: "quality_control_plan", label: "质量控制计划", type: "textarea", required: true },
                                { name: "inspection_criteria", label: "检验标准", type: "textarea" },
                                { name: "defect_classification", label: "缺陷分类", type: "textarea" },
                                { name: "corrective_action_procedure", label: "纠正措施程序", type: "textarea" },
                                { name: "quality_team", label: "质量团队", type: "text" },
                                { name: "review_date", label: "评审日期", type: "date" },
                                { name: "approval_status", label: "审批状态", type: "select", options: ["待审批", "已审批", "需修改"] }
                            ]
                        }
                    },
                    {
                        title: "初始项目清单",
                        description: "建立初始项目清单，包括所有必要的活动、里程碑、交付物等。制定项目时间表和资源分配计划。",
                        formTemplate: {
                            type: "project_checklist",
                            fields: [
                                { name: "project_name", label: "项目名称", type: "text", required: true },
                                { name: "project_manager", label: "项目经理", type: "text", required: true },
                                { name: "project_team", label: "项目团队", type: "textarea", required: true },
                                { name: "project_start_date", label: "项目开始日期", type: "date", required: true },
                                { name: "project_end_date", label: "项目结束日期", type: "date", required: true },
                                { name: "project_budget", label: "项目预算", type: "number" },
                                { name: "key_milestones", label: "关键里程碑", type: "textarea", required: true },
                                { name: "deliverables", label: "交付物清单", type: "textarea", required: true },
                                { name: "resource_requirements", label: "资源需求", type: "textarea" },
                                { name: "risk_assessment", label: "风险评估", type: "textarea" },
                                { name: "quality_objectives", label: "质量目标", type: "textarea" },
                                { name: "communication_plan", label: "沟通计划", type: "textarea" },
                                { name: "stakeholders", label: "相关方", type: "textarea" },
                                { name: "approval_status", label: "审批状态", type: "select", options: ["待审批", "已审批", "需修改"] }
                            ]
                        }
                    },
                    {
                        title: "初始过程流程图",
                        description: "绘制初始过程流程图，识别关键工序和过程步骤。确定过程控制点和质量检查点。"
                    },
                    {
                        title: "产品和过程特殊特性清单",
                        description: "识别产品和过程的特殊特性，包括关键特性、重要特性等。制定特殊特性的控制计划和监控方法。"
                    },
                    {
                        title: "产品保证计划",
                        description: "制定产品保证计划，包括质量保证措施、检验方法、不合格品处理等。确保产品质量的持续改进。"
                    },
                    {
                        title: "产品开发计划",
                        description: "制定详细的产品开发计划，包括各阶段的时间安排、资源需求、风险评估等。确保项目按计划推进。"
                    },
                    {
                        title: "领导支持",
                        description: "获得高层领导的支持和承诺，确保项目有足够的资源和支持。建立项目治理结构。"
                    },
                    {
                        title: "变更管理程序",
                        description: "建立变更管理程序，规范设计变更、过程变更的审批流程。确保变更的受控管理。"
                    },
                    {
                        title: "APQP项目指南",
                        description: "制定APQP项目指南，明确各阶段的工作要求、交付标准、评审要点等。为项目团队提供指导。"
                    },
                    {
                        title: "风险评估与缓解计划",
                        description: "进行项目风险评估，识别潜在风险点。制定风险缓解计划和应急预案。"
                    }
                ],
                description: "策划和定义项目阶段，明确目标、流程、特性、计划及风险。"
            },
            {
                name: "产品设计和开发",
                tasks: [
                    {
                        title: "设计失效模式和影响分析",
                        description: "进行设计FMEA分析，识别设计中的潜在失效模式、失效原因和失效影响。制定预防措施和检测方法。",
                        formTemplate: {
                            type: "design_fmea",
                            fields: [
                                { name: "product_name", label: "产品名称", type: "text", required: true },
                                { name: "fmea_team", label: "FMEA团队", type: "text", required: true },
                                { name: "analysis_date", label: "分析日期", type: "date", required: true },
                                { name: "revision_number", label: "版本号", type: "text" },
                                { name: "system_function", label: "系统功能", type: "textarea", required: true },
                                { name: "potential_failure_mode", label: "潜在失效模式", type: "textarea", required: true },
                                { name: "potential_effects", label: "潜在失效影响", type: "textarea", required: true },
                                { name: "severity_rating", label: "严重度评级", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], required: true },
                                { name: "potential_causes", label: "潜在失效原因", type: "textarea", required: true },
                                { name: "occurrence_rating", label: "发生度评级", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], required: true },
                                { name: "current_controls", label: "现行控制方法", type: "textarea", required: true },
                                { name: "detection_rating", label: "探测度评级", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], required: true },
                                { name: "risk_priority_number", label: "风险优先数(RPN)", type: "number", readonly: true },
                                { name: "recommended_actions", label: "建议措施", type: "textarea" },
                                { name: "responsible_person", label: "责任人", type: "text" },
                                { name: "target_completion_date", label: "目标完成日期", type: "date" },
                                { name: "actions_taken", label: "已采取的措施", type: "textarea" },
                                { name: "final_severity", label: "最终严重度", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"] },
                                { name: "final_occurrence", label: "最终发生度", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"] },
                                { name: "final_detection", label: "最终探测度", type: "select", options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"] },
                                { name: "final_rpn", label: "最终RPN", type: "number", readonly: true },
                                { name: "approval_status", label: "审批状态", type: "select", options: ["待审批", "已审批", "需修改"] }
                            ]
                        }
                    },
                    {
                        title: "可制造性、装配和服务性设计",
                        description: "考虑产品的可制造性、装配性和服务性，优化设计方案。确保产品易于制造、装配和维护。"
                    },
                    {
                        title: "设计评审",
                        description: "组织设计评审会议，邀请相关专家参与评审。确保设计满足所有要求，识别设计问题并制定改进措施。"
                    },
                    {
                        title: "样件制造-控制计划",
                        description: "制定样件制造控制计划，明确样件制造的要求、检验标准、质量控制点等。确保样件质量。"
                    },
                    {
                        title: "工程图（包括数据）",
                        description: "完成工程图纸设计，包括零件图、装配图、技术要求等。确保图纸的准确性和完整性。"
                    },
                    {
                        title: "工程规范",
                        description: "制定工程规范，包括材料规范、工艺规范、检验规范等。为制造和检验提供标准。"
                    },
                    {
                        title: "材料规范",
                        description: "确定材料规范，包括材料牌号、性能要求、供应商要求等。确保材料质量。"
                    },
                    {
                        title: "图纸和规范变更",
                        description: "管理图纸和规范的变更，确保变更的受控和追溯。更新相关文档和记录。"
                    },
                    {
                        title: "新设备、工装和设施",
                        description: "识别新设备、工装和设施的需求，制定采购计划。确保设备满足生产要求。"
                    },
                    {
                        title: "产品和过程特殊特性",
                        description: "更新产品和过程特殊特性清单，确保特殊特性的识别和控制。制定特殊特性的监控计划。"
                    },
                    {
                        title: "量具/试验设备要求",
                        description: "确定量具和试验设备的要求，包括精度要求、校准要求等。确保测量设备的准确性。"
                    },
                    {
                        title: "领导支持",
                        description: "获得领导对产品设计和开发阶段的支持，确保资源投入和决策支持。"
                    },
                    {
                        title: "量具/试验设备要求",
                        description: "制定量具和试验设备的采购计划，确保设备及时到位。建立设备管理制度。"
                    }
                ],
                description: "产品设计和开发阶段，聚焦设计FMEA、评审、规范、工装等。"
            },
            {
                name: "过程设计和开发",
                tasks: [
                    {
                        title: "包装/标识和规范",
                        description: "制定包装和标识规范，包括包装材料、标识要求、运输要求等。确保产品在运输过程中的安全。"
                    },
                    {
                        title: "产品/过程质量体系评审",
                        description: "评审产品/过程质量体系，确保体系的有效性和适用性。识别体系改进机会。"
                    },
                    {
                        title: "过程流程图",
                        description: "完善过程流程图，详细描述每个工序的步骤和要求。确定过程控制点和质量检查点。"
                    },
                    {
                        title: "过程失效模式和影响分析",
                        description: "进行过程FMEA分析，识别过程中的潜在失效模式、失效原因和失效影响。制定预防措施。"
                    },
                    {
                        title: "试生产控制计划",
                        description: "制定试生产控制计划，明确试生产的要求、检验标准、质量控制点等。确保试生产质量。"
                    },
                    {
                        title: "过程能力研究",
                        description: "进行过程能力研究，评估过程的稳定性和能力。识别过程改进机会。"
                    },
                    {
                        title: "测量系统分析",
                        description: "进行测量系统分析，评估测量系统的准确性和稳定性。确保测量结果的可靠性。"
                    },
                    {
                        title: "初始过程能力研究计划",
                        description: "制定初始过程能力研究计划，明确研究的方法、样本量、评价标准等。"
                    },
                    {
                        title: "过程参数研究计划",
                        description: "制定过程参数研究计划，识别关键过程参数，制定参数优化方案。"
                    },
                    {
                        title: "领导支持",
                        description: "获得领导对过程设计和开发阶段的支持，确保资源投入和决策支持。"
                    }
                ],
                description: "过程设计和开发阶段，关注流程、FMEA、能力、测量系统等。"
            },
            {
                name: "产品和过程确认",
                tasks: [
                    {
                        title: "有效生产运行",
                        description: "进行有效生产运行，验证生产过程的稳定性和能力。确保生产过程满足要求。"
                    },
                    {
                        title: "测量系统分析",
                        description: "完成测量系统分析，确保测量系统的准确性和稳定性。验证测量结果的可靠性。"
                    },
                    {
                        title: "初始过程能力研究",
                        description: "完成初始过程能力研究，评估过程的稳定性和能力。确认过程满足要求。"
                    },
                    {
                        title: "生产件批准",
                        description: "完成生产件批准程序，获得客户的批准。确保产品满足客户要求。"
                    },
                    {
                        title: "生产确认测试",
                        description: "进行生产确认测试，验证产品的性能和可靠性。确保产品满足设计要求。"
                    },
                    {
                        title: "包装评审",
                        description: "进行包装评审，验证包装方案的适用性。确保产品在运输和存储过程中的安全。"
                    },
                    {
                        title: "生产控制计划",
                        description: "制定生产控制计划，明确生产过程中的控制要求、检验标准、质量控制点等。"
                    },
                    {
                        title: "质量系统评审和领导层支持",
                        description: "进行质量系统评审，确保质量体系的有效性。获得领导层的持续支持。"
                    }
                ],
                description: "产品和过程确认阶段，聚焦生产验证、能力、控制计划等。"
            },
            {
                name: "反馈评估和纠正措施",
                tasks: [
                    {
                        title: "减少变差",
                        description: "持续监控过程变差，识别变差来源，制定减少变差的措施。提高过程的稳定性和一致性。"
                    },
                    {
                        title: "提升顾客满意度",
                        description: "收集和分析顾客反馈，识别改进机会，制定提升顾客满意度的措施。建立顾客满意度监控机制。"
                    },
                    {
                        title: "改善顾客服务和交付",
                        description: "优化顾客服务流程，改善交付表现，提高顾客服务水平。建立顾客服务标准。"
                    },
                    {
                        title: "有效利用经验教训/最佳实践",
                        description: "总结项目经验教训，识别最佳实践，建立知识管理体系。促进持续改进。"
                    }
                ],
                description: "反馈评估和纠正措施阶段，关注持续改进和客户满意。"
            }
        ];

        // 项目模板
        const projectTemplates = {
            '发动机控制系统': {
                name: '发动机控制系统APQP项目',
                customer: '大众汽车',
                number: 'APQP-2024-001',
                manager: '张工程师',
                startDate: '2024-01-15',
                endDate: '2024-12-31',
                productType: '电子系统',
                status: 'active',
                inputList: ["顾客的声音","商业计划/营销策略","产品/过程标杆数据","产品/过程假设和可靠性研究","顾客事件","顾客需求"],
                phases: {
                    "1": {"0": true, "1": true, "2": true, "3": false, "4": false, "5": false, "6": false, "7": true, "8": false, "9": false, "10": false},
                    "2": {"0": true, "1": true, "2": true, "3": false, "4": true, "5": true, "6": true, "7": false, "8": false, "9": true, "10": false, "11": true, "12": false},
                    "3": {"0": false, "1": false, "2": true, "3": false, "4": false, "5": false, "6": false, "7": false, "8": false, "9": true},
                    "4": {"0": false, "1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false},
                    "5": {"0": false, "1": false, "2": false, "3": false}
                },
                documents: []
            },
            '变速箱总成': {
                name: '自动变速箱总成开发项目',
                customer: '丰田汽车',
                number: 'APQP-2024-002',
                manager: '李经理',
                startDate: '2024-02-01',
                endDate: '2025-01-31',
                productType: '变速箱',
                status: 'active',
                inputList: ["顾客的声音","商业计划/营销策略","产品/过程标杆数据","产品/过程假设和可靠性研究","顾客事件","顾客需求"],
                phases: {
                    "1": {"0": true, "1": true, "2": true, "3": true, "4": true, "5": false, "6": false, "7": true, "8": false, "9": false, "10": false},
                    "2": {"0": true, "1": true, "2": true, "3": true, "4": true, "5": true, "6": true, "7": false, "8": false, "9": true, "10": true, "11": true, "12": false},
                    "3": {"0": true, "1": true, "2": true, "3": false, "4": false, "5": false, "6": false, "7": false, "8": false, "9": true},
                    "4": {"0": false, "1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false},
                    "5": {"0": false, "1": false, "2": false, "3": false}
                },
                documents: []
            },
            '新能源汽车电池包': {
                name: '高能量密度电池包开发',
                customer: '比亚迪',
                number: 'APQP-2024-003',
                manager: '刘博士',
                startDate: '2024-05-01',
                endDate: '2025-04-30',
                productType: '电子系统',
                status: 'planning',
                inputList: ["顾客的声音","商业计划/营销策略","产品/过程标杆数据","产品/过程假设和可靠性研究","顾客事件","顾客需求"],
                phases: {
                    "1": {"0": true, "1": true, "2": false, "3": false, "4": false, "5": false, "6": false, "7": true, "8": false, "9": false, "10": false},
                    "2": {"0": false, "1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false, "8": false, "9": false, "10": false, "11": true, "12": false},
                    "3": {"0": false, "1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false, "8": false, "9": true},
                    "4": {"0": false, "1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false},
                    "5": {"0": false, "1": false, "2": false, "3": false}
                },
                documents: []
            }
        };

        // 全局变量
        let projectData = {
            projectInfo: {},
            phases: {},
            documents: []
        };

        // 输入项数据（本地存储）
        let inputList = [];
        const defaultInputs = [
            "顾客的声音",
            "商业计划/营销策略",
            "产品/过程标杆数据",
            "产品/过程假设和可靠性研究",
            "顾客事件",
            "顾客需求"
        ];

        function loadInputList() {
            const data = localStorage.getItem('apqpInputList');
            if (data) {
                inputList = JSON.parse(data);
            } else {
                inputList = [...defaultInputs];
            }
        }

        function saveInputList() {
            localStorage.setItem('apqpInputList', JSON.stringify(inputList));
        }

        function getCircledNumber(n) {
            // 支持1-20
            const circled = ['①','②','③','④','⑤','⑥','⑦','⑧','⑨','⑩','⑪','⑫','⑬','⑭','⑮','⑯','⑰','⑱','⑲','⑳'];
            return circled[n-1] || n;
        }

        function renderInputList() {
            const ul = document.getElementById('inputList');
            ul.innerHTML = '';
            inputList.forEach((item, idx) => {
                const li = document.createElement('li');
                li.style.display = 'flex';
                li.style.alignItems = 'center';
                li.style.marginBottom = '8px';
                li.innerHTML = `
                    <span style=\"margin-right:8px; color:#667eea; font-weight:bold; min-width:22px; display:inline-block; text-align:right; font-size:1.15em;\">${getCircledNumber(idx+1)}</span>
                    <input type=\"text\" value=\"${item}\" style=\"flex:1; padding:6px 10px; border-radius:6px; border:1.5px solid #e0e0e0; font-size:1em; margin-right:8px;\" onchange=\"editInputItem(${idx}, this.value)\">
                    <button class=\"btn btn-danger\" style=\"padding:4px 10px; font-size:0.95em;\" onclick=\"removeInputItem(${idx})\">删除</button>
                `;
                ul.appendChild(li);
            });
        }

        function addInputItem() {
            inputList.push('');
            saveInputList();
            renderInputList();
            updateStatistics();
        }
        function editInputItem(idx, val) {
            inputList[idx] = val;
            saveInputList();
            updateStatistics();
        }
        function removeInputItem(idx) {
            inputList.splice(idx, 1);
            saveInputList();
            renderInputList();
            updateStatistics();
        }
        // 页面加载时初始化输入区
        window.addEventListener('DOMContentLoaded', function() {
            loadInputList();
            renderInputList();
            // 先渲染输入区再统计
            renderPhases();
            updateStatistics();
            renderDocuments();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProjectData();
            renderPhases();
            updateStatistics();
            renderDocuments();
        });

        // 渲染阶段
        function renderPhases() {
            const container = document.getElementById('phasesContainer');
            container.innerHTML = '';
            
            apqpPhases.forEach((phase, index) => {
                const phaseNumber = index + 1;
                const completedTasks = getCompletedTasks(phaseNumber);
                const totalTasks = phase.tasks.length;
                const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
                const status = progress === 0 ? 'pending' : (progress === 100 ? 'completed' : 'in-progress');
                const statusText = progress === 0 ? '待开始' : (progress === 100 ? '已完成' : '进行中');
                
                // 调试信息
                console.log(`渲染阶段 ${phaseNumber}: ${phase.name}, 任务数: ${totalTasks}, 已完成: ${completedTasks}`);
                phase.tasks.forEach((task, idx) => {
                    console.log(`  任务 ${idx + 1}: ${task.title} (完成: ${isTaskCompleted(phaseNumber, idx)})`);
                });
                
                const phaseCard = document.createElement('div');
                phaseCard.className = 'phase-card';
                phaseCard.innerHTML = `
                    <div class=\"phase-header\">
                        <div class=\"phase-number\">${phaseNumber}</div>
                        <div class=\"phase-title\">${phase.name}</div>
                        <div class=\"phase-status status-${status}\">${statusText}</div>
                    </div>
                    <div class=\"progress-bar\">
                        <div class=\"progress-fill\" style=\"width: ${progress}%\"></div>
                    </div>
                    <div class=\"task-list\">
                        ${phase.tasks.map((task, taskIndex) => {
                            const isCompleted = isTaskCompleted(phaseNumber, taskIndex);
                            return `
                                <div class=\"task-item\" style=\"border-left:none; display:flex; align-items:flex-start; gap:8px; padding:12px;\">
                                    <span style=\"color:#667eea; font-weight:bold; min-width:22px; text-align:right; font-size:1.15em; flex-shrink:0; margin-top:2px;\">${getCircledNumber(taskIndex+1)}</span>
                                    <input type=\"checkbox\" class=\"task-checkbox\" 
                                           ${isCompleted ? 'checked' : ''} 
                                           onchange=\"toggleTask(${phaseNumber}, ${taskIndex})\"
                                           style=\"margin:0; flex-shrink:0; margin-top:2px;\">
                                    <div class=\"task-content\" style=\"flex:1; display:flex; flex-direction:column; gap:4px;\">
                                        <div class=\"task-title\" style=\"color:${isCompleted ? '#aaa' : '#2c3e50'}; font-weight:bold; font-size:15px; line-height:1.3; cursor:pointer; display:flex; align-items:center; gap:6px;\" onclick=\"toggleTaskDescription(${phaseNumber}, ${taskIndex})\">
                                            <span style=\"font-size:12px; transition:transform 0.2s;\" id=\"arrow-${phaseNumber}-${taskIndex}\">▶</span>
                                            ${task.title}
                                        </div>
                                        <div class=\"task-description\" id=\"desc-${phaseNumber}-${taskIndex}\" style=\"color:${isCompleted ? '#aaa' : '#666'}; font-size:13px; line-height:1.4; word-break:break-word; background:${isCompleted ? '#f0f0f0' : '#f8f9fa'}; padding:6px 8px; border-radius:4px; border-left:3px solid ${isCompleted ? '#ccc' : '#667eea'}; display:none; transition:all 0.3s;\">${task.description}</div>
                                        ${task.formTemplate ? `
                                            <div style=\"display:flex; align-items:center; gap:8px; margin-top:6px;\">
                                                <button class=\"btn btn-info\" style=\"padding:6px 12px; font-size:12px;\" onclick=\"openTaskForm(${phaseNumber}, ${taskIndex})\">
                                                    ${isFormCompleted(phaseNumber, taskIndex) ? '📝 编辑表单' : '📝 填写表单'}
                                                </button>
                                                ${isFormCompleted(phaseNumber, taskIndex) ? '<span style=\"color:#27ae60; font-size:12px; font-weight:bold;\">✅ 已完成</span>' : '<span style=\"color:#f39c12; font-size:12px; font-weight:bold;\">⏳ 待完成</span>'}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                    <div style=\"display:flex; gap:8px; margin-top:10px;\">
                        <button class=\"btn btn-success\" onclick=\"openPhaseDetails(${phaseNumber})\">📋 查看详情</button>
                        <button class=\"btn btn-secondary\" onclick=\"toggleAllTaskDescriptions(${phaseNumber})\">📖 展开/收起描述</button>
                        <button class=\"btn btn-info\" onclick=\"showFormProgress(${phaseNumber})\">📊 表单进度</button>
                    </div>
                `;
                container.appendChild(phaseCard);
            });
        }

        // 获取已完成任务数
        function getCompletedTasks(phaseNumber) {
            if (!projectData.phases[phaseNumber]) return 0;
            return Object.values(projectData.phases[phaseNumber]).filter(Boolean).length;
        }

        // 检查任务是否完成
        function isTaskCompleted(phaseNumber, taskIndex) {
            return projectData.phases[phaseNumber] && projectData.phases[phaseNumber][taskIndex];
        }

        // 切换任务状态
        function toggleTask(phaseNumber, taskIndex) {
            if (!projectData.phases[phaseNumber]) {
                projectData.phases[phaseNumber] = {};
            }
            projectData.phases[phaseNumber][taskIndex] = !projectData.phases[phaseNumber][taskIndex];
            saveProjectData();
            renderPhases();
            updateStatistics();
        }

        // 切换任务描述显示
        function toggleTaskDescription(phaseNumber, taskIndex) {
            const descElement = document.getElementById(`desc-${phaseNumber}-${taskIndex}`);
            const arrowElement = document.getElementById(`arrow-${phaseNumber}-${taskIndex}`);
            
            if (descElement.style.display === 'none' || descElement.style.display === '') {
                descElement.style.display = 'block';
                arrowElement.style.transform = 'rotate(90deg)';
            } else {
                descElement.style.display = 'none';
                arrowElement.style.transform = 'rotate(0deg)';
            }
        }

        // 切换所有任务描述显示
        function toggleAllTaskDescriptions(phaseNumber) {
            const phase = apqpPhases[phaseNumber - 1];
            const taskCount = phase.tasks.length;
            
            // 检查当前状态（是否所有描述都隐藏）
            let allHidden = true;
            for (let i = 0; i < taskCount; i++) {
                const descElement = document.getElementById(`desc-${phaseNumber}-${i}`);
                if (descElement && descElement.style.display !== 'none') {
                    allHidden = false;
                    break;
                }
            }
            
            // 根据当前状态切换所有描述
            for (let i = 0; i < taskCount; i++) {
                const descElement = document.getElementById(`desc-${phaseNumber}-${i}`);
                const arrowElement = document.getElementById(`arrow-${phaseNumber}-${i}`);
                
                if (descElement && arrowElement) {
                    if (allHidden) {
                        descElement.style.display = 'block';
                        arrowElement.style.transform = 'rotate(90deg)';
                    } else {
                        descElement.style.display = 'none';
                        arrowElement.style.transform = 'rotate(0deg)';
                    }
                }
            }
        }

        // 打开任务表单
        function openTaskForm(phaseNumber, taskIndex) {
            const phase = apqpPhases[phaseNumber - 1];
            const task = phase.tasks[taskIndex];
            
            if (!task.formTemplate) {
                alert('该任务没有表单模板');
                return;
            }
            
            document.getElementById('taskFormTitle').textContent = `${phase.name} - ${task.title}`;
            renderTaskForm(task.formTemplate, phaseNumber, taskIndex);
            document.getElementById('taskFormModal').style.display = 'block';
        }

        // 渲染任务表单
        function renderTaskForm(formTemplate, phaseNumber, taskIndex) {
            const formContent = document.getElementById('taskFormContent');
            
            // 加载已保存的表单数据
            const savedData = loadTaskFormData(phaseNumber, taskIndex);
            
            let formHTML = `
                <form id="taskForm" onsubmit="saveTaskForm(event, ${phaseNumber}, ${taskIndex})">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            `;
            
            formTemplate.fields.forEach(field => {
                const value = savedData[field.name] || '';
                const required = field.required ? 'required' : '';
                const readonly = field.readonly ? 'readonly' : '';
                
                let inputHTML = '';
                
                switch (field.type) {
                    case 'text':
                        inputHTML = `<input type="text" name="${field.name}" value="${value}" ${required} ${readonly} style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">`;
                        break;
                    case 'textarea':
                        inputHTML = `<textarea name="${field.name}" ${required} ${readonly} style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; min-height: 80px; resize: vertical;">${value}</textarea>`;
                        break;
                    case 'number':
                        inputHTML = `<input type="number" name="${field.name}" value="${value}" ${required} ${readonly} style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">`;
                        break;
                    case 'date':
                        inputHTML = `<input type="date" name="${field.name}" value="${value}" ${required} ${readonly} style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">`;
                        break;
                    case 'select':
                        const options = field.options.map(option => 
                            `<option value="${option}" ${value === option ? 'selected' : ''}>${option}</option>`
                        ).join('');
                        inputHTML = `<select name="${field.name}" ${required} ${readonly} style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">${options}</select>`;
                        break;
                }
                
                formHTML += `
                    <div class="form-group">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">
                            ${field.label}${field.required ? ' *' : ''}
                        </label>
                        ${inputHTML}
                    </div>
                `;
            });
            
            formHTML += `
                    </div>
                    <div style="margin-top: 20px; text-align: center;">
                        <button type="submit" class="btn btn-success" style="margin-right: 10px;">💾 保存表单</button>
                        <button type="button" class="btn btn-secondary" onclick="closeTaskFormModal()">❌ 取消</button>
                        <button type="button" class="btn btn-warning" onclick="exportTaskForm(${phaseNumber}, ${taskIndex})">📤 导出PDF</button>
                        <button type="button" class="btn btn-info" onclick="aiFillTaskFormWithDeepseek(${phaseNumber}, ${taskIndex})">🤖 AI智能填写</button>
                    </div>
                </form>
            `;
            
            formContent.innerHTML = formHTML;
            
            // 如果是FMEA表单，添加RPN计算功能
            if (formTemplate.type === 'design_fmea') {
                addFMEACalculations();
            }
        }

        // 保存任务表单数据
        function saveTaskForm(event, phaseNumber, taskIndex) {
            event.preventDefault();
            
            const form = document.getElementById('taskForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // 保存到本地存储
            const storageKey = `taskForm_${phaseNumber}_${taskIndex}`;
            localStorage.setItem(storageKey, JSON.stringify(data));
            
            // 如果表单完成，自动标记任务为完成
            if (isFormCompleted(phaseNumber, taskIndex)) {
                if (!projectData.phases[phaseNumber]) {
                    projectData.phases[phaseNumber] = {};
                }
                projectData.phases[phaseNumber][taskIndex] = true;
                saveProjectData();
                renderPhases();
                updateStatistics();
            }
            
            alert('表单数据已保存！');
        }

        // 加载任务表单数据
        function loadTaskFormData(phaseNumber, taskIndex) {
            const storageKey = `taskForm_${phaseNumber}_${taskIndex}`;
            const data = localStorage.getItem(storageKey);
            return data ? JSON.parse(data) : {};
        }

        // 关闭任务表单模态框
        function closeTaskFormModal() {
            document.getElementById('taskFormModal').style.display = 'none';
        }

        // 导出任务表单为PDF（模拟）
        function exportTaskForm(phaseNumber, taskIndex) {
            const phase = apqpPhases[phaseNumber - 1];
            const task = phase.tasks[taskIndex];
            const data = loadTaskFormData(phaseNumber, taskIndex);
            
            // 创建导出内容
            let exportContent = `${phase.name} - ${task.title}\n`;
            exportContent += '='.repeat(50) + '\n\n';
            
            task.formTemplate.fields.forEach(field => {
                const value = data[field.name] || '';
                exportContent += `${field.label}: ${value}\n`;
            });
            
            // 创建下载链接
            const blob = new Blob([exportContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${task.title}_${new Date().toISOString().split('T')[0]}.txt`;
            link.click();
            
            alert('表单数据已导出！');
        }

        // 检查表单是否完成
        function isFormCompleted(phaseNumber, taskIndex) {
            const data = loadTaskFormData(phaseNumber, taskIndex);
            const phase = apqpPhases[phaseNumber - 1];
            const task = phase.tasks[taskIndex];
            
            if (!task.formTemplate) return false;
            
            // 检查必填字段是否都已填写
            const requiredFields = task.formTemplate.fields.filter(field => field.required);
            return requiredFields.every(field => data[field.name] && data[field.name].trim() !== '');
        }

        // 显示表单进度
        function showFormProgress(phaseNumber) {
            const phase = apqpPhases[phaseNumber - 1];
            const tasksWithForms = phase.tasks.filter(task => task.formTemplate);
            const completedForms = tasksWithForms.filter((task, index) => isFormCompleted(phaseNumber, index));
            
            let progressHTML = `
                <h3>${phase.name} - 表单完成进度</h3>
                <div style="margin: 20px 0;">
                    <div style="background: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #667eea, #764ba2); height: 100%; width: ${tasksWithForms.length > 0 ? (completedForms.length / tasksWithForms.length * 100) : 0}%; transition: width 0.3s;"></div>
                    </div>
                    <div style="text-align: center; margin-top: 10px; font-weight: bold;">
                        ${completedForms.length} / ${tasksWithForms.length} 个表单已完成 (${tasksWithForms.length > 0 ? Math.round(completedForms.length / tasksWithForms.length * 100) : 0}%)
                    </div>
                </div>
                <div style="max-height: 300px; overflow-y: auto;">
            `;
            
            phase.tasks.forEach((task, index) => {
                if (task.formTemplate) {
                    const isCompleted = isFormCompleted(phaseNumber, index);
                    progressHTML += `
                        <div style="display: flex; align-items: center; gap: 10px; padding: 8px; border-bottom: 1px solid #eee;">
                            <span style="color: ${isCompleted ? '#27ae60' : '#f39c12'}; font-weight: bold;">
                                ${isCompleted ? '✅' : '⏳'}
                            </span>
                            <span style="flex: 1;">${task.title}</span>
                            <button class="btn btn-info" style="padding: 4px 8px; font-size: 12px;" onclick="openTaskForm(${phaseNumber}, ${index}); closeModal();">
                                ${isCompleted ? '编辑' : '填写'}
                            </button>
                        </div>
                    `;
                }
            });
            
            progressHTML += '</div>';
            
            document.getElementById('modalTitle').textContent = '表单进度';
            document.getElementById('modalContent').innerHTML = progressHTML;
            document.getElementById('phaseModal').style.display = 'block';
        }

        // 为FMEA表单添加RPN计算功能
        function addFMEACalculations() {
            const severitySelect = document.querySelector('select[name="severity_rating"]');
            const occurrenceSelect = document.querySelector('select[name="occurrence_rating"]');
            const detectionSelect = document.querySelector('select[name="detection_rating"]');
            const rpnInput = document.querySelector('input[name="risk_priority_number"]');
            
            if (severitySelect && occurrenceSelect && detectionSelect && rpnInput) {
                const calculateRPN = () => {
                    const severity = parseInt(severitySelect.value) || 0;
                    const occurrence = parseInt(occurrenceSelect.value) || 0;
                    const detection = parseInt(detectionSelect.value) || 0;
                    const rpn = severity * occurrence * detection;
                    rpnInput.value = rpn;
                };
                
                severitySelect.addEventListener('change', calculateRPN);
                occurrenceSelect.addEventListener('change', calculateRPN);
                detectionSelect.addEventListener('change', calculateRPN);
                
                // 初始计算
                calculateRPN();
            }
        }

        // 打开阶段详情
        function openPhaseDetails(phaseNumber) {
            const phase = apqpPhases[phaseNumber - 1];
            document.getElementById('modalTitle').textContent = phase.name;
            document.getElementById('modalContent').innerHTML = `
                <p style="margin-bottom: 20px; line-height: 1.6;">${phase.description}</p>
                <h3>阶段任务：</h3>
                <div style="margin-left: 20px;">
                    ${phase.tasks.map((task, index) => `
                        <div style="margin-bottom: 15px; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
                            <h4 style="color: #2c3e50; margin-bottom: 8px; font-size: 16px;">${index + 1}. ${task.title}</h4>
                            <p style="color: #666; line-height: 1.5; margin: 0;">${task.description}</p>
                        </div>
                    `).join('')}
                </div>
            `;
            document.getElementById('phaseModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('phaseModal').style.display = 'none';
        }

        // 保存项目信息
        function saveProjectInfo() {
            projectData.projectInfo = {
                name: document.getElementById('projectName').value,
                customer: document.getElementById('customerName').value,
                number: document.getElementById('projectNumber').value,
                manager: document.getElementById('projectManager').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                productType: document.getElementById('productType').value,
                status: document.getElementById('projectStatus').value
            };
            
            saveProjectData();
            alert('项目信息已保存！');
        }

        // 加载项目信息
        function loadProjectInfo() {
            const data = localStorage.getItem('apqpProjectData');
            if (data) {
                projectData = JSON.parse(data);
                const info = projectData.projectInfo;
                
                document.getElementById('projectName').value = info.name || '';
                document.getElementById('customerName').value = info.customer || '';
                document.getElementById('projectNumber').value = info.number || '';
                document.getElementById('projectManager').value = info.manager || '';
                document.getElementById('startDate').value = info.startDate || '';
                document.getElementById('endDate').value = info.endDate || '';
                document.getElementById('productType').value = info.productType || '';
                document.getElementById('projectStatus').value = info.status || 'planning';
                
                renderPhases();
                updateStatistics();
                renderDocuments();
                alert('项目信息已加载！');
            } else {
                alert('没有找到保存的项目数据！');
            }
        }

        // 导出项目
        function exportProject() {
            const dataStr = JSON.stringify(projectData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'APQP项目数据.json';
            link.click();
        }

        // 创建新项目
        function createNewProject() {
            if (confirm('确定要创建新项目吗？当前项目数据将被清空。')) {
                projectData = {
                    projectInfo: {},
                    phases: {},
                    documents: []
                };
                
                // 清空表单
                document.getElementById('projectName').value = '';
                document.getElementById('customerName').value = '';
                document.getElementById('projectNumber').value = '';
                document.getElementById('projectManager').value = '';
                document.getElementById('startDate').value = '';
                document.getElementById('endDate').value = '';
                document.getElementById('productType').value = '';
                document.getElementById('projectStatus').value = 'planning';
                
                renderPhases();
                updateStatistics();
                renderDocuments();
                alert('新项目已创建！');
            }
        }

        // 显示项目模板
        function showProjectTemplates() {
            const templateGrid = document.getElementById('templateGrid');
            templateGrid.innerHTML = '';
            
            Object.keys(projectTemplates).forEach(templateKey => {
                const template = projectTemplates[templateKey];
                const templateCard = document.createElement('div');
                templateCard.style.cssText = `
                    background: #f8f9fa;
                    border: 2px solid #e0e0e0;
                    border-radius: 10px;
                    padding: 20px;
                    cursor: pointer;
                    transition: all 0.3s;
                `;
                templateCard.onmouseover = () => {
                    templateCard.style.borderColor = '#667eea';
                    templateCard.style.transform = 'translateY(-2px)';
                };
                templateCard.onmouseout = () => {
                    templateCard.style.borderColor = '#e0e0e0';
                    templateCard.style.transform = 'translateY(0)';
                };
                templateCard.onclick = () => loadProjectTemplate(templateKey);
                
                // 计算预设完成的任务数
                let presetCompletedTasks = 0;
                if (template.phases) {
                    Object.values(template.phases).forEach(phaseTasks => {
                        Object.values(phaseTasks).forEach(completed => {
                            if (completed) presetCompletedTasks++;
                        });
                    });
                }
                
                templateCard.innerHTML = `
                    <h3 style="color: #2c3e50; margin-bottom: 10px;">${template.name}</h3>
                    <p style="color: #7f8c8d; margin-bottom: 8px;"><strong>客户：</strong>${template.customer}</p>
                    <p style="color: #7f8c8d; margin-bottom: 8px;"><strong>项目经理：</strong>${template.manager}</p>
                    <p style="color: #7f8c8d; margin-bottom: 8px;"><strong>产品类型：</strong>${template.productType}</p>
                    <p style="color: #7f8c8d; margin-bottom: 15px;"><strong>项目编号：</strong>${template.number}</p>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">5个阶段</span>
                        <span style="background: #27ae60; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">30个任务</span>
                        <span style="background: #f39c12; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">预设完成${presetCompletedTasks}个</span>
                    </div>
                `;
                
                templateGrid.appendChild(templateCard);
            });
            
            document.getElementById('templateModal').style.display = 'block';
        }

        // 加载项目模板
        function loadProjectTemplate(templateKey) {
            const template = projectTemplates[templateKey];
            
            // 更新项目信息到projectData
            projectData.projectInfo = {
                name: template.name,
                customer: template.customer,
                number: template.number,
                manager: template.manager,
                startDate: template.startDate,
                endDate: template.endDate,
                productType: template.productType,
                status: template.status
            };
            
            // 填充表单
            document.getElementById('projectName').value = template.name;
            document.getElementById('customerName').value = template.customer;
            document.getElementById('projectNumber').value = template.number;
            document.getElementById('projectManager').value = template.manager;
            document.getElementById('startDate').value = template.startDate;
            document.getElementById('endDate').value = template.endDate;
            document.getElementById('productType').value = template.productType;
            document.getElementById('projectStatus').value = template.status;
            
            // 输入区
            inputList = template.inputList ? [...template.inputList] : [...defaultInputs];
            saveInputList();
            renderInputList();
            
            // 阶段任务勾选
            projectData.phases = template.phases ? JSON.parse(JSON.stringify(template.phases)) : {};
            
            // 文档
            projectData.documents = template.documents ? JSON.parse(JSON.stringify(template.documents)) : [];
            
            // 保存到本地存储
            saveProjectData();
            
            // 重新渲染
            renderPhases();
            updateStatistics();
            renderDocuments();
            closeTemplateModal();
            
            // 显示详细的加载信息
            let completedTasks = 0;
            let totalTasks = 0;
            if (template.phases) {
                Object.values(template.phases).forEach(phaseTasks => {
                    Object.values(phaseTasks).forEach(completed => {
                        totalTasks++;
                        if (completed) completedTasks++;
                    });
                });
            }
            
            alert(`已加载"${template.name}"项目模板！\n\n预设完成状态：\n• 已完成任务：${completedTasks}个\n• 总任务数：${totalTasks}个\n• 完成率：${totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}%\n\n您可以在各阶段中查看具体的任务完成状态。`);

            // 在loadProjectTemplate函数里加上
            if (template.forms) {
              Object.keys(template.forms).forEach(key => {
                localStorage.setItem(key, template.forms[key]);
              });
            }
        }

        // 关闭模板模态框
        function closeTemplateModal() {
            document.getElementById('templateModal').style.display = 'none';
        }

        // 更新统计信息
        function updateStatistics() {
            let totalTasks = 0;
            let completedTasks = 0;
            apqpPhases.forEach((phase, index) => {
                const phaseNumber = index + 1;
                totalTasks += phase.tasks.length;
                completedTasks += getCompletedTasks(phaseNumber);
            });
            // 统计输入区
            const inputCount = inputList.length;
            document.getElementById('totalTasks').textContent = totalTasks + inputCount;
            document.getElementById('completedTasks').textContent = completedTasks;
            document.getElementById('completionRate').textContent = (totalTasks + inputCount) > 0 ? Math.round((completedTasks / (totalTasks + inputCount)) * 100) + '%' : '0%';
            // 计算剩余天数
            const endDate = document.getElementById('endDate').value;
            if (endDate) {
                const today = new Date();
                const end = new Date(endDate);
                const diffTime = end - today;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                document.getElementById('daysRemaining').textContent = diffDays > 0 ? diffDays : 0;
            }
        }

        // 渲染文档
        function renderDocuments() {
            const grid = document.getElementById('documentsGrid');
            const documents = projectData.documents || [];
            
            if (documents.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; color: #7f8c8d; padding: 40px;">暂无文档</div>';
                return;
            }
            
            grid.innerHTML = documents.map(doc => `
                <div class="document-card">
                    <div class="document-icon">📄</div>
                    <div class="document-name">${doc.name}</div>
                    <div class="document-status">${doc.date}</div>
                </div>
            `).join('');
        }

        // 上传文档（模拟）
        function uploadDocument() {
            const docName = prompt('请输入文档名称：');
            if (docName) {
                if (!projectData.documents) projectData.documents = [];
                projectData.documents.push({
                    name: docName,
                    date: new Date().toISOString().split('T')[0]
                });
                saveProjectData();
                renderDocuments();
                alert('文档已添加！');
            }
        }

        // 查看所有文档
        function viewAllDocuments() {
            const documents = projectData.documents || [];
            if (documents.length === 0) {
                alert('暂无文档');
                return;
            }
            
            let docList = '已上传的文档：\n\n';
            documents.forEach((doc, index) => {
                docList += `${index + 1}. ${doc.name} (${doc.date})\n`;
            });
            alert(docList);
        }

        // 保存项目数据
        function saveProjectData() {
            localStorage.setItem('apqpProjectData', JSON.stringify(projectData));
        }

        // 加载项目数据
        function loadProjectData() {
            const data = localStorage.getItem('apqpProjectData');
            if (data) {
                projectData = JSON.parse(data);
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = ['phaseModal', 'templateModal', 'taskFormModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 公司LOGO上传与显示
        function uploadCompanyLogo(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    localStorage.setItem('company-logo', e.target.result);
                    updateCompanyInfoDisplay();
                };
                reader.readAsDataURL(file);
            }
        }

        // 公司名称输入
        function editCompanyName() {
            const currentName = localStorage.getItem('company-name') || '公司名称';
            const newName = prompt('请输入公司名称：', currentName);
            if (newName !== null && newName.trim() !== '') {
                localStorage.setItem('company-name', newName.trim());
                updateCompanyInfoDisplay();
            }
        }

        // 更新公司信息显示
        function updateCompanyInfoDisplay() {
            const companyName = localStorage.getItem('company-name') || '公司名称';
            const companyLogo = localStorage.getItem('company-logo');
            const nameElement = document.getElementById('company-name');
            const logoElement = document.getElementById('company-logo');
            if (nameElement) nameElement.textContent = companyName;
            if (companyLogo) {
                logoElement.src = companyLogo;
                logoElement.style.display = 'block';
            } else {
                logoElement.style.display = 'none';
            }
        }

        // 页面加载时初始化公司信息
        window.addEventListener('DOMContentLoaded', function() {
            updateCompanyInfoDisplay();
        });

        // 增加"保存为模板"按钮和逻辑
        function saveCurrentAsTemplate() {
            const templateName = prompt('请输入要保存的模板名称：');
            if (!templateName) return;
            // 构建模板对象
            const newTemplate = {
                name: document.getElementById('projectName').value,
                customer: document.getElementById('customerName').value,
                number: document.getElementById('projectNumber').value,
                manager: document.getElementById('projectManager').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                productType: document.getElementById('productType').value,
                status: document.getElementById('projectStatus').value,
                inputList: [...inputList],
                phases: JSON.parse(JSON.stringify(projectData.phases)),
                documents: JSON.parse(JSON.stringify(projectData.documents))
            };
            // 在saveCurrentAsTemplate函数里加上
            let forms = {};
            apqpPhases.forEach((phase, phaseIdx) => {
              phase.tasks.forEach((task, taskIdx) => {
                const key = `taskForm_${phaseIdx+1}_${taskIdx}`;
                const data = localStorage.getItem(key);
                if (data) forms[key] = data;
              });
            });
            newTemplate.forms = forms;
            projectTemplates[templateName] = newTemplate;
            alert('模板已保存！下次可在项目模板中选择。');
        }

        // 在<script>末尾添加Deepseek智能填写函数
        async function aiFillTaskFormWithDeepseek(phaseNumber, taskIndex) {
            const phase = apqpPhases[phaseNumber - 1];
            const task = phase.tasks[taskIndex];
            const formTemplate = task.formTemplate;
            if (!formTemplate) return;

            // 构造prompt
            let prompt = `请根据以下任务，智能填写表单内容，输出格式为JSON对象，key为字段英文名，value为建议内容：\n`;
            prompt += `任务标题：${task.title}\n任务描述：${task.description}\n表单字段：\n`;
            formTemplate.fields.forEach(field => {
                prompt += `- ${field.label}（${field.name}）\n`;
            });

            // Deepseek API Key（请替换为你自己的）
            const API_KEY = "你的Deepseek_API_KEY"; // TODO: 替换为你的Deepseek API Key

            try {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + API_KEY,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: "deepseek-chat",
                        messages: [{role: "user", content: prompt}],
                        temperature: 0.7
                    })
                });
                const data = await response.json();
                if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
                    let aiContent = data.choices[0].message.content;
                    let aiObj = {};
                    try {
                        aiObj = JSON.parse(aiContent.match(/\{[\s\S]*\}/)[0]);
                    } catch (e) {
                        alert("AI返回内容解析失败，请手动填写。原始内容：" + aiContent);
                        return;
                    }
                    formTemplate.fields.forEach(field => {
                        const input = document.querySelector(`[name="${field.name}"]`);
                        if (input && aiObj[field.name]) {
                            input.value = aiObj[field.name];
                        }
                    });
                    alert('AI已自动填写表单内容，请根据实际情况修改后保存。');
                } else {
                    alert("AI未返回有效内容，请稍后重试。");
                }
            } catch (err) {
                alert("调用Deepseek API失败：" + err.message);
            }
        }


        
        // 初始化简道云项目计划模板
        function initJiandaoyunProjectTemplate() {
            // 初始化项目类型分布图表
            const ctx = document.getElementById('projectTypeChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['生产制造', '产品研发', '售后运维', '产品设计'],
                        datasets: [{
                            data: [40, 30, 20, 10],
                            backgroundColor: ['#9192ab', '#3082f2', '#2b5ab3', '#f5b445'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        cutout: '60%'
                    }
                });
            }

            // 初始化项目来源柱状图
            const ctx2 = document.getElementById('projectSourceChart');
            if (ctx2) {
                new Chart(ctx2, {
                    type: 'bar',
                    data: {
                        labels: ['广告营销', '客户介绍', '朋友介绍', '销售自拓'],
                        datasets: [{
                            label: '项目数量',
                            data: [1, 5, 2, 2],
                            backgroundColor: ['#a9c8fc', '#3082f2', '#40d99d', '#ff6b6b'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 5
                            }
                        }
                    }
                });
            }

            // 绑定快速入口点击事件
            const entryItems = document.querySelectorAll('.jiandaoyun-project-template .node-wrapper');
            entryItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.getAttribute('title');
                    handleJiandaoyunEntryClick(title);
                });
                
                // 添加悬停效果
                item.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(28, 75, 141, 0.1)';
                    this.style.transform = 'translateY(-2px)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.transform = 'translateY(0)';
                });
            });

            // 绑定筛选器点击事件
            const filterItems = document.querySelectorAll('.jiandaoyun-project-template .dash-card-item');
            filterItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他选中状态
                    filterItems.forEach(fi => {
                        const wrapper = fi.querySelector('.node-wrapper');
                        wrapper.style.background = '#fff';
                        wrapper.style.color = '#1f2d3d';
                        wrapper.style.border = '1px solid #e0e0e0';
                    });
                    
                    // 设置当前选中状态
                    const wrapper = this.querySelector('.node-wrapper');
                    wrapper.style.background = '#1c4b8d';
                    wrapper.style.color = '#fff';
                    wrapper.style.border = '1px solid #1c4b8d';
                    
                    // 执行筛选
                    const status = wrapper.textContent;
                    filterJiandaoyunTasksByStatus(status);
                });
            });
        }

        // 处理简道云快速入口点击
        function handleJiandaoyunEntryClick(title) {
            switch(title) {
                case '项目管理':
                    showJiandaoyunNotification('跳转到项目管理模块');
                    break;
                case '项目立项':
                    showJiandaoyunNotification('跳转到项目立项模块');
                    break;
                case '项目计划':
                    showJiandaoyunNotification('跳转到项目计划模块');
                    break;
                case '项目合同':
                    showJiandaoyunNotification('跳转到项目合同模块');
                    break;
                case '项目结项申请':
                    showJiandaoyunNotification('跳转到项目结项申请模块');
                    break;
                case '查看全部':
                    showJiandaoyunNotification('显示所有项目列表');
                    break;
            }
        }

        // 按状态筛选简道云任务
        function filterJiandaoyunTasksByStatus(status) {
            showJiandaoyunNotification(`筛选任务状态: ${status}`);
            // 这里可以添加实际的筛选逻辑
        }

        // 显示简道云通知
        function showJiandaoyunNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1c4b8d;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 渲染甘特图
        function renderGanttChart() {
            const ganttData = [
                { name: '子任务1.2', start: '2024-05-06', end: '2024-05-20', progress: 60 },
                { name: '起重设备生产子任务2.2', start: '2024-03-30', end: '2024-04-21', progress: 100 },
                { name: '方案设计', start: '2024-04-22', end: '2024-05-11', progress: 80 },
                { name: '物资采购', start: '2024-04-30', end: '2024-05-12', progress: 40 },
                { name: '采购任务4.1', start: '2024-04-21', end: '2024-04-25', progress: 100 },
                { name: '零件采购', start: '2024-04-28', end: '2024-05-03', progress: 70 },
                { name: '生产制造', start: '2024-05-01', end: '2024-05-19', progress: 30 },
                { name: '设备调试', start: '2024-05-14', end: '2024-05-26', progress: 0 },
                { name: '项目结项', start: '2024-05-17', end: '2024-06-09', progress: 0 }
            ];
            
            const ganttBody = document.getElementById('ganttBody');
            if (!ganttBody) return;
            
            ganttBody.innerHTML = '';
            
            ganttData.forEach(task => {
                const taskElement = document.createElement('div');
                taskElement.style.display = 'grid';
                taskElement.style.gridTemplateColumns = '200px 1fr';
                taskElement.style.gap = '16px';
                taskElement.style.marginBottom = '8px';
                taskElement.style.alignItems = 'center';
                
                const startDate = new Date(task.start);
                const endDate = new Date(task.end);
                const totalDays = (endDate - startDate) / (1000 * 60 * 60 * 24);
                const progressDays = (totalDays * task.progress) / 100;
                
                taskElement.innerHTML = `
                    <div style="font-weight:500;">${task.name}</div>
                    <div style="position:relative;height:20px;background:#e9ecef;border-radius:10px;overflow:hidden;">
                        <div style="position:absolute;left:0;top:0;height:100%;width:${task.progress}%;background:linear-gradient(90deg,#007bff,#0056b3);border-radius:10px;"></div>
                        <div style="position:absolute;left:0;top:0;height:100%;width:100%;display:flex;align-items:center;justify-content:center;font-size:12px;color:#666;">
                            ${task.progress}%
                        </div>
                    </div>
                `;
                
                ganttBody.appendChild(taskElement);
            });
        }
        
        // 渲染任务列表
        function renderTaskList() {
            const tasks = [
                { id: 1, name: '子任务1.2', status: 'in-progress', assignee: '张三', priority: 'high', startDate: '2024-05-06', endDate: '2024-05-20' },
                { id: 2, name: '起重设备生产子任务2.2', status: 'completed', assignee: '李四', priority: 'medium', startDate: '2024-03-30', endDate: '2024-04-21' },
                { id: 3, name: '方案设计', status: 'in-progress', assignee: '王五', priority: 'high', startDate: '2024-04-22', endDate: '2024-05-11' },
                { id: 4, name: '物资采购', status: 'in-progress', assignee: '赵六', priority: 'medium', startDate: '2024-04-30', endDate: '2024-05-12' },
                { id: 5, name: '采购任务4.1', status: 'completed', assignee: '钱七', priority: 'low', startDate: '2024-04-21', endDate: '2024-04-25' },
                { id: 6, name: '零件采购', status: 'in-progress', assignee: '孙八', priority: 'medium', startDate: '2024-04-28', endDate: '2024-05-03' },
                { id: 7, name: '生产制造', status: 'in-progress', assignee: '周九', priority: 'high', startDate: '2024-05-01', endDate: '2024-05-19' },
                { id: 8, name: '设备调试', status: 'not-started', assignee: '吴十', priority: 'high', startDate: '2024-05-14', endDate: '2024-05-26' },
                { id: 9, name: '项目结项', status: 'not-started', assignee: '郑十一', priority: 'medium', startDate: '2024-05-17', endDate: '2024-06-09' }
            ];
            
            const taskList = document.getElementById('taskList');
            if (!taskList) return;
            
            taskList.innerHTML = '';
            
            tasks.forEach(task => {
                const taskElement = document.createElement('div');
                taskElement.className = 'task-item';
                taskElement.style.cssText = 'background:#f8f9fa;border-radius:8px;padding:16px;margin-bottom:12px;border-left:4px solid #007bff;';
                
                const statusColors = {
                    'not-started': '#ffc107',
                    'in-progress': '#17a2b8',
                    'completed': '#28a745'
                };
                
                const statusText = {
                    'not-started': '未开始',
                    'in-progress': '进行中',
                    'completed': '已完成'
                };
                
                const priorityColors = {
                    'low': '#28a745',
                    'medium': '#ffc107',
                    'high': '#dc3545'
                };
                
                const priorityText = {
                    'low': '低',
                    'medium': '中',
                    'high': '高'
                };
                
                taskElement.innerHTML = `
                    <div style="display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:12px;">
                        <div style="flex:1;min-width:200px;">
                            <h4 style="margin:0 0 8px 0;font-size:16px;">${task.name}</h4>
                            <div style="display:flex;gap:16px;flex-wrap:wrap;">
                                <span style="background:${statusColors[task.status]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;">
                                    ${statusText[task.status]}
                                </span>
                                <span style="background:${priorityColors[task.priority]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;">
                                    优先级: ${priorityText[task.priority]}
                                </span>
                                <span style="color:#666;font-size:12px;">负责人: ${task.assignee}</span>
                            </div>
                        </div>
                        <div style="text-align:right;color:#666;font-size:12px;">
                            <div>${task.startDate} ~ ${task.endDate}</div>
                        </div>
                    </div>
                `;
                
                taskList.appendChild(taskElement);
            });
        }
        
        // 渲染里程碑列表
        function renderMilestoneList() {
            const milestones = [
                { id: 1, name: '项目启动', date: '2024-03-30', status: 'completed', description: '项目正式启动，团队组建完成' },
                { id: 2, name: '需求确认', date: '2024-04-15', status: 'completed', description: '客户需求确认，技术方案制定' },
                { id: 3, name: '设计完成', date: '2024-05-11', status: 'in-progress', description: '产品设计和技术设计完成' },
                { id: 4, name: '生产完成', date: '2024-05-19', status: 'not-started', description: '产品生产制造完成' },
                { id: 5, name: '项目交付', date: '2024-06-09', status: 'not-started', description: '项目最终交付客户' }
            ];
            
            const milestoneList = document.getElementById('milestoneList');
            if (!milestoneList) return;
            
            milestoneList.innerHTML = '';
            
            milestones.forEach(milestone => {
                const milestoneElement = document.createElement('div');
                milestoneElement.className = 'milestone-item';
                milestoneElement.style.cssText = 'background:#f8f9fa;border-radius:8px;padding:16px;margin-bottom:12px;border-left:4px solid #28a745;';
                
                const statusColors = {
                    'not-started': '#6c757d',
                    'in-progress': '#17a2b8',
                    'completed': '#28a745'
                };
                
                const statusText = {
                    'not-started': '未开始',
                    'in-progress': '进行中',
                    'completed': '已完成'
                };
                
                milestoneElement.innerHTML = `
                    <div style="display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:12px;">
                        <div style="flex:1;min-width:200px;">
                            <h4 style="margin:0 0 8px 0;font-size:16px;">${milestone.name}</h4>
                            <p style="margin:0;color:#666;font-size:14px;">${milestone.description}</p>
                        </div>
                        <div style="text-align:right;">
                            <div style="background:${statusColors[milestone.status]};color:white;padding:4px 8px;border-radius:4px;font-size:12px;margin-bottom:4px;">
                                ${statusText[milestone.status]}
                            </div>
                            <div style="color:#666;font-size:12px;">${milestone.date}</div>
                        </div>
                    </div>
                `;
                
                milestoneList.appendChild(milestoneElement);
            });
        }
        
        // 渲染项目经理列表
        function renderManagerList() {
            const managers = [
                { name: '张三', projectCount: 3, workload: 80 },
                { name: '李四', projectCount: 2, workload: 60 },
                { name: '王五', projectCount: 4, workload: 90 },
                { name: '赵六', projectCount: 1, workload: 30 }
            ];
            
            const managerList = document.getElementById('managerList');
            if (!managerList) return;
            
            managerList.innerHTML = '';
            
            managers.forEach(manager => {
                const managerElement = document.createElement('div');
                managerElement.style.cssText = 'display:flex;justify-content:space-between;align-items:center;padding:12px;background:white;border-radius:6px;margin-bottom:8px;';
                
                const workloadColor = manager.workload > 80 ? '#dc3545' : manager.workload > 60 ? '#ffc107' : '#28a745';
                
                managerElement.innerHTML = `
                    <div>
                        <div style="font-weight:bold;">${manager.name}</div>
                        <div style="color:#666;font-size:12px;">负责项目: ${manager.projectCount}个</div>
                    </div>
                    <div style="text-align:right;">
                        <div style="color:${workloadColor};font-weight:bold;">${manager.workload}%</div>
                        <div style="width:60px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                            <div style="width:${manager.workload}%;height:100%;background:${workloadColor};"></div>
                        </div>
                    </div>
                `;
                
                managerList.appendChild(managerElement);
            });
        }
        
        // 任务筛选功能
        function filterTasks(status) {
            const buttons = document.querySelectorAll('.task-filters .btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以添加实际的筛选逻辑
            console.log('筛选任务状态:', status);
        }
        
        // 显示添加任务模态框
        function showAddTaskModal() {
            document.getElementById('addTaskModal').style.display = 'block';
        }
        
        // 关闭添加任务模态框
        function closeAddTaskModal() {
            document.getElementById('addTaskModal').style.display = 'none';
        }
        
        // 页面加载时初始化项目计划模板功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化现有的功能
            updateCompanyInfoDisplay();
            
            // 初始化项目计划模板功能
            setTimeout(() => {
                initDashboard();
            }, 100);
            
            // 添加任务表单提交
            const addTaskForm = document.getElementById('addTaskForm');
            if (addTaskForm) {
                addTaskForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const taskData = {
                        name: document.getElementById('taskName').value,
                        description: document.getElementById('taskDescription').value,
                        startDate: document.getElementById('taskStartDate').value,
                        endDate: document.getElementById('taskEndDate').value,
                        assignee: document.getElementById('taskAssignee').value,
                        priority: document.getElementById('taskPriority').value
                    };
                    
                    console.log('新任务数据:', taskData);
                    
                    // 这里可以添加保存任务的逻辑
                    alert('任务添加成功！');
                    closeAddTaskModal();
                    addTaskForm.reset();
                });
            }
        });


    </script>
</body>
</html> 