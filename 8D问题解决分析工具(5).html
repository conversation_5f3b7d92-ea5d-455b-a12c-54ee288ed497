<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8D问题解决报告</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            line-height: 1.7;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
        }
        .page-navigation {
            text-align: center;
            margin: 32px 0 24px 0;
        }
        .page-navigation button {
            padding: 10px 28px;
            margin: 0 16px;
            font-size: 1em;
            cursor: pointer;
            border: 1.5px solid #0056b3;
            border-radius: 6px;
            background-color: #e9f7ff;
            color: #0056b3;
            transition: background 0.2s, color 0.2s;
        }
        .page-navigation button:hover {
            background-color: #0056b3;
            color: #fff;
        }
        .page-navigation .page-navigation button {
            margin: 0 8px;
        }
        .container {
            max-width: none;
            margin: 0;
            background: #fff;
            padding: 15px 15px 15px 15px;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.09);
        }
        h1 {
            color: #fff;
            background: linear-gradient(90deg, #007bff 60%, #0056b3 100%);
            padding: 4px 0; /* 再次减小高度 */
            border-radius: 12px;
            text-align: center;
            margin-bottom: 18px; /* 原为28px，减小下方间距 */
            font-size: 1em;
            font-weight: 700;
            letter-spacing: 2px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .d-step {
            background: #f7fafd;
            margin: 1px 0 2px 0; /* 进一步减小上下间距 */
            padding: 12px 12px 10px 12px; /* 进一步减小左右padding */
            border-radius: 10px;
            box-shadow: 0 1px 6px rgba(0,0,0,0.05);
            font-size: 1em;
            /* 新增：保证与container左对齐 */
            box-sizing: border-box;
        }
        .d-step h2 {
            color: #0056b3;
            margin: 0 0 14px 0;
            font-size: 1.1em;
            font-weight: 600;
            letter-spacing: 1px;
            text-align: left;
        }
        .dstep-block {
            margin: 8px 0 16px 0;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafbfc;
            width: 100%;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            align-items: center;
            gap: 0;
            margin-bottom: 8px;
            flex-wrap: nowrap;
        }
        .form-label {
            width: 110px;
            min-width: 110px;
            text-align: left;
            margin-right: 8px;
            font-weight: 500;
            display: block;
        }
        .form-input {
            flex: 1;
            margin-right: 18px;
            padding: 7px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
            box-sizing: border-box;
            height: 36px;
            min-width: 0;
        }
        .form-row .form-input:last-child {
            margin-right: 0;
        }
        .dstep-block label,
        .dstep-block strong,
        .dstep-block h3 {
            margin-bottom: 6px;
            text-align: left;
        }
        .dstep-block input[type="text"],
        .dstep-block input[type="date"],
        .dstep-block input[type="number"],
        .dstep-block input[type="datetime-local"],
        .dstep-block textarea,
        .dstep-block select {
            width: 100%;
            min-width: 0;
            box-sizing: border-box;
            font-size: 1em;
            padding: 8px 10px;
            border-radius: 4px;
            border: 1px solid #bfc9d1;
            margin-bottom: 0;
        }
        .dstep-block textarea {
            resize: vertical;
        }
        .dstep-block .form-group,
        .dstep-block .why-inputs {
            margin-bottom: 0;
            gap: 10px;
        }
        .dstep-block .why-inputs {
            display: flex;
            flex-wrap: wrap;
        }
        .dstep-block .why-inputs input[type="text"] {
            flex: 1;
            min-width: 110px;
        }
        /* 移除所有.dstep-block内部的多余嵌套div的margin/padding/gap */
        .dstep-block > div,
        .dstep-block > ul,
        .dstep-block > section {
            margin: 0;
            padding: 0;
            gap: 0;
        }
        .fishbone-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .fishbone-table th, .fishbone-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .fishbone-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .fishbone-table textarea {
            width: 95%;
            height: 60px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 1em;
            resize: vertical;
        }

        .why-inputs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 5px;
        }

        .why-inputs input[type="text"] {
            flex: 1;
            min-width: 150px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 1em;
        }


        .evaluation-table {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .evaluation-table table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .evaluation-table th,
        .evaluation-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        
        .evaluation-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }
        
        .evaluation-table tbody tr:nth-child(odd) {
            background-color: #f9f9f9;
        }
        
        .evaluation-table label {
            display: block;
            margin-bottom: 5px;
            cursor: pointer;
        }
        
        .evaluation-table input[type="radio"] {
            margin-right: 5px;
        }
        
        .evaluation-table textarea {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .score {
            font-weight: bold;
            color: #007bff;
        }
        
        .summary-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .hint-section {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        .hint-section h3 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .hint-section ul {
            margin-left: 20px;
        }
        
        .hint-section li {
            margin-bottom: 5px;
        }
        
        .score-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .overall-comments {
            grid-column: 1 / -1;
            margin-top: 20px;
        }
        
        .overall-comments textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 1em;
        }

        @media (max-width: 700px) {
            .container {
                padding: 10px 2vw;
            }
            .d-step {
                padding: 12px 4vw 12px 4vw;
            }
        }

        .dstep-block .form-row {
            display: flex;
            align-items: center;
            gap: 0;
            margin-bottom: 8px;
            flex-wrap: nowrap;
        }
        .dstep-block .form-label {
            width: 110px;
            min-width: 110px;
            text-align: left;
            margin-right: 8px;
            font-weight: 500;
            display: block;
        }
        .dstep-block .form-input {
            flex: 1;
            margin-right: 18px;
            padding: 7px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
            box-sizing: border-box;
            height: 36px;
            min-width: 0;
        }
        .dstep-block .form-row .form-input:last-child {
            margin-right: 0;
        }
        @media (max-width: 900px) {
            .dstep-block .form-row {
                flex-wrap: wrap;
            }
            .dstep-block .form-label, .dstep-block .form-input {
                min-width: 100px;
                max-width: 100%;
            }
        }

        /* --- 优化单选框与描述文字对齐 --- */
.radio-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  gap: 0;
}
.radio-label {
  flex: 3;
  font-size: 14px;
  line-height: 1.4;
  white-space: normal;
  word-wrap: break-word;
  min-width: 300px;
  max-width: 700px;
}
.radio-options {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  left: -80px;
}
.radio-option {
  display: flex;
  align-items: center;
  font-size: 1em;
  cursor: pointer;
  user-select: none;
  line-height: 1.2;
  height: auto;
  padding: 0;
}
.radio-option input[type="radio"] {
  margin-right: 3px;
  position: static;
  width: 16px;
  height: 16px;
  padding: 0;
}

        /* 修复ul和li的默认样式，确保项目符号不超出边框 */
        .dstep-block ul {
            margin: 0;
            padding-left: 20px;
            list-style-position: inside;
        }
        
        .dstep-block li {
            margin: 0;
            padding: 0;
            list-style-position: inside;
        }

        .eightd-progress {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0 28px 0;
            user-select: none;
            gap: 18px;
        }
        .eightd-step {
            background: #f3f4f5;
            color: #8ca0ad;
            font-weight: bold;
            font-size: 1.3em;
            padding: 10px 32px 10px 24px;
            min-width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            clip-path: polygon(0 0, 85% 0, 100% 50%, 85% 100%, 0 100%, 10% 50%);
            transition: background 0.3s, color 0.3s;
            box-shadow: 0 2px 6px rgba(0,0,0,0.04);
        }
        .eightd-step.completed {
            background: #2ecc40;
            color: #fff;
}
      
        @media (max-width: 700px) {
            .container {
                padding: 10px 2vw;
            }
            .d-step {
                padding: 12px 4vw 12px 4vw;
            }
        }
        
        /* 强制显示页面内容 */
        /* .page-content {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        } */
        
        /* 强制显示页面内容 */
        /* #main-8d-page {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        } */
        
        /* 强制显示页面内容 */
        /* #evaluation-page {
            visibility: visible !important;
            opacity: 1 !important;
        } */
        
        #ai-settings-panel {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* 确保flex布局正常工作 */
        .flex-container {
            display: flex !important;
        }
        
        /* 其他样式保持不变 */
        
        /* 全局强制字体和行高修正 */
        body, .container, .d-step, .d-step h2, h1, h2, h3, h4, .ai-settings-panel, .ai-settings-panel * {
            font-size: 16px !important;
            line-height: 1.7 !important;
        }
        #conclusionRow {
            position: relative;
            left: -80px;
        }
        .sticky-header-bar {
            position: sticky;
            top: 0;
            z-index: 3000 !important;
            background: #f4f4f4;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        /* 增加顶部按钮组与背景边框的间距 */
        #function-buttons {
            padding: 18px 24px 18px 24px !important;
            margin-bottom: 6px !important;
            margin-top: 40px !important;
        }
        .page-navigation {
            padding: 12px 24px !important;
            margin-bottom: 18px !important;
        }
        .page-navigation > div, .page-navigation > button {
            margin-right: 8px;
        }
        .page-navigation button {
            margin-left: 8px;
            margin-right: 8px;
        }
        /* 悬浮操作栏样式 */
        .floating-action-bar {
            position: fixed;
            right: 32px;
            bottom: 32px;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            gap: 12px;
            background: rgba(55,71,87,0.98);
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            padding: 12px 10px 12px 10px;
            align-items: center;
        }
        .floating-action-bar button {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 1em;
            padding: 6px 14px;
            border-radius: 8px;
            border: none;
            background: #fff;
            color: #374757;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .floating-action-bar button:hover {
            background: #007bff;
            color: #fff;
        }
        .floating-action-bar .icon {
            font-size: 1.1em;
        }
        /* AI智能分析弹窗始终在所有面板之上 */
        #aiAnalysisOverlay, #aiAnalysisOverlay > div,
        #aiSuggestionOverlay, #aiSuggestionOverlay > div {
            z-index: 2000 !important;
        }
        /* 恢复AI助手面板z-index，避免盖住吸顶栏 */
        #ai-panel {
            z-index: 1002 !important;
        }
        /* AI智能分析弹窗始终在所有面板之上，且顶部留白不遮住吸顶栏 */
        #aiAnalysisOverlay > div,
        #aiSuggestionOverlay > div {
            top: 100px !important;
            max-height: 120px !important;
            min-width: 240px !important;
            max-width: 500px !important;
            overflow: auto !important;
        }
        .sticky-header-bar {
            z-index: 2000 !important;
        }
        #aiAnalysisOverlay, #aiSuggestionOverlay {
            z-index: 4000 !important;
        }
        #aiAnalysisOverlay > div, #aiSuggestionOverlay > div {
            z-index: 4001 !important;
            top: 120px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
        }
        .sticky-header-bar {
            z-index: 3000 !important;
        }
        #aiAnalysisOverlay, #aiSuggestionOverlay {
            position: fixed !important;
            top: 70px !important;
            left: 0 !important;
            width: 100vw !important;
            height: calc(100vh - 70px) !important;
            z-index: 2000 !important;
            background: rgba(0,0,0,0.5) !important;
        }
        #aiAnalysisOverlay > div, #aiSuggestionOverlay > div {
            position: fixed !important;
            top: 90px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            z-index: 2001 !important;
        }
    </style>
    <!-- 集成 AngularJS 及相关依赖 -->
    
    <!-- 删除外部CSS文件引用，避免样式冲突 -->
    <!-- <link rel="stylesheet" href="8D-Analysis Tool_files/app.css"> -->
    <!-- <link rel="stylesheet" href="8D-Analysis Tool_files/default.css"> -->
</head>
<body>
    <div class="sticky-header-bar">
        <div id="function-buttons" style="width: calc(1500px - 40px); max-width: calc(100vw - 40px); margin-left: auto; margin-right: auto; position: relative; margin-bottom: 6px; margin-top: 40px; background-color: #374757; padding: 18px 24px 18px 24px; border-radius: 8px; box-sizing: border-box; min-height: 60px;">
            <!-- 左侧：公司LOGO和名称 -->
            <div style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); display: flex; align-items: center; gap: 12px;">
                <div id="company-logo-container"
                     style="width: 50px; height: 50px; border: none; border-radius: 8px; display: flex; align-items: center; justify-content: center; background: transparent; cursor: pointer; overflow: hidden;"
                     onclick="document.getElementById('company-logo-upload').click()">
                    <img id="company-logo" src="" alt="公司LOGO"
                         style="max-width: 100%; max-height: 100%; display: none; background: transparent;" />
                    <input type="file" id="company-logo-upload" accept="image/*" style="display: none;" onchange="uploadCompanyLogo(this)">
                </div>
                <span id="company-name" style="color: #fff; font-size: 1.3em; font-weight: bold; min-width: 120px; cursor: pointer;" onclick="editCompanyName()">公司名称</span>
            </div>
            <!-- 中间：工具标题（绝对居中） -->
            <div style="text-align: center; width: 100%;">
                <span style="color: #fff; font-size: 2.0em; font-weight: bold; letter-spacing: 1px;">8D问题解决分析工具</span>
            </div>
        </div>
        <div class="page-navigation" style="display: flex; align-items: center; max-width: 1500px; margin: 0 auto; padding: 0 15px;">
            <div style="display: flex; gap: 16px;">
                <button id="nav-main-8d" onclick="showPage('main-8d-page')" style="padding: 10px 16px; font-size: 1em; background: #007bff; color: #fff; border: none; border-radius: 6px; cursor: pointer; transition: background-color 0.3s ease;">8D问题解决报告</button>
                <button id="nav-evaluation" onclick="showPage('evaluation-page')" style="padding: 10px 16px; font-size: 1em; background: #6c757d; color: #fff; border: none; border-radius: 6px; cursor: pointer; transition: background-color 0.3s ease;">8D报告评估表</button>
            </div>
            <div style="display: flex; gap: 2px; margin-left: 16px;">
                <button id="saveDataBtn" style="padding: 6px 12px; font-size: 1em; background: #007bff; color: #fff; border: none; border-radius: 6px; cursor: pointer; margin: 0;">保存数据</button>
                <button id="clearDataBtn" style="padding: 6px 12px; font-size: 1em; background: #dc3545; color: #fff; border: none; border-radius: 6px; cursor: pointer; margin: 0;">清除数据</button>
                <button id="printReportBtn" style="padding: 6px 12px; font-size: 1em; background: #28a745; color: #fff; border: none; border-radius: 6px; cursor: pointer; margin: 0;">打印报告</button>
                <button id="viewReportBtn" style="padding: 6px 12px; font-size: 1em; background: #6f42c1; color: #fff; border: none; border-radius: 6px; cursor: pointer; margin: 0;">查看报告</button>
            </div>
            <div style="margin-left: 16px; display: flex; align-items: center;">
                <span style="color: #dc3545; font-weight: bold; font-size: 1.2em;">没有解决不了的问题，只有转变不了的心态！</span>
                <span style="color: #000; font-weight: bold; font-size: 1.0em; margin-left: 8px;">尝试去找到对的团队、对的方法、对的工具…</span>
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <!-- 8D问题解决报告页面 - 左右分栏布局 -->
    <div id="main-8d-page" class="page-content" style="display: block;">
        <div style="display: flex; max-width: 1500px; margin: 0 auto; gap: 24px; padding: 0; min-height: 500px;">
            <!-- 左侧：8D报告内容 -->
            <div style="flex: 1; min-width: 300px; padding: 20px;">
                <div class="container">
                    <!-- 主页标题区优化 -->
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 6px;">
                        <h1 style="margin: 0; font-size: 1.5em !important; font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; color: #222 !important; font-weight: 700; text-align: left; background: none !important; box-shadow: none; padding: 0; letter-spacing: 1px;">8D问题解决报告</h1>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <label style="margin: 0; font-size: 0.9em; font-weight: 500;">管理编号:</label>
                            <input type="text" id="manageNo" readonly style="background: #f7f7f7; color: #0056b3; font-weight: bold; border: 1px solid #ccc; border-radius: 4px; padding: 4px 8px; font-size: 0.9em; width: 120px;">
                        </div>
                    </div>
                    <div style="border-bottom:2px solid #007bff;margin-bottom:16px;"></div>
                    <!-- 8D进度条（已隐藏，仅保留代码） -->
                    <div class="eightd-progress" style="display:none;">
                        <div class="eightd-step" id="step-d0">D0</div>
                        <div class="eightd-step" id="step-d1">D1</div>
                        <div class="eightd-step" id="step-d2">D2</div>
                        <div class="eightd-step" id="step-d3">D3</div>
                        <div class="eightd-step" id="step-d4">D4</div>
                        <div class="eightd-step" id="step-d5">D5</div>
                        <div class="eightd-step" id="step-d6">D6</div>
                        <div class="eightd-step" id="step-d7">D7</div>
                        <div class="eightd-step" id="step-d8">D8</div>
                    </div>

                    <div class="d-step">
                        <h2>D0: 8D准备</h2>
                        <p>在正式启动8D流程之前，进行必要的准备工作，包括问题识别、初步评估和紧急响应。</p>
                        <div class="dstep-block">
                            <h3><strong>基础信息</strong></h3>
                            <div class="form-row">
                                <label class="form-label">投诉/问题来源</label>
                                <select class="form-input" name="problemSource" style="width: 120px;">
                                    <option value="">请选择</option>
                                    <option value="I">I: 内部</option>
                                    <option value="E">E: 外部</option>
                                </select>
                                <label class="form-label">投诉/问题类型</label>
                                <select class="form-input" name="problemType" style="width: 120px;">
                                    <option value="">请选择</option>
                                    <option value="Q">Q: 质量</option>
                                    <option value="C">C: 成本</option>
                                    <option value="D">D: 交期</option>
                                </select>
                                <label class="form-label">投诉/问题主题</label>
                                <input class="form-input" type="text" id="problemTopic">
                            </div>
                            <div class="form-row">
                                <label class="form-label">客户/部门/供方</label>
                                <input class="form-input" type="text" id="customerDepartment">
                                <label class="form-label">提出日期</label>
                                <input class="form-input" type="date" id="proposeDate">
                                <label class="form-label">完成期限</label>
                                <input class="form-input" type="date" id="deadline">
                            </div>
                            <div style="margin-top: 8px; margin-bottom: 8px;">
                                <label style="width: 140px; text-align: left; margin-bottom: 2px; font-weight: 500; display: inline-block; vertical-align: top;">识别并确认问题:</label>
                                <textarea placeholder="请在此处填写D0步骤的详细内容..." rows="2" style="width: calc(100% - 150px); min-width: 0; padding: 6px 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; font-size: 1em; line-height: 1.5; resize: vertical; min-height: 36px; max-height: 60px; display: inline-block; vertical-align: top;"></textarea>
                            </div>
                            <div style="margin-bottom: 0;">
                                <label style="width: 140px; text-align: left; margin-bottom: 2px; font-weight: 500; display: inline-block; vertical-align: top;">评估是否启动8D:</label>
                                <div style="background: #fafbfc; border: 1px solid #e0e0e0; border-radius: 6px; padding: 12px 16px 8px 16px; margin-top: 2px; width: 100%; vertical-align: top; box-sizing: border-box;">
                                  <div style="display: flex; align-items: flex-start; gap: 32px; flex-wrap: nowrap;">
                                    <!-- 左侧：所有radio-row和textarea -->
                                    <div style="flex: 3 1 0; min-width: 700px; max-width: 100%;">
                                      <div class="radio-row">
                                        <span class="radio-label">1）问题对客户有严重影响（如安全、法规、重大质量缺陷）？</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="impactQuestion" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="impactQuestion" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                      <textarea id="impactDescription" placeholder="请描述问题对客户的影响..." rows="8" style="height:120px;width: 98%; margin: 4px 0 10px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; display: none;"></textarea>
                                      <div class="radio-row">
                                        <span class="radio-label">2）问题根本原因不明？</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="rootCauseQuestion" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="rootCauseQuestion" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                      <textarea id="rootCauseDescription" placeholder="请描述根本原因不明的情况..." rows="8" style="height:120px;width: 98%; margin: 4px 0 10px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; display: none;"></textarea>
                                      <div class="radio-row">
                                        <span class="radio-label">3) 终端客户投诉、客户产线停线或批量退货、客户明确要求提交8D报告。</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="customerRequestQuestion" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="customerRequestQuestion" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                      <div class="radio-row">
                                        <span class="radio-label">4) 重复/系统性失效时：同一问题重复发生≥3次（如连续3批零件尺寸超差）</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="repeat3Question" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="repeat3Question" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                      <div class="radio-row">
                                        <span class="radio-label">5) 导致内部停线≥4小时，或报废成本（如＞1万元）。</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="costQuestion" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="costQuestion" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                      <div class="radio-row">
                                        <span class="radio-label">6) 内部高层要求时。</span>
                                        <span class="radio-options">
                                          <label class="radio-option"><input type="radio" name="seniorRequestQuestion" value="yes" onchange="update8DConclusion()"> 是</label>
                                          <label class="radio-option"><input type="radio" name="seniorRequestQuestion" value="no" onchange="update8DConclusion()"> 否</label>
                                        </span>
                                      </div>
                                    </div>
                                    <!-- 右侧：结论区 -->
                                    <div id="conclusionRow" style="flex: 1 1 0; max-width: 260px; min-width: 180px;">
                                      <strong style="font-size:1.1em;">最终结论判定：</strong><br>
                                      <span id="conclusion" style="font-weight: bold; font-size: 1.1em; display: block; margin-top: 6px;"></span>
                                      <textarea id="conclusionInput" placeholder="请补充说明启动8D的原因或措施..." style="display:none; margin-top:10px; width:130%; font-size:1.0em; height:8em; padding:12px 18px; border-radius:4px; border:1px solid #bfc9d1; resize: vertical; line-height: 1.5;"></textarea>
                                    </div>
                                  </div>
                                </div>
                            </div>
                            <!-- 删除多余的结论区div，避免页面底部重复显示 -->
                        </div>
                        <div class="d-step">
                            <h2>D1: 建立团队</h2>
                            <p>组建一个跨职能团队，拥有解决问题所需的知识、技能和权限（确定团队成员及其角色，明确团队目标和职责）。</p>
                            <ul style="display: none;">
                                <li>确定团队成员及其角色。</li>
                                <li>明确团队目标和职责。</li>
                            </ul>
                            <div style="margin: 8px 0 16px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 6px; background: #fafbfc;">
                                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                    <thead>
                                        <tr>
                                            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center; width: 15%;">职能部门</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center;">姓名</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center;">职务</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center;">职责</th>
                                            <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center;">技能</th>
                                        </tr>
                                    </thead>

                                <tbody>

                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">业务部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">技术部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">生产部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">品管部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">工艺部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                        <tr>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">采购部</td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                            <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="1" style="width: 95%; border: none; font-size: 1.1em;"></textarea></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="d-step">
                            <h2>D2: 描述问题</h2>
                            <p>清晰、准确地描述问题，包括什么、在哪里、何时、谁、为什么，以及如何发生。</p>
                            <div class="dstep-block d2-main" style="display: flex; flex-wrap: wrap; gap: 18px 24px; align-items: center; margin-bottom: 10px;">
                              <div style="display: flex; align-items: center; min-width: 260px; flex: 1 1 260px;">
                                <label for="deliveryDate" style="margin-right: 8px; min-width: 80px;">送货日期：</label>
                                <input type="date" id="deliveryDate" style="flex: 1; min-width: 120px;">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 260px; flex: 1 1 260px;">
                                <label for="productionDate" style="margin-right: 8px; min-width: 80px;">生产日期：</label>
                                <input type="date" id="productionDate" style="flex: 1; min-width: 120px;">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 260px; flex: 1 1 260px;">
                                <label for="discoveryTime" style="margin-right: 8px; min-width: 80px;">发现时间：</label>
                                <input type="datetime-local" id="discoveryTime" style="flex: 1; min-width: 120px;">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 260px; flex: 2 1 320px;">
                                <label for="personLocationDiscovery" style="margin-right: 8px; min-width: 120px;">人员/地点/发现方式：</label>
                                <textarea id="personLocationDiscovery" rows="1" style="flex: 1; min-width: 120px;"></textarea>
                              </div>
                              <div style="display: flex; align-items: center; min-width: 260px; flex: 1 1 260px;">
                                <label for="possibleBatch" style="margin-right: 8px; min-width: 80px;">有可能的哪个批次？</label>
                                <input type="text" id="possibleBatch" style="flex: 1; min-width: 120px;">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 220px; flex: 1 1 220px;">
                                <label for="isRepeat" style="margin-right: 8px; min-width: 80px;">重复发生的吗？</label>
                                <select id="isRepeat" style="flex: 1; min-width: 80px;">
                                  <option value="">请选择</option>
                                  <option value="yes">是</option>
                                  <option value="no">否</option>
                                  <option value="unknown">不确定</option>
                                </select>
                              </div>
                              <div style="display: flex; align-items: center; min-width: 220px; flex: 1 1 220px;">
                                <label style="margin-bottom: 0; margin-right: 8px; min-width: 60px;">检查数：</label>
                                <input type="number" id="checkCount" style="width: 120px;" oninput="calcDefectRate()">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 220px; flex: 1 1 220px;">
                                <label style="margin-bottom: 0; margin-right: 8px; min-width: 60px;">不良数：</label>
                                <input type="number" id="defectCount" style="width: 120px;" oninput="calcDefectRate()">
                              </div>
                              <div style="display: flex; align-items: center; min-width: 220px; flex: 1 1 220px;">
                                <label style="margin-bottom: 0; margin-right: 8px; min-width: 60px;">不良率：</label>
                                <input type="text" id="defectRate" readonly style="width: 120px; background: #f7f7f7;">
                              </div>
                            </div>
                            <li>收集初步信息和数据。</li>
                            <div class="dstep-block">
                                <strong>1. 4M1E变化点</strong>
                                <div style="margin: 6px 0 10px 0; padding-left: 12px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <label style="flex-shrink: 0; margin-right: 10px;">人员：</label>
                                        <textarea placeholder="请填写人员相关变化点..." rows="1" style="flex-grow: 1;"></textarea>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <label style="flex-shrink: 0; margin-right: 10px;">设备：</label>
                                        <textarea placeholder="请填写设备相关变化点..." rows="1" style="flex-grow: 1;"></textarea>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <label style="flex-shrink: 0; margin-right: 10px;">材料：</label>
                                        <textarea placeholder="请填写材料相关变化点..." rows="1" style="flex-grow: 1;"></textarea>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <label style="flex-shrink: 0; margin-right: 10px;">环境：</label>
                                        <textarea placeholder="请填写环境相关变化点..." rows="1" style="flex-grow: 1;"></textarea>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <label style="flex-shrink: 0; margin-right: 10px;">测试：</label>
                                        <textarea placeholder="请填写测试相关变化点..." rows="1" style="flex-grow: 1;"></textarea>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <strong style="margin-right: 10px; min-width: 140px; flex-shrink: 0;">2. 生产履历</strong>
                                    <textarea placeholder="请填写生产履历..." rows="2" style="flex-grow: 1;"></textarea>
                                </div>
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <strong style="margin-right: 10px; min-width: 140px; flex-shrink: 0;">3. 内外部历史问题</strong>
                                    <textarea placeholder="请填写内外部历史问题..." rows="2" style="flex-grow: 1;"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- D3-D8区块全部重构如下，移除所有内联style和多余嵌套，仅保留.dstep-block -->
                        <div class="d-step">
                            <h2>D3: 实施并验证临时遏制措施</h2>
                            <p>在永久性解决方案实施之前，采取临时措施以保护客户，防止问题进一步扩散。</p>
                            <div class="dstep-block">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <span style="margin-right: 10px; min-width: 200px; flex-shrink: 0; font-weight: bold;">1. 制定并实施临时遏制措施:</span>
                                </div>
                                <table style="width:100%; border-collapse:collapse; margin-bottom:12px;">
                                  <thead>
                                    <tr style="background:#2a2986; color:#fff;">
                                      <th style="border:1px solid #333; padding:6px;">&nbsp;</th>
                                      <th style="border:1px solid #333; padding:6px;">是否有库存</th>
                                      <th style="border:1px solid #333; padding:6px;">数量/单位</th>
                                      <th style="border:1px solid #333; padding:6px;">检查数量</th>
                                      <th style="border:1px solid #333; padding:6px;">不良数量</th>
                                      <th style="border:1px solid #333; padding:6px;">(%)不良率</th>
                                      <th style="border:1px solid #333; padding:6px;">实施时间</th>
                                      <th style="border:1px solid #333; padding:6px;">负责人</th>
                                      <th style="border:1px solid #333; padding:6px;">挑选工时</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr class="d3-temp-table-row" style="background:#eee;">
                                      <td style="border:1px solid #333; padding:6px;">原材料</td>
                                      <td style="border:1px solid #333; padding:6px; background:#ff7f00;"><select class="table-stock-select"><option>YES</option><option>NO</option></select></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-check-count" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-defect-count" style="width:90px;"></td>
                                      <td class="table-defect-rate" style="border:1px solid #333; padding:6px; background:#dbe3fa;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                    </tr>
                                    <tr class="d3-temp-table-row" style="background:#eee;">
                                      <td style="border:1px solid #333; padding:6px;">半成品</td>
                                      <td style="border:1px solid #333; padding:6px; background:#ff7f00;"><select class="table-stock-select"><option>YES</option><option>NO</option></select></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-check-count" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-defect-count" style="width:90px;"></td>
                                      <td class="table-defect-rate" style="border:1px solid #333; padding:6px; background:#dbe3fa;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                    </tr>
                                    <tr class="d3-temp-table-row" style="background:#eee;">
                                      <td style="border:1px solid #333; padding:6px;">成品</td>
                                      <td style="border:1px solid #333; padding:6px; background:#ff7f00;"><select class="table-stock-select"><option>YES</option><option>NO</option></select></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-check-count" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-defect-count" style="width:90px;"></td>
                                      <td class="table-defect-rate" style="border:1px solid #333; padding:6px; background:#dbe3fa;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                    </tr>
                                    <tr class="d3-temp-table-row" style="background:#eee;">
                                      <td style="border:1px solid #333; padding:6px;">运输途中</td>
                                      <td style="border:1px solid #333; padding:6px; background:#ff7f00;"><select class="table-stock-select"><option>YES</option><option>NO</option></select></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-check-count" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-defect-count" style="width:90px;"></td>
                                      <td class="table-defect-rate" style="border:1px solid #333; padding:6px; background:#dbe3fa;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                    </tr>
                                    <tr class="d3-temp-table-row" style="background:#eee;">
                                      <td style="border:1px solid #333; padding:6px;">客户库存</td>
                                      <td style="border:1px solid #333; padding:6px; background:#ff7f00;"><select class="table-stock-select"><option>YES</option><option>NO</option></select></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-check-count" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="number" class="table-defect-count" style="width:90px;"></td>
                                      <td class="table-defect-rate" style="border:1px solid #333; padding:6px; background:#dbe3fa;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                      <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:90px;"></td>
                                    </tr>
                                  </tbody>
                                </table>
                                <div style="display: flex; align-items: center; margin-bottom: 8px; margin-top: 12px;">
                                    <span style="margin-right: 10px; min-width: 200px; flex-shrink: 0;">2. 验证临时措施的有效性:</span>
                                    <span style="margin-right: 10px;">责任人:</span>
                                    <input type="text" style="width: 120px; margin-right: 15px;">
                                    <span style="margin-right: 10px;">实施时间:</span>
                                    <input type="date" style="width: 140px;">
                                </div>
                                <textarea placeholder="请在此处填写详细内容..." rows="3"></textarea>
                                <div style="display: flex; align-items: center; margin-top: 8px; margin-bottom: 8px;">
                                  <label style="min-width: 380px; margin-right: 8px;">挑选/返工的作业指导书是否发给客户/关联部门？</label>
                                  <input type="text" style="width: 610px;">
                                </div>
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                  <label style="min-width: 380px; margin-right: 8px;">挑选/返工的标签是否已贴在每个箱子/内包装</label>
                                  <input type="text" style="width: 610px;">
                                </div>
                            </div>
                        </div>
                        <div class="d-step">
                            <h2>D4: 确定并验证根本原因</h2>
                            <div class="dstep-block">
                                <ul>
                                    <li>使用适当的工具（如鱼骨图、5 Why分析）识别潜在根本原因。</li>
                                </ul>
                                <table class="fishbone-table">
                                    <thead>
                                        <tr>
                                            <th style="width: 85%; text-align: center;">要素</th>
                                            <th style="width: 15%; text-align: center;">要因与否</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>人员(例如：人员充分培训？具备所需资质？人员流动性过高？疲劳或注意力不集中导致错误？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="man-cause-group" value="yes"> 是<br><input type="radio" name="man-cause-group" value="no"> 否</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>设备 (例如：定期维护/有故障或磨损/参数设置正确/刀具寿命？/适合当前生产需求？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="machine-cause-group" value="yes"> 是<br><input type="radio" name="machine-cause-group" value="no"> 否</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>材料  (例如：供应商合格？材质/原材料符合规格？保质期/储存条件得当？批次之间存在差异？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="material-cause-group" value="yes"> 是<br><input type="radio" name="material-cause-group" value="no"> 否</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>方法 (例如：SOP/SIP清晰、准确？工艺流程合理？操作人员严格遵守SOP？有未经授权的流程变更？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="method-cause-group" value="yes"> 是<br><input type="radio" name="method-cause-group" value="no"> 否</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>环境 (例如：温度、湿度、洁净度符合要求？照明充足？噪音过大？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="environment-cause-group" value="yes"> 是<br><input type="radio" name="environment-cause-group" value="no"> 否</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 4px 8px;"><p>测量  (例如：校准？方法准确？测量人员经过培训？测量系统存在偏差？)</p><textarea placeholder="在此输入分析内容" rows="3"></textarea></td>
                                            <td style="text-align: center; vertical-align: top; padding: 4px 8px; padding-top: 60px;"><input type="radio" name="measurement-cause-group" value="yes"> 是<br><input type="radio" name="measurement-cause-group" value="no"> 否</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <h3>5Why 分析</h3>
                                <div class="form-group">
                                    <label>为什么问题会发生？</label>
                                    <div class="why-inputs">
                                        <input type="text"><input type="text"><input type="text"><input type="text"><input type="text">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>为什么问题会流出？</label>
                                    <div class="why-inputs">
                                        <input type="text"><input type="text"><input type="text"><input type="text"><input type="text">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>为什么系统会发生问题？</label>
                                    <div class="why-inputs">
                                        <input type="text"><input type="text"><input type="text"><input type="text"><input type="text">
                                    </div>
                                </div>
                                <h3>收集和分析数据</h3>
                                <textarea placeholder="请在此处填写收集和分析数据的详细内容..." rows="3"></textarea>
                                <div style="display: flex; align-items: flex-start; gap: 15px; margin-bottom: 10px;">
                                    <h3 style="margin: 0; min-width: 100px;">不良再现</h3>
                                    <div style="display: flex; align-items: center; gap: 15px; margin-right: 15px;">
                                        <label style="margin: 0;">
                                            <input type="radio" name="defectReproduction" value="yes" onchange="toggleDefectReproductionInput()"> 是
                                        </label>
                                        <label style="margin: 0;">
                                            <input type="radio" name="defectReproduction" value="no" onchange="toggleDefectReproductionInput()"> 否
                                        </label>
                                    </div>
                                    <textarea id="defectReproductionInput" placeholder="请在此处填写不良再现的详细内容..." rows="3" style="display: none; flex: 1; min-width: 300px;"></textarea>
                                </div>
                                <h3>基于根本原因的风险评估</h3>
                                <textarea placeholder="请在此处填写基于根本原因的风险评估，包括对其他产品或流程的影响..." rows="3"></textarea>
                                <button onclick="showPage('evaluation-page'); highlightEvaluation('d4-occurrence-row'); highlightEvaluation('d4-non-detection-row');">评估 D4</button>
                            </div>
                        </div>
                        <div class="d-step">
                            <h2>D5: 确定并验证永久纠正措施</h2>
                            <div class="dstep-block">
                                <table style="width:100%; border-collapse:collapse; margin-bottom:18px; table-layout:fixed;">
                                    <colgroup>
                                        <col span="3" style="width:34%">
                                        <col style="width:20%">
                                        <col style="width:18%">
                                        <col style="width:18%">
                                    </colgroup>
                                    <thead>
                                        <tr style="background:#2a2986; color:#fff;">
                                            <th colspan="3" style="border:1px solid #333; padding:6px; text-align:left;">发生改善措施的计划（避免问题重复发生，你的措施是什么？你要怎样做？）</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">实施期限</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">负责人</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table style="width:100%; border-collapse:collapse; margin-bottom:18px; table-layout:fixed;">
                                    <colgroup>
                                        <col span="3" style="width:34%">
                                        <col style="width:20%">
                                        <col style="width:18%">
                                        <col style="width:18%">
                                    </colgroup>
                                    <thead>
                                        <tr style="background:#2a2986; color:#fff;">
                                            <th colspan="3" style="border:1px solid #333; padding:6px; text-align:left;">流出改善措施的计划（避免问题重复发生，你怎样发现不良？你怎样控制你的生产）</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">实施期限</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">负责人</th>
                                            <th style="border:1px solid #333; padding:6px; white-space:nowrap;">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3" style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><select onchange="updateStatusBg(this)" style="width:100%;"><option value="">请选择</option><option value="未开始">未开始</option><option value="进行中">进行中</option><option value="已关闭">已关闭</option></select></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="d-step">
                            <h2>D6: 实施永久纠正措施</h2>
                            <div class="dstep-block">
                                <table style="width:100%; border-collapse:collapse; margin-bottom:18px;">
                                    <colgroup>
                                        <col style="width:70%">
                                        <col style="width:15%">
                                        <col style="width:15%">
                                    </colgroup>
                                    <thead>
                                        <tr style="background:#2a2986; color:#fff;">
                                            <th style="border:1px solid #333; padding:6px; text-align:left;">措施的有效性追踪与确认</th>
                                            <th style="border:1px solid #333; padding:6px; text-align:center;">负责人</th>
                                            <th style="border:1px solid #333; padding:6px; text-align:center;">完成时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table style="width:100%; border-collapse:collapse; margin-bottom:18px;">
                                    <colgroup>
                                        <col style="width:70%">
                                        <col style="width:15%">
                                        <col style="width:15%">
                                    </colgroup>
                                    <thead>
                                        <tr style="background:#2a2986; color:#fff;">
                                            <th style="border:1px solid #333; padding:6px; text-align:left;">围堵措施是否可以取消</th>
                                            <th style="border:1px solid #333; padding:6px; text-align:center;">负责人</th>
                                            <th style="border:1px solid #333; padding:6px; text-align:center;">取消时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                        <tr>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="text" style="width:100%;"></td>
                                            <td style="border:1px solid #333; padding:6px;"><input type="date" style="width:100%;"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="d-step">
                            <h2>D7: 预防再发生</h2>
                            <p>修改管理系统、操作流程、规范等，以防止类似问题再次发生。</p>
                            <div class="dstep-block">
                                <ul>
                                    <li>
                                        <label>更新相关文件和流程:</label>
                                        <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                            <thead>
                                                <tr>
                                                    <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center; width: 40%;">文档类型</th>
                                                    <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center; width: 30%;">责任人</th>
                                                    <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: center; width: 30%;">实施时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="control_plan"> 控制计划</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="pfmea"> PFMEA</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="work_instruction"> 作业指导书</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="inspection_standard"> 检查基准书</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="technical_drawing"> 技术图纸</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                                <tr>
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: left;"><label><input type="checkbox" name="d7-doc-update" value="other"> 其它</label></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" class="d7-input" style="width: 100%; box-sizing: border-box;"></td>
                                                    <td style="border: 1px solid #ddd; padding: 8px;"><input type="date" class="d7-input-date" style="width: 100%; box-sizing: border-box;"></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </li>
                                    <li>
                                        进行标准化和培训:
                                        <span style="margin-right: 10px;">责任人:</span>
                                        <input type="text" class="d7-input" style="width: 120px; margin-right: 15px;">
                                        <span style="margin-right: 10px;">实施时间:</span>
                                        <input type="date" class="d7-input-date" style="width: 140px;">
                                    </li>
                                </ul>
                                <textarea placeholder="请在此处填写D7步骤的详细内容..." rows="3"></textarea>
                                <button onclick="showPage('evaluation-page'); highlightEvaluation('d7-updates-row');">评估 D7</button>
                            </div>
                        </div>
                        <div class="d-step">
                            <h2>D8: 祝贺团队</h2>
                            <p>认可团队的努力和贡献，并庆祝问题的成功解决。</p>
                            <div class="dstep-block">
                                <ul>
                                    <li>表彰团队成员。</li>
                                    <li>分享经验教训。</li>
                                </ul>
                                <textarea placeholder="请在此处填写D8步骤的详细内容..." rows="3"></textarea>
                                <button onclick="showPage('evaluation-page'); highlightEvaluation('d8-signatures-row');">评估 D8</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右侧：AI助手面板 -->
            <div id="ai-panel" style="width: 320px; min-width: 300px; position: sticky; top: 20px; height: fit-content; padding: 20px 20px 20px 0; background: #f0f0f0;">
                <!-- Deepseek AI设置面板 -->
                <div id="ai-settings-panel" style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; display: block;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0; color: #495057; font-size: 1em;">🤖 AI 智能助手</h3>
                        <div style="display: flex; gap: 6px;">
                            <button id="ai-chat-btn" style="padding: 4px 8px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">💬 对话</button>
                            <button id="toggle-ai-settings" style="padding: 4px 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">设置</button>
                        </div>
                    </div>
                    
                    <div id="ai-settings-content" style="display: none;">
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef; margin-bottom: 12px;">
                            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 0.95em;">API 配置</h4>
                            <div style="margin-bottom: 8px;">
                                <label style="display: block; margin-bottom: 4px; font-size: 0.85em; color: #6c757d;">API 密钥:</label>
                                <input type="password" id="deepseek-api-key" placeholder="请输入您的Deepseek API密钥" 
                                       style="width: calc(100% - 16px); max-width: 280px; padding: 6px 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.85em; box-sizing: border-box;">
                            </div>
                            <div style="display: flex; gap: 6px; margin-bottom: 8px;">
                                <button id="save-api-key" style="padding: 3px 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">保存</button>
                                <button id="clear-api-key" style="padding: 3px 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">清除</button>
                            </div>
                            <button id="test-api-connection" style="padding: 3px 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">测试连接</button>
                        </div>
                        
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid #e9ecef;">
                            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 0.95em;">功能开关</h4>
                            <div style="margin-bottom: 6px;">
                                <label style="display: flex; align-items: center; font-size: 0.85em; cursor: pointer;">
                                    <input type="checkbox" id="enable-ai-chat" checked style="margin-right: 6px;">
                                    启用AI会话助手
                                </label>
                            </div>
                            <div style="margin-bottom: 6px;">
                                <label style="display: flex; align-items: center; font-size: 0.85em; cursor: pointer;">
                                    <input type="checkbox" id="enable-auto-fill" style="margin-right: 6px;">
                                    启用智能自动填充
                                </label>
                            </div>
                            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 0.75em; color: #6c757d;">
                                💡 智能功能说明：<br>
                                • AI会话：实时对话解答8D相关问题<br>
                                • 自动填充：基于上下文智能填充表单
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- AI快速操作 -->
                <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <h4 style="margin: 0 0 12px 0; color: #495057; font-size: 1em;">⚡ AI 快速操作</h4>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 8px;">
                        <button onclick="generateAISuggestion('d0')" style="padding: 8px 12px; background: #e3f2fd; color: #1976d2; border: 1px solid #bbdefb; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">📋 D0 8D准备指导</button>
                        <button onclick="generateAISuggestion('d1')" style="padding: 8px 12px; background: #f3e5f5; color: #7b1fa2; border: 1px solid #e1bee7; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">👥 D1 团队组建指导</button>
                        <button onclick="generateAISuggestion('d2')" style="padding: 8px 12px; background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🔍 D2 问题描述指导</button>
                        <button onclick="generateAISuggestion('d3')" style="padding: 8px 12px; background: #fff8e1; color: #f57f17; border: 1px solid #ffecb3; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🚨 D3 临时措施指导</button>
                        <button onclick="generateAISuggestion('d4')" style="padding: 8px 12px; background: #fff3e0; color: #f57c00; border: 1px solid #ffcc02; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🎯 D4 根本原因分析指导</button>
                        <button onclick="generateAISuggestion('d5')" style="padding: 8px 12px; background: #fce4ec; color: #c2185b; border: 1px solid #f8bbd9; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">✅ D5 纠正措施指导</button>
                        <button onclick="generateAISuggestion('d6')" style="padding: 8px 12px; background: #e0f2f1; color: #00695c; border: 1px solid #b2dfdb; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🔧 D6 实施验证指导</button>
                        <button onclick="generateAISuggestion('d7')" style="padding: 8px 12px; background: #f1f8e9; color: #33691e; border: 1px solid #c5e1a5; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🛡️ D7 预防措施指导</button>
                        <button onclick="generateAISuggestion('d8')" style="padding: 8px 12px; background: #fafafa; color: #424242; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🎉 D8 团队祝贺指导</button>
                        <button id="aiAnalysisBtn" style="padding: 8px 12px; background: #ff6b35; color: white; border: 1px solid #ff6b35; border-radius: 4px; cursor: pointer; font-size: 0.9em; text-align: left;">🔍 AI智能分析</button>
                    </div>
                </div>

                </div>
            </div>
        </div>
    </div>
    
    <!-- 8D报告评估表页面 - 全屏显示，不包含AI助手 -->
    <div id="evaluation-page" class="page-content" style="display: none; width: calc(1500px - 40px); max-width: calc(100vw - 40px); margin: 20px auto 0 auto; padding: 20px; background: white; border-radius: 8px; box-sizing: border-box;">
        <div style="display: flex; align-items: center; margin-bottom: 6px;">
            <h1 style="margin: 0; font-size: 1.5em !important; font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif; color: #222 !important; font-weight: 700; text-align: left; background: none !important; box-shadow: none; padding: 0; letter-spacing: 1px;">8D报告评估表</h1>
        </div>
        <div style="border-bottom:2px solid #007bff;margin-bottom:16px;"></div>
        
        <div class="evaluation-table">
            <table>
                <thead>
                    <tr>
                        <th style="width: 6%;">D 步骤</th>
                        <th style="width: 12%;">类别</th>
                        <th style="width: 18%;">不合格</th>
                        <th style="width: 18%;">合格 (最低)</th>
                        <th style="width: 18%;">优秀</th>
                        <th style="width: 8%;">得分<br>当前 / 最大</th>
                        <th style="width: 20%;">改进意见<br>8D评级理由</th>
                    </tr>
                </thead>
                <tbody>
                    <tr id="d4-occurrence-row">
                        <td style="text-align: center;">D4</td>
                        <td>根本原因 D4<br>(发生)</td>
                        <td>
                            <label><input type="radio" name="d4-occurrence" value="0"> (0) 根本原因或假设不充分</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d4-occurrence" value="1.5"> (+1.5) 仅发现TRC，并附加直接原因</label><br>
                            <label><input type="radio" name="d4-occurrence" value="3"> (+3) OK. 发现TRC + SRC，并附加直接原因</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d4-occurrence" value="4"> (+1) 所有可能性已确认 (石川图，5个为什么已完成/附上)</label>
                        </td>
                        <td><span class="score" data-max="4.0">0.0 / 4.0</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d5-effectiveness-row">
                        <td style="text-align: center;">D5</td>
                        <td>永久纠正措施 D5<br>(有效性)</td>
                        <td>
                            <label><input type="radio" name="d5-effectiveness" value="0"> (0) 纠正措施无效或未验证</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d5-effectiveness" value="1.5"> (+1.5) 纠正措施有效性已验证</label><br>
                            <label><input type="radio" name="d5-effectiveness" value="3"> (+3) OK. 纠正措施有效性已验证并有数据支持</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d5-effectiveness" value="4"> (+1) 纠正措施有效性已验证并有数据支持，且有预防措施</label>
                        </td>
                        <td><span class="score" data-max="4.0">0.0 / 4.0</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d6-occurrence-row">
                        <td style="text-align: center;">D6</td>
                        <td>纠正措施 D6<br>(发生)</td>
                        <td>
                            <label><input type="radio" name="d6-occurrence" value="0"> (0) 针对不合格项的纠正措施不充分，缺乏</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-occurrence" value="1"> (+1) 仅针对TRC的有效纠正措施</label><br>
                            <label><input type="radio" name="d6-occurrence" value="2"> (+2) OK. 针对TRC + SRC的有效纠正措施</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-occurrence" value="3"> (+1) 相关附加数据 (例如照片、草图、防错等)</label>
                        </td>
                        <td><span class="score" data-max="3.0">0.0 / 3.0</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d6-non-detection-row">
                        <td style="text-align: center;">D6</td>
                        <td>纠正措施 D6<br>(未检测)</td>
                        <td>
                            <label><input type="radio" name="d6-non-detection" value="0"> (0) 差，不清晰，不健全，无时间点，无实际行动证据</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-non-detection" value="1"> (+1) 仅针对TRC的有效纠正措施</label><br>
                            <label><input type="radio" name="d6-non-detection" value="2"> (+2) OK. 针对TRC + SRC的有效纠正措施</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-non-detection" value="3"> (+1) 相关附加数据 (例如照片、草图、防错等)</label>
                        </td>
                        <td><span class="score" data-max="3.0">0.0 / 3.0</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d6-confirmation-row">
                        <td style="text-align: center;">D6</td>
                        <td>纠正措施确认 D6</td>
                        <td>
                            <label><input type="radio" name="d6-confirmation" value="0"> (0) 空白或"弱"方法</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-confirmation" value="2"> (+2) OK. 有效纠正措施的解释</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d6-confirmation" value="0.5"> (+0.5) 有效方法并附有备用数据 (例如检查表)</label>
                        </td>
                        <td><span class="score" data-max="2.5">0.0 / 2.5</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d7-updates-row">
                        <td style="text-align: center;">D7</td>
                        <td>质量管理体系更新<br>(PQP, FMEA等)</td>
                        <td>
                            <label><input type="radio" name="d7-updates" value="0"> (0) 空白</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d7-updates" value="1"> (+1) FMEA已更新，吸取了关于其他流程、产品、地点等的经验教训</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d7-updates" value="1.5"> (+0.5) 如果适用，解释CP和/或FMEA如何更新或CN/CoC如何识别经验教训</label>
                        </td>
                        <td><span class="score" data-max="1.5">0.0 / 1.5</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr id="d8-signatures-row">
                        <td style="text-align: center;">D8</td>
                        <td>签名</td>
                        <td>
                            <label><input type="radio" name="d8-signatures" value="0"> (0) 无经理签字</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d8-signatures" value="1"> (+1) 经理签字</label>
                        </td>
                        <td>
                            <label><input type="radio" name="d8-signatures" value="0.5"> (+0.5) 额外签字 (例如生产经理、工厂经理等)</label>
                        </td>
                        <td><span class="score" data-max="1.5">0.0 / 1.5</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                    <tr>
                        <td>-</td>
                        <td>其他附件/报告</td>
                        <td>
                            <label><input type="radio" name="attachments" value="0"> (0) 不相关或未附文件</label>
                        </td>
                        <td>不适用</td>
                        <td>
                            <label><input type="radio" name="attachments" value="0.5"> (+0.5) 附有相关文件 (例如分析数据表等)</label>
                        </td>
                        <td><span class="score" data-max="0.5">0.0 / 0.5</span></td>
                        <td><textarea class="comment" placeholder="可选"></textarea></td>
                    </tr>
                </tbody>
            </table>
            
            <div class="summary-container">
                <div class="hint-section">
                    <h3>提示</h3>
                    <ul>
                        <li>TRC 表示"技术根本原因" / SRC 表示"系统根本原因"，也称为"管理根本原因"</li>
                        <li>8D 报告必须是一份易于理解的独立文件，描述根本原因的发现、理解和错误的消除。</li>
                        <li>必须让客户相信所有可能性都经过分析，已采取适当措施，并且为客户调查提供的任何提示都符合他们的最佳利益，以消除错误。</li>
                    </ul>
                </div>
                <div class="score-summary">
                    总分: <span id="total-score">0.0</span> / <span id="max-total-score">20.0</span><br>
                    百分比: <span id="percentage">0.0%</span>
                </div>
                <div class="overall-comments">
                    总体/一般评论:<br>
                    <textarea rows="3" style="width: 100%;"></textarea>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AI会话窗口 -->
    <div id="ai-chat-window" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 500px; height: 400px; background: white; border: 1px solid #dee2e6; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; display: none;">
        <div style="background: #6f42c1; color: white; padding: 12px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 1.1em;">💬 AI 智能助手</h3>
            <button id="close-chat" style="background: none; border: none; color: white; cursor: pointer; font-size: 18px;">×</button>
        </div>
        <div style="height: 300px; overflow-y: auto; padding: 12px; border-bottom: 1px solid #dee2e6;">
            <div id="chat-messages">
                <div style="background: #f8f9fa; padding: 8px 12px; border-radius: 6px; margin-bottom: 8px; font-size: 0.9em;">
                    <strong>AI助手:</strong> 您好！我是您的8D问题解决助手。我可以帮助您完善8D报告的各个步骤，回答相关问题，并提供专业的指导建议。请告诉我您需要什么帮助？
                </div>
            </div>
        </div>
        <div style="padding: 12px; display: flex; gap: 8px;">
            <input type="text" id="chat-input" placeholder="请输入您的问题..." style="flex: 1; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.9em;">
            <button id="send-message" style="padding: 8px 16px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em;">发送</button>
        </div>
    </div>
    
    <!-- 初始化自动填充功能 -->
    <script>
        function enableAutoFill() {
            const enableAutoFillCheckbox = document.getElementById('enable-auto-fill');
            if (enableAutoFillCheckbox.checked) {
                // 在这里添加自动填充功能的逻辑
                console.log('自动填充功能已启用');
            } else {
                // 在这里添加自动填充功能的逻辑
                console.log('自动填充功能已禁用');
            }
        }
    </script>
    
    <!-- AI快速操作功能 -->
    <script>
        window.generateAISuggestion = function(step) {
            const suggestions = {
                'd0': {
                    title: 'D0 8D准备指导',
                    content: 'D0步骤是8D流程的准备工作，需要从以下方面进行：\n\n1. 问题识别与确认：\n• 明确问题的具体表现和现象\n• 确定问题发生的时间、地点、频率\n• 评估问题的严重程度和影响范围\n• 收集初步证据和数据\n\n2. 8D启动条件评估：\n• 问题对客户有严重影响（安全、法规、重大质量缺陷）\n• 根本原因不明，需要系统性分析\n• 客户明确要求提交8D报告\n• 重复/系统性失效（≥3次）\n• 成本影响显著（停线≥4小时或报废成本＞1万元）\n• 高层管理要求\n\n3. 紧急响应准备：\n• 确定是否需要立即采取遏制措施\n• 评估问题的紧急程度\n• 准备必要的资源和权限\n• 建立初步的沟通机制'
                },
                'd1': {
                    title: 'D1 团队组建指导',
                    content: 'D1步骤是建立跨职能团队，需要从以下方面进行：\n\n1. 核心团队成员确定：\n• 团队负责人（项目经理/质量经理）- 负责整体协调\n• 技术专家（工艺工程师/设计工程师）- 负责技术分析\n• 质量工程师 - 负责质量体系相关\n• 生产主管 - 负责生产实施\n• 供应商质量工程师（如涉及供应商）\n\n2. 支持团队成员：\n• 客户代表（如适用）- 了解客户需求\n• 采购代表（如涉及供应商）- 处理供应商问题\n• 财务代表（如涉及成本分析）- 评估成本影响\n• 法务代表（如涉及法规问题）- 处理合规问题\n\n3. 团队职责分工：\n• 明确每个成员的具体职责和权限\n• 建立沟通机制和汇报流程\n• 确定决策权限和协调方式\n• 制定团队工作计划和时间节点\n• 建立团队协作规则'
                },
                'd2': {
                    title: 'D2 问题描述指导',
                    content: 'D2步骤是详细描述问题，建议使用5W1H方法：\n\n1. What（什么）：\n• 详细描述问题现象和特征\n• 确定问题的具体表现\n• 收集相关数据和证据\n• 明确问题的性质（质量、安全、成本等）\n\n2. When（何时）：\n• 问题首次发现的时间\n• 问题发生的频率和规律\n• 问题持续的时间段\n• 问题发生的时间模式（季节性、周期性等）\n\n3. Where（何地）：\n• 问题发生的具体位置\n• 涉及的产品、工序、设备\n• 影响的范围和程度\n• 问题传播的路径\n\n4. Who（何人）：\n• 问题发现者\n• 受影响的人员\n• 相关责任人员\n• 利益相关方\n\n5. Why（为何）：\n• 问题的初步分析\n• 可能的原因假设\n• 需要进一步调查的方向\n• 问题的根本原因初步判断\n\n6. How（如何）：\n• 问题发现的方式\n• 问题的影响机制\n• 临时措施的效果\n• 问题的传播机制'
                },
                'd3': {
                    title: 'D3 临时措施指导',
                    content: 'D3步骤是实施临时措施，需要从以下方面进行：\n\n1. 立即遏制措施：\n• 停止问题产品的生产或发货\n• 隔离问题产品，防止流入客户\n• 召回已发货的问题产品\n• 通知相关方，建立应急响应\n\n2. 客户保护措施：\n• 与客户沟通，说明情况\n• 提供替代方案或补偿措施\n• 建立客户投诉处理机制\n• 确保客户生产不受影响\n\n3. 内部控制措施：\n• 加强相关工序的检验\n• 增加临时检验点\n• 调整工艺参数\n• 加强人员培训\n\n4. 措施验证：\n• 验证临时措施的有效性\n• 监控措施实施效果\n• 收集反馈数据\n• 及时调整措施\n\n5. 措施记录：\n• 详细记录所有临时措施\n• 记录措施的实施时间和效果\n• 记录相关成本和资源消耗\n• 为后续分析提供依据'
                },
                'd4': {
                    title: 'D4 根本原因分析指导',
                    content: 'D4步骤是根本原因分析，建议采用系统性的分析方法：\n\n1. 鱼骨图分析（6M法）：\n• 人员因素：技能、培训、意识、疲劳、责任心等\n• 机器因素：设备状态、维护、精度、老化、故障等\n• 材料因素：规格、质量、批次、供应商、存储等\n• 方法因素：工艺、标准、程序、参数、操作等\n• 环境因素：温度、湿度、清洁度、照明、安全等\n• 测量因素：检测方法、设备、标准、人员、环境等\n\n2. 5个为什么分析：\n• 针对每个可能原因进行深入分析\n• 找到根本原因而非表面现象\n• 验证原因的正确性和完整性\n• 建立因果关系链\n\n3. 根本原因验证：\n• 通过实验或数据分析验证\n• 确认原因与问题的因果关系\n• 评估原因的重要性和优先级\n• 排除非关键因素\n\n4. 技术根本原因（TRC）vs 系统根本原因（SRC）：\n• TRC：直接的技术或工艺原因\n• SRC：管理、流程、系统层面的原因\n• 需要同时识别和解决两种根本原因'
                },
                'd5': {
                    title: 'D5 纠正措施指导',
                    content: 'D5步骤是选择和验证纠正措施，需要从以下方面进行：\n\n1. 纠正措施选择标准：\n• 有效性：能够解决根本原因\n• 可行性：技术、成本、时间可行\n• 风险性：评估实施风险\n• 可持续性：长期效果和稳定性\n• 经济性：成本效益分析\n\n2. 纠正措施类型：\n• 技术措施：工艺改进、设备升级、材料优化\n• 管理措施：流程优化、标准更新、培训加强\n• 预防措施：防错设计、监控系统、预警机制\n• 组织措施：职责调整、资源配置、激励制度\n\n3. 验证方法：\n• 实验室验证：小规模试验\n• 试生产验证：小批量生产\n• 数据分析：对比分析、趋势分析\n• 客户确认：客户试用和反馈\n• 专家评审：技术专家评估\n\n4. 实施计划：\n• 明确时间节点和里程碑\n• 分配责任人和资源\n• 制定应急预案\n• 建立监控和反馈机制\n• 制定培训计划'
                },
                'd6': {
                    title: 'D6 实施验证指导',
                    content: 'D6步骤是实施和验证纠正措施，需要从以下方面进行：\n\n1. 实施计划执行：\n• 按照D5制定的计划执行\n• 确保所有措施得到正确实施\n• 监控实施进度和质量\n• 及时处理实施过程中的问题\n\n2. 效果验证：\n• 收集实施后的数据\n• 对比实施前后的效果\n• 验证问题是否得到解决\n• 确认措施的有效性\n\n3. 长期监控：\n• 建立长期监控机制\n• 定期检查措施效果\n• 收集持续改进的数据\n• 确保措施持续有效\n\n4. 文档更新：\n• 更新相关工艺文件\n• 更新作业指导书\n• 更新检验标准\n• 记录实施过程和结果\n\n5. 人员培训：\n• 培训相关人员\n• 确保操作人员掌握新方法\n• 建立考核机制\n• 持续改进培训内容'
                },
                'd7': {
                    title: 'D7 预防措施指导',
                    content: 'D7步骤是预防再发生，需要从以下方面进行：\n\n1. 文件更新：\n• 更新控制计划（Control Plan）\n• 更新PFMEA（过程失效模式分析）\n• 更新作业指导书\n• 更新检验基准书\n• 更新技术图纸\n\n2. 体系改进：\n• 完善质量管理体系\n• 更新相关程序文件\n• 建立预防机制\n• 加强过程控制\n\n3. 人员培训：\n• 进行标准化培训\n• 提高人员技能\n• 增强质量意识\n• 建立培训档案\n\n4. 经验总结：\n• 总结本次8D的经验教训\n• 建立知识库\n• 分享最佳实践\n• 防止类似问题再发生\n\n5. 持续改进：\n• 建立持续改进机制\n• 定期评估预防措施\n• 收集改进建议\n• 推动体系优化'
                },
                'd8': {
                    title: 'D8 团队祝贺指导',
                    content: 'D8步骤是祝贺团队，需要从以下方面进行：\n\n1. 团队表彰：\n• 认可团队的努力和贡献\n• 表彰表现突出的成员\n• 给予适当的奖励和激励\n• 建立团队荣誉感\n\n2. 经验分享：\n• 总结项目成功经验\n• 分享解决问题的思路\n• 交流团队协作经验\n• 建立知识分享机制\n\n3. 能力提升：\n• 评估团队能力提升\n• 识别改进机会\n• 制定能力发展计划\n• 促进团队成长\n\n4. 文化建设：\n• 强化质量文化\n• 建立持续改进意识\n• 促进团队协作精神\n• 营造积极的工作氛围\n\n5. 未来规划：\n• 制定后续改进计划\n• 建立长期合作机制\n• 规划团队发展方向\n• 为未来项目做准备'
                }
            };
            
            const suggestion = suggestions[step];
            if (suggestion) {
                // 显示AI建议弹窗
                showAISuggestionModal(suggestion.title, suggestion.content);
                
                // 跳转到对应的8D步骤（只跳转一次，不循环）
                const stepTitles = {
                    'd0': 'D0: 8D准备',
                    'd1': 'D1: 建立团队', 
                    'd2': 'D2: 描述问题',
                    'd3': 'D3: 实施并验证临时遏制措施',
                    'd4': 'D4: 确定并验证根本原因',
                    'd5': 'D5: 确定并验证永久纠正措施',
                    'd6': 'D6: 实施永久纠正措施',
                    'd7': 'D7: 预防再发生',
                    'd8': 'D8: 祝贺团队'
                };
                
                const targetTitle = stepTitles[step];
                if (targetTitle) {
                    // 确保在主8D页面
                    showPage('main-8d-page');
                    
                    // 查找对应的步骤标题
                    setTimeout(() => {
                        const targetElement = Array.from(document.querySelectorAll('.d-step h2')).find(h2 => h2.textContent === targetTitle);
                        if (targetElement) {
                            targetElement.closest('.d-step').scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                    }, 100);
                }
            }
        };
        
        // 显示AI建议弹窗
        function showAISuggestionModal(title, content) {
            const modalHtml = `
                <div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #6f42c1;border-radius:8px;max-height:80vh;overflow-y:auto;z-index:1000;min-width:600px;max-width:800px;">
                    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
                        <h3 style="margin:0;color:#6f42c1;">${title}</h3>
                        <button onclick="closeAISuggestionModal()" style="background:none;border:none;font-size:24px;cursor:pointer;color:#6c757d;">×</button>
                    </div>
                    <div style="font-size:0.9em;line-height:1.6;color:#333;white-space:pre-line;margin-bottom:15px;">${content}</div>
                    <div style="text-align:center;">
                        <button onclick="applyAISuggestion('${title.split(' ')[0].toLowerCase()}')" style="margin-right:10px;padding:8px 16px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;">应用建议</button>
                        <button onclick="closeAISuggestionModal()" style="padding:8px 16px;background:#6c757d;color:white;border:none;border-radius:4px;cursor:pointer;">关闭</button>
                    </div>
                </div>
            `;
            
            const overlay = document.createElement('div');
            overlay.id = 'aiSuggestionOverlay';
            overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;';
            overlay.innerHTML = modalHtml;
            document.body.appendChild(overlay);
        }
        
        // 关闭AI建议弹窗
        window.closeAISuggestionModal = function() {
            const overlay = document.getElementById('aiSuggestionOverlay');
            if (overlay) {
                overlay.remove();
            }
        };
        
        window.applyAISuggestion = function(step) {
            // 根据步骤实际应用建议到表单字段
            switch(step) {
                case 'd0':
                    // 应用D0建议到基础信息字段
                    const d0Textarea = document.querySelector('.d-step textarea[placeholder*="D0步骤"]');
                    if (d0Textarea) {
                        d0Textarea.value = '基于AI建议，请在此处详细描述：\n1. 问题的具体表现和现象\n2. 问题发生的时间、地点、频率\n3. 问题的严重程度和影响范围\n4. 收集的初步证据和数据\n5. 8D启动条件的评估结果';
                    }
                    break;
                    
                case 'd1':
                    // 应用D1建议到团队表格
                    const teamTextareas = document.querySelectorAll('.d-step:nth-of-type(2) table textarea');
                    if (teamTextareas.length >= 4) {
                        teamTextareas[0].value = '张三'; // 业务部姓名
                        teamTextareas[1].value = '业务经理'; // 业务部职务
                        teamTextareas[2].value = '客户沟通协调'; // 业务部职责
                        teamTextareas[3].value = '客户关系管理'; // 业务部技能
                    }
                    if (teamTextareas.length >= 8) {
                        teamTextareas[4].value = '李四'; // 技术部姓名
                        teamTextareas[5].value = '技术工程师'; // 技术部职务
                        teamTextareas[6].value = '技术分析解决'; // 技术部职责
                        teamTextareas[7].value = '工艺技术分析'; // 技术部技能
                    }
                    break;
                    
                case 'd2':
                    // 应用D2建议到问题描述字段
                    const d2Textarea = document.querySelector('textarea[placeholder*="D2步骤"]');
                    if (d2Textarea) {
                        d2Textarea.value = '基于AI建议，请使用5W1H方法详细描述问题：\n\nWhat（什么）：\n- 详细描述问题现象和特征\n- 确定问题的具体表现\n\nWhen（何时）：\n- 问题首次发现的时间\n- 问题发生的频率和规律\n\nWhere（何地）：\n- 问题发生的具体位置\n- 涉及的产品、工序、设备\n\nWho（何人）：\n- 问题发现者\n- 受影响的人员\n\nWhy（为何）：\n- 问题的初步分析\n- 可能的原因假设\n\nHow（如何）：\n- 问题发现的方式\n- 问题的影响机制';
                    }
                    break;
                    
                case 'd3':
                    // 应用D3建议到临时措施字段
                    const d3Textarea = document.querySelector('textarea[placeholder*="D3步骤"]');
                    if (d3Textarea) {
                        d3Textarea.value = '基于AI建议，请详细描述临时措施：\n\n1. 立即遏制措施：\n- 停止问题产品的生产或发货\n- 隔离问题产品，防止流入客户\n\n2. 客户保护措施：\n- 与客户沟通，说明情况\n- 提供替代方案或补偿措施\n\n3. 内部控制措施：\n- 加强相关工序的检验\n- 增加临时检验点\n\n4. 措施验证：\n- 验证临时措施的有效性\n- 监控措施实施效果';
                    }
                    break;
                    
                case 'd4':
                    // 应用D4建议到根本原因分析字段
                    const d4Textarea = document.querySelector('textarea[placeholder*="D4步骤"]');
                    if (d4Textarea) {
                        d4Textarea.value = '基于AI建议，请进行根本原因分析：\n\n1. 鱼骨图分析（6M法）：\n- 人员因素：技能、培训、意识等\n- 机器因素：设备状态、维护、精度等\n- 材料因素：规格、质量、批次等\n- 方法因素：工艺、标准、程序等\n- 环境因素：温度、湿度、清洁度等\n- 测量因素：检测方法、设备、标准等\n\n2. 5个为什么分析：\n- 针对每个可能原因进行深入分析\n- 找到根本原因而非表面现象\n\n3. 根本原因验证：\n- 通过实验或数据分析验证\n- 确认原因与问题的因果关系';
                    }
                    break;
                    
                case 'd5':
                    // 应用D5建议到纠正措施字段
                    const d5Textarea = document.querySelector('textarea[placeholder*="D5步骤"]');
                    if (d5Textarea) {
                        d5Textarea.value = '基于AI建议，请选择和验证纠正措施：\n\n1. 纠正措施选择标准：\n- 有效性：能够解决根本原因\n- 可行性：技术、成本、时间可行\n- 风险性：评估实施风险\n- 可持续性：长期效果和稳定性\n\n2. 纠正措施类型：\n- 技术措施：工艺改进、设备升级\n- 管理措施：流程优化、标准更新\n- 预防措施：防错设计、监控系统\n\n3. 验证方法：\n- 实验室验证：小规模试验\n- 试生产验证：小批量生产\n- 数据分析：对比分析、趋势分析';
                    }
                    break;
                    
                case 'd6':
                    // 应用D6建议到实施验证字段
                    const d6Textarea = document.querySelector('textarea[placeholder*="D6步骤"]');
                    if (d6Textarea) {
                        d6Textarea.value = '基于AI建议，请实施和验证纠正措施：\n\n1. 实施计划执行：\n- 按照D5制定的计划执行\n- 确保所有措施得到正确实施\n- 监控实施进度和质量\n\n2. 效果验证：\n- 收集实施后的数据\n- 对比实施前后的效果\n- 验证问题是否得到解决\n\n3. 长期监控：\n- 建立长期监控机制\n- 定期检查措施效果\n- 确保措施持续有效\n\n4. 文档更新：\n- 更新相关工艺文件\n- 更新作业指导书';
                    }
                    break;
                    
                case 'd7':
                    // 应用D7建议到预防措施字段
                    const d7Textarea = document.querySelector('textarea[placeholder*="D7步骤"]');
                    if (d7Textarea) {
                        d7Textarea.value = '基于AI建议，请制定预防再发生措施：\n\n1. 文件更新：\n- 更新控制计划（Control Plan）\n- 更新PFMEA（过程失效模式分析）\n- 更新作业指导书\n- 更新检验基准书\n\n2. 体系改进：\n- 完善质量管理体系\n- 更新相关程序文件\n- 建立预防机制\n- 加强过程控制\n\n3. 人员培训：\n- 进行标准化培训\n- 提高人员技能\n- 增强质量意识\n\n4. 经验总结：\n- 总结本次8D的经验教训\n- 建立知识库\n- 分享最佳实践';
                    }
                    break;
                    
                case 'd8':
                    // 应用D8建议到团队祝贺字段
                    const d8Textarea = document.querySelector('textarea[placeholder*="D8步骤"]');
                    if (d8Textarea) {
                        d8Textarea.value = '基于AI建议，请进行团队祝贺和总结：\n\n1. 团队表彰：\n- 认可团队的努力和贡献\n- 表彰表现突出的成员\n- 给予适当的奖励和激励\n\n2. 经验分享：\n- 总结项目成功经验\n- 分享解决问题的思路\n- 交流团队协作经验\n\n3. 能力提升：\n- 评估团队能力提升\n- 识别改进机会\n- 制定能力发展计划\n\n4. 文化建设：\n- 强化质量文化\n- 建立持续改进意识\n- 促进团队协作精神';
                    }
                    break;
            }
            
            alert(`AI建议已应用到${step.toUpperCase()}步骤！相关字段已更新，请检查并完善内容。`);
            closeAISuggestionModal();
        };
        
        function checkD0Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 基础信息检查
            const source = document.querySelector('select[name="problemSource"]')?.value;
            const type = document.querySelector('select[name="problemType"]')?.value;
            const topic = document.getElementById('problemTopic')?.value;
            const customer = document.getElementById('customerDepartment')?.value;
            const proposeDate = document.getElementById('proposeDate')?.value;
            const deadline = document.getElementById('deadline')?.value;
            
            // 检查"识别并确认问题"字段
            const d0Textarea = document.querySelector('.d-step:first-child textarea[placeholder*="D0步骤"]');
            const d0Content = d0Textarea?.value?.trim() || '';
            
            if (!d0Content || d0Content.length < 10) {
                results.completeness.push({
                    step: 'D0',
                    field: '识别并确认问题',
                    status: 'fail',
                    message: '未填写或填写不充分"识别并确认问题"（建议至少10个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '识别并确认问题',
                    status: 'pass',
                    message: '已填写"识别并确认问题"'
                });
            }
            
            if (!source) {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题来源',
                    status: 'fail',
                    message: '未选择投诉/问题来源'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题来源',
                    status: 'pass',
                    message: '已选择投诉/问题来源'
                });
            }
            
            if (!type) {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题类型',
                    status: 'fail',
                    message: '未选择投诉/问题类型'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题类型',
                    status: 'pass',
                    message: '已选择投诉/问题类型'
                });
            }
            
            if (!topic || topic.trim().length < 5) {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题主题',
                    status: 'fail',
                    message: '问题主题描述不充分（建议至少5个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '投诉/问题主题',
                    status: 'pass',
                    message: '问题主题描述充分'
                });
            }
            
            if (!customer) {
                results.completeness.push({
                    step: 'D0',
                    field: '客户/部门/供方',
                    status: 'fail',
                    message: '未填写客户/部门/供方信息'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '客户/部门/供方',
                    status: 'pass',
                    message: '已填写客户/部门/供方信息'
                });
            }
            
            if (!proposeDate) {
                results.completeness.push({
                    step: 'D0',
                    field: '提出日期',
                    status: 'fail',
                    message: '未选择提出日期'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '提出日期',
                    status: 'pass',
                    message: '已选择提出日期'
                });
            }
            
            if (!deadline) {
                results.completeness.push({
                    step: 'D0',
                    field: '完成期限',
                    status: 'fail',
                    message: '未选择完成期限'
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '完成期限',
                    status: 'pass',
                    message: '已选择完成期限'
                });
            }
            
            // 8D启动条件检查
            const impactQuestion = document.querySelector('input[name="impactQuestion"]:checked')?.value;
            const rootCauseQuestion = document.querySelector('input[name="rootCauseQuestion"]:checked')?.value;
            const customerRequestQuestion = document.querySelector('input[name="customerRequestQuestion"]:checked')?.value;
            const repeat3Question = document.querySelector('input[name="repeat3Question"]:checked')?.value;
            const costQuestion = document.querySelector('input[name="costQuestion"]:checked')?.value;
            const seniorRequestQuestion = document.querySelector('input[name="seniorRequestQuestion"]:checked')?.value;
            
            const answeredQuestions = [impactQuestion, rootCauseQuestion, customerRequestQuestion, repeat3Question, costQuestion, seniorRequestQuestion].filter(q => q).length;
            
            if (answeredQuestions < 6) {
                results.completeness.push({
                    step: 'D0',
                    field: '8D启动条件评估',
                    status: 'fail',
                    message: `8D启动条件评估不完整（已回答${answeredQuestions}/6个问题）`
                });
            } else {
                results.completeness.push({
                    step: 'D0',
                    field: '8D启动条件评估',
                    status: 'pass',
                    message: '8D启动条件评估完整'
                });
            }
            
            // 检查"是"选项的描述文本框
            if (impactQuestion === 'yes') {
                const conclusionInput = document.getElementById('conclusionInput');
                if (conclusionInput && conclusionInput.style.display !== 'none') {
                    const conclusionContent = conclusionInput.value?.trim();
                    if (!conclusionContent || conclusionContent.length < 10) {
                        results.completeness.push({
                            step: 'D0',
                            field: '问题影响描述',
                            status: 'fail',
                            message: '选择了"问题对客户有严重影响"，但未在启动8D说明中详细描述影响情况'
                        });
                    } else {
                        results.completeness.push({
                            step: 'D0',
                            field: '问题影响描述',
                            status: 'pass',
                            message: '已详细描述问题影响情况'
                        });
                    }
                }
            }
            
            if (rootCauseQuestion === 'yes') {
                const rootCauseDesc = document.getElementById('rootCauseDescription')?.value?.trim();
                if (!rootCauseDesc || rootCauseDesc.length < 10) {
                    results.completeness.push({
                        step: 'D0',
                        field: '根本原因描述',
                        status: 'fail',
                        message: '选择了"问题根本原因不明"，但未详细描述原因不明的情况'
                    });
                }
            }
            
            // 规范性检查
            if (proposeDate && deadline) {
                const proposeDateObj = new Date(proposeDate);
                const deadlineObj = new Date(deadline);
                if (deadlineObj <= proposeDateObj) {
                    results.compliance.push({
                        step: 'D0',
                        field: '完成期限',
                        status: 'fail',
                        message: '完成期限不能早于或等于提出日期'
                    });
                } else {
                    results.compliance.push({
                        step: 'D0',
                        field: '完成期限',
                        status: 'pass',
                        message: '完成期限设置合理'
                    });
                }
            }
            
            // 检查结论输入
            const conclusionInput = document.getElementById('conclusionInput');
            if (conclusionInput && conclusionInput.style.display !== 'none') {
                const conclusionContent = conclusionInput.value?.trim();
                if (!conclusionContent || conclusionContent.length < 10) {
                    results.completeness.push({
                        step: 'D0',
                        field: '启动8D说明',
                        status: 'fail',
                        message: '需要启动8D但未补充说明启动原因或措施'
                    });
                }
            }
            
            return results;
        }
        
        function checkD1Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查团队表格填写情况
            const teamTextareas = document.querySelectorAll('.d-step:nth-of-type(2) table textarea');
            let filledCount = 0;
            let totalFields = 0;
            
            teamTextareas.forEach((textarea, index) => {
                totalFields++;
                if (textarea.value && textarea.value.trim().length > 0) {
                    filledCount++;
                }
            });
            
            const fillRate = totalFields > 0 ? (filledCount / totalFields) * 100 : 0;
            
            if (fillRate < 50) {
                results.completeness.push({
                    step: 'D1',
                    field: '团队信息',
                    status: 'fail',
                    message: `团队信息填写不完整（填写率${fillRate.toFixed(1)}%，建议至少填写50%）`                });
            } else {
                results.completeness.push({
                    step: 'D1',
                    field: '团队信息',
                    status: 'pass',
                    message: `团队信息填写完整（填写率${fillRate.toFixed(1)}%）`
                });
            }
            
            // 检查是否包含关键角色
            const teamText = Array.from(teamTextareas).map(el => el.value.toLowerCase()).join(' ');
            const keyRoles = ['负责人', '经理', '工程师', '主管', '专家'];
            const hasKeyRole = keyRoles.some(role => teamText.includes(role));
            
            if (!hasKeyRole) {
                results.suggestions.push({
                    step: 'D1',
                    field: '团队角色',
                    message: '建议包含团队负责人、技术专家等关键角色'
                });
            }
            
            return results;
        }
        
        function checkD2Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查关键字段
            const deliveryDate = document.getElementById('deliveryDate')?.value;
            const productionDate = document.getElementById('productionDate')?.value;
            const discoveryTime = document.getElementById('discoveryTime')?.value;
            const personLocationDiscovery = document.getElementById('personLocationDiscovery')?.value;
            const possibleBatch = document.getElementById('possibleBatch')?.value;
            const checkCount = document.getElementById('checkCount')?.value;
            const defectCount = document.getElementById('defectCount')?.value;
            const isRepeat = document.getElementById('isRepeat')?.value;
            
            if (!deliveryDate) {
                results.completeness.push({
                    step: 'D2',
                    field: '交付日期',
                    status: 'fail',
                    message: '未填写交付日期'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '交付日期',
                    status: 'pass',
                    message: '已填写交付日期'
                });
            }
            
            if (!productionDate) {
                results.completeness.push({
                    step: 'D2',
                    field: '生产日期',
                    status: 'fail',
                    message: '未填写生产日期'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '生产日期',
                    status: 'pass',
                    message: '已填写生产日期'
                });
            }
            
            if (!discoveryTime) {
                results.completeness.push({
                    step: 'D2',
                    field: '发现时间',
                    status: 'fail',
                    message: '未填写发现时间'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '发现时间',
                    status: 'pass',
                    message: '已填写发现时间'
                });
            }
            
            if (!personLocationDiscovery || personLocationDiscovery.trim().length < 3) {
                results.completeness.push({
                    step: 'D2',
                    field: '发现人员/地点',
                    status: 'fail',
                    message: '发现人员/地点信息不充分（建议至少3个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '发现人员/地点',
                    status: 'pass',
                    message: '已填写发现人员/地点'
                });
            }
            
            if (!possibleBatch) {
                results.completeness.push({
                    step: 'D2',
                    field: '可能批次',
                    status: 'fail',
                    message: '未填写可能批次信息'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '可能批次',
                    status: 'pass',
                    message: '已填写可能批次信息'
                });
            }
            
            if (!isRepeat) {
                results.completeness.push({
                    step: 'D2',
                    field: '重复发生',
                    status: 'fail',
                    message: '未选择是否重复发生'
                });
            } else {
                results.completeness.push({
                    step: 'D2',
                    field: '重复发生',
                    status: 'pass',
                    message: '已选择是否重复发生'
                });
            }
            
            // 检查4M1E变化点
            const d2Step = document.querySelector('.d-step:nth-of-type(3)'); // D2步骤
            if (d2Step) {
                const m4e1Textareas = d2Step.querySelectorAll('textarea[placeholder*="变化点"]');
                let filledM4e1 = 0;
                
                m4e1Textareas.forEach(textarea => {
                    if (textarea.value && textarea.value.trim().length > 0) {
                        filledM4e1++;
                    }
                });
                
                if (filledM4e1 < 2) {
                    results.completeness.push({
                        step: 'D2',
                        field: '4M1E变化点',
                        status: 'fail',
                        message: `4M1E变化点分析不充分（已填写${filledM4e1}/5个方面，建议至少填写2个）`
                    });
                } else {
                    results.completeness.push({
                        step: 'D2',
                        field: '4M1E变化点',
                        status: 'pass',
                        message: `4M1E变化点分析充分（已填写${filledM4e1}/5个方面）`
                    });
                }
            }
            
            // 检查不良率计算
            if (checkCount && defectCount) {
                const checkNum = parseFloat(checkCount);
                const defectNum = parseFloat(defectCount);
                if (checkNum > 0 && defectNum >= 0) {
                    const calculatedRate = (defectNum / checkNum * 100).toFixed(2);
                    const displayedRate = document.getElementById('defectRate')?.value;
                    
                    if (displayedRate && Math.abs(parseFloat(displayedRate) - parseFloat(calculatedRate)) > 0.01) {
                        results.compliance.push({
                            step: 'D2',
                            field: '不良率',
                            status: 'fail',
                            message: `不良率计算错误（应为${calculatedRate}%，当前显示${displayedRate}%）`
                        });
                    } else {
                        results.compliance.push({
                            step: 'D2',
                            field: '不良率',
                            status: 'pass',
                            message: '不良率计算正确'
                        });
                    }
                }
            } else if (!checkCount && !defectCount) {
                results.completeness.push({
                    step: 'D2',
                    field: '检查数据',
                    status: 'fail',
                    message: '未填写检查数和不良数'
                });
            }
            
            return results;
        }
        
        function checkD3Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查临时措施表格
            const tempMeasureRows = document.querySelectorAll('.d3-temp-table-row');
            let filledRows = 0;
            
            tempMeasureRows.forEach(row => {
                const inputs = row.querySelectorAll('input, select');
                const hasContent = Array.from(inputs).some(input => input.value && input.value.trim().length > 0);
                if (hasContent) filledRows++;
            });
            
            if (filledRows === 0) {
                results.completeness.push({
                    step: 'D3',
                    field: '临时措施',
                    status: 'fail',
                    message: '未填写任何临时措施'
                });
            } else {
                results.completeness.push({
                    step: 'D3',
                    field: '临时措施',
                    status: 'pass',
                    message: `已填写${filledRows}项临时措施`
                });
            }
            
            return results;
        }
        
        function checkD4Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查鱼骨图分析
            const fishboneTextareas = document.querySelectorAll('.fishbone-table textarea');
            let filledFishbone = 0;
            
            fishboneTextareas.forEach(textarea => {
                if (textarea.value && textarea.value.trim().length > 0) {
                    filledFishbone++;
                }
            });
            
            if (filledFishbone < 3) {
                results.completeness.push({
                    step: 'D4',
                    field: '鱼骨图分析',
                    status: 'fail',
                    message: '鱼骨图分析不充分（建议至少填写3个因素）'
                });
            } else {
                results.completeness.push({
                    step: 'D4',
                    field: '鱼骨图分析',
                    status: 'pass',
                    message: `鱼骨图分析充分（已填写${filledFishbone}个因素）`
                });
            }
            
            // 检查5Why分析
            const whyInputs = document.querySelectorAll('.why-inputs input[type="text"]');
            let filledWhy = 0;
            
            whyInputs.forEach(input => {
                if (input.value && input.value.trim().length > 0) {
                    filledWhy++;
                }
            });
            
            if (filledWhy < 5) {
                results.completeness.push({
                    step: 'D4',
                    field: '5Why分析',
                    status: 'fail',
                    message: '5Why分析不充分（建议至少填写5个为什么）'
                });
            } else {
                results.completeness.push({
                    step: 'D4',
                    field: '5Why分析',
                    status: 'pass',
                    message: `5Why分析充分（已填写${filledWhy}个为什么）`
                });
            }
            
            return results;
        }
        
        function checkD5Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查纠正措施内容
            const d5Content = document.querySelector('textarea[placeholder*="D5步骤"]')?.value;
            
            if (!d5Content || d5Content.trim().length < 20) {
                results.completeness.push({
                    step: 'D5',
                    field: '纠正措施',
                    status: 'fail',
                    message: '纠正措施描述不充分（建议至少20个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D5',
                    field: '纠正措施',
                    status: 'pass',
                    message: '纠正措施描述充分'
                });
            }
            
            return results;
        }
        
        function checkD6Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查实施验证内容
            const d6Content = document.querySelector('textarea[placeholder*="D6步骤"]')?.value;
            
            if (!d6Content || d6Content.trim().length < 20) {
                results.completeness.push({
                    step: 'D6',
                    field: '实施验证',
                    status: 'fail',
                    message: '实施验证描述不充分（建议至少20个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D6',
                    field: '实施验证',
                    status: 'pass',
                    message: '实施验证描述充分'
                });
            }
            
            return results;
        }
        
        function checkD7Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查预防措施内容
            const d7Content = document.querySelector('textarea[placeholder*="D7步骤"]')?.value;
            
            if (!d7Content || d7Content.trim().length < 20) {
                results.completeness.push({
                    step: 'D7',
                    field: '预防措施',
                    status: 'fail',
                    message: '预防措施描述不充分（建议至少20个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D7',
                    field: '预防措施',
                    status: 'pass',
                    message: '预防措施描述充分'
                });
            }
            
            return results;
        }
        
        function checkD8Completeness() {
            const results = { completeness: [], compliance: [], suggestions: [] };
            
            // 检查团队祝贺内容
            const d8Content = document.querySelector('textarea[placeholder*="D8步骤"]')?.value;
            
            if (!d8Content || d8Content.trim().length < 10) {
                results.completeness.push({
                    step: 'D8',
                    field: '团队祝贺',
                    status: 'fail',
                    message: '团队祝贺内容不充分（建议至少10个字符）'
                });
            } else {
                results.completeness.push({
                    step: 'D8',
                    field: '团队祝贺',
                    status: 'pass',
                    message: '团队祝贺内容充分'
                });
            }
            
            return results;
        }
        
        function showAIAnalysisResults(results) {
            const modalHtml = `
                <div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #ff6b35;border-radius:8px;max-height:80vh;overflow-y:auto;z-index:1000;min-width:700px;max-width:900px;">
                    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
                        <h3 style="margin:0;color:#ff6b35;">AI智能分析报告</h3>
                        <button onclick="closeAIAnalysisModal()" style="background:none;border:none;font-size:24px;cursor:pointer;color:#6c757d;">×</button>
                    </div>
                    
                    <div style="margin-bottom:20px;text-align:center;">
                        <div style="font-size:2em;font-weight:bold;color:${results.score >= 80 ? '#28a745' : results.score >= 60 ? '#ffc107' : '#dc3545'};">
                            ${results.score}分
                        </div>
                        <div style="font-size:1.1em;color:#666;margin-top:5px;">
                            ${results.score >= 80 ? '优秀' : results.score >= 60 ? '良好' : '需要改进'}
                        </div>
                    </div>
                    
                    <div style="margin-bottom:20px;">
                        <h4 style="color:#dc3545;margin-bottom:10px;">❌ 填写遗漏 (${results.completeness.filter(item => item.status === 'fail').length})</h4>
                        ${results.completeness.filter(item => item.status === 'fail').map(item => 
                            `<div style="background:#fff5f5;border-left:4px solid #dc3545;padding:8px;margin-bottom:5px;font-size:0.9em;">
                                <strong>${item.step} - ${item.field}:</strong> ${item.message}
                            </div>`
                        ).join('')}
                    </div>
                    
                    <div style="margin-bottom:20px;">
                        <h4 style="color:#ffc107;margin-bottom:10px;">⚠️ 规范性问题 (${results.compliance.filter(item => item.status === 'fail').length})</h4>
                        ${results.compliance.filter(item => item.status === 'fail').map(item => 
                            `<div style="background:#fffbf0;border-left:4px solid #ffc107;padding:8px;margin-bottom:5px;font-size:0.9em;">
                                <strong>${item.step} - ${item.field}:</strong> ${item.message}
                            </div>`
                        ).join('')}
                    </div>
                    
                    ${results.suggestions.length > 0 ? `
                    <div style="margin-bottom:20px;">
                        <h4 style="color:#17a2b8;margin-bottom:10px;">💡 改进建议 (${results.suggestions.length})</h4>
                        ${results.suggestions.map(item => 
                            `<div style="background:#f0f8ff;border-left:4px solid #17a2b8;padding:8px;margin-bottom:5px;font-size:0.9em;">
                                <strong>${item.step} - ${item.field}:</strong> ${item.message}
                            </div>`
                        ).join('')}
                    </div>
                    ` : ''}
                    
                    <div style="text-align:center;">
                        <button onclick="closeAIAnalysisModal()" style="padding:8px 16px;background:#6c757d;color:white;border:none;border-radius:4px;cursor:pointer;">关闭</button>
                    </div>
                </div>
            `;
            
            const overlay = document.createElement('div');
            overlay.id = 'aiAnalysisOverlay';
            overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;';
            overlay.innerHTML = modalHtml;
            document.body.appendChild(overlay);
        }
        
        function closeAIAnalysisModal() {
            const overlay = document.getElementById('aiAnalysisOverlay');
            if (overlay) {
                overlay.remove();
            }
        }
    </script>
    
    <!-- 主要功能按钮事件处理 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保存数据按钮
            document.getElementById('saveDataBtn').onclick = function() {
                const proposeDate = document.getElementById('proposeDate')?.value;
                const manageNo = document.getElementById('manageNo')?.value;
                
                // 检查是否选择了提出日期
                if (!proposeDate) {
                    alert('请先选择提出日期！');
                    return;
                }
                
                // 检查是否有管理编号
                if (!manageNo) {
                    alert('请先填写提出日期生成管理编号！');
                    return;
                }
                
                // 验证管理编号格式（来源-类型-年月流水号）
                const sourceSelect = document.querySelector('select[name="problemSource"]');
                const typeSelect = document.querySelector('select[name="problemType"]');
                
                if (!sourceSelect || !typeSelect || !sourceSelect.value || !typeSelect.value) {
                    alert('请先选择投诉/问题来源和类型！');
                    return;
                }
                
                const expectedSource = sourceSelect.value;
                const expectedType = typeSelect.value;
                const datePrefix = proposeDate.replace(/-/g, '').slice(0, 6);
                const expectedPrefix = `${expectedSource}-${expectedType}-${datePrefix}`;
                
                if (!manageNo.startsWith(expectedPrefix)) {
                    alert('管理编号格式不正确！请重新选择来源、类型和提出日期生成正确的管理编号。');
                    return;
                }
                
                const data = {
                    manageNo: manageNo,
                    saveTime: new Date().toLocaleString(),
                    // D0 基础信息 - 修复选择器，使用第一个d-step
                    base: {
                        source: document.querySelector('select[name="problemSource"]')?.value || '',
                        type: document.querySelector('select[name="problemType"]')?.value || '',
                        topic: document.getElementById('problemTopic')?.value || '',
                        customer: document.getElementById('customerDepartment')?.value || '',
                        proposeDate: document.getElementById('proposeDate')?.value || '',
                        deadline: document.getElementById('deadline')?.value || ''
                    },
                    d0: {
                        confirm: document.querySelector('.d-step:first-child textarea[placeholder*="D0步骤"]')?.value || '',
                        conclusion: document.getElementById('conclusionInput')?.value || '',
                        // 保存评估状态
                        impactQuestion: document.querySelector('input[name="impactQuestion"]:checked')?.value || '',
                        rootCauseQuestion: document.querySelector('input[name="rootCauseQuestion"]:checked')?.value || '',
                        customerRequestQuestion: document.querySelector('input[name="customerRequestQuestion"]:checked')?.value || '',
                        repeat3Question: document.querySelector('input[name="repeat3Question"]:checked')?.value || '',
                        costQuestion: document.querySelector('input[name="costQuestion"]:checked')?.value || '',
                        seniorRequestQuestion: document.querySelector('input[name="seniorRequestQuestion"]:checked')?.value || '',
                        impactDescription: document.getElementById('impactDescription')?.value || '',
                        rootCauseDescription: document.getElementById('rootCauseDescription')?.value || ''
                    },
                    d1: {
                        team: Array.from(document.querySelectorAll('.d-step:nth-of-type(2) table textarea')).map(el => el.value)
                    },
                    d2: {
                        // D2详细内容
                        deliveryDate: document.getElementById('deliveryDate')?.value || '',
                        productionDate: document.getElementById('productionDate')?.value || '',
                        discoveryTime: document.getElementById('discoveryTime')?.value || '',
                        personLocationDiscovery: document.getElementById('personLocationDiscovery')?.value || '',
                        possibleBatch: document.getElementById('possibleBatch')?.value || '',
                        isRepeat: document.getElementById('isRepeat')?.value || '',
                        checkCount: document.getElementById('checkCount')?.value || '',
                        defectCount: document.getElementById('defectCount')?.value || '',
                        defectRate: document.getElementById('defectRate')?.value || '',
                        // 4M1E变化点 - 修复选择器，使用D2步骤内的dstep-block
                        manChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="人员相关变化点"]')?.value || '',
                        machineChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="设备相关变化点"]')?.value || '',
                        materialChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="材料相关变化点"]')?.value || '',
                        environmentChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="环境相关变化点"]')?.value || '',
                        testChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="测试相关变化点"]')?.value || '',
                        productionHistory: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="生产履历"]')?.value || '',
                        historyProblems: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="内外部历史问题"]')?.value || ''
                    },
                    d3: {
                        // D3临时遏制措施表格数据
                        tempMeasures: Array.from(document.querySelectorAll('.d3-temp-table-row')).map(row => {
                            const inputs = row.querySelectorAll('input, select');
                            return Array.from(inputs).map(input => input.value || '');
                        }),
                        // D3其他内容
                        content: document.querySelector('textarea[placeholder*="D3步骤"]')?.value || '',
                        // D3验证临时措施 - 修复选择器，使用D3步骤内的dstep-block
                        verificationResponsible: document.querySelector('.d-step:nth-of-type(4) .dstep-block input[type="text"]')?.value || '',
                        verificationDate: document.querySelector('.d-step:nth-of-type(4) .dstep-block input[type="date"]')?.value || '',
                        workInstruction: document.querySelector('.d-step:nth-of-type(4) .dstep-block input[type="text"]:nth-of-type(2)')?.value || '',
                        labelAttached: document.querySelector('.d-step:nth-of-type(4) .dstep-block input[type="text"]:nth-of-type(3)')?.value || ''
                    },
                    d4: {
                        content: document.querySelector('textarea[placeholder*="D4步骤"]')?.value || '',
                        fishbone: Array.from(document.querySelectorAll('.fishbone-table textarea')).map(el => el.value),
                        // 鱼骨图单选按钮状态
                        manCause: document.querySelector('input[name="man-cause-group"]:checked')?.value || '',
                        machineCause: document.querySelector('input[name="machine-cause-group"]:checked')?.value || '',
                        materialCause: document.querySelector('input[name="material-cause-group"]:checked')?.value || '',
                        methodCause: document.querySelector('input[name="method-cause-group"]:checked')?.value || '',
                        environmentCause: document.querySelector('input[name="environment-cause-group"]:checked')?.value || '',
                        measurementCause: document.querySelector('input[name="measurement-cause-group"]:checked')?.value || '',
                        // 5Why分析 - 修复选择器，使用D4步骤内的why-inputs
                        why1: Array.from(document.querySelector('.d-step:nth-of-type(5) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(0, 5).map(el => el.value),
                        why2: Array.from(document.querySelector('.d-step:nth-of-type(5) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(5, 10).map(el => el.value),
                        why3: Array.from(document.querySelector('.d-step:nth-of-type(5) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(10, 15).map(el => el.value),
                        why1: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(0, 5).map(el => el.value),
                        why2: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(5, 10).map(el => el.value),
                        why3: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(10, 15).map(el => el.value),
                        dataAnalysis: document.querySelector('textarea[placeholder*="收集和分析数据"]')?.value || '',
                        defectReproduction: {
                            value: document.querySelector('input[name="defectReproduction"]:checked')?.value || '',
                            content: document.getElementById('defectReproductionInput')?.value || ''
                        },
                        riskAssessment: document.querySelector('textarea[placeholder*="基于根本原因的风险评估"]')?.value || ''
                    },
                    d5: {
                        content: document.querySelector('textarea[placeholder*="D5步骤"]')?.value || '',
                        // D5详细内容 - 修复选择器，使用D5步骤内的输入框
                        correctivePlan: {
                            responsible: document.querySelector('.d-step:nth-of-type(5) input[type="text"]')?.value || '',
                            implementationDate: document.querySelector('.d-step:nth-of-type(5) input[type="date"]')?.value || ''
                        },
                        verification: {
                            responsible: document.querySelector('.d-step:nth-of-type(5) input[type="text"]:nth-of-type(2)')?.value || '',
                            implementationDate: document.querySelector('.d-step:nth-of-type(5) input[type="date"]:nth-of-type(2)')?.value || ''
                        }
                    },
                    d6: {
                        content: document.querySelector('textarea[placeholder*="D6步骤"]')?.value || '',
                        // D6详细内容 - 修复选择器，使用D6步骤内的输入框
                        implementation: {
                            responsible: document.querySelector('.d-step:nth-of-type(6) input[type="text"]')?.value || '',
                            implementationDate: document.querySelector('.d-step:nth-of-type(6) input[type="date"]')?.value || ''
                        },
                        effectiveness: {
                            responsible: document.querySelector('.d-step:nth-of-type(6) input[type="text"]:nth-of-type(2)')?.value || '',
                            implementationDate: document.querySelector('.d-step:nth-of-type(6) input[type="date"]:nth-of-type(2)')?.value || ''
                        }
                    },
                    d7: {
                        content: document.querySelector('textarea[placeholder*="D7步骤"]')?.value || '',
                        // D7文档更新表格
                        docUpdates: Array.from(document.querySelectorAll('input[name="d7-doc-update"]:checked')).map(checkbox => checkbox.value),
                        docResponsibles: Array.from(document.querySelectorAll('.d7-input')).map(input => input.value),
                        docDates: Array.from(document.querySelectorAll('.d7-input-date')).map(input => input.value),
                        // D7标准化和培训 - 修复选择器，使用D7步骤内的输入框
                        standardization: {
                            responsible: document.querySelector('.d-step:nth-of-type(7) input[type="text"]')?.value || '',
                            implementationDate: document.querySelector('.d-step:nth-of-type(7) input[type="date"]')?.value || ''
                        }
                    },
                    d8: {
                        content: document.querySelector('textarea[placeholder*="D8步骤"]')?.value || ''
                    }
                };
                
                const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
                allReports[manageNo] = data;
                localStorage.setItem('eightdAllReports', JSON.stringify(allReports));
                
                // 添加调试信息
                console.log('保存的数据结构:', data);
                console.log('保存的管理编号:', manageNo);
                console.log('基础信息字段调试:');
                console.log('- 投诉/问题来源:', data.base.source);
                console.log('- 投诉/问题类型:', data.base.type);
                console.log('- 投诉/问题主题:', data.base.topic);
                console.log('- 客户/部门/供方:', data.base.customer);
                console.log('- 提出日期:', data.base.proposeDate);
                console.log('- 完成期限:', data.base.deadline);
                
                // 检查选择器是否正确
                console.log('选择器调试:');
                console.log('- topic选择器结果:', document.querySelector('.d-step:first-child .form-row:first-child input[type="text"]'));
                console.log('- customer选择器结果:', document.querySelector('.d-step:first-child .form-row:nth-child(2) input[type="text"]'));
                console.log('- deadline选择器结果:', document.querySelector('.d-step:first-child .form-row:nth-child(2) input[type="date"]'));
                
                alert(`数据已保存！管理编号：${manageNo}\n\n保存的数据包括：\n- 基础信息：${Object.keys(data.base).length}个字段\n- D0评估：${Object.keys(data.d0).length}个字段\n- D1团队：${data.d1.team.length}个成员\n- D2问题描述：${Object.keys(data.d2).length}个字段\n- D3临时措施：${data.d3.tempMeasures.length}行表格数据\n- D4根本原因：${Object.keys(data.d4).length}个字段\n- D5-D8：各步骤详细内容`);
            };
            
            // 清除数据按钮
            document.getElementById('clearDataBtn').onclick = function() {
                if (confirm('确定要清除所有数据吗？此操作不可恢复！')) {
                    // 清除所有输入框
                    document.querySelectorAll('input[type="text"], input[type="date"], input[type="number"], textarea').forEach(el => {
                        el.value = '';
                    });
                    
                    // 清除单选按钮
                    document.querySelectorAll('input[type="radio"]').forEach(el => {
                        el.checked = false;
                    });
                    
                    // 清除复选框
                    document.querySelectorAll('input[type="checkbox"]').forEach(el => {
                        el.checked = false;
                    });
                    
                    alert('所有数据已清除！');
                }
            };
            
            // 打印报告按钮
            document.getElementById('printReportBtn').onclick = function() {
                window.print();
            };
            
            // 查看报告按钮
            document.getElementById('viewReportBtn').onclick = function() {
                showReportList();
            };
            
            // AI智能分析按钮
            document.getElementById('aiAnalysisBtn').onclick = function() {
                performAIAnalysis();
            };
            
            // AI智能分析功能
            function performAIAnalysis() {
                // 检查API密钥
                const apiKey = localStorage.getItem('deepseek-api-key');
                if (!apiKey) {
                    alert('请先在AI助手设置中配置DeepSeek API密钥！\n\n配置步骤：\n1. 点击"⚙️ API配置"按钮\n2. 在设置中输入您的DeepSeek API密钥\n3. 点击"保存"按钮');
                    return;
                }
                
                // 收集所有填写的数据
                const reportData = collectAllReportData();
                
                // 显示加载状态
                showAIAnalysisModal('正在连接DeepSeek AI进行智能分析，请稍候...', true);
                
                // 构建分析请求
                const analysisPrompt = buildAnalysisPrompt(reportData);
                
                // 调用DeepSeek AI API
                callDeepSeekAI(analysisPrompt, apiKey)
                    .then(response => {
                        showAIAnalysisResults(response);
                    })
                    .catch(error => {
                        console.error('AI分析错误:', error);
                        showAIAnalysisModal('AI分析失败：' + error.message + '\n\n请检查网络连接和API密钥是否正确。');
                    });
            }
            
            function collectAllReportData() {
                const data = {
                    // 基础信息
                    base: {
                        source: document.querySelector('select[name="problemSource"]')?.value || '',
                        type: document.querySelector('select[name="problemType"]')?.value || '',
                        topic: document.getElementById('problemTopic')?.value || '',
                        customer: document.getElementById('customerDepartment')?.value || '',
                        proposeDate: document.getElementById('proposeDate')?.value || '',
                        deadline: document.getElementById('deadline')?.value || ''
                    },
                    // D0 问题识别
                    d0: {
                        confirm: document.querySelector('.d-step:first-child textarea[placeholder*="D0步骤"]')?.value || '',
                        conclusion: document.getElementById('conclusionInput')?.value || ''
                    },
                    // D1 团队组建
                    d1: {
                        team: Array.from(document.querySelectorAll('.d-step:nth-of-type(2) table textarea')).map(el => el.value).filter(v => v.trim())
                    },
                    // D2 问题描述
                    d2: {
                        deliveryDate: document.getElementById('deliveryDate')?.value || '',
                        productionDate: document.getElementById('productionDate')?.value || '',
                        discoveryTime: document.getElementById('discoveryTime')?.value || '',
                        personLocationDiscovery: document.getElementById('personLocationDiscovery')?.value || '',
                        possibleBatch: document.getElementById('possibleBatch')?.value || '',
                        isRepeat: document.getElementById('isRepeat')?.value || '',
                        checkCount: document.getElementById('checkCount')?.value || '',
                        defectCount: document.getElementById('defectCount')?.value || '',
                        defectRate: document.getElementById('defectRate')?.value || '',
                        manChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="人员相关变化点"]')?.value || '',
                        machineChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="设备相关变化点"]')?.value || '',
                        materialChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="材料相关变化点"]')?.value || '',
                        environmentChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="环境相关变化点"]')?.value || '',
                        testChange: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="测试相关变化点"]')?.value || '',
                        productionHistory: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="生产履历"]')?.value || '',
                        historyProblems: document.querySelector('.d-step:nth-of-type(3) .dstep-block textarea[placeholder*="内外部历史问题"]')?.value || ''
                    },
                    // D3 临时措施
                    d3: {
                        tempMeasures: Array.from(document.querySelectorAll('.d3-temp-table-row')).map(row => {
                            const inputs = row.querySelectorAll('input, select');
                            return Array.from(inputs).map(input => input.value || '');
                        }).filter(row => row.some(v => v.trim())),
                        content: document.querySelector('textarea[placeholder*="D3步骤"]')?.value || ''
                    },
                    // D4 根本原因分析
                    d4: {
                        content: document.querySelector('textarea[placeholder*="D4步骤"]')?.value || '',
                        fishbone: Array.from(document.querySelectorAll('.fishbone-table textarea')).map(el => el.value).filter(v => v.trim()),
                        why1: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(0, 5).map(el => el.value).filter(v => v.trim()),
                        why2: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(5, 10).map(el => el.value).filter(v => v.trim()),
                        why3: Array.from(document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || []).slice(10, 15).map(el => el.value).filter(v => v.trim()),
                        dataAnalysis: document.querySelector('textarea[placeholder*="收集和分析数据"]')?.value || '',
                        defectReproduction: document.getElementById('defectReproductionInput')?.value || '',
                        riskAssessment: document.querySelector('textarea[placeholder*="基于根本原因的风险评估"]')?.value || ''
                    },
                    // D5 纠正措施
                    d5: {
                        content: document.querySelector('textarea[placeholder*="D5步骤"]')?.value || ''
                    },
                    // D6 实施验证
                    d6: {
                        content: document.querySelector('textarea[placeholder*="D6步骤"]')?.value || ''
                    },
                    // D7 预防措施
                    d7: {
                        content: document.querySelector('textarea[placeholder*="D7步骤"]')?.value || ''
                    },
                    // D8 团队祝贺
                    d8: {
                        content: document.querySelector('textarea[placeholder*="D8步骤"]')?.value || ''
                    }
                };
                
                return data;
            }
            
            function buildAnalysisPrompt(reportData) {
                const topic = reportData.base.topic || reportData.d0.confirm || '';
                return `你是一位资深的动力电池制造工艺与质量专家。请针对如下投诉/问题主题，进行专业的原因分析和改善建议，内容要结合IATF 16949、VDA 6.3等权威标准，输出结构如下：\n\n1. 问题简述\n2. 可能原因分析（列举多条，结合工艺、设备、材料、人员、方法、环境等角度）\n3. 针对每个原因给出具体的改善建议（可操作、可落地）\n4. 如有相关标准条款或最佳实践，请引用\n\n投诉/问题主题：${topic}\n\n请用简明、专业、结构化的中文输出。`;
            }
            
            async function callDeepSeekAI(prompt, apiKey) {
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一位资深的8D问题解决专家和质量工程师，精通ISO 9001、IATF 16949、VDA 6.3等质量管理体系标准。请提供专业、准确的分析和建议。'
                            },
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        temperature: 0.3,
                        max_tokens: 4000
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                return data.choices[0].message.content;
            }
            
            function showAIAnalysisModal(message, isLoading = false) {
                const modalHtml = `
                    <div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #ff6b35;border-radius:8px;max-height:80vh;overflow-y:auto;z-index:1000;min-width:600px;max-width:800px;">
                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
                            <h3 style="margin:0;color:#ff6b35;">🔍 AI智能分析</h3>
                            <button onclick="closeAIAnalysisModal()" style="background:none;border:none;font-size:24px;cursor:pointer;color:#6c757d;">×</button>
                        </div>
                        
                        <div style="margin-bottom:20px;">
                            ${isLoading ? '<div style="text-align:center;color:#ff6b35;">⏳ ' : ''}${message.replace(/\n/g, '<br>')}${isLoading ? '</div>' : ''}
                        </div>
                        
                        ${!isLoading ? `
                        <div style="text-align:center;">
                            <button onclick="closeAIAnalysisModal()" style="padding:8px 16px;background:#6c757d;color:white;border:none;border-radius:4px;cursor:pointer;">关闭</button>
                        </div>
                        ` : ''}
                    </div>
                `;
                
                const overlay = document.createElement('div');
                overlay.id = 'aiAnalysisOverlay';
                overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;';
                overlay.innerHTML = modalHtml;
                document.body.appendChild(overlay);
            }
            
            function showAIAnalysisResults(aiResponse) {
                const modalHtml = `
                    <div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #ff6b35;border-radius:8px;max-height:80vh;overflow-y:auto;z-index:1000;min-width:700px;max-width:900px;">
                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;">
                            <h3 style="margin:0;color:#ff6b35;">🔍 AI智能分析报告</h3>
                            <button onclick="closeAIAnalysisModal()" style="background:none;border:none;font-size:24px;cursor:pointer;color:#6c757d;">×</button>
                        </div>
                        
                        <div style="margin-bottom:20px;background:#f8f9fa;padding:15px;border-radius:6px;border-left:4px solid #ff6b35;">
                            <h4 style="margin:0 0 10px 0;color:#ff6b35;">📊 基于DeepSeek AI的权威分析</h4>
                            <p style="margin:0;font-size:0.9em;color:#666;">本分析基于ISO 9001、IATF 16949、VDA 6.3等质量管理体系标准，结合您填写的8D报告内容进行智能分析。</p>
                        </div>
                        
                        <div style="margin-bottom:20px;line-height:1.6;font-size:0.95em;">
                            ${aiResponse.replace(/\n/g, '<br>')}
                        </div>
                        
                        <div style="text-align:center;">
                            <button onclick="closeAIAnalysisModal()" style="padding:8px 16px;background:#6c757d;color:white;border:none;border-radius:4px;cursor:pointer;">关闭</button>
                        </div>
                    </div>
                `;
                
                const overlay = document.getElementById('aiAnalysisOverlay');
                if (overlay) {
                    overlay.innerHTML = modalHtml;
                }
            }
            
            function closeAIAnalysisModal() {
                const overlay = document.getElementById('aiAnalysisOverlay');
                if (overlay) {
                    overlay.remove();
                }
            }
            
            // 显示报告列表弹窗
            function showReportList() {
                const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
                const reportKeys = Object.keys(allReports);
                
                if (reportKeys.length === 0) {
                    alert('暂无保存的报告！');
                    return;
                }
                
                let listHtml = '<div style="position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border:2px solid #007bff;border-radius:8px;max-height:80vh;overflow-y:auto;z-index:1000;min-width:500px;">';
                listHtml += '<h3 style="margin-top:0;color:#007bff;">已保存的报告列表</h3>';
                listHtml += '<table style="border-collapse:collapse;width:100%;">';
                listHtml += '<tr style="background:#f0f0f0;"><th style="border:1px solid #ccc;padding:8px;">管理编号</th><th style="border:1px solid #ccc;padding:8px;">保存时间</th><th style="border:1px solid #ccc;padding:8px;">操作</th></tr>';
                
                reportKeys.forEach(key => {
                    const report = allReports[key];
                    listHtml += `<tr>
                        <td style="border:1px solid #ccc;padding:8px;">${key}</td>
                        <td style="border:1px solid #ccc;padding:8px;">${report.saveTime || '未知'}</td>
                        <td style="border:1px solid #ccc;padding:8px;">
                            <button onclick="loadReport('${key}');document.getElementById('reportListOverlay').remove();" style="margin-right:5px;padding:4px 8px;background:#007bff;color:white;border:none;border-radius:3px;cursor:pointer;">加载</button>
                            <button onclick="deleteReport('${key}');" style="padding:4px 8px;background:#dc3545;color:white;border:none;border-radius:3px;cursor:pointer;">删除</button>
                        </td>
                    </tr>`;
                });
                
                listHtml += '</table>';
                listHtml += '<div style="margin-top:15px;text-align:center;">';
                listHtml += '<button onclick="document.getElementById(\'reportListOverlay\').remove();" style="padding:8px 16px;background:#6c757d;color:white;border:none;border-radius:4px;cursor:pointer;">关闭</button>';
                listHtml += '</div>';
                listHtml += '</div>';
                
                const overlay = document.createElement('div');
                overlay.id = 'reportListOverlay';
                overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;';
                overlay.innerHTML = listHtml;
                document.body.appendChild(overlay);
            }
            
            // 删除报告函数
            window.deleteReport = function(manageNo) {
                if (confirm(`确定要删除报告 ${manageNo} 吗？`)) {
                    const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
                    delete allReports[manageNo];
                    localStorage.setItem('eightdAllReports', JSON.stringify(allReports));
                    alert('报告已删除！');
                    showReportList(); // 刷新列表
                }
            };
            
            // 加载报告函数
            window.loadReport = function(manageNo) {
                const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
                const report = allReports[manageNo];
                
                if (!report) {
                    alert('未找到该报告！');
                    return;
                }
                
                // 填充基础信息
                if (report.base) {
                    // 填充投诉/问题来源
                    if (report.base.source) {
                        const sourceSelect = document.querySelector('select[name="problemSource"]');
                        if (sourceSelect) {
                            sourceSelect.value = report.base.source;
                        }
                    }
                    
                    // 填充投诉/问题类型
                    if (report.base.type) {
                        const typeSelect = document.querySelector('select[name="problemType"]');
                        if (typeSelect) {
                            typeSelect.value = report.base.type;
                        }
                    }
                    
                    // 填充投诉/问题主题
                    if (report.base.topic) {
                        const topicInput = document.getElementById('problemTopic');
                        if (topicInput) {
                            topicInput.value = report.base.topic;
                        }
                    }
                    
                    // 填充客户/部门/供方
                    if (report.base.customer) {
                        const customerInput = document.getElementById('customerDepartment');
                        if (customerInput) {
                            customerInput.value = report.base.customer;
                        }
                    }
                    
                    // 填充提出日期
                    if (document.getElementById('proposeDate')) {
                        document.getElementById('proposeDate').value = report.base.proposeDate || '';
                    }
                    
                    // 填充管理编号
                    if (document.getElementById('manageNo')) {
                        document.getElementById('manageNo').value = report.manageNo || '';
                        // 设置当前管理编号，防止重新选择日期时生成新的编号
                        window._currentManageNo = report.manageNo || '';
                    }
                    
                    // 填充完成期限
                    if (report.base.deadline) {
                        const deadlineInput = document.getElementById('deadline');
                        if (deadlineInput) {
                            deadlineInput.value = report.base.deadline;
                        }
                    }
                }
                
                // 填充D0内容
                if (report.d0) {
                    const d0Textarea = document.querySelector('.d-step:first-child textarea[placeholder*="D0步骤"]');
                    if (d0Textarea) {
                        d0Textarea.value = report.d0.confirm || '';
                    }
                    if (document.getElementById('conclusionInput')) {
                        document.getElementById('conclusionInput').value = report.d0.conclusion || '';
                    }
                    
                    // 恢复评估状态
                    if (report.d0.impactQuestion) {
                        const radio = document.querySelector(`input[name="impactQuestion"][value="${report.d0.impactQuestion}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d0.rootCauseQuestion) {
                        const radio = document.querySelector(`input[name="rootCauseQuestion"][value="${report.d0.rootCauseQuestion}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d0.customerRequestQuestion) {
                        const radio = document.querySelector(`input[name="customerRequestQuestion"][value="${report.d0.customerRequestQuestion}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d0.repeat3Question) {
                        const radio = document.querySelector(`input[name="repeat3Question"][value="${report.d0.repeat3Question}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d0.costQuestion) {
                        const radio = document.querySelector(`input[name="costQuestion"][value="${report.d0.costQuestion}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d0.seniorRequestQuestion) {
                        const radio = document.querySelector(`input[name="seniorRequestQuestion"][value="${report.d0.seniorRequestQuestion}"]`);
                        if (radio) radio.checked = true;
                    }
                    
                    // 恢复描述文本框
                    if (report.d0.impactDescription) {
                        const impactDesc = document.getElementById('impactDescription');
                        if (impactDesc) {
                            impactDesc.value = report.d0.impactDescription;
                            if (report.d0.impactQuestion === 'yes') {
                                impactDesc.style.display = 'block';
                            }
                        }
                    }
                    if (report.d0.rootCauseDescription) {
                        const rootCauseDesc = document.getElementById('rootCauseDescription');
                        if (rootCauseDesc) {
                            rootCauseDesc.value = report.d0.rootCauseDescription;
                            if (report.d0.rootCauseQuestion === 'yes') {
                                rootCauseDesc.style.display = 'block';
                            }
                        }
                    }
                    
                    // 更新结论显示
                    if (typeof update8DConclusion === 'function') {
                        update8DConclusion();
                    }
                    
                    // 确保conclusionInput的显示状态正确
                    const conclusionInput = document.getElementById('conclusionInput');
                    if (conclusionInput) {
                        const shouldStart8D = report.d0.impactQuestion === 'yes' || 
                                           report.d0.rootCauseQuestion === 'yes' || 
                                           report.d0.customerRequestQuestion === 'yes' || 
                                           report.d0.repeat3Question === 'yes' || 
                                           report.d0.costQuestion === 'yes' || 
                                           report.d0.seniorRequestQuestion === 'yes';
                        if (shouldStart8D) {
                            conclusionInput.style.display = 'block';
                        } else {
                            conclusionInput.style.display = 'none';
                        }
                    }
                }
                
                // 填充D1团队信息
                if (report.d1 && report.d1.team) {
                    const teamTextareas = document.querySelectorAll('.d-step:nth-of-type(2) table textarea');
                    report.d1.team.forEach((value, index) => {
                        if (teamTextareas[index]) {
                            teamTextareas[index].value = value || '';
                        }
                    });
                }
                
                // 填充D2详细内容
                if (report.d2) {
                    if (report.d2.deliveryDate) document.getElementById('deliveryDate').value = report.d2.deliveryDate;
                    if (report.d2.productionDate) document.getElementById('productionDate').value = report.d2.productionDate;
                    if (report.d2.discoveryTime) document.getElementById('discoveryTime').value = report.d2.discoveryTime;
                    if (report.d2.personLocationDiscovery) document.getElementById('personLocationDiscovery').value = report.d2.personLocationDiscovery;
                    if (report.d2.possibleBatch) document.getElementById('possibleBatch').value = report.d2.possibleBatch;
                    if (report.d2.isRepeat) document.getElementById('isRepeat').value = report.d2.isRepeat;
                    if (report.d2.checkCount) document.getElementById('checkCount').value = report.d2.checkCount;
                    if (report.d2.defectCount) document.getElementById('defectCount').value = report.d2.defectCount;
                    if (report.d2.defectRate) document.getElementById('defectRate').value = report.d2.defectRate;
                    
                    // 4M1E变化点 - 修复选择器，使用D2步骤内的dstep-block
                    if (report.d2.manChange) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="人员相关变化点"]');
                        if (textarea) textarea.value = report.d2.manChange;
                    }
                    if (report.d2.machineChange) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="设备相关变化点"]');
                        if (textarea) textarea.value = report.d2.machineChange;
                    }
                    if (report.d2.materialChange) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="材料相关变化点"]');
                        if (textarea) textarea.value = report.d2.materialChange;
                    }
                    if (report.d2.environmentChange) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="环境相关变化点"]');
                        if (textarea) textarea.value = report.d2.environmentChange;
                    }
                    if (report.d2.testChange) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="测试相关变化点"]');
                        if (textarea) textarea.value = report.d2.testChange;
                    }
                    if (report.d2.productionHistory) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="生产履历"]');
                        if (textarea) textarea.value = report.d2.productionHistory;
                    }
                    if (report.d2.historyProblems) {
                        const textarea = document.querySelector('.d-step:nth-of-type(2) .dstep-block textarea[placeholder*="内外部历史问题"]');
                        if (textarea) textarea.value = report.d2.historyProblems;
                    }
                }
                
                // 填充D3临时遏制措施表格
                if (report.d3 && Array.isArray(report.d3.tempMeasures)) {
                    const rows = document.querySelectorAll('.d3-temp-table-row');
                    report.d3.tempMeasures.forEach((rowData, rowIndex) => {
                        if (rows[rowIndex]) {
                            const inputs = rows[rowIndex].querySelectorAll('input, select');
                            rowData.forEach((value, inputIndex) => {
                                if (inputs[inputIndex]) {
                                    inputs[inputIndex].value = value || '';
                                }
                            });
                            
                            // 重新应用库存选择框的背景颜色
                            const stockSelect = rows[rowIndex].querySelector('.table-stock-select');
                            if (stockSelect && typeof updateStockBg === 'function') {
                                updateStockBg(stockSelect);
                            }
                        }
                    });
                }
                
                // 填充D3验证临时措施 - 修复选择器，使用D3步骤内的dstep-block
                if (report.d3) {
                    if (report.d3.verificationResponsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(3) .dstep-block input[type="text"]');
                        if (inputs[0]) inputs[0].value = report.d3.verificationResponsible;
                    }
                    if (report.d3.verificationDate) {
                        const dateInput = document.querySelector('.d-step:nth-of-type(3) .dstep-block input[type="date"]');
                        if (dateInput) dateInput.value = report.d3.verificationDate;
                    }
                    if (report.d3.workInstruction) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(3) .dstep-block input[type="text"]');
                        if (inputs[1]) inputs[1].value = report.d3.workInstruction;
                    }
                    if (report.d3.labelAttached) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(3) .dstep-block input[type="text"]');
                        if (inputs[2]) inputs[2].value = report.d3.labelAttached;
                    }
                }
                
                // 填充D4鱼骨图内容
                if (report.d4 && Array.isArray(report.d4.fishbone)) {
                    const fishboneTextareas = document.querySelectorAll('.fishbone-table textarea');
                    report.d4.fishbone.forEach((value, idx) => {
                        if (fishboneTextareas[idx]) {
                            fishboneTextareas[idx].value = value || '';
                        }
                    });
                }
                
                // 恢复D4鱼骨图单选按钮状态
                if (report.d4) {
                    if (report.d4.manCause) {
                        const radio = document.querySelector(`input[name="man-cause-group"][value="${report.d4.manCause}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d4.machineCause) {
                        const radio = document.querySelector(`input[name="machine-cause-group"][value="${report.d4.machineCause}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d4.materialCause) {
                        const radio = document.querySelector(`input[name="material-cause-group"][value="${report.d4.materialCause}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d4.methodCause) {
                        const radio = document.querySelector(`input[name="method-cause-group"][value="${report.d4.methodCause}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d4.environmentCause) {
                        const radio = document.querySelector(`input[name="environment-cause-group"][value="${report.d4.environmentCause}"]`);
                        if (radio) radio.checked = true;
                    }
                    if (report.d4.measurementCause) {
                        const radio = document.querySelector(`input[name="measurement-cause-group"][value="${report.d4.measurementCause}"]`);
                        if (radio) radio.checked = true;
                    }
                }
                
                // 填充D4的5Why分析 - 修复选择器，使用D4步骤内的why-inputs
                if (report.d4) {
                    const d4WhyInputs = document.querySelector('.d-step:nth-of-type(4) .why-inputs')?.querySelectorAll('input[type="text"]') || [];
                    if (Array.isArray(report.d4.why1)) {
                        report.d4.why1.forEach((value, idx) => {
                            if (d4WhyInputs[idx]) d4WhyInputs[idx].value = value || '';
                        });
                    }
                    if (Array.isArray(report.d4.why2)) {
                        report.d4.why2.forEach((value, idx) => {
                            if (d4WhyInputs[idx + 5]) d4WhyInputs[idx + 5].value = value || '';
                        });
                    }
                    if (Array.isArray(report.d4.why3)) {
                        report.d4.why3.forEach((value, idx) => {
                            if (d4WhyInputs[idx + 10]) d4WhyInputs[idx + 10].value = value || '';
                        });
                    }
                    if (report.d4.dataAnalysis) {
                        const textarea = document.querySelector('textarea[placeholder*="收集和分析数据"]');
                        if (textarea) textarea.value = report.d4.dataAnalysis;
                    }
                    // 恢复不良再现状态
                    if (report.d4.defectReproduction) {
                        const radio = document.querySelector(`input[name="defectReproduction"][value="${report.d4.defectReproduction.value}"]`);
                        if (radio) {
                            radio.checked = true;
                            toggleDefectReproductionInput(); // 更新输入框显示状态
                        }
                        
                        // 恢复输入框内容
                        if (report.d4.defectReproduction.content) {
                            const textarea = document.getElementById('defectReproductionInput');
                            if (textarea) {
                                textarea.value = report.d4.defectReproduction.content;
                            }
                        }
                    }
                    if (report.d4.riskAssessment) {
                        const textarea = document.querySelector('textarea[placeholder*="基于根本原因的风险评估"]');
                        if (textarea) textarea.value = report.d4.riskAssessment;
                    }
                }
                
                // 填充D5详细内容 - 修复选择器，使用D5步骤内的输入框
                if (report.d5 && report.d5.correctivePlan) {
                    if (report.d5.correctivePlan.responsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(5) input[type="text"]');
                        if (inputs[0]) inputs[0].value = report.d5.correctivePlan.responsible;
                    }
                    if (report.d5.correctivePlan.implementationDate) {
                        const dateInputs = document.querySelectorAll('.d-step:nth-of-type(5) input[type="date"]');
                        if (dateInputs[0]) dateInputs[0].value = report.d5.correctivePlan.implementationDate;
                    }
                }
                if (report.d5 && report.d5.verification) {
                    if (report.d5.verification.responsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(5) input[type="text"]');
                        if (inputs[1]) inputs[1].value = report.d5.verification.responsible;
                    }
                    if (report.d5.verification.implementationDate) {
                        const dateInputs = document.querySelectorAll('.d-step:nth-of-type(5) input[type="date"]');
                        if (dateInputs[1]) dateInputs[1].value = report.d5.verification.implementationDate;
                    }
                }
                
                // 填充D6详细内容 - 修复选择器，使用D6步骤内的输入框
                if (report.d6 && report.d6.implementation) {
                    if (report.d6.implementation.responsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(6) input[type="text"]');
                        if (inputs[0]) inputs[0].value = report.d6.implementation.responsible;
                    }
                    if (report.d6.implementation.implementationDate) {
                        const dateInputs = document.querySelectorAll('.d-step:nth-of-type(6) input[type="date"]');
                        if (dateInputs[0]) dateInputs[0].value = report.d6.implementation.implementationDate;
                    }
                }
                if (report.d6 && report.d6.effectiveness) {
                    if (report.d6.effectiveness.responsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(6) input[type="text"]');
                        if (inputs[1]) inputs[1].value = report.d6.effectiveness.responsible;
                    }
                    if (report.d6.effectiveness.implementationDate) {
                        const dateInputs = document.querySelectorAll('.d-step:nth-of-type(6) input[type="date"]');
                        if (dateInputs[1]) dateInputs[1].value = report.d6.effectiveness.implementationDate;
                    }
                }
                
                // 填充D7标准化和培训 - 修复选择器，使用D7步骤内的输入框
                if (report.d7 && report.d7.standardization) {
                    if (report.d7.standardization.responsible) {
                        const inputs = document.querySelectorAll('.d-step:nth-of-type(7) input[type="text"]');
                        if (inputs[0]) inputs[0].value = report.d7.standardization.responsible;
                    }
                    if (report.d7.standardization.implementationDate) {
                        const dateInputs = document.querySelectorAll('.d-step:nth-of-type(7) input[type="date"]');
                        if (dateInputs[0]) dateInputs[0].value = report.d7.standardization.implementationDate;
                    }
                }
                
                // 填充D7文档更新部分
                if (report.d7) {
                    // 恢复文档更新复选框
                    if (Array.isArray(report.d7.docUpdates)) {
                        report.d7.docUpdates.forEach(docType => {
                            const checkbox = document.querySelector(`input[name="d7-doc-update"][value="${docType}"]`);
                            if (checkbox) checkbox.checked = true;
                        });
                    }
                    
                    // 恢复责任人
                    if (Array.isArray(report.d7.docResponsibles)) {
                        const responsibles = document.querySelectorAll('.d7-input');
                        report.d7.docResponsibles.forEach((value, idx) => {
                            if (responsibles[idx]) responsibles[idx].value = value || '';
                        });
                    }
                    
                    // 恢复日期
                    if (Array.isArray(report.d7.docDates)) {
                        const dates = document.querySelectorAll('.d7-input-date');
                        report.d7.docDates.forEach((value, idx) => {
                            if (dates[idx]) dates[idx].value = value || '';
                        });
                    }
                }
                
                // 填充其他步骤内容
                ['d2', 'd3', 'd4', 'd5', 'd6', 'd7', 'd8'].forEach(step => {
                    if (report[step] && report[step].content) {
                        const textarea = document.querySelector(`textarea[placeholder*="${step.toUpperCase()}步骤"]`);
                        if (textarea) {
                            textarea.value = report[step].content || '';
                        }
                    }
                });
                
                // 添加调试信息
                console.log('加载的数据结构:', report);
                console.log('加载的管理编号:', manageNo);
                
                alert(`报告 ${manageNo} 已加载！\n\n加载的数据包括：\n- 基础信息：${Object.keys(report.base || {}).length}个字段\n- D0评估：${Object.keys(report.d0 || {}).length}个字段\n- D1团队：${(report.d1?.team || []).length}个成员\n- D2问题描述：${Object.keys(report.d2 || {}).length}个字段\n- D3临时措施：${(report.d3?.tempMeasures || []).length}行表格数据\n- D4根本原因：${Object.keys(report.d4 || {}).length}个字段\n- D5-D8：各步骤详细内容`);
            };
            
            // AI设置面板功能
            document.getElementById('toggle-ai-settings').onclick = function() {
                const content = document.getElementById('ai-settings-content');
                const button = document.getElementById('toggle-ai-settings');
                if (content.style.display === 'none' || content.style.display === '') {
                    content.style.display = 'block';
                    button.textContent = '收起';
                } else {
                    content.style.display = 'none';
                    button.textContent = '设置';
                }
            };
            
            // API配置按钮功能
            document.getElementById('save-api-key').onclick = function() {
                const apiKey = document.getElementById('deepseek-api-key').value;
                if (!apiKey.trim()) {
                    alert('请输入API密钥！');
                    return;
                }
                localStorage.setItem('deepseek-api-key', apiKey);
                alert('API密钥已保存！');
            };
            
            document.getElementById('clear-api-key').onclick = function() {
                document.getElementById('deepseek-api-key').value = '';
                localStorage.removeItem('deepseek-api-key');
                alert('API密钥已清除！');
            };
            
            // 页面加载时自动填充保存的API Key
            document.addEventListener('DOMContentLoaded', function() {
                const savedApiKey = localStorage.getItem('deepseek-api-key');
                if (savedApiKey) {
                    document.getElementById('deepseek-api-key').value = savedApiKey;
                }
            });
            
            document.getElementById('test-api-connection').onclick = function() {
                const apiKey = document.getElementById('deepseek-api-key').value;
                if (!apiKey.trim()) {
                    alert('请先输入API密钥！');
                    return;
                }
                
                // 显示测试中状态
                const testBtn = document.getElementById('test-api-connection');
                const originalText = testBtn.textContent;
                testBtn.textContent = '测试中...';
                testBtn.disabled = true;
                
                // 实际连接Deepseek AI API
                fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '你好，这是一个API连接测试。'
                            }
                        ],
                        max_tokens: 50
                    })
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    alert('✅ API连接成功！\n\nDeepseek AI响应正常，可以正常使用AI功能。');
                })
                .catch(error => {
                    console.error('API连接错误:', error);
                    if (error.message.includes('401')) {
                        alert('❌ API连接失败！\n\n错误：API密钥无效或已过期。\n请检查API密钥是否正确。');
                    } else if (error.message.includes('429')) {
                        alert('❌ API连接失败！\n\n错误：请求频率过高，请稍后再试。');
                    } else if (error.message.includes('500')) {
                        alert('❌ API连接失败！\n\n错误：Deepseek AI服务器内部错误，请稍后再试。');
                    } else {
                        alert(`❌ API连接失败！\n\n错误：${error.message}\n\n请检查网络连接和API密钥。`);
                    }
                })
                .finally(() => {
                    // 恢复按钮状态
                    testBtn.textContent = originalText;
                    testBtn.disabled = false;
                });
            };
            
            // AI会话窗口功能
            document.getElementById('ai-chat-btn').onclick = function() {
                if (!document.getElementById('enable-ai-chat').checked) {
                    alert('请先启用AI会话助手功能！');
                    return;
                }
                const chatWindow = document.getElementById('ai-chat-window');
                chatWindow.style.display = chatWindow.style.display === 'none' ? 'block' : 'none';
            };
            
            document.getElementById('close-chat').onclick = function() {
                document.getElementById('ai-chat-window').style.display = 'none';
            };
            
            // 发送消息功能
            document.getElementById('send-message').onclick = function() {
                sendMessage();
            };
            
            document.getElementById('chat-input').onkeypress = function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            };
            
            function sendMessage() {
                const input = document.getElementById('chat-input');
                const message = input.value.trim();
                if (!message) return;
                
                // 添加用户消息
                addMessage('用户', message, 'user');
                input.value = '';
                
                // 获取API Key
                const apiKey = localStorage.getItem('deepseek-api-key');
                
                if (apiKey) {
                    // 使用真实的Deepseek AI API
                    fetch('https://api.deepseek.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: 'deepseek-chat',
                            messages: [
                                {
                                    role: 'system',
                                    content: '你是一个专业的汽车行业8D问题解决和质量管理的AI助手。请根据用户的问题提供专业、准确、实用的回答。回答时请使用清晰的段落结构，避免使用markdown格式符号如##等，直接使用换行和缩进来组织内容。如果是关于具体技术问题，请提供基于行业标准和最佳实践的建议。'
                                },
                                {
                                    role: 'user',
                                    content: message
                                }
                            ],
                            max_tokens: 800,
                            temperature: 0.7
                        })
                    })
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        } else {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                    })
                    .then(data => {
                        const aiResponse = data.choices[0].message.content;
                        addMessage('AI助手', aiResponse, 'ai');
                    })
                    .catch(error => {
                        console.error('AI API错误:', error);
                        // 如果API调用失败，使用本地知识库回答
                        const localResponse = generateLocalAIResponse(message);
                        addMessage('AI助手', localResponse, 'ai');
                    });
                } else {
                    // 如果没有API Key，使用本地知识库回答
                    const localResponse = generateLocalAIResponse(message);
                    addMessage('AI助手', localResponse, 'ai');
                }
            }
            
            function generateLocalAIResponse(userMessage) {
                const lowerMessage = userMessage.toLowerCase();
                
                // 8D相关问题
                if (lowerMessage.includes('8d') || lowerMessage.includes('问题解决')) {
                    return '8D问题解决方法是一种系统性的问题解决流程，包含8个步骤：\n\nD0 - 8D准备：评估问题是否满足启动8D的条件\nD1 - 建立团队：组建跨职能团队\nD2 - 描述问题：使用5W1H方法详细描述\nD3 - 实施临时措施：立即遏制问题\nD4 - 根本原因分析：找到真正的原因\nD5 - 选择和验证纠正措施：确保措施有效\nD6 - 实施和验证纠正措施：执行并验证\nD7 - 预防再发生：完善体系防止重复\nD8 - 祝贺团队：总结经验教训\n\n每个步骤都有明确的目标和输出，确保问题得到彻底解决。';
                }
                
                // 具体技术问题
                if (lowerMessage.includes('脱胶') || lowerMessage.includes('粘接')) {
                    return '关于脱胶问题的分析：\n\n可能的原因：\n1. 胶粘剂选择不当或质量不良\n2. 表面处理不充分（清洁、活化）\n3. 固化条件不符合要求（温度、时间、压力）\n4. 环境因素影响（湿度、温度变化）\n5. 应力集中或设计不合理\n\n建议的解决步骤：\n1. 检查胶粘剂规格和有效期\n2. 确认表面处理工艺\n3. 验证固化参数\n4. 进行环境适应性测试\n5. 优化设计减少应力集中\n\n建议使用8D方法系统分析，从D2问题描述开始，详细记录脱胶的具体现象、位置、时间等信息。';
                }
                
                if (lowerMessage.includes('尺寸') || lowerMessage.includes('公差')) {
                    return '关于尺寸超差问题：\n\n常见原因：\n1. 设备精度问题（机床、模具磨损）\n2. 工艺参数不当（温度、压力、时间）\n3. 材料批次差异\n4. 测量方法或设备问题\n5. 操作人员技能不足\n\n解决建议：\n1. 进行MSA（测量系统分析）\n2. 检查设备精度和维护状态\n3. 优化工艺参数\n4. 加强过程控制\n5. 培训操作人员\n\n建议使用SPC（统计过程控制）监控关键尺寸，建立预警机制。';
                }
                
                if (lowerMessage.includes('不良') || lowerMessage.includes('缺陷')) {
                    return '关于不良品分析：\n\n分析步骤：\n1. 收集不良品样本和数据\n2. 分类统计不良类型和频率\n3. 使用鱼骨图分析可能原因\n4. 进行根本原因验证\n5. 制定纠正和预防措施\n\n常用工具：\n- 帕累托图：识别主要不良类型\n- 鱼骨图：分析根本原因\n- 5个为什么：深入分析\n- 控制图：监控过程稳定性\n\n建议按照8D流程系统处理，确保问题得到彻底解决。';
                }
                
                // 质量管理相关问题
                if (lowerMessage.includes('质量') || lowerMessage.includes('管理')) {
                    return '质量管理体系要点：\n\n核心要素：\n1. 领导作用：管理层承诺和参与\n2. 过程方法：识别和管理关键过程\n3. 持续改进：PDCA循环\n4. 基于事实的决策：数据驱动\n5. 全员参与：各层级人员参与\n\n常用工具：\n- FMEA：失效模式分析\n- SPC：统计过程控制\n- 8D：问题解决方法\n- 5S：现场管理\n- 精益生产：消除浪费\n\n建议建立完善的质量管理体系，定期进行内审和管理评审。';
                }
                
                // 默认回答
                return '感谢您的提问！我是专业的8D问题解决和质量管理AI助手。\n\n我可以帮助您：\n• 分析具体的技术问题（如脱胶、尺寸超差、不良品等）\n• 解释8D问题解决方法的各个步骤\n• 提供质量管理相关的专业建议\n• 协助制定纠正和预防措施\n\n请详细描述您遇到的具体问题，我会根据行业标准和最佳实践为您提供专业的分析和建议。';
            }
            
            function addMessage(sender, content, type) {
                const messagesDiv = document.getElementById('chat-messages');
                const messageDiv = document.createElement('div');
                messageDiv.style.cssText = `
                    background: ${type === 'user' ? '#e3f2fd' : '#f8f9fa'};
                    padding: 8px 12px;
                    border-radius: 6px;
                    margin-bottom: 8px;
                    font-size: 0.9em;
                    text-align: ${type === 'user' ? 'right' : 'left'};
                    white-space: pre-line;
                    line-height: 1.5;
                `;
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
                messagesDiv.appendChild(messageDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
            
            // 初始化自动填充功能
            enableAutoFill();
        });
        
        // 管理编号自动生成 - 新规则：来源-类型-年月流水号
        function generateManageNo() {
            const sourceSelect = document.querySelector('select[name="problemSource"]') || 
                                document.querySelector('select:has(option[value="I"])');
            const typeSelect = document.querySelector('select[name="problemType"]') || 
                              document.querySelector('select:has(option[value="Q"])');
            const dateInput = document.getElementById('proposeDate');
            
            if (!sourceSelect || !typeSelect || !dateInput || !dateInput.value) {
                return '';
            }
            
            const source = sourceSelect.value;
            const type = typeSelect.value;
            const date = new Date(dateInput.value);
            
            if (!source || !type || isNaN(date.getTime())) {
                return '';
            }
            
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const prefix = `${source}-${type}-${year}${month}`;
            
            // 获取所有已保存的报告
            const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
            const existingReports = Object.keys(allReports).filter(key => key.startsWith(prefix));
            
            let nextSerial = 1;
            if (existingReports.length > 0) {
                // 找到当前前缀下的最大流水号
                const maxSerial = Math.max(...existingReports.map(key => {
                    const serialStr = key.substring(prefix.length); // 去掉前缀，取流水号部分
                    return parseInt(serialStr, 10) || 0;
                }));
                nextSerial = maxSerial + 1;
            }
            
            const serial = nextSerial.toString().padStart(3, '0');
            const manageNo = `${prefix}${serial}`;
            window._currentManageNo = manageNo;
            return manageNo;
        }
        
        // 监听来源、类型和日期变化，自动生成管理编号
        function setupManageNoGeneration() {
            const sourceSelect = document.querySelector('select:has(option[value="I"])');
            const typeSelect = document.querySelector('select:has(option[value="Q"])');
            const dateInput = document.getElementById('proposeDate');
            const manageNoInput = document.getElementById('manageNo');
            
            if (sourceSelect && typeSelect && dateInput && manageNoInput) {
                const updateManageNo = () => {
                    const newNo = generateManageNo();
                    if (newNo) {
                        manageNoInput.value = newNo;
                    }
                };
                
                sourceSelect.addEventListener('change', updateManageNo);
                typeSelect.addEventListener('change', updateManageNo);
                dateInput.addEventListener('change', updateManageNo);
            }
        }
        
        // 管理编号自动生成 - 修复为原来的年月流水号规则
        function generateManageNoByDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return '';
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const prefix = `${year}${month}`;
            
            // 获取所有已保存的报告
            const allReports = JSON.parse(localStorage.getItem('eightdAllReports') || '{}');
            const existingReports = Object.keys(allReports).filter(key => key.startsWith(prefix));
            
            let nextSerial = 1;
            if (existingReports.length > 0) {
                // 找到当前年月下的最大流水号
                const maxSerial = Math.max(...existingReports.map(key => {
                    const serialStr = key.substring(6); // 去掉年月前缀，取流水号部分
                    return parseInt(serialStr, 10) || 0;
                }));
                nextSerial = maxSerial + 1;
            }
            
            const serial = nextSerial.toString().padStart(3, '0');
            const manageNo = `${prefix}${serial}`;
            window._currentManageNo = manageNo;
            return manageNo;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 先隐藏所有内容区
            document.querySelectorAll('.page-content').forEach(el => el.style.display = 'none');
            // 恢复上次访问的页面状态
            const lastPage = localStorage.getItem('eightd-current-page');
            const pageToShow = (lastPage && document.getElementById(lastPage)) ? lastPage : 'main-8d-page';
            const page = document.getElementById(pageToShow);
            if (page) page.style.display = 'block';
            
            // 初始化导航按钮状态
            const navMain8d = document.getElementById('nav-main-8d');
            const navEvaluation = document.getElementById('nav-evaluation');
            
            if (navMain8d && navEvaluation) {
                if (pageToShow === 'main-8d-page') {
                    // 当前在8D问题解决报告页面
                    navMain8d.style.background = '#007bff'; // 活跃状态 - 蓝色
                    navEvaluation.style.background = '#6c757d'; // 非活跃状态 - 灰色
                } else if (pageToShow === 'evaluation-page') {
                    // 当前在8D报告评估表页面
                    navMain8d.style.background = '#6c757d'; // 非活跃状态 - 灰色
                    navEvaluation.style.background = '#007bff'; // 活跃状态 - 蓝色
                }
            }
            
            // 根据当前页面控制AI助手面板显示/隐藏
            const aiPanel = document.getElementById('ai-panel');
            if (aiPanel) {
                if (pageToShow === 'main-8d-page') {
                    aiPanel.style.display = 'block';
                } else {
                    aiPanel.style.display = 'none';
                }
            }
            
            // 绑定评估表事件
            const radioButtons = document.querySelectorAll('.evaluation-table input[type="radio"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    calculateScore();
                });
            });
            setTimeout(calculateScore, 100);
            // 自动填充保存的API Key
            const savedApiKey = localStorage.getItem('deepseek-api-key');
            if (savedApiKey) {
                const apiKeyInput = document.getElementById('deepseek-api-key');
                if (apiKeyInput) {
                    apiKeyInput.value = savedApiKey;
                }
            }
            // 监听提出日期变化
            var proposeDateInput = document.getElementById('proposeDate');
            var manageNoInput = document.getElementById('manageNo');
            if (proposeDateInput && manageNoInput) {
                proposeDateInput.addEventListener('change', function() {
                    if (proposeDateInput.value) {
                        if (!window._currentManageNo || !window._currentManageNo.startsWith(proposeDateInput.value.replace(/-/g, '').slice(0,6))) {
                            var no = generateManageNoByDate(proposeDateInput.value);
                            manageNoInput.value = no;
                        }
                    } else {
                        manageNoInput.value = '';
                    }
                });
            }
            
            // 设置新的管理编号生成规则
            setupManageNoGeneration();
            
            // 加载公司信息
            updateCompanyInfoDisplay();
        });
    </script>
    <script>
    function calcDefectRate() {
        var check = parseFloat(document.getElementById('checkCount').value);
        var defect = parseFloat(document.getElementById('defectCount').value);
        var rateInput = document.getElementById('defectRate');
        if (!isNaN(check) && check > 0 && !isNaN(defect) && defect >= 0) {
            var rate = (defect / check) * 100;
            rateInput.value = rate.toFixed(2) + '%';
        } else {
            rateInput.value = '';
        }
    }
    </script>
    <script>
    function updateTableDefectRate(row) {
        var checkInput = row.querySelector('.table-check-count');
        var defectInput = row.querySelector('.table-defect-count');
        var rateCell = row.querySelector('.table-defect-rate');
        var check = parseFloat(checkInput.value);
        var defect = parseFloat(defectInput.value);
        rateCell.style.textAlign = 'center'; // 居中
        if (!isNaN(check) && check > 0 && !isNaN(defect) && defect >= 0) {
            var rate = (defect / check) * 100;
            rateCell.textContent = rate.toFixed(2) + '%';
        } else {
            rateCell.textContent = '';
        }
    }

    function updateStockBg(select) {
        var td = select.parentElement;
        if (select.value === 'YES') {
            td.style.background = '#ff3b30'; // 红色
            td.style.color = '#fff';
        } else if (select.value === 'NO') {
            td.style.background = '#34c759'; // 绿色
            td.style.color = '#fff';
        } else {
            td.style.background = '#ff7f00'; // 橙色
            td.style.color = '#000';
        }
    }

    function updateStatusBg(select) {
        var td = select.parentElement;
        if (select.value === '未开始') {
            td.style.background = '#ff3b30'; // 红色
            td.style.color = '#fff';
        } else if (select.value === '进行中') {
            td.style.background = '#ffcc02'; // 黄色
            td.style.color = '#000';
        } else if (select.value === '已关闭') {
            td.style.background = '#34c759'; // 绿色
            td.style.color = '#fff';
        } else {
            td.style.background = ''; // 默认背景
            td.style.color = '';
        }
    }

    // 不良再现输入框显示/隐藏控制
    function toggleDefectReproductionInput() {
        const defectReproductionInput = document.getElementById('defectReproductionInput');
        const yesRadio = document.querySelector('input[name="defectReproduction"][value="yes"]');
        
        if (yesRadio && yesRadio.checked) {
            defectReproductionInput.style.display = 'block';
        } else {
            defectReproductionInput.style.display = 'none';
            defectReproductionInput.value = ''; // 清空内容
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.d3-temp-table-row').forEach(function(row) {
            var checkInput = row.querySelector('.table-check-count');
            var defectInput = row.querySelector('.table-defect-count');
            checkInput.addEventListener('input', function() { updateTableDefectRate(row); });
            defectInput.addEventListener('input', function() { updateTableDefectRate(row); });
            // 绑定库存下拉框变色
            var stockSelect = row.querySelector('.table-stock-select');
            if(stockSelect) {
                stockSelect.addEventListener('change', function() { updateStockBg(stockSelect); });
                updateStockBg(stockSelect); // 初始化
            }
        });
    });
    
    // 页面加载时初始化
    </script>
    <div id="copyright-info"
     style="width: calc(1500px - 40px); max-width: calc(100vw - 40px); margin: 32px auto 18px auto; background-color: #374757; border-radius: 8px; color: #fff; font-size: 1em; letter-spacing: 1px; text-align: center; padding: 12px; box-sizing: border-box;">
    版权所有 © 吴志明 | 电话&微信：13959240478 |
    <span style="font-family: 'KaiTi', '楷体', serif; font-weight: bold;">
        弘扬匠心、传递知识、为企业创造价值！
    </span>
</div>
<!-- 在</body>前插入悬浮操作栏（带图标） -->
<div class="floating-action-bar">
    <button onclick="document.getElementById('saveDataBtn').click()"><span class="icon"><i class="fa-solid fa-floppy-disk"></i></span>保存</button>
    <button class="clear" onclick="document.getElementById('clearDataBtn').click()"><span class="icon"><i class="fa-solid fa-trash"></i></span>清除</button>
    <button class="print" onclick="document.getElementById('printReportBtn').click()"><span class="icon"><i class="fa-solid fa-print"></i></span>打印</button>
    <button class="view" onclick="document.getElementById('viewReportBtn').click()"><span class="icon"><i class="fa-solid fa-eye"></i></span>查看</button>
</div>
<script>
// ======= 公司LOGO和公司名称功能 =======
function updateCompanyInfoDisplay() {
    const companyName = localStorage.getItem('company-name') || '公司名称';
    const companyLogo = localStorage.getItem('company-logo');
    const nameElement = document.getElementById('company-name');
    const logoElement = document.getElementById('company-logo');
    if (nameElement) nameElement.textContent = companyName;
    if (logoElement) {
        if (companyLogo) {
            logoElement.src = companyLogo;
            logoElement.style.display = 'block';
        } else {
            logoElement.style.display = 'none';
        }
    }
}
function editCompanyName() {
    const currentName = localStorage.getItem('company-name') || '公司名称';
    const newName = prompt('请输入公司名称：', currentName);
    if (newName !== null && newName.trim() !== '') {
        localStorage.setItem('company-name', newName.trim());
        updateCompanyInfoDisplay();
    }
}
function uploadCompanyLogo(input) {
    const file = input.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            localStorage.setItem('company-logo', e.target.result);
            updateCompanyInfoDisplay();
        };
        reader.readAsDataURL(file);
    }
}
document.addEventListener('DOMContentLoaded', function() {
    updateCompanyInfoDisplay();
});
</script>
</body>
</html>
