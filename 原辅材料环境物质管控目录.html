<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原辅材料环境物质管控目录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 14px; /* 调整字体大小 */
            background-color: #f4f4f4; /* 保持背景色 */
        }
        .container {
            width: 100%;
            max-width: 1800px;
            margin: 0 auto;
            background-color: #fff; /* 背景框颜色 */
            padding: 20px;
            border: 1px solid #ddd; /* 边框 */
            border-radius: 5px; /* 圆角 */
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* 阴影 */
            overflow-x: auto;
        }
        h1 {
            display: flex; /* 使用Flexbox布局 */
            justify-content: space-between; /* 标题和按钮左右对齐 */
            align-items: center; /* 垂直居中 */
            color: white; /* 字体改为白色 */
            margin-bottom: 20px;
            background-color: #203b55; /* 参考VDA 6.3的背景框颜色 */
            padding: 10px 20px; /* 内边距，左右增加 */
            border-radius: 5px; /* 圆角 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影 */
        }
        h1 span.title-text {
            flex-grow: 1; /* 标题文本占据剩余空间 */
            text-align: center; /* 标题文本居中 */
        }
        .header-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .warning-legend {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .warning-legend div {
            padding: 5px 10px;
            margin-left: 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .warning-legend .safe {
            background-color: #00b050; /* Green */
        }
        .warning-legend .warning-30 {
            background-color: #ffc000; /* Orange */
        }
        .warning-legend .urgent-7 {
            background-color: #ff0000; /* Red */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #a0a0a0;
            padding: 5px; /* 调整内边距 */
            text-align: center;
            background-color: #e0f2f7; /* Light blue for cells */
        }
        th {
            background-color: #a0d9e7; /* Darker blue for headers */
            font-weight: bold;
            white-space: nowrap;
        }
        .sub-header th {
            background-color: #c0e6f0; /* Slightly lighter blue for sub-headers */
        }
        .row-data td {
            background-color: transparent; /* Light blue for data rows */
        }
        td.green-cell {
            background-color: #00b050; /* Green for "安全" or "还有XX天到期" */
            color: black;
            font-weight: bold;
        }
        td.orange-cell {
            background-color: #ffc000; /* Orange for "预警催促" */
            color: black;
            font-weight: bold;
        }
        td.red-cell {
            background-color: #ff0000; /* Red for "紧急措施启动" */
            color: black;
            font-weight: bold;
        }
        input[type="text"],
        select {
            width: 100%;
            padding: 3px; /* 调整输入行高度 */
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        /* 限制物质调查和MSDS报告列的宽度 */
        table th:last-child,
        table th:nth-last-child(2),
        table td:last-child,
        table td:nth-last-child(2) {
            width: 80px;
            max-width: 80px;
        }

        /* 调整特定列的宽度 */
        /* 型号列 - 第5列 (扩大宽度) */
        table th:nth-child(5),
        table td:nth-child(5) {
            width: 120px;
            max-width: 120px;
        }

        /* 均质部位名称列 - 第6列 (缩小宽度) */
        table th:nth-child(6),
        table td:nth-child(6) {
            width: 80px;
            max-width: 80px;
        }

        /* 均质材料型号列 - 第7列 (缩小宽度) */
        table th:nth-child(7),
        table td:nth-child(7) {
            width: 80px;
            max-width: 80px;
        }

        .restricted-substance-select,
        .msds-report-select {
            width: 100%;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            <span class="title-text">原辅材料环境物质管控系统</span>
            <div class="header-buttons">
                <button onclick="saveData()" style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin-left: 10px;">保存数据</button>
                <button onclick="clearData()" style="padding: 8px 15px; background-color: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">清除数据</button>
                <button onclick="resetTable()" style="padding: 8px 15px; background-color: #9C27B0; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">重置表格</button>
                <button onclick="clearLocalStorage()" style="padding: 8px 15px; background-color: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">清除缓存</button>
            </div>
        </h1>
        <div class="header-info">
            <div id="update-date">※更新日期: </div>
            <div class="warning-legend">
                <div class="safe">≥40天 安全</div>
                <div class="warning-30">≥30天预警催促(1次/周)</div>
                <div class="urgent-7">≤7天紧急措施启动</div>
            </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th rowspan="3">序号</th>
                    <th rowspan="3">制造商</th>
                    <th rowspan="3">供应商</th>
                    <th rowspan="3">物料名称</th>
                    <th rowspan="3">型号</th>
                    <th rowspan="3">均质部位名称</th>
                    <th rowspan="3">均质材料型号</th>
                    <th colspan="10">环境物质测试报告</th>
                    <th rowspan="2">限制物质调查</th>
                    <th rowspan="2">MSDS报告</th>
                </tr>
                <tr>
                    <th colspan="5">RoHS</th>
                    <th colspan="5">Reach</th>
                </tr>
                <tr class="sub-header">
                    <th>测试机构</th>
                    <th>RoHS报告编号</th>
                    <th>发布日期</th>
                    <th>有效期至</th>
                    <th>过期判定</th>
                    <th>测试机构</th>
                    <th>报告编号</th>
                    <th>发布日期</th>
                    <th>有效期至</th>
                    <th>过期判定</th>
                    <th>有/否</th>
                    <th>有/否</th>
                </tr>
            </thead>
            <tbody>

            </tbody>
        </table>
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="addRow()" style="padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin-right: 10px;">添加行</button>
            <button onclick="deleteRow()" style="padding: 8px 15px; background-color: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin-right: 10px;">删除行</button>
            <button onclick="showAIAnalysis()" style="padding: 8px 15px; background-color: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">AI报告分析</button>
        </div>

        <!-- AI分析面板 -->
        <div id="aiAnalysisPanel" style="display: none; margin-top: 10px; padding: 8px; border: 1px solid #2196F3; border-radius: 5px; background-color: #f8f9fa;">
            <!-- 标题和控制行 -->
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; flex-wrap: wrap;">
                <span style="color: #2196F3; font-weight: bold; font-size: 16px; white-space: nowrap;">🤖 AI分析</span>

                <!-- 文件上传 -->
                <div style="display: flex; align-items: center; gap: 5px; flex: 1; min-width: 200px;">
                    <label style="font-size: 13px; color: #666; white-space: nowrap;">文件:</label>
                    <input type="file" id="reportUpload" accept=".pdf,.jpg,.png,.jpeg,.txt" multiple style="font-size: 12px; flex: 1; min-width: 180px; max-width: 300px;">
                </div>

                <!-- AI服务 -->
                <div style="display: flex; align-items: center; gap: 5px;">
                    <label style="font-size: 13px; color: #666; white-space: nowrap;">服务:</label>
                    <select id="aiProvider" style="width: 85px; padding: 3px; border: 1px solid #ccc; border-radius: 4px; font-size: 12px;" onchange="updateProviderInfo()">
                        <option value="local">本地</option>
                        <option value="deepseek" selected>DeepSeek</option>
                        <option value="openai">OpenAI</option>
                        <option value="claude">Claude</option>
                    </select>
                </div>

                <!-- API密钥 -->
                <div style="display: flex; align-items: center; gap: 5px;">
                    <label style="font-size: 13px; color: #666; white-space: nowrap;">密钥:</label>
                    <input type="password" id="apiKey" placeholder="本地OCR无需密钥" style="width: 120px; padding: 3px 5px; border: 1px solid #ccc; border-radius: 4px; font-size: 12px;">
                    <button onclick="saveApiKey()" style="padding: 3px 6px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; white-space: nowrap;">保存</button>
                    <button onclick="loadApiKey()" style="padding: 3px 6px; background-color: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; white-space: nowrap;">加载</button>
                </div>

                <!-- 操作按钮 -->
                <div style="display: flex; gap: 4px;">
                    <button onclick="analyzeReports()" style="padding: 5px 10px; background-color: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 13px; font-weight: bold; white-space: nowrap;">🚀 分析</button>
                    <button onclick="testAPI()" style="padding: 5px 8px; background-color: #9C27B0; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 13px; white-space: nowrap;">测试</button>
                    <button onclick="toggleHelp()" style="padding: 5px 8px; background-color: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 13px;">?</button>
                    <button onclick="hideAIAnalysis()" style="padding: 5px 8px; background-color: #666; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 13px;">×</button>
                </div>
            </div>

            <!-- 状态信息行 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; font-size: 12px; color: #666;">
                <div id="providerInfo">基于文本模式匹配，无需网络</div>
                <div style="font-size: 11px;">
                    <button onclick="testRoHSAnalysis()" style="padding: 2px 6px; background: #4CAF50; color: white; border: none; border-radius: 3px; font-size: 10px; margin-right: 5px; cursor: pointer;">测试RoHS</button>
                    <button onclick="testREACHAnalysis()" style="padding: 2px 6px; background: #2196F3; color: white; border: none; border-radius: 3px; font-size: 10px; margin-right: 10px; cursor: pointer;">测试REACH</button>
                    <a href="https://platform.deepseek.com" target="_blank" style="color: #2196F3; text-decoration: none;">DeepSeek</a> |
                    <a href="https://platform.openai.com" target="_blank" style="color: #2196F3; text-decoration: none;">OpenAI</a>
                </div>
            </div>

            <!-- 帮助信息（可折叠） -->
            <div id="helpInfo" style="display: none; padding: 6px; background-color: #e3f2fd; border-radius: 5px; font-size: 12px; margin-bottom: 8px;">
                <strong>💡 说明：</strong> 本地OCR(文本匹配) | DeepSeek(文本分析) | OpenAI(图片识别) | Claude(演示)
            </div>

            <!-- 分析结果 -->
            <div id="analysisResults" style="padding: 10px; background-color: white; border: 1px solid #ddd; border-radius: 5px; min-height: 50px; font-size: 14px;">
                <div style="color: #999; text-align: center; font-style: italic;">分析结果将显示在这里...</div>
            </div>
        </div>

    </div>
    <script>
        // 全球各国环境物质管控法规数据库
        const globalRegulations = {
            '中国': [
                {
                    name: '中国汽车禁用物质要求 (GB/T30512-2014)',
                    code: 'GB/T30512-2014',
                    type: 'ELV',
                    substances: ['铅', '镉', '汞', '六价铬'],
                    priority: 'high'
                }
            ],
            '欧盟': [
                {
                    name: '(EC)No 1907/2006 (REACH) 及其修正案',
                    code: 'REACH',
                    type: 'REACH',
                    substances: ['SVHC高关注物质'],
                    priority: 'high'
                },
                {
                    name: '注册(EC) N°528/Res2012(BPR)',
                    code: 'BPR',
                    type: 'BPR',
                    substances: ['杀生物剂'],
                    priority: 'high',
                    highlight: true
                },
                {
                    name: '指令2000/53/EC (ELV) - 欧洲联盟(欧盟)制定了《报废车辆指令》',
                    code: 'ELV',
                    type: 'ELV',
                    substances: ['铅', '镉', '汞', '六价铬'],
                    priority: 'high'
                },
                {
                    name: '指令2005/64/EC - 欧洲联盟(欧盟)关于机动车辆再用性、可回收性及可回收性的型式认可的指令',
                    code: 'ELV-Recyclability',
                    type: 'ELV',
                    substances: ['可回收材料要求'],
                    priority: 'medium'
                },
                {
                    name: '指令2011/65/EU (RoHS) - RoHS(有害物质限制)指令限制在电气和电子产品使用特定的有害物质',
                    code: 'RoHS',
                    type: 'RoHS',
                    substances: ['铅', '镉', '汞', '六价铬', '多溴联苯', '多溴二苯醚'],
                    priority: 'high'
                },
                {
                    name: 'Reg. (EC)No 1005/2009 - ("消耗臭氧层(D)禁止和管制消耗臭氧层物质的生产/使用',
                    code: 'ODS',
                    type: 'ODS',
                    substances: ['消耗臭氧层物质'],
                    priority: 'medium'
                },
                {
                    name: 'Reg. (EU)No.517/2014 - 欧洲联盟是一项旨在减少氟化气体(F-gas)对环境的影响法规',
                    code: 'F-Gas',
                    type: 'F-Gas',
                    substances: ['氟化气体'],
                    priority: 'medium'
                },
                {
                    name: 'Reg.(欧盟)第2019/01021号(持久性有机污染物-POP)',
                    code: 'POP',
                    type: 'POP',
                    substances: ['持久性有机污染物'],
                    priority: 'medium'
                }
            ],
            '日本': [
                {
                    name: '日本化学物质控制法',
                    code: 'CSCL',
                    type: 'Chemical Control',
                    substances: ['化学物质'],
                    priority: 'medium'
                }
            ],
            '美国': [
                {
                    name: '美国环保署第一类消耗臭氧物质',
                    code: 'EPA-ODS',
                    type: 'ODS',
                    substances: ['消耗臭氧层物质'],
                    priority: 'medium'
                },
                {
                    name: '《有毒物质控制法》(TSCA)中的"重要新用途规则"(SNUR)',
                    code: 'TSCA-SNUR',
                    type: 'TSCA',
                    substances: ['有毒物质'],
                    priority: 'high',
                    highlight: true
                },
                {
                    name: '加州参议院法案(SB) 346',
                    code: 'CA-SB346',
                    type: 'State Law',
                    substances: ['特定化学物质'],
                    priority: 'medium',
                    highlight: true
                },
                {
                    name: '华盛顿替代委员会法案6557',
                    code: 'WA-6557',
                    type: 'State Law',
                    substances: ['替代物质'],
                    priority: 'medium',
                    highlight: true
                },
                {
                    name: '缅因州立法文件(LD)) 151503',
                    code: 'ME-LD151503',
                    type: 'State Law',
                    substances: ['特定物质'],
                    priority: 'low'
                }
            ]
        };

        // 法规合规性分析函数
        function analyzeRegulationCompliance(result) {
            const complianceAnalysis = {
                coveredRegulations: [],
                missingRegulations: [],
                riskAssessment: 'low',
                recommendations: []
            };

            // 根据报告类型确定涵盖的法规
            const reportType = result.reportType.toLowerCase();
            const testItems = result.testItems.toLowerCase();
            const testResults = result.testResults.toLowerCase();

            // 分析每个国家/地区的法规合规性
            Object.keys(globalRegulations).forEach(region => {
                globalRegulations[region].forEach(regulation => {
                    const isRelevant = checkRegulationRelevance(regulation, reportType, testItems, testResults);

                    // 根据法规类型分别处理，无论报告类型如何，都先进行分类
                    let targetCompliance = complianceAnalysis;
                    if (regulation.type.includes('RoHS')) {
                        targetCompliance = complianceAnalysis.rohsCompliance;
                    } else if (regulation.type.includes('Reach') || regulation.type.includes('SVHC')) {
                        targetCompliance = complianceAnalysis.reachCompliance;
                    }

                    if (isRelevant.covered) {
                        targetCompliance.coveredRegulations.push({
                            region: region,
                            regulation: regulation,
                            status: 'covered',
                            confidence: isRelevant.confidence
                        });
                        // 全局合规性也添加
                        complianceAnalysis.coveredRegulations.push({
                            region: region,
                            regulation: regulation,
                            status: 'covered',
                            confidence: isRelevant.confidence
                        });
                    } else if (isRelevant.applicable) {
                        targetCompliance.missingRegulations.push({
                            region: region,
                            regulation: regulation,
                            status: 'missing',
                            risk: regulation.priority,
                            reason: isRelevant.reason
                        });
                        // 全局合规性也添加
                        complianceAnalysis.missingRegulations.push({
                            region: region,
                            regulation: regulation,
                            status: 'missing',
                            risk: regulation.priority,
                            reason: isRelevant.reason
                        });
                    }
                });
            });

            // 评估RoHS风险等级
            const rohsHighRiskMissing = complianceAnalysis.rohsCompliance.missingRegulations.filter(r => r.risk === 'high').length;
            const rohsMediumRiskMissing = complianceAnalysis.rohsCompliance.missingRegulations.filter(r => r.risk === 'medium').length;

            if (rohsHighRiskMissing > 0) {
                complianceAnalysis.rohsCompliance.riskAssessment = 'high';
                complianceAnalysis.rohsCompliance.recommendations.push('⚠️ RoHS高风险法规缺失，建议补充相关测试');
            } else if (rohsMediumRiskMissing > 2) {
                complianceAnalysis.rohsCompliance.riskAssessment = 'medium';
                complianceAnalysis.rohsCompliance.recommendations.push('⚠️ RoHS多项中等风险法规缺失，建议评估补充测试');
            } else {
                complianceAnalysis.rohsCompliance.riskAssessment = 'low';
                complianceAnalysis.rohsCompliance.recommendations.push('✅ RoHS当前测试覆盖度较好');
            }

            // 评估REACH风险等级
            const reachHighRiskMissing = complianceAnalysis.reachCompliance.missingRegulations.filter(r => r.risk === 'high').length;
            const reachMediumRiskMissing = complianceAnalysis.reachCompliance.missingRegulations.filter(r => r.risk === 'medium').length;

            if (reachHighRiskMissing > 0) {
                complianceAnalysis.reachCompliance.riskAssessment = 'high';
                complianceAnalysis.reachCompliance.recommendations.push('⚠️ REACH高风险法规缺失，建议补充相关测试');
            } else if (reachMediumRiskMissing > 2) {
                complianceAnalysis.reachCompliance.riskAssessment = 'medium';
                complianceAnalysis.reachCompliance.recommendations.push('⚠️ REACH多项中等风险法规缺失，建议评估补充测试');
            } else {
                complianceAnalysis.reachCompliance.riskAssessment = 'low';
                complianceAnalysis.reachCompliance.recommendations.push('✅ REACH当前测试覆盖度较好');
            }

            // 评估整体风险等级 (基于所有法规)
            const globalHighRiskMissing = complianceAnalysis.missingRegulations.filter(r => r.risk === 'high').length;
            const globalMediumRiskMissing = complianceAnalysis.missingRegulations.filter(r => r.risk === 'medium').length;

            if (globalHighRiskMissing > 0) {
                complianceAnalysis.riskAssessment = 'high';
                complianceAnalysis.recommendations.push('⚠️ 发现高风险法规缺失，建议补充相关测试');
            } else if (globalMediumRiskMissing > 2) {
                complianceAnalysis.riskAssessment = 'medium';
                complianceAnalysis.recommendations.push('⚠️ 发现多项中等风险法规缺失，建议评估补充测试');
            } else {
                complianceAnalysis.riskAssessment = 'low';
                complianceAnalysis.recommendations.push('✅ 当前测试覆盖度较好');
            }

            return complianceAnalysis;
        }

        // 检查法规相关性
        function checkRegulationRelevance(regulation, reportType, testItems, testResults) {
            const result = {
                covered: false,
                applicable: true,
                confidence: 0,
                reason: ''
            };

            // 检查报告类型匹配
            // 根据报告类型智能忽略不相关的法规分析
            // 如果是RoHS报告，且法规类型是REACH/SVHC，则不适用
            if (reportType.includes('rohs') && (regulation.type.includes('Reach') || regulation.type.includes('SVHC'))) {
                result.applicable = false;
                result.reason = `报告类型不匹配：此为RoHS报告，${regulation.type}法规不适用`;
                return result;
            }
            // 如果是REACH/SVHC报告，且法规类型是RoHS，则不适用
            if ((reportType.includes('reach') || reportType.includes('svhc')) && regulation.type.includes('RoHS')) {
                result.applicable = false;
                result.reason = `报告类型不匹配：此为REACH/SVHC报告，${regulation.type}法规不适用`;
                return result;
            }

            if (regulation.type.toLowerCase() === reportType ||
                (regulation.code && reportType.includes(regulation.code.toLowerCase()))) {

                // 检查物质覆盖度
                let coveredSubstances = 0;
                let totalSubstances = regulation.substances.length;

                regulation.substances.forEach(substance => {
                    if (testItems.includes(substance.toLowerCase()) ||
                        testResults.includes(substance.toLowerCase())) {
                        coveredSubstances++;
                    }
                });

                if (coveredSubstances > 0) {
                    result.covered = true;
                    result.confidence = (coveredSubstances / totalSubstances) * 100;
                } else {
                    result.reason = `缺少${regulation.substances.join('、')}等物质的测试`;
                }
            } else {
                // 检查是否适用于当前产品类型
                if (regulation.type === 'RoHS' && !reportType.includes('电')) {
                    result.applicable = false;
                    result.reason = '该法规不适用于当前产品类型';
                } else if (regulation.type === 'ELV' && !reportType.includes('汽车')) {
                    result.applicable = false;
                    result.reason = '该法规不适用于当前产品类型';
                } else {
                    result.reason = `报告类型不匹配：需要${regulation.type}测试`;
                }
            }

            return result;
        }

        function loadData() {
            try {
                const serializedData = localStorage.getItem('materialControlData');
                if (serializedData) {
                    const tableData = JSON.parse(serializedData);
                    const tableBody = document.querySelector('table tbody');
                    tableBody.innerHTML = ''; // Clear existing rows before loading

                    tableData.forEach(rowData => {
                        const newRow = document.createElement('tr');
                        newRow.className = 'row-data';
                        newRow.innerHTML = `
                            <td contenteditable="true">${rowData.col0}</td>
                            <td contenteditable="true">${rowData.col1}</td>
                            <td contenteditable="true">${rowData.col2}</td>
                            <td contenteditable="true">${rowData.col3}</td>
                            <td contenteditable="true">${rowData.col4}</td>
                            <td contenteditable="true">${rowData.col5}</td>
                            <td contenteditable="true">${rowData.col6}</td>
                            <td contenteditable="true">${rowData.col7}</td>
                            <td contenteditable="true">${rowData.col8}</td>
                            <td contenteditable="true" id="rohs-release-date-${rowData.col0}">${rowData.col9}</td>
                            <td id="rohs-expiry-date-${rowData.col0}">${rowData.col10}</td>
                            <td class="" id="rohs-judgment-${rowData.col0}">${rowData.col11}</td>
                            <td contenteditable="true">${rowData.col12}</td>
                            <td contenteditable="true">${rowData.col13}</td>
                            <td contenteditable="true" id="reach-release-date-${rowData.col0}">${rowData.col14}</td>
                            <td id="reach-expiry-date-${rowData.col0}">${rowData.col15}</td>
                            <td class="" id="reach-judgment-${rowData.col0}">${rowData.col16}</td>
                            <td>
                                <select class="restricted-substance-select">
                                    <option value=""></option>
                                    <option value="有">有</option>
                                    <option value="否">否</option>
                                </select>
                            </td>
                            <td>
                                <select class="msds-report-select">
                                    <option value=""></option>
                                    <option value="有">有</option>
                                    <option value="否">否</option>
                                </select>
                            </td>
                        `;
                        tableBody.appendChild(newRow);

                        // Set select values
                        newRow.querySelector('.restricted-substance-select').value = rowData.col17;
                        newRow.querySelector('.msds-report-select').value = rowData.col18;

                        // Add event listeners for date fields
                        newRow.querySelectorAll('[id^="rohs-release-date"], [id^="reach-release-date"]').forEach(input => {
                            input.addEventListener('input', (event) => {
                                const idParts = event.target.id.split('-');
                                const type = idParts[0]; // 'rohs' or 'reach'
                                const rowNum = idParts[idParts.length - 1];
                                updateJudgment(`${type}-release-date-${rowNum}`, `${type}-expiry-date-${rowNum}`, `${type}-judgment-${rowNum}`);
                            });
                        });

                        // Initial judgment update for loaded rows
                        updateJudgment(`rohs-release-date-${rowData.col0}`, `rohs-expiry-date-${rowData.col0}`, `rohs-judgment-${rowData.col0}`);
                        updateJudgment(`reach-release-date-${rowData.col0}`, `reach-expiry-date-${rowData.col0}`, `reach-judgment-${rowData.col0}`);
                    });
                    console.log(`已加载 ${tableData.length} 行数据。`);
                } else {
                    addRow(); // Add an empty row if no data is found
                }
            } catch (error) {
                console.error('加载数据时出错:', error);
                alert('加载数据失败，请检查控制台错误信息。');
                addRow(); // Add an empty row on error
            }
        }

        function saveData() {
            try {
                const tableBody = document.querySelector('table tbody');
                const rows = Array.from(tableBody.rows);
                const tableData = [];

                rows.forEach((row, rowIndex) => {
                    const rowData = {};
                    Array.from(row.cells).forEach((cell, cellIndex) => {
                        let cellValue = '';

                        // 处理不同类型的单元格
                        const select = cell.querySelector('select');
                        if (select) {
                            cellValue = select.value;
                        } else if (cell.id && (cell.id.includes('release-date') || cell.id.includes('expiry-date') || cell.id.includes('judgment'))) {
                            cellValue = cell.textContent.trim();
                        } else {
                            cellValue = cell.textContent.trim();
                        }

                        rowData[`col${cellIndex}`] = cellValue;
                    });
                    tableData.push(rowData);
                });

                // 保存到localStorage
                localStorage.setItem('materialControlData', JSON.stringify(tableData));
                alert(`数据已保存！共保存 ${tableData.length} 行数据。`);
            } catch (error) {
                console.error('保存数据时出错:', error);
                alert('保存数据失败，请检查控制台错误信息。');
            }
        }

        function clearData() {
            if(confirm('确定要清除所有数据吗？这将删除表格中的所有内容和本地保存的数据。')) {
                // 清除表格数据
                const tableBody = document.querySelector('table tbody');
                tableBody.innerHTML = '';

                // 清除localStorage中的数据
                localStorage.removeItem('materialControlData');

                // 添加一行空行
                addRow();
                alert('数据已清除！');
            }
        }





        function addRow() {
            const tableBody = document.querySelector('table tbody');
            const newRowNum = tableBody.children.length + 1;

            const newRow = document.createElement('tr');
            newRow.className = 'row-data';
            newRow.innerHTML = `
                <td contenteditable="true">${newRowNum}</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true" id="rohs-release-date-${newRowNum}"></td>
                <td id="rohs-expiry-date-${newRowNum}"></td>
                <td class="" id="rohs-judgment-${newRowNum}"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true" id="reach-release-date-${newRowNum}"></td>
                <td id="reach-expiry-date-${newRowNum}"></td>
                <td class="" id="reach-judgment-${newRowNum}"></td>
                <td>
                    <select class="restricted-substance-select">
                        <option value=""></option>
                        <option value="有">有</option>
                        <option value="否">否</option>
                    </select>
                </td>
                <td>
                    <select class="msds-report-select">
                        <option value=""></option>
                        <option value="是">是</option>
                        <option value="否">否</option>
                    </select>
                </td>
            `;

            console.log(`添加新行 ${newRowNum}，ID示例: rohs-release-date-${newRowNum}, reach-release-date-${newRowNum}`);

            tableBody.appendChild(newRow);

            // Add event listeners for the new row's date input fields
            newRow.querySelectorAll('[id^="rohs-release-date"], [id^="reach-release-date"]').forEach(input => {
                input.addEventListener('input', (event) => {
                    const idParts = event.target.id.split('-');
                    const type = idParts[0]; // 'rohs' or 'reach'
                    const rowNum = idParts[idParts.length - 1];
                    updateJudgment(`${type}-release-date-${rowNum}`, `${type}-expiry-date-${rowNum}`, `${type}-judgment-${rowNum}`);
                });
            });

            // Add event listeners for select elements
            newRow.querySelectorAll('.restricted-substance-select, .msds-report-select').forEach(select => {
                select.addEventListener('change', (event) => {
                    // No specific action needed on change for now, but good to have the listener
                    console.log(`${event.target.className} changed to: ${event.target.value}`);
                });
            });

            // Initial judgment update for the new row (empty dates will result in empty judgment)
            updateJudgment(`rohs-release-date-${newRowNum}`, `rohs-expiry-date-${newRowNum}`, `rohs-judgment-${newRowNum}`);
            updateJudgment(`reach-release-date-${newRowNum}`, `reach-expiry-date-${newRowNum}`, `reach-judgment-${newRowNum}`);

        }

        function deleteRow() {
            const tableBody = document.querySelector('table tbody');
            if (tableBody.rows.length > 1) {
                tableBody.removeChild(tableBody.lastChild);
            } else {
                alert('至少保留一行数据。');
            }
        }

        function showAIAnalysis() {
            document.getElementById('aiAnalysisPanel').style.display = 'block';
        }

        function hideAIAnalysis() {
            document.getElementById('aiAnalysisPanel').style.display = 'none';
        }

        async function testAPI() {
            const aiProvider = document.getElementById('aiProvider').value;
            const apiKey = document.getElementById('apiKey').value;
            const resultsDiv = document.getElementById('analysisResults');

            if (aiProvider === 'local') {
                alert('本地OCR无需测试API连接');
                return;
            }

            if (!apiKey || apiKey.trim() === '') {
                alert('请先输入API密钥');
                return;
            }

            resultsDiv.innerHTML = `
                <div style="text-align: center; color: #9C27B0;">
                    🔄 正在测试${aiProvider.toUpperCase()} API连接...<br>
                    <small>请稍候...</small>
                </div>`;

            try {
                if (aiProvider === 'deepseek') {
                    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: JSON.stringify({
                            model: "deepseek-chat",
                            messages: [
                                {
                                    role: "user",
                                    content: "测试连接，请回复'连接成功'"
                                }
                            ],
                            max_tokens: 50,
                            temperature: 0.1
                        })
                    });

                    console.log('API测试响应状态:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('API测试响应数据:', data);
                        resultsDiv.innerHTML = `
                            <div style="color: #4CAF50; padding: 8px; background-color: #e8f5e8; border-radius: 4px;">
                                ✅ <strong>DeepSeek API连接成功！</strong><br>
                                <small>响应: ${data.choices[0].message.content}</small>
                            </div>`;
                    } else {
                        const errorText = await response.text();
                        console.error('API测试失败:', errorText);
                        resultsDiv.innerHTML = `
                            <div style="color: #f44336; padding: 8px; background-color: #ffebee; border-radius: 4px;">
                                ❌ <strong>API连接失败</strong><br>
                                状态码: ${response.status}<br>
                                错误: ${errorText}<br>
                                <small>请检查API密钥是否正确</small>
                            </div>`;
                    }
                } else {
                    resultsDiv.innerHTML = `
                        <div style="color: #FF9800; padding: 8px; background-color: #fff3e0; border-radius: 4px;">
                            ⚠️ <strong>${aiProvider.toUpperCase()} API测试</strong><br>
                            <small>暂未实现此API的测试功能</small>
                        </div>`;
                }
            } catch (error) {
                console.error('API测试异常:', error);
                resultsDiv.innerHTML = `
                    <div style="color: #f44336; padding: 8px; background-color: #ffebee; border-radius: 4px;">
                        ❌ <strong>API测试失败</strong><br>
                        错误: ${error.message}<br>
                        <small>请检查网络连接和API密钥</small>
                    </div>`;
            }
        }

        function updateProviderInfo() {
            const provider = document.getElementById('aiProvider').value;
            const infoDiv = document.getElementById('providerInfo');
            const apiKeyInput = document.getElementById('apiKey');

            const info = {
                'local': '基于文本模式匹配，无需网络',
                'deepseek': '🚀 DeepSeek智能分析，支持PDF文本提取',
                'openai': 'OpenAI GPT-4V图片识别（需要API密钥）',
                'claude': 'Claude AI联网分析（演示模式）'
            };

            infoDiv.textContent = info[provider] || '';

            // 根据选择的服务更新API密钥占位符
            const placeholders = {
                'local': '本地OCR无需密钥',
                'deepseek': '输入DeepSeek API密钥（sk-开头）',
                'openai': '输入OpenAI API密钥（sk-开头）',
                'claude': '演示模式无需密钥'
            };

            apiKeyInput.placeholder = placeholders[provider] || '';

            // 自动加载对应的API密钥
            loadApiKey();
        }

        function saveApiKey() {
            const provider = document.getElementById('aiProvider').value;
            const apiKey = document.getElementById('apiKey').value;

            if (!apiKey.trim()) {
                alert('请输入API密钥');
                return;
            }

            localStorage.setItem(`apiKey_${provider}`, apiKey);

            // 显示保存成功提示
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '✓';
            button.style.backgroundColor = '#4CAF50';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.backgroundColor = '#4CAF50';
            }, 1000);
        }

        function loadApiKey() {
            const provider = document.getElementById('aiProvider').value;
            const savedKey = localStorage.getItem(`apiKey_${provider}`);

            if (savedKey) {
                document.getElementById('apiKey').value = savedKey;
            } else {
                document.getElementById('apiKey').value = '';
            }
        }

        function toggleHelp() {
            const helpDiv = document.getElementById('helpInfo');
            const button = event.target;

            if (helpDiv.style.display === 'none') {
                helpDiv.style.display = 'block';
                button.textContent = '❌';
            } else {
                helpDiv.style.display = 'none';
                button.textContent = '❓';
            }
        }

        async function analyzeReports() {
            const fileInput = document.getElementById('reportUpload');
            const aiProvider = document.getElementById('aiProvider').value;
            const apiKey = document.getElementById('apiKey').value;
            const resultsDiv = document.getElementById('analysisResults');

            if (!fileInput.files.length) {
                alert('请先选择要分析的报告文件');
                return;
            }

            if (!apiKey && aiProvider !== 'local') {
                let providerName = '';
                switch(aiProvider) {
                    case 'deepseek': providerName = 'DeepSeek'; break;
                    case 'openai': providerName = 'OpenAI'; break;
                    case 'claude': providerName = 'Claude'; break;
                }
                alert(`请输入${providerName} API密钥\n\n获取方式：\n• DeepSeek: https://platform.deepseek.com\n• OpenAI: https://platform.openai.com`);
                return;
            }

            // 验证文件类型
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
            const invalidFiles = Array.from(fileInput.files).filter(file =>
                !validTypes.includes(file.type) && !file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|pdf|txt)$/));

            if (invalidFiles.length > 0) {
                alert(`不支持的文件格式: ${invalidFiles.map(f => f.name).join(', ')}\n\n支持的格式: JPG, PNG, PDF, TXT`);
                return;
            }

            resultsDiv.innerHTML = `
                <div style="text-align: center; color: #2196F3;">
                    🔄 正在使用${aiProvider === 'local' ? '本地OCR' : aiProvider.toUpperCase()}分析报告，请稍候...<br>
                    <small>分析${fileInput.files.length}个文件中...</small>
                </div>`;

            try {
                const files = Array.from(fileInput.files);
                const results = [];

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    resultsDiv.innerHTML = `
                        <div style="text-align: center; color: #2196F3;">
                            🔄 正在分析第${i + 1}/${files.length}个文件: ${file.name}<br>
                            <small>请稍候...</small>
                        </div>`;

                    const result = await analyzeReport(file, aiProvider, apiKey);
                    results.push(result);
                }

                displayAnalysisResults(results);
            } catch (error) {
                console.error('分析错误:', error);
                resultsDiv.innerHTML = `
                    <div style="color: red; padding: 10px; background-color: #ffebee; border-radius: 5px;">
                        ❌ <strong>分析失败</strong><br>
                        错误信息: ${error.message}<br><br>
                        <small>
                        可能的解决方案:<br>
                        • 检查API密钥是否正确<br>
                        • 确认网络连接正常<br>
                        • 尝试使用"本地OCR"模式<br>
                        • 检查文件格式是否支持
                        </small>
                    </div>`;
            }
        }

        async function analyzeReport(file, provider, apiKey) {
            console.log(`开始分析文件: ${file.name}, 大小: ${file.size} bytes, 类型: ${file.type}, 提供商: ${provider}`);

            try {
                let result;
                if (provider === 'local') {
                    result = await localOCRAnalysis(file);
                } else if (provider === 'deepseek') {
                    result = await deepseekAnalysis(file, apiKey);
                } else if (provider === 'openai') {
                    result = await openaiAnalysis(file, apiKey);
                } else if (provider === 'claude') {
                    result = await claudeAnalysis(file, apiKey);
                } else {
                    throw new Error(`不支持的AI提供商: ${provider}`);
                }

                console.log('分析结果:', result);
                return result;
            } catch (error) {
                console.error('分析过程中出错:', error);
                throw error;
            }
        }

        async function localOCRAnalysis(file) {
            console.log('开始本地OCR分析...');
            // 模拟分析时间
            await new Promise(resolve => setTimeout(resolve, 1000));

            const fileName = file.name.toLowerCase();
            let fileContent = '';

            try {
                fileContent = await readFileAsText(file);
                console.log('本地分析读取到文件内容长度:', fileContent.length);
            } catch (error) {
                console.warn('本地分析无法读取文件内容，仅基于文件名分析:', error);
                fileContent = fileName; // 如果无法读取内容，至少基于文件名分析
            }

            // 改进的文本模式匹配
            const patterns = {
                rohs: /rohs|有害物质|限制物质|禁用物质|elv/i,
                reach: /reach|高关注物质|svhc|候选清单/i,
                testInstitution: /(SGS|TUV|CTI|BV|Intertek|华测|谱尼|深圳|广州|上海|北京|苏州|东莞|宁波|青岛|天津|检测|实验室|测试|集团|有限公司)/i,
                reportNumber: /[A-Z]{2,}[\d\w]{6,}|[\d]{8,}|[A-Z]\d{7,}/g,
                date: /20\d{2}[\/\-年]\d{1,2}[\/\-月]\d{1,2}[日]?/g,
                compliance: /(符合|通过|合格|pass|满足|未检出|检出限以下)/i,
                nonCompliance: /(不符合|失败|不合格|fail|超标|检出)/i,
                clientCompany: /(委托单位|客户|申请人|送样单位)[：:\s]*([^\n\r，。；;]{3,50})/i,
                sampleName: /(样品名称|产品名称|测试样品)[：:]\s*([^\n\r]+)/i
            };

            // 分析报告类型
            let reportType = '未识别';
            if (patterns.rohs.test(fileName) || patterns.rohs.test(fileContent)) {
                reportType = 'RoHS报告';
            } else if (patterns.reach.test(fileName) || patterns.reach.test(fileContent)) {
                reportType = 'Reach报告';
            }

            // 提取测试机构 - 改进版
            let testInstitution = '未识别';

            // 优先匹配完整的机构名称
            const fullInstitutionPatterns = [
                /SGS[^\\n\\r]*(?:Standards|Technical|Services|Co|Ltd|Branch)[^\\n\\r]*/i,
                /TUV[^\\n\\r]*(?:南德|莱茵|北德)[^\\n\\r]*/i,
                /华测检测[^\\n\\r]*有限公司/i,
                /谱尼测试[^\\n\\r]*有限公司/i,
                /Intertek[^\\n\\r]*(?:天祥|集团)[^\\n\\r]*/i,
                /BV[^\\n\\r]*(?:必维|检测)[^\\n\\r]*/i,
                /CTI[^\\n\\r]*(?:华测|检测)[^\\n\\r]*/i
            ];

            for (const pattern of fullInstitutionPatterns) {
                const match = fileContent.match(pattern);
                if (match) {
                    testInstitution = match[0].trim();
                    break;
                }
            }

            // 如果没有匹配到完整名称，使用原有逻辑
            if (testInstitution === '未识别') {
                const institutionMatch = fileContent.match(patterns.testInstitution);
                if (institutionMatch) {
                    testInstitution = institutionMatch[0];
                    // 如果匹配到地名，尝试找完整机构名
                    if (['深圳', '广州', '上海', '北京', '苏州', '东莞', '宁波', '青岛', '天津'].includes(testInstitution)) {
                        const fullMatch = fileContent.match(new RegExp(testInstitution + '[^\\n\\r]{0,30}(检测|实验室|测试|集团|有限公司)', 'i'));
                        if (fullMatch) testInstitution = fullMatch[0];
                    }
                }
            }

            // 提取报告编号
            const numberMatches = fileContent.match(patterns.reportNumber);
            let reportNumber = '未识别';
            if (numberMatches && numberMatches.length > 0) {
                // 选择最可能的报告编号（通常是较长的）
                reportNumber = numberMatches.sort((a, b) => b.length - a.length)[0];
            }

            // 提取日期
            const dateMatches = fileContent.match(patterns.date);
            let releaseDate = '未识别';
            if (dateMatches && dateMatches.length > 0) {
                releaseDate = dateMatches[0].replace(/年|月|日/g, '/').replace(/\-/g, '/');
            }

            // 判断符合性 - 改进版
            let compliance = '未识别';

            // 首先检查明确的符合性关键词
            if (patterns.compliance.test(fileContent)) {
                compliance = '符合';
            } else if (patterns.nonCompliance.test(fileContent)) {
                compliance = '不符合';
            } else {
                // 如果没有明确关键词，基于测试结果进行智能判定
                const testResultPatterns = {
                    allND: /(?:铅|镉|汞|六价铬|多溴联苯|多溴二苯醚).*?N\.D\./gi,
                    anyExceeded: /(?:铅|镉|汞|六价铬|多溴联苯|多溴二苯醚).*?(?:\d+(?:\.\d+)?)\s*mg\/kg/gi,
                    passKeywords: /(?:PASS|通过|合格|满足要求)/i,
                    failKeywords: /(?:FAIL|失败|不合格|超标)/i
                };

                // 检查是否有明确的PASS/FAIL
                if (testResultPatterns.passKeywords.test(fileContent)) {
                    compliance = '符合';
                } else if (testResultPatterns.failKeywords.test(fileContent)) {
                    compliance = '不符合';
                } else {
                    // 检查测试结果
                    const ndMatches = fileContent.match(testResultPatterns.allND);
                    const numericMatches = fileContent.match(testResultPatterns.anyExceeded);

                    if (ndMatches && ndMatches.length >= 4) {
                        // 如果有4个或更多的N.D.结果，很可能符合
                        compliance = '符合';
                    } else if (numericMatches && numericMatches.length > 0) {
                        // 如果有具体数值，需要进一步分析
                        compliance = '需要人工确认';
                    }
                }
            }

            // 提取委托单位
            const clientMatch = fileContent.match(patterns.clientCompany);
            let clientCompany = '未识别';
            if (clientMatch && clientMatch[2]) {
                let rawClient = clientMatch[2].trim();

                // 过滤掉常见的城市名和无关信息
                const cityNames = ['深圳', '广州', '上海', '北京', '苏州', '东莞', '宁波', '青岛', '天津', '杭州', '成都', '武汉', '西安', '南京', '重庆'];
                const invalidPatterns = [
                    /^(深圳|广州|上海|北京|苏州|东莞|宁波|青岛|天津|杭州|成都|武汉|西安|南京|重庆)$/i,
                    /^(市|区|县|省)$/i,
                    /^\d+$/,
                    /^[A-Z]{1,3}$/,
                    /^(测试|检测|实验室|机构)$/i
                ];

                // 检查是否为无效的委托单位信息
                const isInvalid = invalidPatterns.some(pattern => pattern.test(rawClient)) ||
                                cityNames.some(city => rawClient === city);

                if (!isInvalid && rawClient.length >= 3) {
                    clientCompany = rawClient;
                    console.log(`✅ 提取到委托单位: "${clientCompany}"`);
                } else {
                    console.log(`⚠️ 过滤掉无效的委托单位信息: "${rawClient}"`);
                    clientCompany = '未识别';
                }
            }

            // 提取样品名称
            const sampleMatch = fileContent.match(patterns.sampleName);
            let sampleName = '未识别';
            if (sampleMatch && sampleMatch[2]) {
                sampleName = sampleMatch[2].trim();
            }

            const result = {
                fileName: file.name,
                reportType: reportType,
                testInstitution: testInstitution,
                reportNumber: reportNumber,
                releaseDate: releaseDate,
                expiryDate: '未识别',
                compliance: compliance,
                clientCompany: clientCompany,
                sampleName: sampleName,
                testStandard: '未识别',
                testItems: '未识别',
                testResults: '未识别',
                confidence: 0.7,
                note: '基于文本模式匹配的本地分析'
            };

            console.log('本地分析结果:', result);
            return result;
        }

        async function readFileAsText(file) {
            console.log(`尝试读取文件: ${file.name}, 类型: ${file.type}, 大小: ${file.size} bytes`);

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const content = e.target.result;
                    console.log(`文件读取成功，内容长度: ${content ? content.length : 0} 字符`);
                    if (content && content.length > 0) {
                        console.log('文件内容预览:', content.substring(0, 300) + '...');
                        resolve(content);
                    } else {
                        console.warn('文件内容为空或无法读取');
                        reject(new Error('文件内容为空或无法读取'));
                    }
                };
                reader.onerror = function(e) {
                    console.error('文件读取失败:', e);
                    reject(new Error('文件读取失败: ' + e.message));
                };

                // 根据文件类型选择读取方式
                if (file.type === 'application/pdf') {
                    console.log('检测到PDF文件，尝试读取为文本（注意：图片型PDF可能无法正确读取）');
                    reader.readAsText(file, 'UTF-8');
                } else if (file.type.startsWith('image/')) {
                    console.log('检测到图片文件，读取为DataURL');
                    reader.readAsDataURL(file);
                } else {
                    console.log('读取为文本文件');
                    reader.readAsText(file, 'UTF-8');
                }
            });
        }

        async function deepseekAnalysis(file, apiKey) {
            try {
                console.log('🎯 DeepSeek分析开始，文件类型:', file.type);

                // 对于PDF和图片文件，都使用视觉分析
                if (file.type === 'application/pdf' || file.type.startsWith('image/')) {
                    console.log('📄 使用DeepSeek视觉模型分析PDF/图片');
                    return await deepseekVisionAnalysis(file, apiKey);
                } else {
                    // 对于纯文本文件，使用文本模型
                    console.log('📝 使用DeepSeek文本模型分析');
                    return await deepseekTextAnalysis(file, apiKey);
                }
            } catch (error) {
                console.error('❌ DeepSeek分析错误:', error);
                throw new Error(`DeepSeek分析失败: ${error.message}`);
            }
        }

        async function deepseekTextAnalysis(file, apiKey) {
            console.log('🚀 开始DeepSeek AI分析...');

            // 验证API密钥
            if (!apiKey || apiKey.trim() === '') {
                throw new Error('请输入DeepSeek API密钥');
            }

            // 读取文件内容
            let fileContent;
            try {
                fileContent = await readFileAsText(file);
                if (!fileContent || fileContent.trim() === '') {
                    throw new Error('文件内容为空，可能是PDF图片文件，请尝试使用OpenAI进行图片识别');
                }
            } catch (error) {
                console.error('❌ 文件读取失败，强制使用DeepSeek分析文件名:', error);
                // 即使文件读取失败，也尝试基于文件名进行分析
                fileContent = `文件名: ${file.name}\n文件大小: ${file.size} bytes\n文件类型: ${file.type}`;
            }

            // 限制内容长度，避免超出token限制
            const limitedContent = fileContent.substring(0, 4000);
            console.log(`📤 准备发送给DeepSeek的内容长度: ${limitedContent.length} 字符`);
            console.log('📄 文件内容预览:', limitedContent.substring(0, 500));

            // 构建专业的分析提示词
            const prompt = `你是一个专业的测试报告分析专家。请仔细分析以下RoHS/Reach/ELV测试报告内容，提取所有关键信息：

文件内容：
${limitedContent}

请按照以下要求提取信息：

1. **报告编号**：寻找测试报告编号、样品编号、委托编号等（如：MSH89QQG6702927C1、SGS2024001234等）
2. **测试日期**：寻找测试日期、报告日期、发布日期等（格式：YYYY/MM/DD）
3. **检测机构**：寻找实验室名称（如：谱尼测试集团深圳有限公司、SGS、TUV、华测等）
4. **测试项目及结果**：
   - RoHS测试：铅(Pb)、镉(Cd)、汞(Hg)、六价铬(Cr6+)、多溴联苯(PBBs)、多溴二苯醚(PBDEs)
   - Reach测试：SVHC高关注物质
   - ELV测试：汽车禁用物质
   - 结果状态：未检出(N.D.)、符合标准、超标等
5. **结论**：符合/不符合相关标准要求

请以以下JSON格式返回结果：
{
  "reportNumber": "报告编号",
  "testDate": "测试日期(YYYY/MM/DD)",
  "testInstitution": "检测机构全名",
  "testItems": "测试项目详情",
  "testResults": "测试结果详情",
  "conclusion": "测试结论",
  "reportType": "报告类型(RoHS/Reach/ELV)",
  "clientCompany": "委托单位",
  "sampleName": "样品名称",
  "testStandard": "测试标准",
  "analysisSource": "DeepSeek AI分析"
}

要求：
- 仔细查找文档中的具体数值和结果
- 如果某项信息确实找不到，填写"未识别"
- 不要编造信息，只提取文档中实际存在的内容
- 请只返回JSON格式，不要其他文字

开始分析：`;

            console.log('📡 发送请求到DeepSeek API...');

            try {
                // 调用DeepSeek API
                const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: "deepseek-chat",
                        messages: [
                            {
                                role: "user",
                                content: prompt
                            }
                        ],
                        max_tokens: 1200,
                        temperature: 0.1,
                        stream: false
                    })
                });

                console.log(`📊 DeepSeek API响应状态: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ DeepSeek API错误响应:', errorText);
                    throw new Error(`DeepSeek API调用失败 (${response.status}): ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ DeepSeek API响应成功:', data);

                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    throw new Error('DeepSeek API返回格式异常');
                }

                const content = data.choices[0].message.content;
                console.log('🎯 DeepSeek分析结果:', content);

                return parseEnhancedDeepSeekResponse(content, file.name);

            } catch (error) {
                console.error('❌ DeepSeek API调用异常:', error);
                // 抛出错误，不要回退到本地分析，让用户知道API调用失败
                throw new Error(`DeepSeek AI分析失败: ${error.message}`);
            }
        }

        async function deepseekVisionAnalysis(file, apiKey) {
            console.log('🖼️ 开始DeepSeek视觉分析...');

            // 将文件转换为base64
            const base64Data = await fileToBase64(file);
            console.log('📸 文件转换为base64完成，长度:', base64Data.length);

            // 构建专业的分析提示词
            const prompt = `你是一个专业的测试报告分析专家。请仔细分析这份RoHS/Reach/ELV测试报告文档，提取所有关键信息：

请按照以下要求提取信息：

1. **报告编号**：寻找测试报告编号、样品编号、委托编号等（如：MSH89QQG6702927C1、SGS2024001234等）
2. **测试日期**：寻找测试日期、报告日期、发布日期等（格式：YYYY/MM/DD）
3. **检测机构**：寻找实验室名称（如：谱尼测试集团深圳有限公司、SGS、TUV、华测等）
4. **测试项目及结果**：
   - RoHS测试：铅(Pb)、镉(Cd)、汞(Hg)、六价铬(Cr6+)、多溴联苯(PBBs)、多溴二苯醚(PBDEs)
   - Reach测试：SVHC高关注物质
   - ELV测试：汽车禁用物质
   - 结果状态：未检出(N.D.)、符合标准、超标等
5. **结论**：符合/不符合相关标准要求
6. **委托单位**：委托测试的公司名称
7. **样品信息**：样品名称、型号等

请以以下JSON格式返回结果：
{
  "reportNumber": "报告编号",
  "testDate": "测试日期(YYYY/MM/DD)",
  "testInstitution": "检测机构全名",
  "testItems": "测试项目详情",
  "testResults": "测试结果详情",
  "conclusion": "测试结论",
  "reportType": "报告类型(RoHS/Reach/ELV)",
  "clientCompany": "委托单位",
  "sampleName": "样品名称",
  "testStandard": "测试标准",
  "analysisSource": "DeepSeek Vision分析"
}

要求：
- 仔细查找文档中的具体数值和结果
- 如果某项信息确实找不到，填写"未识别"
- 不要编造信息，只提取文档中实际存在的内容
- 请只返回JSON格式，不要其他文字`;

            console.log('📡 发送请求到DeepSeek Vision API...');

            // 尝试提取PDF文本内容
            console.log('🔍 DeepSeek智能分析：尝试提取文档内容...');

            let extractedText = '';

            try {
                if (file.type === 'application/pdf') {
                    console.log('📄 检测到PDF文件，提取文本内容...');
                    extractedText = await extractPDFText(file);
                    console.log('✅ PDF文本提取成功，长度:', extractedText.length);
                } else if (file.type.startsWith('image/')) {
                    console.log('🖼️ 检测到图片文件，使用文件名分析');
                    extractedText = `图片文件: ${file.name}\n由于浏览器限制，无法直接识别图片内容。建议使用支持图片识别的AI服务。`;
                } else {
                    console.log('📝 检测到文本文件，直接读取内容');
                    extractedText = await readFileAsText(file);
                }
            } catch (error) {
                console.log('⚠️ 文档内容提取失败，使用文件名分析:', error.message);
                extractedText = `文件名: ${file.name}\n文档内容提取失败: ${error.message}`;
            }

            // 使用DeepSeek文本模型进行分析
            const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: "deepseek-chat",
                    messages: [
                        {
                            role: "system",
                            content: "你是一个专业的测试报告分析专家。请仔细分析提供的文档内容，提取所有关键信息。如果是完整的文档内容，请精确提取；如果只有文件名，请基于专业知识进行合理推测。"
                        },
                        {
                            role: "user",
                            content: `请分析以下测试报告内容，提取关键信息：

**文档内容：**
${extractedText}

**分析要求：**
请仔细查找并提取以下信息：

1. **报告编号**：寻找类似"MSH89QQG6702927C1"、"报告编号"、"Report No."、"委托编号"等
2. **测试日期**：寻找"测试日期"、"Test Date"、"检测日期"、"报告日期"等（格式：YYYY/MM/DD）
3. **检测机构**：寻找实验室名称，如"谱尼测试集团"、"SGS"、"华测检测"、"TUV"等
4. **测试项目**：寻找具体的测试项目列表，如"铅"、"镉"、"汞"、"六价铬"等
5. **测试结果**：寻找每个项目的具体检测结果，格式如：
   - 铅(Pb): N.D. (未检出)
   - 镉(Cd): N.D. (未检出)
   - 汞(Hg): N.D. (未检出)
   - 六价铬(Cr6+): N.D. (未检出)
   - 多溴联苯(PBBs): N.D. (未检出)
   - 多溴二苯醚(PBDEs): N.D. (未检出)
6. **结论**：寻找"符合"、"不符合"、"PASS"、"FAIL"、"满足要求"等结论
7. **委托单位**：寻找"委托单位"、"Client"、"申请人"、"送样单位"等
8. **样品信息**：寻找样品名称、型号、编号等
9. **测试标准**：寻找引用的标准，如"GB/T 30512-2014"、"RoHS"、"REACH"等

**输出格式：**
请以JSON格式返回结果：
{
  "reportNumber": "提取的报告编号或'未识别'",
  "releaseDate": "测试日期(YYYY/MM/DD)或'未识别'",
  "testInstitution": "检测机构全名或'未识别'",
  "testItems": "铅、镉、汞、六价铬、多溴联苯、多溴二苯醚",
  "testResults": "铅(Pb): N.D.; 镉(Cd): N.D.; 汞(Hg): N.D.; 六价铬(Cr6+): N.D.; 多溴联苯(PBBs): N.D.; 多溴二苯醚(PBDEs): N.D.",
  "compliance": "测试结论或'未识别'",
  "reportType": "报告类型(RoHS/Reach/ELV)或'未识别'",
  "clientCompany": "委托单位或'未识别'",
  "sampleName": "样品名称或'未识别'",
  "testStandard": "测试标准或'未识别'",
  "expiryDate": "有效期至或'未识别'",
  "note": "DeepSeek智能文本分析"
}

**重要要求：**
- 如果文档内容完整，请精确提取实际信息
- 测试结果请按照"项目名称: 结果值"的格式，用分号分隔
- 如果看到表格，请提取每一行的限用物质和对应的检测结果
- 常见结果格式：N.D.(未检出)、<1000mg/kg、符合等
- 如果信息不完整，基于专业知识合理推测
- 不要编造具体的数字和日期
- 如果确实找不到，请填写"未识别"
- 请只返回JSON格式，不要其他文字

**测试结果示例格式：**
"铅(Pb): N.D.; 镉(Cd): N.D.; 汞(Hg): N.D.; 六价铬(Cr6+): N.D.; 多溴联苯(PBBs): N.D.; 多溴二苯醚(PBDEs): N.D."`
                        }
                    ],
                    max_tokens: 1000,
                    temperature: 0.1,
                    stream: false
                })
            });

            console.log(`📊 DeepSeek Vision API响应状态: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ DeepSeek Vision API错误响应:', errorText);
                throw new Error(`DeepSeek Vision API调用失败 (${response.status}): ${errorText}`);
            }

            const data = await response.json();
            console.log('✅ DeepSeek Vision API响应成功:', data);

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('DeepSeek Vision API返回格式异常');
            }

            const content = data.choices[0].message.content;
            console.log('🎯 DeepSeek Vision分析结果:', content);

            return parseEnhancedDeepSeekResponse(content, file.name);
        }

        function parseEnhancedDeepSeekResponse(content, fileName) {
            console.log('🔍 开始解析DeepSeek响应...');

            // 尝试解析JSON响应
            try {
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const parsed = JSON.parse(jsonMatch[0]);
                    console.log('✅ JSON解析成功:', parsed);

                    // 处理测试结果，确保是字符串格式
                    let testResults = parsed.testResults || '未识别';
                    if (typeof testResults === 'object') {
                        testResults = JSON.stringify(testResults);
                    }

                    // 处理委托单位，过滤无效信息并截取过长内容
                    let clientCompany = parsed.clientCompany || '未识别';

                    if (clientCompany !== '未识别') {
                        // 过滤掉无效的委托单位信息
                        const cityNames = ['深圳', '广州', '上海', '北京', '苏州', '东莞', '宁波', '青岛', '天津', '杭州', '成都', '武汉', '西安', '南京', '重庆'];
                        const invalidPatterns = [
                            /^(深圳|广州|上海|北京|苏州|东莞|宁波|青岛|天津|杭州|成都|武汉|西安|南京|重庆)$/i,
                            /^(市|区|县|省)$/i,
                            /^\d+$/,
                            /^[A-Z]{1,3}$/,
                            /^(测试|检测|实验室|机构)$/i
                        ];

                        const isInvalid = invalidPatterns.some(pattern => pattern.test(clientCompany)) ||
                                        cityNames.some(city => clientCompany === city);

                        if (isInvalid || clientCompany.length < 3) {
                            console.log(`⚠️ DeepSeek分析过滤掉无效委托单位: "${clientCompany}"`);
                            clientCompany = '未识别';
                        } else {
                            console.log(`✅ DeepSeek分析提取到委托单位: "${clientCompany}"`);
                            // 如果太长则截取
                            if (clientCompany.length > 40) {
                                clientCompany = clientCompany.substring(0, 40) + '...';
                            }
                        }
                    }

                    // 处理测试项目，保留完整信息
                    let testItems = parsed.testItems || '未识别';

                    // 判断是否为正规检测机构报告，如果是则置信度为100%
                    const testInstitution = parsed.testInstitution || '未识别';
                    const isOfficialInstitution = testInstitution && testInstitution !== '未识别' && (
                        testInstitution.includes('SGS') ||
                        testInstitution.includes('TUV') ||
                        testInstitution.includes('华测') ||
                        testInstitution.includes('谱尼') ||
                        testInstitution.includes('PONY') ||
                        testInstitution.includes('BV') ||
                        testInstitution.includes('Intertek') ||
                        testInstitution.includes('CTC') ||
                        testInstitution.includes('CTI') ||
                        testInstitution.includes('检测') ||
                        testInstitution.includes('实验室') ||
                        testInstitution.includes('Laboratory') ||
                        testInstitution.includes('Testing')
                    );

                    const confidenceLevel = isOfficialInstitution ? 1.0 : 0.95;
                    const noteText = isOfficialInstitution
                        ? '正规检测机构报告 - 100%可信'
                        : (parsed.analysisSource || 'DeepSeek AI专业分析');

                    return {
                        fileName: fileName,
                        reportType: parsed.reportType || '未识别',
                        testInstitution: testInstitution,
                        reportNumber: parsed.reportNumber || '未识别',
                        releaseDate: parsed.testDate || parsed.releaseDate || '未识别',
                        expiryDate: parsed.expiryDate || '未识别',
                        compliance: parsed.conclusion || parsed.compliance || '未识别',
                        clientCompany: clientCompany,
                        sampleName: parsed.sampleName || '未识别',
                        testStandard: parsed.testStandard || '未识别',
                        testItems: testItems,
                        testResults: testResults,
                        confidence: confidenceLevel,
                        note: noteText
                    };
                } else {
                    console.warn('⚠️ 未找到JSON格式，尝试文本解析');
                    throw new Error('AI返回格式不正确');
                }
            } catch (parseError) {
                console.error('❌ JSON解析失败:', parseError);
                console.log('📄 原始AI响应:', content);

                // 如果JSON解析失败，尝试从文本中提取信息
                return extractAdvancedInfoFromText(content, fileName);
            }
        }

        function parseDeepSeekResponse(content, fileName) {
            // 保留原有函数作为备用
            return parseEnhancedDeepSeekResponse(content, fileName);
        }

        function extractAdvancedInfoFromText(content, fileName) {
            console.log('🔧 使用高级文本提取...');

            // 更强大的正则表达式模式
            const patterns = {
                reportNumber: /(?:报告编号|测试编号|样品编号|委托编号|Report\s*No)[：:\s]*([A-Z0-9]{6,})/i,
                testDate: /(?:测试日期|报告日期|发布日期|Date)[：:\s]*(\d{4}[\/\-年]\d{1,2}[\/\-月]\d{1,2})/i,
                institution: /(?:检测机构|实验室|Laboratory)[：:\s]*([^\n\r]{5,50})/i,
                conclusion: /(?:结论|Conclusion)[：:\s]*([^\n\r]{5,50})/i,
                testItems: /(?:测试项目|Test\s*Items)[：:\s]*([^\n\r]{10,100})/i,
                testResults: /(?:测试结果|Results)[：:\s]*([^\n\r]{10,100})/i,
                clientCompany: /(?:委托单位|客户|申请人|送样单位|Client)[：:\s]*([^\n\r，。；;]{3,50})/i,
                sampleName: /(?:样品名称|Sample\s*Name)[：:\s]*([^\n\r]{3,50})/i
            };

            const result = {
                fileName: fileName,
                reportType: content.includes('RoHS') ? 'RoHS报告' : (content.includes('Reach') || content.includes('REACH') || content.includes('SVHC')) ? 'REACH报告' : '未识别',
                testInstitution: '未识别',
                reportNumber: '未识别',
                releaseDate: '未识别',
                expiryDate: '未识别',
                compliance: '未识别',
                clientCompany: '未识别',
                sampleName: '未识别',
                testStandard: '未识别',
                testItems: '未识别',
                testResults: '未识别',
                confidence: 0.6,
                note: 'DeepSeek AI文本提取分析'
            };

            // 提取各项信息
            Object.keys(patterns).forEach(key => {
                const match = content.match(patterns[key]);
                if (match && match[1]) {
                    const value = match[1].trim();
                    switch(key) {
                        case 'reportNumber':
                            result.reportNumber = value;
                            break;
                        case 'testDate':
                            result.releaseDate = value.replace(/年|月|日/g, '/').replace(/\-/g, '/');
                            break;
                        case 'institution':
                            result.testInstitution = value;
                            break;
                        case 'conclusion':
                            result.compliance = value;
                            break;
                        case 'testItems':
                            result.testItems = value;
                            break;
                        case 'testResults':
                            result.testResults = value;
                            break;
                        case 'clientCompany':
                            // 过滤掉无效的委托单位信息
                            const cityNames = ['深圳', '广州', '上海', '北京', '苏州', '东莞', '宁波', '青岛', '天津', '杭州', '成都', '武汉', '西安', '南京', '重庆'];
                            const invalidPatterns = [
                                /^(深圳|广州|上海|北京|苏州|东莞|宁波|青岛|天津|杭州|成都|武汉|西安|南京|重庆)$/i,
                                /^(市|区|县|省)$/i,
                                /^\d+$/,
                                /^[A-Z]{1,3}$/,
                                /^(测试|检测|实验室|机构)$/i
                            ];

                            const isInvalid = invalidPatterns.some(pattern => pattern.test(value)) ||
                                            cityNames.some(city => value === city);

                            if (!isInvalid && value.length >= 3) {
                                result.clientCompany = value;
                                console.log(`✅ 高级提取到委托单位: "${value}"`);
                            } else {
                                console.log(`⚠️ 高级提取过滤掉无效委托单位: "${value}"`);
                                result.clientCompany = '未识别';
                            }
                            break;
                        case 'sampleName':
                            result.sampleName = value;
                            break;
                    }
                }
            });

            // 根据检测机构调整置信度
            const isOfficialInstitution = result.testInstitution && result.testInstitution !== '未识别' && (
                result.testInstitution.includes('SGS') ||
                result.testInstitution.includes('TUV') ||
                result.testInstitution.includes('华测') ||
                result.testInstitution.includes('谱尼') ||
                result.testInstitution.includes('PONY') ||
                result.testInstitution.includes('BV') ||
                result.testInstitution.includes('Intertek') ||
                result.testInstitution.includes('CTC') ||
                result.testInstitution.includes('CTI') ||
                result.testInstitution.includes('检测') ||
                result.testInstitution.includes('实验室') ||
                result.testInstitution.includes('Laboratory') ||
                result.testInstitution.includes('Testing')
            );

            if (isOfficialInstitution) {
                result.confidence = 1.0;
                result.note = '正规检测机构报告 - 100%可信';
            }

            console.log('🎯 高级文本提取结果:', result);
            return result;
        }

        function extractInfoFromText(text, fileName) {
            // 从AI返回的文本中提取信息的备用方法
            const patterns = {
                reportType: /(RoHS|Reach)报告/i,
                testInstitution: /测试机构[：:]\s*([^\n\r,，]+)/i,
                reportNumber: /报告编号[：:]\s*([^\n\r,，\s]+)/i,
                releaseDate: /发布日期[：:]\s*(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/i,
                expiryDate: /有效期[：:]\s*(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/i,
                compliance: /(符合|不符合)/i
            };

            const result = {
                fileName: fileName,
                reportType: '未识别',
                testInstitution: '未识别',
                reportNumber: '未识别',
                releaseDate: '未识别',
                expiryDate: '未识别',
                compliance: '未识别',
                confidence: 0.6,
                note: 'DeepSeek文本解析结果'
            };

            for (const [key, pattern] of Object.entries(patterns)) {
                const match = text.match(pattern);
                if (match) {
                    result[key] = match[1] || match[0];
                }
            }

            // 根据检测机构调整置信度
            const isOfficialInstitution = result.testInstitution && result.testInstitution !== '未识别' && (
                result.testInstitution.includes('SGS') ||
                result.testInstitution.includes('TUV') ||
                result.testInstitution.includes('华测') ||
                result.testInstitution.includes('谱尼') ||
                result.testInstitution.includes('PONY') ||
                result.testInstitution.includes('BV') ||
                result.testInstitution.includes('Intertek') ||
                result.testInstitution.includes('CTC') ||
                result.testInstitution.includes('CTI') ||
                result.testInstitution.includes('检测') ||
                result.testInstitution.includes('实验室') ||
                result.testInstitution.includes('Laboratory') ||
                result.testInstitution.includes('Testing')
            );

            if (isOfficialInstitution) {
                result.confidence = 1.0;
                result.note = '正规检测机构报告 - 100%可信';
            }

            return result;
        }

        async function openaiAnalysis(file, apiKey) {
            // OpenAI API实现示例
            try {
                const base64 = await fileToBase64(file);

                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: "gpt-4-vision-preview",
                        messages: [
                            {
                                role: "user",
                                content: [
                                    {
                                        type: "text",
                                        text: `你是一个专业的测试报告分析专家。请仔细分析这份RoHS/Reach/ELV测试报告文档，提取所有关键信息：

请按照以下要求提取信息：

1. **报告编号**：寻找测试报告编号、样品编号、委托编号等（如：MSH89QQG6702927C1、SGS2024001234等）
2. **测试日期**：寻找测试日期、报告日期、发布日期等（格式：YYYY/MM/DD）
3. **检测机构**：寻找实验室名称（如：谱尼测试集团深圳有限公司、SGS、TUV、华测等）
4. **测试项目及结果**：
   - RoHS测试：铅(Pb)、镉(Cd)、汞(Hg)、六价铬(Cr6+)、多溴联苯(PBBs)、多溴二苯醚(PBDEs)
   - Reach测试：SVHC高关注物质
   - ELV测试：汽车禁用物质
   - 结果状态：未检出(N.D.)、符合标准、超标等
5. **结论**：符合/不符合相关标准要求
6. **委托单位**：委托测试的公司名称
7. **样品信息**：样品名称、型号等

请以以下JSON格式返回结果：
{
  "reportNumber": "报告编号",
  "releaseDate": "测试日期(YYYY/MM/DD)",
  "testInstitution": "检测机构全名",
  "testItems": "测试项目详情",
  "testResults": "测试结果详情",
  "compliance": "测试结论",
  "reportType": "报告类型(RoHS/Reach/ELV)",
  "clientCompany": "委托单位",
  "sampleName": "样品名称",
  "testStandard": "测试标准",
  "expiryDate": "有效期至(YYYY/MM/DD)",
  "note": "OpenAI GPT-4V视觉分析"
}

要求：
- 仔细查找文档中的具体数值和结果
- 如果某项信息确实找不到，填写"未识别"
- 不要编造信息，只提取文档中实际存在的内容
- 请只返回JSON格式，不要其他文字`
                                    },
                                    {
                                        type: "image_url",
                                        image_url: {
                                            url: base64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 1500,
                        temperature: 0.1
                    })
                });

                console.log(`📊 OpenAI API响应状态: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ OpenAI API错误响应:', errorText);
                    throw new Error(`OpenAI API调用失败 (${response.status}): ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ OpenAI API响应成功:', data);

                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    throw new Error('OpenAI API返回格式异常');
                }

                const content = data.choices[0].message.content;
                console.log('🎯 OpenAI分析结果:', content);

                return parseEnhancedDeepSeekResponse(content, file.name);
            } catch (error) {
                throw new Error(`OpenAI分析失败: ${error.message}`);
            }
        }

        async function claudeAnalysis(file, apiKey) {
            // Claude API实现示例
            await new Promise(resolve => setTimeout(resolve, 1500));
            return {
                fileName: file.name,
                reportType: 'Claude分析结果',
                testInstitution: '需要实际API实现',
                reportNumber: 'CLAUDE_DEMO',
                releaseDate: '2024/01/01',
                expiryDate: '2025/01/01',
                compliance: '需要实际分析',
                confidence: 0.8,
                note: '需要配置Claude API'
            };
        }

        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        // PDF文本提取功能（使用PDF.js库）
        async function extractPDFText(file) {
            console.log('📄 开始提取PDF文本...');

            // 检查是否有PDF.js库
            if (typeof pdfjsLib === 'undefined') {
                console.log('⚠️ PDF.js库未加载，尝试动态加载...');
                await loadPDFJS();
            }

            try {
                const arrayBuffer = await file.arrayBuffer();
                const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                console.log(`📖 PDF共有 ${pdf.numPages} 页`);

                let fullText = '';

                // 提取前5页的文本（避免文本过长）
                const maxPages = Math.min(pdf.numPages, 5);

                for (let i = 1; i <= maxPages; i++) {
                    const page = await pdf.getPage(i);
                    const textContent = await page.getTextContent();
                    const pageText = textContent.items.map(item => item.str).join(' ');
                    fullText += `\n--- 第${i}页 ---\n${pageText}\n`;
                    console.log(`✅ 第${i}页文本提取完成，长度: ${pageText.length}`);
                }

                console.log(`🎯 PDF文本提取完成，总长度: ${fullText.length}`);
                return fullText;

            } catch (error) {
                console.error('❌ PDF文本提取失败:', error);
                throw new Error(`PDF文本提取失败: ${error.message}`);
            }
        }

        // 动态加载PDF.js库
        async function loadPDFJS() {
            return new Promise((resolve, reject) => {
                console.log('📚 动态加载PDF.js库...');

                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
                script.onload = () => {
                    console.log('✅ PDF.js库加载成功');
                    // 设置PDF.js的worker
                    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
                    resolve();
                };
                script.onerror = () => {
                    console.error('❌ PDF.js库加载失败');
                    reject(new Error('PDF.js库加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        function parseAIResponse(data, fileName) {
            // 解析AI返回的数据
            return {
                fileName: fileName,
                reportType: data.reportType || '未识别',
                testInstitution: data.testInstitution || '未识别',
                reportNumber: data.reportNumber || '未识别',
                releaseDate: data.releaseDate || '未识别',
                expiryDate: data.expiryDate || '未识别',
                compliance: data.compliance || '未识别',
                confidence: data.confidence || 0.8
            };
        }

        function displayAnalysisResults(results) {
            const resultsDiv = document.getElementById('analysisResults');
            let html = '<div style="font-weight: bold; margin-bottom: 12px; color: #2196F3; font-size: 16px;">📊 专业分析结果</div>';

            results.forEach((result, index) => {
                const confidenceColor = result.confidence > 0.8 ? '#4CAF50' : result.confidence > 0.6 ? '#FF9800' : '#f44336';
                const isDeepSeekAnalysis = result.note && result.note.includes('DeepSeek');

                html += `
                    <div style="border: 1px solid #e0e0e0; margin: 8px 0; padding: 10px; border-radius: 5px; background-color: ${isDeepSeekAnalysis ? '#f0f8ff' : '#fafafa'};">
                        <!-- 标题行 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="font-weight: bold; font-size: 14px; color: #333;">📄 ${result.fileName}</span>
                                ${isDeepSeekAnalysis ? '<span style="background: #2196F3; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">AI分析</span>' : ''}
                                <span style="color: ${confidenceColor}; font-size: 12px;">置信度: ${(result.confidence * 100).toFixed(0)}%</span>
                            </div>
                            <button onclick="applyToTable(${index})" style="padding: 4px 10px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">应用到表格</button>
                        </div>

                        <!-- 核心信息紧凑布局 - 同一行显示 -->
                        <div style="background: white; padding: 10px; border-radius: 4px; margin-bottom: 8px;">
                            <!-- 所有核心信息在同一行：报告编号、测试日期、结论、检测机构 -->
                            <div style="display: flex; flex-wrap: wrap; gap: 15px; font-size: 14px; align-items: center;">
                                <div><span style="color: #666; font-weight: bold;">报告编号:</span> <span style="color: #333;">${result.reportNumber}</span></div>
                                <div><span style="color: #666; font-weight: bold;">测试日期:</span> <span style="color: #333;">${result.releaseDate}</span></div>
                                <div><span style="color: #666; font-weight: bold;">结论:</span> <span style="color: ${(() => {
                                    const finalCompliance = isDeepSeekAnalysis && result.testResults && result.testResults !== '未识别'
                                        ? getFinalCompliance(result.testResults, result.reportType)
                                        : result.compliance;
                                    return finalCompliance.includes('符合') ? '#4CAF50' : finalCompliance.includes('不符合') ? '#f44336' : '#666';
                                })()}; font-weight: bold;">${isDeepSeekAnalysis && result.testResults && result.testResults !== '未识别' ? getFinalCompliance(result.testResults, result.reportType) : result.compliance}</span></div>
                                <div style="flex: 1; min-width: 200px;"><span style="color: #666; font-weight: bold;">检测机构:</span> <span style="color: #333;" title="${result.testInstitution}">${result.testInstitution.length > 40 ? result.testInstitution.substring(0, 40) + '...' : result.testInstitution}</span></div>
                            </div>
                        </div>

                        <!-- 其他信息紧凑显示 -->
                        <div style="font-size: 13px; margin-bottom: 8px;">
                            <!-- 样品和委托信息 -->
                            <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 6px;">
                                ${result.sampleName && result.sampleName !== '未识别' ? `<div><span style="color: #666; font-weight: bold;">样品:</span> <span style="color: #333;">${result.sampleName}</span></div>` : ''}
                                ${result.clientCompany && result.clientCompany !== '未识别' ? `<div><span style="color: #666; font-weight: bold;">委托:</span> <span style="color: #333;">${result.clientCompany}</span></div>` : ''}
                            </div>

                            <!-- 测试标准 -->
                            ${result.testStandard && result.testStandard !== '未识别' ? `
                            <div style="margin-bottom: 6px;">
                                <span style="color: #666; font-weight: bold;">测试标准:</span> <span style="color: #333;">${result.testStandard}</span>
                            </div>` : ''}

                            <!-- 测试项目显示 -->
                            ${isDeepSeekAnalysis && result.testItems && result.testItems !== '未识别' ? `
                            <div style="margin-bottom: 8px;">
                                <span style="color: #666; font-weight: bold;">测试项目:</span>
                                <span style="color: #333;">${result.testItems}</span>
                            </div>` : ''}

                            <!-- 测试结果显示 -->
                            ${isDeepSeekAnalysis && result.testResults && result.testResults !== '未识别' ? `
                            <div style="margin-bottom: 6px;">
                                <div style="font-size: 13px; color: #666; font-weight: bold; margin-bottom: 6px;">测试结果:</div>
                                <div style="overflow-x: auto;">
                                    ${result.reportType.includes('Reach') || result.reportType.includes('REACH') || result.reportType.includes('SVHC')
                                        ? formatReachTestResultsSummary(result.testResults, result.reportType)
                                        : formatTestResults(result.testResults)
                                    }
                                </div>
                            </div>` : ''}
                        </div>

                        <!-- RoHS合规性分析 -->
                        ${(() => {
                            const compliance = analyzeRegulationCompliance(result);
                            if (result.reportType.includes('rohs')) {
                                const rohsCompliance = compliance.rohsCompliance;
                                const riskColor = rohsCompliance.riskAssessment === 'high' ? '#f44336' :
                                                rohsCompliance.riskAssessment === 'medium' ? '#FF9800' : '#4CAF50';

                                return `
                                <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid ${riskColor};">
                                    <div style="font-weight: bold; font-size: 13px; color: #333; margin-bottom: 6px;">
                                        🌍 RoHS合规性分析
                                        <span style="color: ${riskColor}; font-size: 12px;">[风险等级: ${rohsCompliance.riskAssessment.toUpperCase()}]</span>
                                    </div>

                                    <!-- 已覆盖法规 -->
                                    ${rohsCompliance.coveredRegulations.length > 0 ? `
                                    <div style="margin-bottom: 6px;">
                                        <div style="font-size: 12px; color: #4CAF50; font-weight: bold;">✅ 已覆盖法规 (${rohsCompliance.coveredRegulations.length}项):</div>
                                        <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                            ${rohsCompliance.coveredRegulations.map(item =>
                                                `<span style="display: inline-block; margin: 2px 5px 2px 0; padding: 2px 6px; background: #e8f5e8; border-radius: 3px;">
                                                    ${item.region}: ${item.regulation.code} (${item.confidence.toFixed(0)}%)
                                                </span>`
                                            ).join('')}
                                        </div>
                                    </div>` : ''}

                                    <!-- 缺失法规 -->
                                    ${rohsCompliance.missingRegulations.length > 0 ? `
                                    <div style="margin-bottom: 6px;">
                                        <div style="font-size: 12px; color: #f44336; font-weight: bold;">⚠️ 可能缺失的法规 (${rohsCompliance.missingRegulations.length}项):</div>
                                        <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                            ${rohsCompliance.missingRegulations.slice(0, 5).map(item => {
                                                const priorityColor = item.risk === 'high' ? '#f44336' : item.risk === 'medium' ? '#FF9800' : '#4CAF50';
                                                const highlight = item.regulation.highlight ? 'font-weight: bold; border: 1px solid #FF9800;' : '';
                                                return `<span style="display: inline-block; margin: 2px 5px 2px 0; padding: 2px 6px; background: #ffe0b2; border-radius: 3px; ${highlight}">
                                                            ${item.region}: ${item.regulation.code} (风险: <span style="color: ${priorityColor};">${item.risk}</span>) - ${item.reason}
                                                        </span>`;
                                            }).join('')}
                                            ${rohsCompliance.missingRegulations.length > 5 ? `<span style="font-size: 11px; color: #999;">...等${rohsCompliance.missingRegulations.length}项</span>` : ''}
                                        </div>
                                    </div>` : ''}

                                    <!-- 建议 -->
                                    ${rohsCompliance.recommendations.length > 0 ? `
                                    <div style="font-size: 12px; color: #333; margin-top: 6px;">
                                        <div style="font-weight: bold;">建议:</div>
                                        <ul style="margin: 0; padding-left: 20px;">
                                            ${rohsCompliance.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                        </ul>
                                    </div>` : ''}
                                </div>`;
                            } else if (result.reportType.includes('reach') || result.reportType.includes('svhc')) {
                                const reachCompliance = compliance.reachCompliance;
                                const riskColor = reachCompliance.riskAssessment === 'high' ? '#f44336' :
                                                reachCompliance.riskAssessment === 'medium' ? '#FF9800' : '#4CAF50';

                                return `
                                <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid ${riskColor};">
                                    <div style="font-weight: bold; font-size: 13px; color: #333; margin-bottom: 6px;">
                                        🌍 REACH/SVHC合规性分析
                                        <span style="color: ${riskColor}; font-size: 12px;">[风险等级: ${reachCompliance.riskAssessment.toUpperCase()}]</span>
                                    </div>

                                    <!-- 已覆盖法规 -->
                                    ${reachCompliance.coveredRegulations.length > 0 ? `
                                    <div style="margin-bottom: 6px;">
                                        <div style="font-size: 12px; color: #4CAF50; font-weight: bold;">✅ 已覆盖法规 (${reachCompliance.coveredRegulations.length}项):</div>
                                        <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                            ${reachCompliance.coveredRegulations.map(item =>
                                                `<span style="display: inline-block; margin: 2px 5px 2px 0; padding: 2px 6px; background: #e8f5e8; border-radius: 3px;">
                                                    ${item.region}: ${item.regulation.code} (${item.confidence.toFixed(0)}%)
                                                </span>`
                                            ).join('')}
                                        </div>
                                    </div>` : ''}

                                    <!-- 缺失法规 -->
                                    ${reachCompliance.missingRegulations.length > 0 ? `
                                    <div style="margin-bottom: 6px;">
                                        <div style="font-size: 12px; color: #f44336; font-weight: bold;">⚠️ 可能缺失的法规 (${reachCompliance.missingRegulations.length}项):</div>
                                        <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                            ${reachCompliance.missingRegulations.slice(0, 5).map(item => {
                                                const priorityColor = item.risk === 'high' ? '#f44336' : item.risk === 'medium' ? '#FF9800' : '#4CAF50';
                                                const highlight = item.regulation.highlight ? 'font-weight: bold; border: 1px solid #FF9800;' : '';
                                                return `<span style="display: inline-block; margin: 2px 5px 2px 0; padding: 2px 6px; background: #ffe0b2; border-radius: 3px; ${highlight}">
                                                            ${item.region}: ${item.regulation.code} (风险: <span style="color: ${priorityColor};">${item.risk}</span>) - ${item.reason}
                                                        </span>`;
                                            }).join('')}
                                            ${reachCompliance.missingRegulations.length > 5 ? `<span style="font-size: 11px; color: #999;">...等${reachCompliance.missingRegulations.length}项</span>` : ''}
                                        </div>
                                    </div>` : ''}

                                    <!-- 建议 -->
                                    ${reachCompliance.recommendations.length > 0 ? `
                                    <div style="font-size: 12px; color: #333; margin-top: 6px;">
                                        <div style="font-weight: bold;">建议:</div>
                                        <ul style="margin: 0; padding-left: 20px;">
                                            ${reachCompliance.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                        </ul>
                                    </div>` : ''}
                                </div>`;
                            } else {
                                return ''; // 如果报告类型不匹配RoHS或REACH/SVHC，则不显示分类分析
                            }
                        })()}

                        <!-- 全球法规合规性分析 -->
                        ${(() => {
                            const compliance = analyzeRegulationCompliance(result);
                            const riskColor = compliance.riskAssessment === 'high' ? '#f44336' :
                                            compliance.riskAssessment === 'medium' ? '#FF9800' : '#4CAF50';

                            return `
                            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid ${riskColor};">
                                <div style="font-weight: bold; font-size: 13px; color: #333; margin-bottom: 6px;">
                                    🌍 全球法规合规性分析
                                    <span style="color: ${riskColor}; font-size: 12px;">[风险等级: ${compliance.riskAssessment.toUpperCase()}]</span>
                                </div>

                                <!-- 已覆盖法规 -->
                                ${compliance.coveredRegulations.length > 0 ? `
                                <div style="margin-bottom: 6px;">
                                    <div style="font-size: 12px; color: #4CAF50; font-weight: bold;">✅ 已覆盖法规 (${compliance.coveredRegulations.length}项):</div>
                                    <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                        ${compliance.coveredRegulations.map(item =>
                                            `<span style="display: inline-block; margin: 2px 5px 2px 0; padding: 2px 6px; background: #e8f5e8; border-radius: 3px;">
                                                ${item.region}: ${item.regulation.code} (${item.confidence.toFixed(0)}%)
                                            </span>`
                                        ).join('')}
                                    </div>
                                </div>` : ''}

                                <!-- 缺失法规 -->
                                ${compliance.missingRegulations.length > 0 ? `
                                <div style="margin-bottom: 6px;">
                                    <div style="font-size: 12px; color: #f44336; font-weight: bold;">⚠️ 可能缺失的法规 (${compliance.missingRegulations.length}项):</div>
                                    <div style="font-size: 11px; color: #666; margin-left: 10px;">
                                        ${compliance.missingRegulations.slice(0, 5).map(item => {
                                            const priorityColor = item.risk === 'high' ? '#f44336' : item.risk === 'medium' ? '#FF9800' : '#4CAF50';
                                            const highlight = item.regulation.highlight ? 'font-weight: bold; border: 1px solid #FF9800;' : '';
                                            return `<div style="margin: 3px 0; padding: 3px 6px; background: #fff3e0; border-radius: 3px; ${highlight}">
                                                <span style="color: ${priorityColor}; font-weight: bold;">[${item.risk.toUpperCase()}]</span>
                                                <span style="color: #333;">${item.region}: ${item.regulation.name}</span>
                                                <div style="font-size: 10px; color: #999; margin-top: 2px;">${item.reason}</div>
                                            </div>`;
                                        }).join('')}
                                        ${compliance.missingRegulations.length > 5 ? `<div style="font-size: 10px; color: #999; margin-top: 3px;">...还有${compliance.missingRegulations.length - 5}项法规</div>` : ''}
                                    </div>
                                </div>` : ''}

                                <!-- 建议 -->
                                <div style="font-size: 11px; color: #666; font-style: italic;">
                                    ${compliance.recommendations.join(' | ')}
                                </div>
                            </div>`;
                        })()}

                        <!-- 底部信息 -->
                        <div style="font-size: 12px; color: #999; font-style: italic; text-align: right;">${result.note || 'AI分析结果'}</div>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;

            // 保存结果供后续使用
            window.analysisResults = results;
        }

        // 格式化测试结果为内联显示（同一行）
        function formatTestResultsInline(testResults) {
            if (!testResults || testResults === '未识别') {
                return testResults;
            }

            try {
                const results = JSON.parse(testResults);
                if (Array.isArray(results)) {
                    // 限制显示的项目数量，避免过长
                    const maxItems = 6;
                    const displayResults = results.slice(0, maxItems);
                    const resultSummary = displayResults.map(item => {
                        const element = item.element || item.项目 || item.测试项目 || '未知';
                        const result = item.result || item.结果 || item.测试结果 || '未知';
                        const unit = item.unit || item.单位 || '';

                        // 简化元素名称
                        const shortElement = element.replace(/EDXRF \+ ICP-OES\/AAS|EDXRF \+ ICP-OES|EDXRF \+ |ICP-OES\/AAS|ICP-OES/g, '').trim();
                        return `${shortElement || element}: ${result}${unit}`;
                    }).join(', ');

                    const moreText = results.length > maxItems ? `... (共${results.length}项)` : '';
                    return `<span style="color: #333;">${resultSummary}${moreText}</span>`;
                }
            } catch (e) {
                // 如果不是JSON格式，尝试简单的文本处理
                if (typeof testResults === 'string') {
                    // 如果文本太长，截断显示
                    const maxLength = 150;
                    const truncated = testResults.length > maxLength ?
                        testResults.substring(0, maxLength) + '...' : testResults;
                    return `<span style="color: #333;">${truncated}</span>`;
                }
            }

            return `<span style="color: #333;">${testResults}</span>`;
        }

        // 格式化REACH测试结果为简化显示
        function formatReachTestResultsSummary(testResults, reportType) {
            console.log(`=== REACH测试结果分析开始 ===`);
            console.log(`原始测试结果:`, testResults);
            console.log(`报告类型:`, reportType);

            if (!testResults || testResults === '未识别') {
                console.log(`测试结果为空或未识别，返回原值`);
                return testResults;
            }

            let items = [];
            let compliantCount = 0;
            let nonCompliantCount = 0;
            let totalCount = 0;

            // 尝试解析JSON格式
            try {
                const parsed = JSON.parse(testResults);
                if (Array.isArray(parsed)) {
                    items = parsed;
                } else {
                    // 如果不是数组，尝试文本解析
                    items = testResults.split(';').map(item => item.trim()).filter(item => item);
                }
            } catch (e) {
                // JSON解析失败，使用文本解析
                items = testResults.split(';').map(item => item.trim()).filter(item => item);
            }

            // 特殊处理SVHC报告格式
            if (items.length === 0 || (items.length > 0 && items.some(item => typeof item === 'string' && item.includes('All tested')))) {
                console.log(`检测到SVHC报告格式，特殊处理...`);
                console.log(`当前items:`, items);

                // 处理类似 "All tested SVHC in Candidate list: ND; All tested Potential SVHC: ND" 的格式
                const svhcPattern = /(All tested.*?):\s*(ND|N\.D\.|未检出|未发现|未识别出|Pass|符合|PASS)/gi;
                const matches = [];
                let match;

                // 重置正则表达式的lastIndex
                svhcPattern.lastIndex = 0;

                while ((match = svhcPattern.exec(testResults)) !== null) {
                    const element = match[1].trim();
                    const result = match[2].trim();
                    matches.push({
                        element: element,
                        result: result
                    });
                    console.log(`SVHC正则匹配到: "${element}" -> "${result}"`);
                }

                if (matches.length > 0) {
                    items = matches;
                    console.log(`SVHC格式解析成功，解析出${matches.length}个项目:`, items);
                } else {
                    // 如果正则匹配失败，尝试简单的分号分割
                    console.log(`正则匹配失败，尝试简单分割...`);
                    const parts = testResults.split(';').map(part => part.trim()).filter(part => part);
                    items = parts.map(part => {
                        const colonIndex = part.lastIndexOf(':');
                        if (colonIndex > 0) {
                            return {
                                element: part.substring(0, colonIndex).trim(),
                                result: part.substring(colonIndex + 1).trim()
                            };
                        } else {
                            return {
                                element: part,
                                result: 'ND' // 默认假设为未检出
                            };
                        }
                    });
                    console.log(`简单分割解析结果:`, items);
                }
            }

            console.log(`最终解析的items数组:`, items);
            console.log(`items数组长度: ${items.length}`);

            // 统计符合性
            console.log(`=== REACH测试结果分析开始 ===`);
            console.log(`原始测试结果:`, testResults);
            console.log(`解析后的项目数组:`, items);

            items.forEach((item, index) => {
                let substance, result, limitValue;

                console.log(`\n--- 分析第${index + 1}项 ---`);
                console.log(`原始项目:`, item);

                if (typeof item === 'object' && item !== null) {
                    substance = item.element || item.项目 || item.测试项目 || '';
                    result = item.result || item.结果 || item.测试结果 || '';
                    limitValue = item.limit || item.限值 || null;
                } else {
                    const match = item.match(/^(.+?)(?:\([^)]*\))?\s*:\s*(.+)$/);
                    if (match) {
                        substance = match[1].trim();
                        result = match[2].trim();
                    } else {
                        console.log(`❌ 无法解析项目格式: ${item}`);
                        return;
                    }
                }

                console.log(`解析结果 - 物质: "${substance}", 结果: "${result}", 限值: ${limitValue}`);

                if (substance && result) {
                    totalCount++;

                    // 判定符合性 - 修复SVHC/REACH判定逻辑
                    console.log(`开始判定符合性 - 物质: "${substance}", 结果: "${result}"`);

                    // 1. 首先检查明确的符合表述
                    if (result.includes('ND') || result.includes('N.D.') || result.includes('未检出') ||
                        result.includes('未检测到') || result.includes('未发现') || result.includes('未识别出') ||
                        result.toLowerCase().includes('pass') || result.includes('合格') || result.includes('符合')) {
                        compliantCount++;
                        console.log(`✅ 符合 (明确表述): ${substance} - ${result}`);
                    }
                    // 2. 检查数值结果
                    else {
                        const numMatch = result.match(/(\d+(?:\.\d+)?)/);
                        console.log(`数值匹配结果:`, numMatch);

                        if (numMatch) {
                            const value = parseFloat(numMatch[1]);
                            console.log(`解析的数值: ${value}`);

                            // 获取限值
                            if (!limitValue) {
                                if (reportType && (reportType.includes('Reach') || reportType.includes('REACH') || reportType.includes('SVHC'))) {
                                    limitValue = 1000; // 默认REACH限值 0.1% = 1000mg/kg

                                    // 特殊物质的限值
                                    if (substance.includes('多环芳烃') || substance.includes('PAH') ||
                                        substance.includes('苯并芘') || substance.includes('BaP') ||
                                        substance.includes('苯并蒽') || substance.includes('BaA') ||
                                        substance.includes('苯并荧蒽') || substance.includes('BbFA') ||
                                        substance.includes('苯并苝') || substance.includes('BkFA') ||
                                        substance.toLowerCase().includes('benzo') ||
                                        substance.toLowerCase().includes('anthracene') ||
                                        substance.toLowerCase().includes('pyrene')) {
                                        limitValue = 1;
                                    }
                                } else {
                                    limitValue = 1000;
                                }
                            } else {
                                const limitMatch = String(limitValue).match(/(\d+(?:\.\d+)?)/);
                                if (limitMatch) {
                                    limitValue = parseFloat(limitMatch[1]);
                                }
                            }

                            console.log(`使用的限值: ${limitValue}`);

                            if (value <= limitValue) {
                                compliantCount++;
                                console.log(`✅ 符合: ${substance} (${value} ≤ ${limitValue})`);
                            } else {
                                nonCompliantCount++;
                                console.log(`❌ 不符合: ${substance} (${value} > ${limitValue})`);
                            }
                        } else {
                            // 无法解析数值且不是明确的符合表述，需要进一步判断
                            console.log(`⚠️ 无法明确判定: ${substance} - ${result}`);
                            // 对于SVHC报告，如果无法明确判定，倾向于认为是符合的
                            if (reportType && (reportType.includes('SVHC') || reportType.includes('REACH'))) {
                                compliantCount++;
                                console.log(`✅ 符合 (SVHC默认判定): ${substance} - ${result}`);
                            } else {
                                nonCompliantCount++;
                                console.log(`❌ 不符合 (无法判定): ${substance} - ${result}`);
                            }
                        }
                    }
                } else {
                    console.log(`❌ 物质或结果为空，跳过: 物质="${substance}", 结果="${result}"`);
                }
            });

            console.log(`\n=== REACH分析结果统计 ===`);
            console.log(`总项目数: ${totalCount}`);
            console.log(`符合项目数: ${compliantCount}`);
            console.log(`不符合项目数: ${nonCompliantCount}`);

            // 生成简化显示
            const overallConclusion = nonCompliantCount === 0 ? '符合' : '不符合';
            const conclusionColor = overallConclusion === '符合' ? '#4CAF50' : '#f44336';

            return `<div style="font-size: 12px;">
                <div style="margin-bottom: 4px;">
                    <span style="font-weight: bold;">测试项目数量:</span> <span style="color: #333;">${totalCount}项</span>
                </div>
                <div style="margin-bottom: 4px;">
                    <span style="font-weight: bold;">标准限值:</span> <span style="color: #666;">REACH SVHC ≤0.1% (1000mg/kg), PAHs ≤1mg/kg</span>
                </div>
                <div>
                    <span style="font-weight: bold;">整体结论:</span>
                    <span style="color: ${conclusionColor}; font-weight: bold;">${overallConclusion}</span>
                    <span style="color: #666; font-size: 11px;">(符合: ${compliantCount}项, 不符合: ${nonCompliantCount}项)</span>
                </div>
            </div>`;
        }

        // 测试函数 - 模拟RoHS报告分析
        function testRoHSAnalysis() {
            const mockResult = {
                fileName: 'RoHS测试报告.pdf',
                reportNumber: 'TEST-RoHS-2024-001',
                releaseDate: '2024-01-15',
                reportType: 'RoHS',
                testInstitution: 'SGS通标标准技术服务有限公司',
                sampleName: '电子产品样品',
                clientCompany: '测试公司',
                testStandard: 'IEC 62321-1:2013',
                testItems: '铅、镉、汞、六价铬、多溴联苯、多溴二苯醚',
                testResults: '铅(Pb): N.D. mg/kg; 镉(Cd): N.D. mg/kg; 汞(Hg): N.D. mg/kg; 六价铬(Cr6+): N.D. mg/kg; 多溴联苯(PBB): N.D. mg/kg; 多溴二苯醚(PBDE): N.D. mg/kg',
                compliance: '符合RoHS指令要求',
                confidence: 0.95,
                note: 'DeepSeek AI专业分析'
            };

            displayAnalysisResults([mockResult]);
        }

        // 测试函数 - 模拟REACH报告分析
        function testREACHAnalysis() {
            const mockResult = {
                fileName: 'REACH测试报告.pdf',
                reportNumber: 'TEST-REACH-2024-001',
                releaseDate: '2024-01-15',
                reportType: 'REACH SVHC',
                testInstitution: 'TUV南德意志集团',
                sampleName: '塑料制品样品',
                clientCompany: '测试公司',
                testStandard: 'REACH法规 (EC) No 1907/2006',
                testItems: 'SVHC高关注物质清单',
                testResults: '邻苯二甲酸二丁酯(DBP): N.D. mg/kg; 邻苯二甲酸二异丁酯(DIBP): N.D. mg/kg; 邻苯二甲酸二异辛酯(DEHP): N.D. mg/kg; 多环芳烃(PAH): N.D. mg/kg',
                compliance: '符合REACH法规要求',
                confidence: 0.95,
                note: 'DeepSeek AI专业分析'
            };

            displayAnalysisResults([mockResult]);
        }

        // 智能判定测试结论
        function determineConclusion(result, limit) {
            if (!result || result === '未知') {
                return '待确认';
            }

            // 检查是否为未检出
            if (result.includes('N.D.') || result.includes('ND') ||
                result.includes('未检出') || result.includes('未检测到') ||
                result.includes('Not Detected') || result.includes('<') ||
                result.includes('Below') || result.includes('小于')) {
                return '符合';
            }

            // 检查是否明确标注符合/不符合
            if (result.includes('符合') || result.includes('Pass') || result.includes('PASS')) {
                return '符合';
            }
            if (result.includes('不符合') || result.includes('Fail') || result.includes('FAIL')) {
                return '不符合';
            }

            // 数值比较判定
            if (limit) {
                const resultMatch = result.match(/(\d+(?:\.\d+)?)/);
                const limitMatch = limit.match(/(\d+(?:\.\d+)?)/);

                if (resultMatch && limitMatch) {
                    const resultValue = parseFloat(resultMatch[1]);
                    const limitValue = parseFloat(limitMatch[1]);

                    if (!isNaN(resultValue) && !isNaN(limitValue)) {
                        return resultValue <= limitValue ? '符合' : '不符合';
                    }
                }
            }

            // 如果无法判定，返回符合（大多数检测报告通过才会出具）
            return '符合';
        }

        // 格式化测试结果为表格显示
        function formatTestResults(testResults) {
            if (!testResults || testResults === '未识别') {
                return testResults;
            }

            // 如果已经是表格格式，直接返回
            if (testResults.includes('<table')) {
                return testResults;
            }

            let items = [];

            // 尝试解析JSON格式
            try {
                const parsed = JSON.parse(testResults);
                if (Array.isArray(parsed)) {
                    items = parsed;
                } else {
                    // 如果不是数组，尝试文本解析
                    items = testResults.split(';').map(item => item.trim()).filter(item => item);
                }
            } catch (e) {
                // JSON解析失败，使用文本解析
                items = testResults.split(';').map(item => item.trim()).filter(item => item);
            }

            if (items.length === 0) {
                console.log('formatTestResults: 没有找到测试项目，返回原始文本');
                return `<div style="padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">${testResults}</div>`;
            }

            console.log('formatTestResults: 找到测试项目数量:', items.length);
            console.log('formatTestResults: 测试项目:', items);

            // 对测试项目进行分类
            const categories = {
                '重金属': [],
                '阻燃剂': [],
                '邻苯二甲酸酯': [],
                '多环芳烃': [],
                '其他': []
            };

            // 构建紧凑表格HTML
            let tableHtml = `
                <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                    <thead>
                        <tr style="background-color: #e8f4fd;">
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: left; font-size: 12px;">类别/限用物质</th>
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: center; font-size: 12px;">测试方法</th>
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: center; font-size: 12px;">结果</th>
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: center; font-size: 12px;">单位</th>
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: center; font-size: 12px;">结论</th>
                            <th style="border: 1px solid #ccc; padding: 5px 8px; text-align: center; font-size: 12px;">标准限值</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 定义测试方法和限值映射
            const testMethods = {
                // RoHS物质
                '铅': 'EDXRF + ICP-OES/AAS',
                '镉': 'EDXRF + ICP-OES/AAS',
                '汞': 'EDXRF + ICP-OES',
                '六价铬': 'EDXRF + UV-Vis',
                '多溴联苯': 'EDXRF + GC-MS',
                '多溴二苯醚': 'EDXRF + GC-MS',
                // REACH SVHC物质
                '邻苯二甲酸二丁酯': 'GC-MS',
                '邻苯二甲酸二异丁酯': 'GC-MS',
                '邻苯二甲酸二异辛酯': 'GC-MS',
                '邻苯二甲酸二正辛酯': 'GC-MS',
                '多环芳烃': 'GC-MS',
                '苯并芘': 'GC-MS',
                '苯并蒽': 'GC-MS',
                '苯并荧蒽': 'GC-MS',
                '苯并苝': 'GC-MS'
            };

            const limits = {
                // RoHS限值
                '铅': '≤1000',
                '镉': '≤100',
                '汞': '≤1000',
                '六价铬': '≤1000',
                '多溴联苯': '≤1000',
                '多溴二苯醚': '≤1000',
                // REACH SVHC限值 (0.1% = 1000 mg/kg)
                '邻苯二甲酸二丁酯': '≤1000',
                '邻苯二甲酸二异丁酯': '≤1000',
                '邻苯二甲酸二异辛酯': '≤1000',
                '邻苯二甲酸二正辛酯': '≤1000',
                '多环芳烃': '≤1',
                '苯并芘': '≤1',
                '苯并蒽': '≤1',
                '苯并荧蒽': '≤1',
                '苯并苝': '≤1'
            };

            // 处理每个测试项目并分类
            items.forEach(item => {
                let substance, result, method, unit, conclusion, limit;

                // 如果是JSON对象格式
                if (typeof item === 'object' && item !== null) {
                    substance = item.element || item.项目 || item.测试项目 || '未知';
                    result = item.result || item.结果 || item.测试结果 || '未知';
                    method = item.method || item.测试方法 || testMethods[substance] || 'EDXRF';
                    unit = item.unit || item.单位 || 'mg/kg';
                    conclusion = item.conclusion || item.结论 || determineConclusion(item.result || item.结果 || item.测试结果, item.limit || item.限值 || limits[substance]);
                    limit = item.limit || item.限值 || limits[substance] || '≤1000';
                } else {
                    // 如果是文本格式
                    const match = item.match(/^(.+?)(?:\([^)]*\))?\s*:\s*(.+)$/);
                    if (match) {
                        substance = match[1].trim();
                        result = match[2].trim();

                        // 获取中文物质名称
                        let chineseName = substance;
                        // RoHS物质识别
                        if (substance.includes('Pb')) chineseName = '铅';
                        else if (substance.includes('Cd')) chineseName = '镉';
                        else if (substance.includes('Hg')) chineseName = '汞';
                        else if (substance.includes('Cr')) chineseName = '六价铬';
                        else if (substance.includes('PBB')) chineseName = '多溴联苯';
                        else if (substance.includes('PBDE')) chineseName = '多溴二苯醚';
                        // REACH SVHC物质识别
                        else if (substance.includes('DBP') || substance.includes('邻苯二甲酸二丁酯')) chineseName = '邻苯二甲酸二丁酯';
                        else if (substance.includes('DIBP') || substance.includes('邻苯二甲酸二异丁酯')) chineseName = '邻苯二甲酸二异丁酯';
                        else if (substance.includes('DEHP') || substance.includes('邻苯二甲酸二异辛酯')) chineseName = '邻苯二甲酸二异辛酯';
                        else if (substance.includes('DNOP') || substance.includes('邻苯二甲酸二正辛酯')) chineseName = '邻苯二甲酸二正辛酯';
                        else if (substance.includes('PAH') || substance.includes('多环芳烃')) chineseName = '多环芳烃';
                        else if (substance.includes('BaP') || substance.includes('苯并芘')) chineseName = '苯并芘';
                        else if (substance.includes('BaA') || substance.includes('苯并蒽')) chineseName = '苯并蒽';
                        else if (substance.includes('BbFA') || substance.includes('苯并荧蒽')) chineseName = '苯并荧蒽';
                        else if (substance.includes('BkFA') || substance.includes('苯并苝')) chineseName = '苯并苝';

                        method = testMethods[chineseName] || 'EDXRF';
                        limit = limits[chineseName] || '≤1000';
                        unit = 'mg/kg';

                        // 改进的结论判定逻辑
                        conclusion = determineConclusion(result, limit);
                        substance = chineseName;
                    } else {
                        // 无法解析的项目也要处理
                        substance = item;
                        result = '未知';
                        method = 'EDXRF';
                        unit = 'mg/kg';
                        conclusion = '待确认';
                        limit = '≤1000';
                    }
                }

                // 将物质分类
                if (['铅', '镉', '汞', '六价铬'].includes(substance)) {
                    categories['重金属'].push({substance, result, method, unit, conclusion, limit});
                } else if (['多溴联苯', '多溴二苯醚'].includes(substance)) {
                    categories['阻燃剂'].push({substance, result, method, unit, conclusion, limit});
                } else if (['邻苯二甲酸二丁酯', '邻苯二甲酸二异丁酯', '邻苯二甲酸二异辛酯', '邻苯二甲酸二正辛酯'].includes(substance)) {
                    categories['邻苯二甲酸酯'].push({substance, result, method, unit, conclusion, limit});
                } else if (['多环芳烃', '苯并芘', '苯并蒽', '苯并荧蒽', '苯并苝'].includes(substance)) {
                    categories['多环芳烃'].push({substance, result, method, unit, conclusion, limit});
                } else {
                    categories['其他'].push({substance, result, method, unit, conclusion, limit});
                }
            });

            // 按类别生成表格行
            let rowIndex = 0;
            let totalItems = 0;
            Object.keys(categories).forEach(category => {
                totalItems += categories[category].length;
            });

            console.log('formatTestResults: 分类结果:', categories);
            console.log('formatTestResults: 总项目数:', totalItems);

            if (totalItems === 0) {
                console.log('formatTestResults: 没有有效的测试项目，返回原始文本');
                return `<div style="padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">${testResults}</div>`;
            }

            Object.keys(categories).forEach(category => {
                if (categories[category].length > 0) {
                    // 添加类别标题行
                    tableHtml += `
                        <tr style="background-color: #f0f8ff;">
                            <td colspan="6" style="border: 1px solid #ccc; padding: 5px 8px; font-weight: bold; font-size: 12px; color: #2196F3;">${category}</td>
                        </tr>
                    `;

                    // 添加该类别下的所有物质
                    categories[category].forEach(item => {
                        tableHtml += `
                            <tr style="background-color: ${rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9'};">
                                <td style="border: 1px solid #ccc; padding: 4px 8px; font-size: 12px; padding-left: 20px;">　${item.substance}</td>
                                <td style="border: 1px solid #ccc; padding: 4px 8px; text-align: center; font-size: 11px;">${item.method}</td>
                                <td style="border: 1px solid #ccc; padding: 4px 8px; text-align: center; font-weight: bold; font-size: 12px;">${item.result}</td>
                                <td style="border: 1px solid #ccc; padding: 4px 8px; text-align: center; font-size: 11px;">${item.unit}</td>
                                <td style="border: 1px solid #ccc; padding: 4px 8px; text-align: center; color: ${item.conclusion === '符合' ? '#4CAF50' : '#FF9800'}; font-weight: bold; font-size: 12px;">${item.conclusion}</td>
                                <td style="border: 1px solid #ccc; padding: 4px 8px; text-align: center; font-size: 11px;">${item.limit}</td>
                            </tr>
                        `;
                        rowIndex++;
                    });
                }
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            return tableHtml;
        }

        // 基于测试结果进行最终符合性判定
        function getFinalCompliance(testResults, reportType = '') {
            if (!testResults || testResults === '未识别') {
                return '未识别';
            }

            let items = [];

            // 尝试解析JSON格式
            try {
                const parsed = JSON.parse(testResults);
                if (Array.isArray(parsed)) {
                    items = parsed;
                } else {
                    // 如果不是数组，尝试文本解析
                    items = testResults.split(';').map(item => item.trim()).filter(item => item);
                }
            } catch (e) {
                // JSON解析失败，使用文本解析
                items = testResults.split(';').map(item => item.trim()).filter(item => item);
            }

            let allCompliant = true;
            let hasResults = false;

            for (const item of items) {
                let substance, result, limitValue;

                // 如果是JSON对象格式
                if (typeof item === 'object' && item !== null) {
                    substance = item.element || item.项目 || item.测试项目 || '';
                    result = item.result || item.结果 || item.测试结果 || '';
                    limitValue = item.limit || item.限值 || null;
                } else {
                    // 如果是文本格式
                    const match = item.match(/^(.+?)(?:\([^)]*\))?\s*:\s*(.+)$/);
                    if (match) {
                        substance = match[1].trim();
                        result = match[2].trim();
                    } else {
                        continue;
                    }
                }

                if (substance && result) {
                    hasResults = true;

                    // 如果不是N.D.或未检出，需要检查数值
                    if (!result.includes('N.D.') && !result.includes('未检出') && !result.includes('未检测到')) {
                        const numMatch = result.match(/(\d+(?:\.\d+)?)/);
                        if (numMatch) {
                            const value = parseFloat(numMatch[1]);

                            // 根据报告类型和物质类型判断限值
                            if (!limitValue) {
                                if (reportType.includes('Reach') || reportType.includes('REACH') || reportType.includes('SVHC')) {
                                    // REACH SVHC限值通常为0.1% (1000 mg/kg)
                                    limitValue = 1000;

                                    // 特殊物质的限值
                                    if (substance.includes('邻苯二甲酸') || substance.includes('phthalate')) {
                                        limitValue = 1000; // 0.1%
                                    } else if (substance.includes('多环芳烃') || substance.includes('PAH')) {
                                        limitValue = 1; // 1 mg/kg for some PAHs
                                    } else if (substance.includes('苯并芘') || substance.includes('BaP')) {
                                        limitValue = 1; // 1 mg/kg
                                    }
                                } else {
                                    // RoHS限值
                                    limitValue = 1000; // 默认限值
                                    if (substance.includes('镉') || substance.includes('Cd')) {
                                        limitValue = 100;
                                    }
                                }
                            } else {
                                // 如果有明确的限值，解析数值
                                const limitMatch = String(limitValue).match(/(\d+(?:\.\d+)?)/);
                                if (limitMatch) {
                                    limitValue = parseFloat(limitMatch[1]);
                                }
                            }

                            if (value > limitValue) {
                                allCompliant = false;
                                break;
                            }
                        }
                    }
                }
            }

            if (!hasResults) {
                return '未识别';
            }

            return allCompliant ? '符合' : '不符合';
        }

        // 强制触发有效期计算的辅助函数
        function forceUpdateJudgment(rowNum, reportType, releaseDate) {
            console.log(`=== 强制计算有效期开始 ===`);
            console.log(`行号: ${rowNum}, 报告类型: ${reportType}, 发布日期: ${releaseDate}`);

            const prefix = (reportType.includes('Reach') || reportType.includes('REACH') || reportType.includes('SVHC')) ? 'reach' : 'rohs';
            const releaseDateId = `${prefix}-release-date-${rowNum}`;
            const expiryDateId = `${prefix}-expiry-date-${rowNum}`;
            const judgmentId = `${prefix}-judgment-${rowNum}`;

            console.log(`目标元素ID:`);
            console.log(`- 发布日期: ${releaseDateId}`);
            console.log(`- 有效期: ${expiryDateId}`);
            console.log(`- 判定: ${judgmentId}`);

            // 多次尝试，确保元素存在
            let attempts = 0;
            const maxAttempts = 8; // 增加尝试次数

            const tryUpdate = () => {
                attempts++;
                console.log(`\n--- 尝试第${attempts}次计算有效期 ---`);

                const releaseDateElement = document.getElementById(releaseDateId);
                const expiryDateElement = document.getElementById(expiryDateId);
                const judgmentElement = document.getElementById(judgmentId);

                console.log(`元素查找结果:`);
                console.log(`- 发布日期元素:`, releaseDateElement);
                console.log(`- 有效期元素:`, expiryDateElement);
                console.log(`- 判定元素:`, judgmentElement);

                if (releaseDateElement && expiryDateElement && judgmentElement) {
                    console.log(`✅ 所有元素找到，开始计算有效期`);

                    // 确保发布日期已设置
                    console.log(`当前发布日期内容: "${releaseDateElement.innerText}"`);
                    if (!releaseDateElement.innerText.trim()) {
                        releaseDateElement.innerText = releaseDate;
                        console.log(`设置发布日期为: ${releaseDate}`);
                    }

                    // 记录计算前的状态
                    console.log(`计算前状态:`);
                    console.log(`- 发布日期: "${releaseDateElement.innerText}"`);
                    console.log(`- 有效期: "${expiryDateElement.innerText}"`);
                    console.log(`- 判定: "${judgmentElement.innerText}"`);

                    // 确保发布日期格式正确
                    const dateText = releaseDateElement.innerText.trim();
                    console.log(`验证发布日期格式: "${dateText}"`);

                    // 直接调用updateJudgment
                    console.log(`调用updateJudgment函数...`);
                    try {
                        updateJudgment(releaseDateId, expiryDateId, judgmentId);
                        console.log(`updateJudgment函数调用完成`);
                    } catch (error) {
                        console.error(`updateJudgment函数调用出错:`, error);
                    }

                    // 立即检查结果
                    console.log(`立即检查计算结果:`);
                    console.log(`- 发布日期: "${releaseDateElement.innerText}"`);
                    console.log(`- 有效期: "${expiryDateElement.innerText}"`);
                    console.log(`- 判定: "${judgmentElement.innerText}"`);

                    // 等待一下再检查结果（给DOM更新时间）
                    setTimeout(() => {
                        console.log(`延迟检查计算结果:`);
                        console.log(`- 发布日期: "${releaseDateElement.innerText}"`);
                        console.log(`- 有效期: "${expiryDateElement.innerText}"`);
                        console.log(`- 判定: "${judgmentElement.innerText}"`);

                        // 如果有效期仍为空，尝试模拟输入事件
                        if (!expiryDateElement.innerText.trim()) {
                            console.log(`⚠️ 有效期仍为空，尝试模拟输入事件...`);

                            // 尝试多种事件类型
                            const events = ['input', 'change', 'blur', 'keyup'];
                            events.forEach(eventType => {
                                const event = new Event(eventType, { bubbles: true });
                                releaseDateElement.dispatchEvent(event);
                                console.log(`触发${eventType}事件`);
                            });

                            // 再次检查
                            setTimeout(() => {
                                console.log(`事件触发后最终状态:`);
                                console.log(`- 有效期: "${expiryDateElement.innerText}"`);
                                console.log(`- 判定: "${judgmentElement.innerText}"`);

                                if (!expiryDateElement.innerText.trim()) {
                                    console.error(`❌ 所有尝试都失败了，有效期仍为空`);
                                    console.error(`请检查updateJudgment函数是否正常工作`);
                                } else {
                                    console.log(`✅ 有效期计算成功！`);
                                }
                            }, 200);
                        } else {
                            console.log(`✅ 有效期计算成功！`);
                        }
                    }, 100);

                    console.log(`✅ 有效期计算完成`);
                    return true;
                } else {
                    const missing = [];
                    if (!releaseDateElement) missing.push('发布日期');
                    if (!expiryDateElement) missing.push('有效期');
                    if (!judgmentElement) missing.push('判定');

                    console.log(`❌ 元素未找到，缺少: ${missing.join(', ')}`);

                    if (attempts < maxAttempts) {
                        const delay = 200 * attempts; // 增加延迟时间
                        console.log(`${delay}ms后进行第${attempts + 1}次尝试...`);
                        setTimeout(tryUpdate, delay);
                    } else {
                        console.error(`❌ 经过${maxAttempts}次尝试，仍无法找到必要元素`);
                        console.error(`请检查addRow函数是否正确生成了元素ID`);
                    }
                    return false;
                }
            };

            // 立即尝试一次，然后设置延迟重试
            if (!tryUpdate()) {
                console.log(`首次尝试失败，200ms后重试...`);
                setTimeout(tryUpdate, 200);
            }
        }

        // 生成机构名称缩写
        function generateAbbreviation(fullName) {
            if (!fullName || fullName === '未识别') return fullName;

            console.log(`生成机构缩写 - 原始名称: "${fullName}"`);

            // 优先使用简短的知名机构名称
            const shortNames = {
                // SGS相关
                'SGS': 'SGS',
                'SGS-CSTC': 'SGS',
                'SGS通标': 'SGS',
                // Intertek相关
                'Intertek': 'Intertek',
                'ITS': 'Intertek',
                // Bureau Veritas相关
                'Bureau Veritas': 'BV',
                'BV': 'BV',
                // TÜV相关
                'TÜV': 'TÜV',
                'TUV': 'TÜV',
                // UL相关
                'UL': 'UL',
                // 其他知名机构
                'Nemko': 'Nemko',
                'CTI': 'CTI',
                'CTC': 'CTC',
                'DEKRA': 'DEKRA',
                'CCIC': 'CCIC',
                'CQC': 'CQC'
            };

            // 检查是否包含知名机构名称（不区分大小写）
            const upperFullName = fullName.toUpperCase();
            for (const [key, shortName] of Object.entries(shortNames)) {
                if (upperFullName.includes(key.toUpperCase())) {
                    console.log(`✅ 匹配到知名机构: ${key} → ${shortName}`);
                    return shortName;
                }
            }

            // 详细的机构名称映射（作为备用）
            const detailedMappings = {
                'SGS-CSTC Standards Technical Services Co., Ltd.': 'SGS',
                'SGS-CSTC Standards Technical Services': 'SGS',
                'Intertek Testing Services': 'Intertek',
                'Bureau Veritas Consumer Products Services': 'BV',
                'TÜV Rheinland Group': 'TÜV',
                'UL Verification Services': 'UL'
            };

            // 检查详细映射
            for (const [full, abbr] of Object.entries(detailedMappings)) {
                if (fullName.includes(full)) {
                    console.log(`✅ 匹配到详细映射: ${full} → ${abbr}`);
                    return abbr;
                }
            }

            // 自动生成缩写逻辑
            console.log(`未匹配到预定义机构，开始自动生成缩写...`);

            // 1. 优先提取知名机构关键词
            const institutionKeywords = [
                'SGS', 'INTERTEK', 'TUV', 'TÜV', 'UL', 'BV', 'NEMKO',
                'CTI', 'CTC', 'DEKRA', 'CCIC', 'CQC', 'BUREAU'
            ];

            for (const keyword of institutionKeywords) {
                if (upperFullName.includes(keyword)) {
                    console.log(`✅ 提取到机构关键词: ${keyword}`);
                    return keyword === 'BUREAU' ? 'BV' : keyword;
                }
            }

            // 2. 如果包含英文，提取主要单词的首字母（限制长度）
            const englishMatch = fullName.match(/[A-Z][a-z]*\s*[A-Z][a-z]*/g);
            if (englishMatch) {
                const words = englishMatch.join(' ').split(/\s+/).filter(word =>
                    word.length > 2 && !['Ltd', 'Co', 'Inc', 'Corp', 'Group', 'Services', 'Testing', 'Standards'].includes(word)
                );
                if (words.length >= 1) {
                    const abbreviation = words.slice(0, 3).map(word => word.charAt(0).toUpperCase()).join('');
                    console.log(`✅ 英文缩写生成: ${abbreviation}`);
                    return abbreviation;
                }
            }

            // 3. 如果是中文机构名，优先取机构主体名称
            if (/[\u4e00-\u9fa5]/.test(fullName)) {
                // 移除常见的后缀词
                let cleanName = fullName.replace(/(有限公司|股份有限公司|技术服务有限公司|检测技术有限公司|认证有限公司|实验室|检测中心|认证中心|公司|有限|集团)/g, '');

                // 如果清理后的名称较短，直接使用
                if (cleanName.length <= 6 && cleanName.length > 0) {
                    console.log(`✅ 中文机构名简化: ${cleanName}`);
                    return cleanName;
                }

                // 否则取前3-4个字符
                const shortName = cleanName.substring(0, 4);
                console.log(`✅ 中文机构名截取: ${shortName}`);
                return shortName;
            }

            // 4. 默认处理：取前几个字符
            const defaultAbbr = fullName.length > 8 ? fullName.substring(0, 6) : fullName;
            console.log(`✅ 默认缩写: ${defaultAbbr}`);
            return defaultAbbr;
        }

        function applyToTable(resultIndex) {
            const result = window.analysisResults[resultIndex];
            if (!result) return;

            let targetRow;
            let rowNum;
            const tableBody = document.querySelector('table tbody');

            // 1. 根据报告类型决定是否添加新行
            if (result.reportType.includes('RoHS') || result.reportType.includes('ELV')) {
                // RoHS报告：添加新行
                addRow();
                targetRow = tableBody.rows[tableBody.rows.length - 1];
                // 使用表格数据行的编号，而不是DOM行索引
                rowNum = tableBody.rows.length;
                console.log(`=== RoHS报告：添加新行并应用到第${rowNum}行 ===`);
                console.log(`DOM行索引: ${targetRow.rowIndex}, 数据行编号: ${rowNum}`);
            } else if (result.reportType.includes('Reach') || result.reportType.includes('REACH') || result.reportType.includes('SVHC')) {
                // REACH报告：使用现有行（最后一行或选中行）
                if (tableBody.rows.length === 0) {
                    // 如果没有行，添加一行
                    addRow();
                }
                targetRow = tableBody.rows[tableBody.rows.length - 1];
                // 使用表格数据行的编号，而不是DOM行索引
                rowNum = tableBody.rows.length;
                console.log(`=== REACH报告：应用到现有第${rowNum}行 ===`);
                console.log(`DOM行索引: ${targetRow.rowIndex}, 数据行编号: ${rowNum}`);
            } else {
                // 其他报告类型：使用现有行
                if (tableBody.rows.length === 0) {
                    addRow();
                }
                targetRow = tableBody.rows[tableBody.rows.length - 1];
                // 使用表格数据行的编号，而不是DOM行索引
                rowNum = tableBody.rows.length;
                console.log(`=== 其他报告：应用到现有第${rowNum}行 ===`);
                console.log(`DOM行索引: ${targetRow.rowIndex}, 数据行编号: ${rowNum}`);
            }

            console.log(`报告类型: ${result.reportType}`);
            console.log(`发布日期: ${result.releaseDate}`);

            // 3. 应用基本信息：供应商和物料名称
            let appliedInfo = [];
            const isRoHS = result.reportType.includes('RoHS') || result.reportType.includes('ELV');
            const isREACH = result.reportType.includes('Reach') || result.reportType.includes('REACH') || result.reportType.includes('SVHC');

            // 应用供应商信息（不填制造商，只填供应商）
            if (result.clientCompany && result.clientCompany !== '未识别') {
                const supplier = targetRow.cells[2]; // 供应商栏位
                const abbreviatedCompany = generateAbbreviation(result.clientCompany);

                if (supplier) {
                    // RoHS报告：直接填入（新行）
                    // REACH报告：只在为空时填入（现有行）
                    if (isRoHS || (!supplier.innerText || supplier.innerText.trim() === '')) {
                        supplier.innerText = abbreviatedCompany;
                        supplier.title = result.clientCompany; // 完整名称作为tooltip
                        appliedInfo.push('供应商');
                        console.log(`✅ 供应商已应用: ${abbreviatedCompany}`);
                    } else {
                        console.log(`⚠️ 供应商已有内容，跳过: "${supplier.innerText.trim()}"`);
                    }
                }
            }

            // 应用物料名称
            if (result.sampleName && result.sampleName !== '未识别') {
                const materialName = targetRow.cells[3]; // 物料名称栏位

                if (materialName) {
                    // RoHS报告：直接填入（新行）
                    // REACH报告：只在为空时填入（现有行）
                    if (isRoHS || (!materialName.innerText || materialName.innerText.trim() === '')) {
                        materialName.innerText = result.sampleName;
                        appliedInfo.push('物料名称');
                        console.log(`✅ 物料名称已应用: ${result.sampleName}`);
                    } else {
                        console.log(`⚠️ 物料名称已有内容，跳过: "${materialName.innerText.trim()}"`);
                    }
                }
            }

            // 4. 根据报告类型应用到对应的专门栏位
            if (result.reportType.includes('RoHS') || result.reportType.includes('ELV')) {
                // 应用RoHS数据到RoHS专门栏位 (删除交易状态列后，索引减1)
                const rohsInstitution = targetRow.cells[7];  // RoHS测试机构 (原来是8)
                const rohsNumber = targetRow.cells[8];       // RoHS报告编号 (原来是9)
                const rohsDate = targetRow.cells[9];         // RoHS发布日期 (原来是10)

                if (rohsInstitution && result.testInstitution !== '未识别') {
                    const abbreviatedInstitution = generateAbbreviation(result.testInstitution);
                    rohsInstitution.innerText = abbreviatedInstitution;
                    rohsInstitution.title = result.testInstitution; // 完整名称作为tooltip
                    appliedInfo.push('RoHS测试机构');
                }
                if (rohsNumber && result.reportNumber !== '未识别') {
                    rohsNumber.innerText = result.reportNumber;
                    appliedInfo.push('RoHS报告编号');
                }
                if (rohsDate && result.releaseDate !== '未识别') {
                    rohsDate.innerText = result.releaseDate;
                    appliedInfo.push('RoHS发布日期');

                    // 使用强制计算函数确保有效期被正确计算
                    forceUpdateJudgment(rowNum, 'RoHS', result.releaseDate);
                }
            }

            if (result.reportType.includes('Reach') || result.reportType.includes('REACH') || result.reportType.includes('SVHC')) {
                console.log(`=== 开始应用REACH数据 ===`);
                console.log(`目标行总列数: ${targetRow.cells.length}`);
                console.log(`目标行HTML:`, targetRow.outerHTML.substring(0, 200) + '...');

                // 应用REACH数据到REACH专门栏位 (删除交易状态列后，索引减1)
                const reachInstitution = targetRow.cells[12]; // REACH测试机构 (原来是13)
                const reachNumber = targetRow.cells[13];      // REACH报告编号 (原来是14)
                const reachDate = targetRow.cells[14];        // REACH发布日期 (原来是15)

                console.log(`REACH测试机构元素 (cells[12]):`, reachInstitution);
                console.log(`REACH报告编号元素 (cells[13]):`, reachNumber);
                console.log(`REACH发布日期元素 (cells[14]):`, reachDate);

                // 检查元素的innerHTML和innerText
                if (reachInstitution) {
                    console.log(`REACH测试机构当前内容: "${reachInstitution.innerText}", HTML: "${reachInstitution.innerHTML}"`);
                }
                if (reachNumber) {
                    console.log(`REACH报告编号当前内容: "${reachNumber.innerText}", HTML: "${reachNumber.innerHTML}"`);
                }
                if (reachDate) {
                    console.log(`REACH发布日期当前内容: "${reachDate.innerText}", HTML: "${reachDate.innerHTML}"`);
                }

                console.log(`AI识别的数据:`);
                console.log(`- 测试机构: "${result.testInstitution}"`);
                console.log(`- 报告编号: "${result.reportNumber}"`);
                console.log(`- 发布日期: "${result.releaseDate}"`);

                if (reachInstitution && result.testInstitution && result.testInstitution !== '未识别') {
                    const abbreviatedInstitution = generateAbbreviation(result.testInstitution);
                    console.log(`准备应用REACH测试机构: "${abbreviatedInstitution}"`);
                    reachInstitution.innerText = abbreviatedInstitution;
                    reachInstitution.title = result.testInstitution; // 完整名称作为tooltip
                    appliedInfo.push('REACH测试机构');
                    console.log(`✅ REACH测试机构已应用: ${abbreviatedInstitution}`);
                    console.log(`应用后内容: "${reachInstitution.innerText}"`);
                } else {
                    console.log(`❌ REACH测试机构未应用:`);
                    console.log(`  - 元素存在: ${!!reachInstitution}`);
                    console.log(`  - 数据存在: ${!!result.testInstitution}`);
                    console.log(`  - 数据有效: ${result.testInstitution !== '未识别'}`);
                    console.log(`  - 数据内容: "${result.testInstitution}"`);
                }

                if (reachNumber && result.reportNumber && result.reportNumber !== '未识别') {
                    console.log(`准备应用REACH报告编号: "${result.reportNumber}"`);
                    reachNumber.innerText = result.reportNumber;
                    appliedInfo.push('REACH报告编号');
                    console.log(`✅ REACH报告编号已应用: ${result.reportNumber}`);
                    console.log(`应用后内容: "${reachNumber.innerText}"`);
                } else {
                    console.log(`❌ REACH报告编号未应用:`);
                    console.log(`  - 元素存在: ${!!reachNumber}`);
                    console.log(`  - 数据存在: ${!!result.reportNumber}`);
                    console.log(`  - 数据有效: ${result.reportNumber !== '未识别'}`);
                    console.log(`  - 数据内容: "${result.reportNumber}"`);
                }

                if (reachDate && result.releaseDate && result.releaseDate !== '未识别') {
                    console.log(`准备应用REACH发布日期: "${result.releaseDate}"`);
                    reachDate.innerText = result.releaseDate;
                    appliedInfo.push('REACH发布日期');
                    console.log(`✅ REACH发布日期已应用: ${result.releaseDate}`);
                    console.log(`应用后内容: "${reachDate.innerText}"`);

                    // 使用强制计算函数确保有效期被正确计算
                    forceUpdateJudgment(rowNum, 'Reach', result.releaseDate);
                } else {
                    console.log(`❌ REACH发布日期未应用:`);
                    console.log(`  - 元素存在: ${!!reachDate}`);
                    console.log(`  - 数据存在: ${!!result.releaseDate}`);
                    console.log(`  - 数据有效: ${result.releaseDate !== '未识别'}`);
                    console.log(`  - 数据内容: "${result.releaseDate}"`);
                }

                console.log(`=== REACH数据应用完成 ===`);
            } else {
                console.log(`❌ 报告类型不匹配REACH: "${result.reportType}"`);
            }

            // 5. 显示应用成功的详细信息
            const actionText = isRoHS ? '新行' : '现有行';

            const successMessage = appliedInfo.length > 0
                ? `✅ 数据已应用到${actionText}！\n\n应用的信息：\n${appliedInfo.join('、')}\n\n报告类型：${result.reportType}\n置信度：${(result.confidence * 100).toFixed(0)}%\n\n请检查数据准确性并保存。`
                : `⚠️ 未能识别到有效信息！\n\n报告类型：${result.reportType}\n置信度：${(result.confidence * 100).toFixed(0)}%\n\n请检查报告内容或手动填入。`;

            alert(successMessage);
        }

        function updateJudgment(releaseDateId, expiryDateId, judgmentId) {
            // 启用调试模式来诊断问题
            const DEBUG = true;

            if (DEBUG) {
                console.log(`--- updateJudgment called ---`);
                console.log(`releaseDateId: ${releaseDateId}`);
            }

            const releaseDateElement = document.getElementById(releaseDateId);
            const expiryDateElement = document.getElementById(expiryDateId);
            const judgmentElement = document.getElementById(judgmentId);

            // 清除内容和样式
            if (judgmentElement) {
                judgmentElement.innerText = '';
                judgmentElement.className = '';
            }

            if (!releaseDateElement || !expiryDateElement || !judgmentElement) {
                if (DEBUG) {
                    console.warn(`Elements not found for ${releaseDateId}`);
                }
                return;
            }

            try {
                const releaseDateStr = releaseDateElement.innerText.trim();
                if (DEBUG) console.log(`releaseDateStr for ${releaseDateId}: '${releaseDateStr}'`);

                if (!releaseDateStr) {
                    judgmentElement.innerText = '';
                    judgmentElement.className = '';
                    expiryDateElement.innerText = ''; // 清空有效期至
                    if (DEBUG) console.log(`Release date string is empty for ${releaseDateId}. Judgment cleared and expiry date cleared.`);
                    return;
                }

                const parseDate = (dateStr) => {
                    if (DEBUG) console.log(`parseDate input dateStr: '${dateStr}'`);
                    const cleanedDateStr = dateStr.replace(/[^\d\/\-]/g, '').trim();
                    if (DEBUG) console.log(`parseDate cleanedDateStr: '${cleanedDateStr}'`);
                    const parts = cleanedDateStr.split(/[-\/]/);
                    if (DEBUG) console.log(`parseDate parts: ${parts}`);
                    if (parts.length === 3) {
                        const year = parseInt(parts[0], 10);
                        const month = parseInt(parts[1], 10) - 1;
                        const day = parseInt(parts[2], 10);
                        if (isNaN(year) || isNaN(month) || isNaN(day) || month < 0 || month > 11 || day < 1 || day > 31) {
                            if (DEBUG) console.log(`parseDate: Invalid date parts - year: ${year}, month: ${month}, day: ${day}`);
                            return null;
                        }
                        const parsedDate = new Date(year, month, day);
                        if (DEBUG) console.log(`parseDate: Parsed as YYYY/MM/DD: ${parsedDate}`);
                        return parsedDate;
                    } else if (cleanedDateStr.length === 8 && !isNaN(parseInt(cleanedDateStr, 10))) {
                        const year = parseInt(cleanedDateStr.substring(0, 4), 10);
                        const month = parseInt(cleanedDateStr.substring(4, 6), 10) - 1;
                        const day = parseInt(cleanedDateStr.substring(6, 8), 10);
                        if (isNaN(year) || isNaN(month) || isNaN(day) || month < 0 || month > 11 || day < 1 || day > 31) {
                            if (DEBUG) console.log(`parseDate: Invalid date parts (YYYYMMDD) - year: ${year}, month: ${month}, day: ${day}`);
                            return null;
                        }
                        const parsedDate = new Date(year, month, day);
                        if (DEBUG) console.log(`parseDate: Parsed as YYYYMMDD: ${parsedDate}`);
                        return parsedDate;
                    }
                    if (DEBUG) console.log(`parseDate: No valid date format found for '${cleanedDateStr}'`);
                    return null;
                };

                const releaseDate = parseDate(releaseDateStr);
                if (DEBUG) console.log(`Parsed releaseDate for ${releaseDateId}: ${releaseDate}`);
                const currentDate = new Date();
                if (DEBUG) console.log(`Current Date: ${currentDate.getFullYear()}/${currentDate.getMonth() + 1}/${currentDate.getDate()}`);

                if (!releaseDate || isNaN(releaseDate.getTime())) {
                    judgmentElement.innerText = '发布日期格式错误';
                    judgmentElement.className = 'red-cell';
                    expiryDateElement.innerText = ''; // 清空有效期至
                    if (DEBUG) console.log(`Invalid release date format for ${releaseDateId}. Judgment set to '发布日期格式错误'. Expiry date cleared.`);
                    return;
                }

                let calculatedExpiryDate = new Date(releaseDate);
                if (releaseDateId.includes('reach-release-date')) {
                    // For Reach, expiry date is release date + 1 year (exactly)
                    calculatedExpiryDate.setFullYear(releaseDate.getFullYear() + 1);
                } else {
                    // For RoHS, expiry date is release date + 1 year - 1 day
                    calculatedExpiryDate.setFullYear(releaseDate.getFullYear() + 1);
                    calculatedExpiryDate.setDate(calculatedExpiryDate.getDate() - 1);
                }
                if (DEBUG) console.log(`Calculated expiryDate for ${expiryDateId}: ${calculatedExpiryDate}`);

                const formattedExpiryDate = `${calculatedExpiryDate.getFullYear()}/${(calculatedExpiryDate.getMonth() + 1).toString().padStart(2, '0')}/${calculatedExpiryDate.getDate().toString().padStart(2, '0')}`;
                expiryDateElement.innerText = formattedExpiryDate;
                if (DEBUG) console.log(`Expiry date element ${expiryDateId} innerText set to: ${formattedExpiryDate}`);

                const expiryDate = new Date(calculatedExpiryDate); // 使用新Date对象，避免修改calculatedExpiryDate
                expiryDate.setHours(0, 0, 0, 0);
                currentDate.setHours(0, 0, 0, 0);
                if (DEBUG) console.log(`Final expiryDate for comparison: ${expiryDate}`);
                if (DEBUG) console.log(`Current date for comparison: ${currentDate}`);

                const diffTime = expiryDate.getTime() - currentDate.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                if (DEBUG) console.log(`Expiry Date (for diff): ${expiryDate.getFullYear()}/${expiryDate.getMonth() + 1}/${expiryDate.getDate()}`);
                if (DEBUG) console.log(`Difference in milliseconds: ${diffTime}`);
                if (DEBUG) console.log(`Difference in days: ${diffDays}`);

                let judgmentText = '';
                let judgmentClass = '';

                if (diffDays < 0) {
                    judgmentClass = 'red-cell';
                    judgmentText = '已经过期';
                } else if (diffDays <= 7) {
                    judgmentClass = 'red-cell';
                    judgmentText = `还有${diffDays}天到期`;
                } else if (diffDays < 30) { // 7 < diffDays < 30
                    judgmentClass = 'orange-cell';
                    judgmentText = `还有${diffDays}天到期`;
                } else { // diffDays >= 30
                    judgmentClass = 'green-cell';
                    judgmentText = `还有${diffDays}天到期`;
                }

                judgmentElement.innerText = judgmentText;
                judgmentElement.className = judgmentClass;
                if (DEBUG) console.log(`Judgment for ${judgmentId} set to: '${judgmentText}' with class '${judgmentClass}'. DiffDays: ${diffDays}`);

            } catch (error) {
                if (judgmentElement) {
                    judgmentElement.innerText = `错误: ${error.message}`;
                    judgmentElement.className = 'red-cell';
                }
                if (DEBUG) console.error(`Error in updateJudgment for ${judgmentId}: ${error.message}`, error);
            }
        }

        function saveData() {
            const tableData = [];
            document.querySelectorAll('tbody tr').forEach(row => {
                const rowData = {};
                row.querySelectorAll('td').forEach((cell, index) => {
                    if (cell.contentEditable === 'true') {
                        rowData[`col${index}`] = cell.innerText;
                    } else if (cell.querySelector('select')) {
                        rowData[`col${index}`] = cell.querySelector('select').value;
                    } else {
                        rowData[`col${index}`] = cell.innerText;
                    }
                });
                tableData.push(rowData);
            });
            localStorage.setItem('materialControlData', JSON.stringify(tableData));
            alert('数据已保存！');
        }



                document.addEventListener('DOMContentLoaded', (event) => {
            loadData(); // Load data when the page loads
        });
            // 减少控制台输出
            const DEBUG = false;

            if (DEBUG) console.log('=== 页面加载开始 ===');
            const today = new Date();
            const year = today.getFullYear();
            const month = (today.getMonth() + 1).toString().padStart(2, '0');
            const day = today.getDate().toString().padStart(2, '0');
            document.getElementById('update-date').textContent = `※更新日期: ${year}/${month}/${day}`;
            if (DEBUG) console.log('更新日期设置完成');

            // 清除旧的localStorage数据（因为删除了交易状态列，数据格式已改变）
            // console.log('清除旧的localStorage数据以避免列错位问题');
            // localStorage.removeItem('materialControlData'); // 暂时注释，避免每次加载都清除数据

            // 初始化AI服务提供商信息显示
            updateProviderInfo();

            // 由于删除了交易状态列，跳过加载旧数据，直接初始化空表格
            console.log('由于表格结构已更改（删除交易状态列），跳过加载旧数据');
            const tableBody = document.querySelector('table tbody');
            tableBody.innerHTML = ''; // 确保表格为空

            console.log('表格初始化完成，表格为空状态');
    </script>
</body>
</html>