<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8D问题解决步骤</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            font-size: 16px; /* 增加基础字体大小 */
        }
        .page-navigation {
            text-align: center;
            margin-bottom: 20px;
        }
        .page-navigation button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            cursor: pointer;
            border: 1px solid #0056b3;
            border-radius: 5px;
            background-color: #e9f7ff;
            color: #0056b3;
        }
        /* Removed '通用描述' button */
        .page-navigation button:hover {
            background-color: #0056b3;
            color: white;
        }
        .container {
            max-width: 1200px; /* Increased width */
            margin: 20px auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 1em; /* 确保容器内的文本继承或保持适当大小 */
        }
        h1 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 30px;
        }
        .d-step {
            background-color: #e9f7ff;
            border-left: 5px solid #007bff;
            margin-bottom: 20px;
            padding: 15px 20px;
            border-radius: 5px;
        }
        .d-step h2 {
            color: #007bff;
            margin-top: 0;
            font-size: 1.5em;
        }
        .d-step p {
            margin-bottom: 10px;
        }
        .d-step ul {
            list-style-type: disc;
            margin-left: 20px;
            padding-left: 0;
        }
        .d-step ul li {
            margin-bottom: 5px;
        }
        footer {
            text-align: center;
            margin-top: 40px;
            color: #777;
            font-size: 0.9em;
        }
    .fishbone-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .fishbone-table th, .fishbone-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .fishbone-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .fishbone-table textarea {
            width: 95%;
            height: 60px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 0.9em;
            resize: vertical;
        }

        .why-inputs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 5px;
        }

        .why-inputs input[type="text"] {
            flex: 1;
            min-width: 150px; /* Adjust as needed */
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .evaluation-table {
            margin-top: 40px;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .evaluation-table h2 {
            text-align: center;
            color: #0056b3;
            margin-bottom: 20px;
        }

        .evaluation-table table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .evaluation-table th,
        .evaluation-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word; /* 允许长单词或URL地址在必要时换行 */
            white-space: normal; /* 确保文本正常换行 */
        }

        .evaluation-table th {
            background-color: #e9f7ff;
            font-weight: bold;
            color: #0056b3;
        }

        .evaluation-table tbody tr:nth-child(odd) {
            background-color: #f2f2f2;
        }

        .summary-container {
            display: flex;
            flex-wrap: wrap; /* 允许项目换行 */
            justify-content: flex-start;
            margin-top: 20px;
            align-items: flex-start;
            gap: 20px; /* 增加项目之间的间距 */
        }

        .score-summary {
            padding: 10px;
            border: 1px solid #ccc;
            flex: 1 1 auto; /* 允许其增长和收缩，并根据内容调整宽度 */
            min-width: 150px; /* 最小宽度 */
            height: auto; /* 高度自适应 */
            border-radius: 5px;
            box-sizing: border-box;
            background-color: rgb(25, 137, 25);
            color: white;
            text-align: left;
            line-height: 1.5;
        }

        .overall-comments {
            padding: 10px;
            border: 1px solid #ccc;
            flex: 2 1 auto; /* 允许其增长和收缩，并占据更多空间 */
            min-width: 250px; /* 最小宽度 */
            height: auto; /* 高度自适应 */
            border-radius: 5px;
            box-sizing: border-box;
            resize: vertical;
            overflow: auto;
            text-align: left;
            line-height: 1.5;
        }

            .hint-section {
                margin-top: 0px; /* Remove margin-top as flexbox handles alignment */
                padding: 5px 10px; /* Unified padding */
                background-color: white;
                border: 1px solid #ffeeba;
                color: #856404;
                display: flex;
                align-items: flex-start;
                flex-grow: 1; /* Allow hint section to grow */
                flex-basis: auto; /* Allow hint section to adjust width based on content */
                border-radius: 5px;
                min-height: 80px; /* Set min-height to match other elements */
                margin-right: 20px; /* Add margin to the right */
            }

            .hint-section h3 {
                color: #856404;
                margin-top: 0;
                margin-bottom: 10px;
                text-align: left; /* Ensure title is left-aligned */
            }

            .hint-section ul {
                list-style-type: disc;
                margin-left: 20px;
                padding: 0;
                text-align: left; /* Ensure list items are left-aligned */
            }

            .hint-section li {
                margin-bottom: 5px;
                text-indent: -1em;
                padding-left: 1em;
            }

        .page-content {
            display: none; /* Ensure all pages are hidden by default */
        }

    </style>
</head>
<body>
    <div class="page-navigation">
        <button onclick="showPage('main-8d-page')">8D问题解决步骤</button>
        <button onclick="showPage('evaluation-page')">评估标准</button>

    </div>

    <div id="main-8d-page" class="page-content">
        <div class="container">
        <h1>8D问题解决步骤</h1>

        <div class="d-step">
            <h2>D0: 8D准备 (Preparation for 8D)</h2>
            <p>在正式启动8D流程之前，进行必要的准备工作，包括问题识别、初步评估和紧急响应。</p>
            <div style="margin: 8px 0 16px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 6px; background: #fafbfc;">
                <h3>基础信息</h3>
                <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
                    <p style="width: 30%;">投诉主题: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">投诉类型: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">发生场所: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">发生时间: <input type="datetime-local" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">提出日期: <input type="date" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">完成期限: <input type="date" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                </div>
                <h3>问题描述</h3>
                <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
                    <p style="width: 30%;">品名: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">料号: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">总批量数: <input type="number" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">批次号: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">检验数: <input type="number" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">不良数: <input type="number" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                    <p style="width: 30%;">不良率: <input type="text" style="width: calc(100% - 80px); padding: 4px; border: 1px solid #ccc; border-radius: 4px;"></p>
                </div>
                <p style="width: 100%;">不良现象: <textarea rows="2" style="width: calc(100% - 80px); margin-top: 5px; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea></p>
            </div>
            <ul>
                <li>识别并确认问题。</li>
                <textarea placeholder="请填写识别并确认问题的详细内容..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>

                <li>采取紧急措施以防止问题恶化。</li>
                <textarea placeholder="请填写采取紧急措施的详细内容..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                <li>评估是否启动8D。</li>
                <div style="margin: 8px 0 16px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 6px; background: #fafbfc;">
                    <p>问题对客户有严重影响（如安全、法规、重大质量缺陷）？
                        <input type="radio" name="impactQuestion" value="yes" onchange="toggleDescription('impactDescription', 'conclusion')"> 是
                        <input type="radio" name="impactQuestion" value="no" onchange="toggleDescription('impactDescription', 'conclusion')"> 否
                    </p>
                    <textarea id="impactDescription" placeholder="请描述问题对客户的影响..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; display: none;"></textarea>

                    <p>问题根本原因不明？
                        <input type="radio" name="rootCauseQuestion" value="yes" onchange="toggleDescription('rootCauseDescription', 'conclusion')"> 是
                        <input type="radio" name="rootCauseQuestion" value="no" onchange="toggleDescription('rootCauseDescription', 'conclusion')"> 否
                    </p>
                    <textarea id="rootCauseDescription" placeholder="请描述根本原因不明的情况..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; display: none;"></textarea>

                    <p>现有流程无法解决问题？
                        <input type="radio" name="processQuestion" value="yes" onchange="toggleDescription('processDescription', 'conclusion')"> 是
                        <input type="radio" name="processQuestion" value="no" onchange="toggleDescription('processDescription', 'conclusion')"> 否
                    </p>
                    <textarea id="processDescription" placeholder="请描述现有流程无法解决问题的情况..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; display: none;"></textarea>

                    <p>最终结论判定：<span id="conclusion" style="font-weight: bold;"></span></p>
                </div>
                <script>
                    function toggleDescription(textareaId, conclusionId) {
                        const impactYes = document.querySelector('input[name="impactQuestion"]:checked')?.value === 'yes';
                        const rootCauseYes = document.querySelector('input[name="rootCauseQuestion"]:checked')?.value === 'yes';
                        const processYes = document.querySelector('input[name="processQuestion"]:checked')?.value === 'yes';

                        document.getElementById('impactDescription').style.display = impactYes ? 'block' : 'none';
                        document.getElementById('rootCauseDescription').style.display = rootCauseYes ? 'block' : 'none';
                        document.getElementById('processDescription').style.display = processYes ? 'block' : 'none';

                        const conclusionSpan = document.getElementById(conclusionId);
                        if (impactYes || rootCauseYes || processYes) {
                            conclusionSpan.textContent = '需要启动8D';
                            conclusionSpan.style.color = 'red';
                        } else if (document.querySelector('input[name="impactQuestion"]:checked') &&
                                   document.querySelector('input[name="rootCauseQuestion"]:checked') &&
                                   document.querySelector('input[name="processQuestion"]:checked')) {
                            conclusionSpan.textContent = '不需要启动8D';
                            conclusionSpan.style.color = 'green';
                        } else {
                            conclusionSpan.textContent = '';
                        }
                    }
                </script>

                </ul>
                <textarea placeholder="请在此处填写D0步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
            </div>

            <div class="d-step">
                <h2>D1: 建立团队 (Establish the Team)</h2>
                <p>组建一个跨职能团队，拥有解决问题所需的知识、技能和权限（确定团队成员及其角色，明确团队目标和职责）。</p>
                <ul style="display: none;">
                    <li>确定团队成员及其角色。</li>
                    <li>明确团队目标和职责。</li>
                </ul>
                <div style="margin: 8px 0 16px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 6px; background: #fafbfc;">
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <thead>
                            <tr>
                                <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">职能部门</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">姓名</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">职务</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">职责</th>
                                <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;">技能</th>
                            </tr>
                        </thead>

                    <tbody>

                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">业务部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">技术部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">生产部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><input type="text" style="width: 95%; border: none;"></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">品管部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">工艺部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">采购部</td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                                <td style="border: 1px solid #ddd; padding: 8px;"><textarea rows="3" style="width: 95%; border: none;"></textarea></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <textarea placeholder="请在此处填写D1步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
            </div>

            <div class="d-step">
                <h2>D2: 描述问题 (Describe the Problem)</h2>
                <p>清晰、准确地描述问题，包括什么、在哪里、何时、谁、为什么以及如何发生。</p>
                <ul>
                    <li>使用5W2H方法（What, Where, When, Who, Why, How, How much）进行描述。</li>
                </ul>
                <textarea placeholder="请在此处填写D2步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                <li>收集初步信息和数据。</li>
<div style="margin: 8px 0 16px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 6px; background: #fafbfc;">
  <strong>1. 4M1E变化点</strong>
  <div style="margin: 6px 0 10px 0; padding-left: 12px;">
    <label>人员：</label><textarea placeholder="请填写人员相关变化点..." rows="3" style="width: 95%; margin: 4px 0 8px 0; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea><br/>
    <label>设备：</label><textarea placeholder="请填写设备相关变化点..." rows="3" style="width: 95%; margin: 4px 0 8px 0; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea><br/>
    <label>材料：</label><textarea placeholder="请填写材料相关变化点..." rows="3" style="width: 95%; margin: 4px 0 8px 0; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea><br/>
    <label>环境：</label><textarea placeholder="请填写环境相关变化点..." rows="3" style="width: 95%; margin: 4px 0 8px 0; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea><br/>
    <label>测试：</label><textarea placeholder="请填写测试相关变化点..." rows="3" style="width: 95%; margin: 4px 0 8px 0; padding: 4px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
  </div>
  <strong>2. 生产履历</strong>
  <textarea placeholder="请填写生产履历..." rows="2" style="width: 98%; margin: 6px 0 12px 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
  <strong>3. 内外部历史问题</strong>
  <textarea placeholder="请填写内外部历史问题..." rows="2" style="width: 98%; margin: 6px 0 0 0; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
</div>
            </div>

            <div class="d-step">
                <h2>D3: 实施并验证临时遏制措施 (Implement and Verify Interim Containment Actions)</h2>
                <p>在永久性解决方案实施之前，采取临时措施以保护客户，防止问题进一步扩散。</p>
                <ul>
                    <li>制定并实施临时遏制措施。</li>
                    <li>验证临时措施的有效性。</li>
                </ul>
                <textarea placeholder="请在此处填写D3步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
            </div>

            <div class="d-step">
                <h2>D4: 确定并验证根本原因 (Determine and Verify Root Causes)</h2>
                <p>通过系统性分析，识别并验证导致问题发生的所有根本原因。</p>
                <ul>
                    <li>收集和分析数据。</li>
                    <li>使用适当的工具（如鱼骨图、5 Why分析）识别潜在根本原因。</li>

                </ul>

                <table class="fishbone-table">
                    <thead>
                        <tr>
                            <th style="width: 85%;">要素</th>
                            <th style="width: 15%;">要因与否</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td style="width: 85%;">
                                <p>人员(例如：人员充分培训？具备所需资质？人员流动性过高？疲劳或注意力不集中导致错误？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="man-cause" name="man-cause-group" value="yes">
                                <label for="man-cause">是</label><br>
                                <input type="radio" id="man-non-cause" name="man-cause-group" value="no">
                                <label for="man-non-cause">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 85%;">
                                <p>设备 (例如：定期维护/有故障或磨损/参数设置正确/刀具寿命？/适合当前生产需求？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="machine-cause" name="machine-cause-group" value="yes">
                                <label for="machine-cause">是</label><br>
                                <input type="radio" id="machine-non-cause" name="machine-cause-group" value="no">
                                <label for="machine-non-cause">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 85%;">
                                <p>材料  (例如：供应商合格？材质/原材料符合规格？保质期/储存条件得当？批次之间存在差异？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="material-cause" name="material-cause-group" value="yes">
                                <label for="material-cause">是</label><br>
                                <input type="radio" id="material-non-cause" name="material-cause-group" value="no">
                                <label for="material-non-cause">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 85%;">
                                <p>方法 (例如：SOP/SIP清晰、准确？工艺流程合理？操作人员严格遵守SOP？有未经授权的流程变更？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="method-cause" name="method-cause-group" value="yes">
                                <label for="method-cause">是</label><br>
                                <input type="radio" id="method-non-cause" name="method-cause-group" value="no">
                                <label for="method-non-cause">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 85%;">
                                <p>环境 (例如：温度、湿度、洁净度符合要求？照明充足？噪音过大？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="environment-cause" name="environment-cause-group" value="yes">
                                <label for="environment-cause">是</label><br>
                                <input type="radio" id="environment-non-cause" name="environment-cause-group" value="no">
                                <label for="environment-non-cause">否</label>
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 85%;">
                                <p>测量  (例如：校准？方法准确？测量人员经过培训？测量系统存在偏差？)</p>
                                <textarea placeholder="在此输入分析内容" rows="6" style="width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box;"></textarea>
                            </td>
                            <td style="width: 15%;">
                                <input type="radio" id="measurement-cause" name="measurement-cause-group" value="yes">
                                <label for="measurement-cause">是</label><br>
                                <input type="radio" id="measurement-non-cause" name="measurement-cause-group" value="no">
                                <label for="measurement-non-cause">否</label>
                            </td>
                        </tr>
                    </tbody>
                </table>


                    <h3>3L5Why 分析</h3>
                    <div class="form-group">
                        <label>为什么问题会发生？</label>
                        <div class="why-inputs">
                            <input type="text" placeholder="原因1">
                            <input type="text" placeholder="原因2">
                            <input type="text" placeholder="原因3">
                            <input type="text" placeholder="原因4">
                            <input type="text" placeholder="原因5">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>为什么问题会流出？</label>
                        <div class="why-inputs">
                            <input type="text" placeholder="原因1">
                            <input type="text" placeholder="原因2">
                            <input type="text" placeholder="原因3">
                            <input type="text" placeholder="原因4">
                            <input type="text" placeholder="原因5">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>为什么系统会发生问题？</label>
                        <div class="why-inputs">
                            <input type="text" placeholder="原因1">
                            <input type="text" placeholder="原因2">
                            <input type="text" placeholder="原因3">
                            <input type="text" placeholder="原因4">
                            <input type="text" placeholder="原因5">
                        </div>
                    </div>

                <h3>验证根本原因</h3>
                <textarea placeholder="请在此处填写验证根本原因的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                <button onclick="showPage('evaluation-page'); highlightEvaluation('d4-occurrence-row'); highlightEvaluation('d4-non-detection-row');" style="margin-top: 10px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">评估 D4</button>
            </div>

            <div class="d-step">
                 <h2>D5: 确定并验证永久纠正措施 (Define and Verify Permanent Corrective Actions)</h2>
                 <p>基于根本原因，制定并验证能够永久消除问题的纠正措施。</p>
                 <ul>
                     <li>制定纠正措施计划。</li>
                     <li>验证纠正措施的有效性。</li>
                 </ul>
                 <textarea placeholder="请在此处填写D5步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                 <button onclick="showPage('evaluation-page'); highlightEvaluation('d5-effectiveness-row');" style="margin-top: 10px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">评估 D5</button>
             </div>

            <div class="d-step">
                <h2>D6: 实施永久纠正措施 (Implement Permanent Corrective Actions)</h2>
                <p>全面实施已验证的永久纠正措施。</p>
                <ul>
                    <li>执行纠正措施。</li>
                    <li>监控实施过程。</li>
                </ul>
                <textarea placeholder="请在此处填写D6步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                <button onclick="showPage('evaluation-page'); highlightEvaluation('d6-occurrence-row'); highlightEvaluation('d6-non-detection-row'); highlightEvaluation('d6-confirmation-row');" style="margin-top: 10px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">评估 D6</button>
            </div>

            <div class="d-step">
                <h2>D7: 预防再发生 (Prevent Recurrence)</h2>
                <p>修改管理系统、操作流程、规范等，以防止类似问题再次发生。</p>
                <ul>
                    <li>更新相关文件和流程。</li>
                    <li>进行标准化和培训。</li>
                </ul>
                <textarea placeholder="请在此处填写D7步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                <button onclick="showPage('evaluation-page'); highlightEvaluation('d7-updates-row');" style="margin-top: 10px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">评估 D7</button>
            </div>

            <div class="d-step">
                <h2>D8: 祝贺团队 (Congratulate the Team)</h2>
                <p>认可团队的努力和贡献，并庆祝问题的成功解决。</p>
                <ul>
                    <li>表彰团队成员。</li>
                    <li>分享经验教训。</li>
                </ul>
                <textarea placeholder="请在此处填写D8步骤的详细内容..." rows="5" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; margin-top: 10px;"></textarea>
                <button onclick="showPage('evaluation-page'); highlightEvaluation('d8-signatures-row');" style="margin-top: 10px; padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">评估 D8</button>
            </div>
        </div>
    </div>

    <div id="evaluation-page" class="page-content">
        <footer>
            <div class="container">
                <h1>8D报告标准</h1>
                <div class="evaluation-table">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 8%;">D 步骤</th>
                                <th style="width: 15%;">类别</th>
                                <th style="width: 20%;">不合格</th>
                                <th style="width: 20%;">合格 (最低)</th>
                                <th style="width: 20%;">优秀</th>
                                <th style="width: 7%;">得分<br>当前 / 最大</th>
                                <th style="width: 10%;">改进意见<br>8D评级理由</th>
                            </tr>
                        </thead>

                    <tbody>

                            <tr id="d4-occurrence-row">
                                <td>D4</td>
                                <td>根本原因 D4<br>(发生)</td>
                                <td>
                                    <label><input type="radio" name="d4-occurrence" value="0"> (0) 根本原因或假设不充分</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d4-occurrence" value="1.5"> (+1.5) 仅发现TRC，并附加直接原因</label><br>
                                    <label><input type="radio" name="d4-occurrence" value="3"> (+3) OK. 发现TRC + SRC，并附加直接原因</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d4-occurrence" value="4"> (+1) 所有可能性已确认 (石川图，5个为什么已完成/附上)</label>
                                </td>
                                <td><span class="score" data-max="4.0">0.0 / 4.0</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr id="d5-effectiveness-row">
                                <td>D5</td>
                                <td>永久纠正措施 D5<br>(有效性)</td>
                                <td>
                                    <label><input type="radio" name="d5-effectiveness" value="0"> (0) 纠正措施无效或未验证</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d5-effectiveness" value="1.5"> (+1.5) 纠正措施有效性已验证</label><br>
                                    <label><input type="radio" name="d5-effectiveness" value="3"> (+3) OK. 纠正措施有效性已验证并有数据支持</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d5-effectiveness" value="4"> (+1) 纠正措施有效性已验证并有数据支持，且有预防措施</label>
                                </td>
                                <td><span class="score" data-max="4.0">0.0 / 4.0</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr id="d6-occurrence-row">
                                <td>D6</td>
                                <td>纠正措施 D6<br>(发生)</td>
                                <td>
                                    <label><input type="radio" name="d6-occurrence" value="0"> (0) 针对不合格项的纠正措施不充分，缺乏</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-occurrence" value="1"> (+1) 仅针对TRC的有效纠正措施</label><br>
                                    <label><input type="radio" name="d6-occurrence" value="2"> (+2) OK. 针对TRC + SRC的有效纠正措施</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-occurrence" value="3"> (+1) 相关附加数据 (例如照片、草图、防错等)</label>
                                </td>
                                <td><span class="score" data-max="3.0">0.0 / 3.0</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>

                            <tr id="d6-non-detection-row">
                                <td>D6</td>
                                <td>纠正措施 D6<br>(未检测)</td>
                                <td>
                                    <label><input type="radio" name="d6-non-detection" value="0"> (0) 差，不清晰，不健全，无时间点，无实际行动证据</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-non-detection" value="1"> (+1) 仅针对TRC的有效纠正措施</label><br>
                                    <label><input type="radio" name="d6-non-detection" value="2"> (+2) OK. 针对TRC + SRC的有效纠正措施</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-non-detection" value="3"> (+1) 相关附加数据 (例如照片、草图、防错等)</label>
                                </td>
                                <td><span class="score" data-max="3.0">0.0 / 3.0</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr id="d6-confirmation-row">
                                <td>D6</td>
                                <td>纠正措施确认 D6</td>
                                <td>
                                    <label><input type="radio" name="d6-confirmation" value="0"> (0) 空白或“弱”方法</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-confirmation" value="2"> (+2) OK. 有效纠正措施的解释</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d6-confirmation" value="0.5"> (+0.5) 有效方法并附有备用数据 (例如检查表)</label>
                                </td>
                                <td><span class="score" data-max="2.5">0.0 / 2.5</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr id="d7-updates-row">
                                <td>D7</td>
                                <td>质量管理体系更新<br>(PQP, FMEA等)</td>
                                <td>
                                    <label><input type="radio" name="d7-updates" value="0"> (0) 空白</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d7-updates" value="1"> (+1) FMEA已更新，吸取了关于其他流程、产品、地点等的经验教训</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d7-updates" value="1.5"> (+0.5) 如果适用，解释CP和/或FMEA如何更新或CN/CoC如何识别经验教训</label>
                                </td>
                                <td><span class="score" data-max="1.5">0.0 / 1.5</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr id="d8-signatures-row">
                                <td>D8</td>
                                <td>签名</td>
                                <td>
                                    <label><input type="radio" name="d8-signatures" value="0"> (0) 无经理签字</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d8-signatures" value="1"> (+1) 经理签字</label>
                                </td>
                                <td>
                                    <label><input type="radio" name="d8-signatures" value="0.5"> (+0.5) 额外签字 (例如生产经理、工厂经理等)</label>
                                </td>
                                <td><span class="score" data-max="1.5">0.0 / 1.5</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                            <tr>
                                <td>-</td>
                                <td>其他附件/报告</td>
                                <td>
                                    <label><input type="radio" name="attachments" value="0"> (0) 不相关或未附文件</label>
                                </td>
                                <td>不适用</td>
                                <td>
                                    <label><input type="radio" name="attachments" value="0.5"> (+0.5) 附有相关文件 (例如分析数据表等)</label>
                                </td>
                                <td><span class="score" data-max="0.5">0.0 / 0.5</span></td>
                                <td><textarea class="comment" placeholder="可选"></textarea></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="summary-container">
                        <div class="hint-section">
                            <h3>提示</h3>
                            <ul>
                                <li>TRC 表示“技术根本原因” / SRC 表示“系统根本原因”，也称为“管理根本原因”</li>
                                <li>8D 报告必须是一份易于理解的独立文件，描述根本原因的发现、理解和错误的消除。</li>
                                <li>必须让客户相信所有可能性都经过分析，已采取适当措施，并且为客户调查提供的任何提示都符合他们的最佳利益，以消除错误。</li>
                            </ul>
                        </div>
                        <div class="score-summary">
                            总分: <span id="total-score">0.0</span> / <span id="max-total-score">20.0</span><br>
                            百分比: <span id="percentage">0.0%</span>
                        </div>
                        <div class="overall-comments">
                            总体/一般评论:<br>
                            <textarea rows="3" style="width: 100%;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

</body>
    <script>
        function showPage(pageId) {
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => {
                page.style.display = 'none';
            });
            document.getElementById(pageId).style.display = 'block';
        }

        function highlightEvaluation(rowId) {
            const row = document.getElementById(rowId);
            if (row) {
                row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                row.style.backgroundColor = '#ffffcc'; // Highlight color
                setTimeout(() => {
                    row.style.backgroundColor = ''; // Remove highlight after a short delay
                }, 3000);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            showPage('main-8d-page'); // Show the 8D problem solving steps page by default

            const radioButtons = document.querySelectorAll('.evaluation-table input[type="radio"]');

            radioButtons.forEach(radio => {
                radio.addEventListener('change', calculateScore);
            });
            const totalScoreSpan = document.getElementById('total-score');
            const maxTotalScoreSpan = document.getElementById('max-total-score');
            const percentageSpan = document.getElementById('percentage');

            let maxPossibleScore = 0;
            document.querySelectorAll('.evaluation-table .score').forEach(span => {
                maxPossibleScore += parseFloat(span.dataset.max);
            });
            maxTotalScoreSpan.textContent = maxPossibleScore.toFixed(1);

            calculateScore(); // Initial calculation on page load

            function calculateScore() {
                let currentTotalScore = 0;
                const categoryScores = {};

                radioButtons.forEach(radio => {
                    if (radio.checked) {
                        const name = radio.name;
                        let value = parseFloat(radio.value);

                        // Check if the selected option is 'Excellent' and set score to max if it is
                        // The 'Excellent' column is the 5th column (index 4) in the table
                        if (radio.closest('td').cellIndex === 4) {
                            const scoreSpan = radio.closest('tr').querySelector('.score');
                            if (scoreSpan) {
                                value = parseFloat(scoreSpan.dataset.max);
                            }
                        }
                        categoryScores[name] = value;
                    }
                });

                for (const name in categoryScores) {
                    currentTotalScore += categoryScores[name];
                }

                totalScoreSpan.textContent = currentTotalScore.toFixed(1);
                const percentage = (currentTotalScore / maxPossibleScore) * 100;
                percentageSpan.textContent = percentage.toFixed(1) + '%';

                // Update individual score spans
                document.querySelectorAll('.evaluation-table tbody tr').forEach(row => {
                    const categoryName = row.querySelector('input[type="radio"]').name;
                    const scoreSpan = row.querySelector('.score');
                    if (scoreSpan) {
                        const maxScore = parseFloat(scoreSpan.dataset.max);
                        const currentCategoryScore = categoryScores[categoryName] || 0;
                        scoreSpan.textContent = `${currentCategoryScore.toFixed(1)} / ${maxScore.toFixed(1)}`;
                    }
                });
            }
        });
    </script>
</html>