<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制计划</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: #f4f4f4;
            font-size: 10pt;
        }

        .container {
            width: 297mm; /* A4 width */
            min-height: 210mm; /* A4 height */
            margin: 10mm auto;
            background-color: #fff;
            padding: 5mm;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
            overflow-x: auto;
        }

        h1 {
            text-align: center;
            color: #333;
            font-size: 1.5em;
            margin-top: 0;
            margin-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
            font-size: 10pt;
            table-layout: fixed;
        }

        th, td {
            border: 1px solid #000;
            padding: 1px 2px;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
        }

        th {
            background-color: #e9e9e9;
            font-weight: bold;
            text-align: center;
        }

        .header-row th {
            background-color: #ADD8E6; /* Light Blue */
        }

        .sub-header-row th {
            background-color: #ADD8E6; /* Light Blue */
        }

        input[type="text"] {
            width: calc(100% - 4px); /* Adjust width for padding and border */
            border: 1px solid #ddd;
            padding: 1px;
            box-sizing: border-box;
            font-size: 10pt;
        }

        input[type="checkbox"] {
            margin-right: 3px;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            height: 100%;
        }

        .checkbox-group label {
            white-space: nowrap;
            margin-right: 10px;
        }

        .top-left-text {
            font-size: 0.8em;
            text-align: left;
            padding-left: 2px;
        }

        .small-text {
            font-size: 0.7em;
            color: darkred;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="controlPlanTable"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tableContainer = document.getElementById('controlPlanTable');
            let tableHTML = '<table>';

            // Row 1: AIAG CP 2024 1st and Control Plan Title
            tableHTML += '<td colspan="2" class="top-left-text">AIAG CP 2024 1st</td>';
            tableHTML += '<td colspan="20" style="text-align: center;"><h1>控制计划</h1></td>';
            tableHTML += '</tr>';

            // Row 2: Stage Checkboxes
            tableHTML += '<tr>';
            tableHTML += '<td colspan="2">阶段</td>';
            tableHTML += '<td colspan="20">';
            tableHTML += '<input type="checkbox" id="prototype" name="stage" value="原型样件"> <label for="prototype">原型样件</label>';
            tableHTML += '<input type="checkbox" id="pre_launch" name="stage" value="试生产"> <label for="pre_launch">试生产</label>';
            tableHTML += '<input type="checkbox" id="production" name="stage" value="生产"> <label for="production">生产</label>';
            tableHTML += '<input type="checkbox" id="safe_launch" name="stage" value="安全投产"> <label for="safe_launch">安全投产</label>';
            tableHTML += '<span class="small-text">(如果“试生产”或“生产控制计划”中包含“安全投产”,请选中其两个框)</span>';
            tableHTML += '</td>';
            tableHTML += '</tr>';

            // Row 3: Control Plan No., Key Contact, Date (Initial), Date (Revision)
            tableHTML += '<tr>';
            tableHTML += '<td colspan="2">控制计划编号</td>';
            tableHTML += '<td colspan="4"><input type="text" value=""></td>';
            tableHTML += '<td colspan="3">关键联系人/电话</td>';
            tableHTML += '<td colspan="3"><input type="text" value=""></td>';
            tableHTML += '<td colspan="4">日期(初始)</td>';
            tableHTML += '<td colspan="6"><input type="text" value=""></td>';
            tableHTML += '</tr>';

            // Row 4: Part No./Rev. Level, Core Team, Customer Eng. Approval/Date
            tableHTML += '<tr>';
            tableHTML += '<td colspan="2">零件编号/最新更改等级</td>';
            tableHTML += '<td colspan="4"><input type="text" value=""></td>';
            tableHTML += '<td colspan="3">核心小组</td>';
            tableHTML += '<td colspan="3"><input type="text" value=""></td>';
            tableHTML += '<td colspan="4">顾客工程批准/日期(如需要)</td>';
            tableHTML += '<td colspan="6"><input type="text" value=""></td>';
            tableHTML += '</tr>';

            // Row 5: Part Name/Description, Org./Plant Approval/Date, Customer Quality Approval/Date
            tableHTML += '<tr>';
            tableHTML += '<td colspan="2">零件名称/描述</td>';
            tableHTML += '<td colspan="4"><input type="text" value=""></td>';
            tableHTML += '<td colspan="3">组织/工厂批准/日期</td>';
            tableHTML += '<td colspan="3"><input type="text" value=""></td>';
            tableHTML += '<td colspan="4">顾客质量批准/日期(如需要)</td>';
            tableHTML += '<td colspan="6"><input type="text" value=""></td>';
            tableHTML += '</tr>';

            // Row 6: Organization/Plant, Org. Code, Other Approval Date (if req'd) x2
            tableHTML += '<tr>';
            tableHTML += '<td colspan="2">组织/工厂</td>';
            tableHTML += '<td colspan="4"><input type="text" value=""></td>';
            tableHTML += '<td colspan="3">组织代码</td>';
            tableHTML += '<td colspan="3"><input type="text" value=""></td>';
            tableHTML += '<td colspan="4">其他批准日期(如需要)</td>';
            tableHTML += '<td colspan="6"><input type="text" value=""></td>';
            tableHTML += '</tr>';

            // Main Header Row
            tableHTML += '<tr class="header-row">';
            tableHTML += '<th rowspan="2" colspan="2">零件/<br>过程编号</th>';
            tableHTML += '<th rowspan="2" colspan="2">过程名称/<br>操作描述</th>';
            tableHTML += '<th rowspan="2" colspan="2">制造用机器/<br>装置/夹具/工具</th>';
            tableHTML += '<th colspan="10">特性</th>';
            tableHTML += '<th colspan="4">方法</th>';
            tableHTML += '<th colspan="4">反应计划</th>';
            tableHTML += '</tr>';

            // Sub-Header Row
            tableHTML += '<tr class="sub-header-row">';
            tableHTML += '<th>编号</th>';
            tableHTML += '<th>产品</th>';
            tableHTML += '<th>过程</th>';
            tableHTML += '<th>特殊特性<br>分类</th>';
            tableHTML += '<th colspan="2">产品/过程<br>规范/公差</th>';
            tableHTML += '<th>评价/测量<br>技术</th>';
            tableHTML += '<th>样本量</th>';
            tableHTML += '<th>频率</th>';
            tableHTML += '<th>责任者</th>';
            tableHTML += '<th>控制方法</th>';
            tableHTML += '<th>措施</th>';
            tableHTML += '<th colspan="3">所有者/<br>责任人</th>';
            tableHTML += '</tr>';

            // Data Rows (10 empty rows for now)
            for (let i = 0; i < 10; i++) {
                tableHTML += '<tr>';
                tableHTML += '<td colspan="2"><input type="text" value=""></td>'; // 零件/过程编号
                tableHTML += '<td colspan="2"><input type="text" value=""></td>'; // 过程名称/操作描述
                tableHTML += '<td colspan="2"><input type="text" value=""></td>'; // 制造用机器/装置/夹具/工具
                tableHTML += '<td><input type="text" value=""></td>'; // 编号
                tableHTML += '<td><input type="text" value=""></td>'; // 产品
                tableHTML += '<td><input type="text" value=""></td>'; // 过程
                tableHTML += '<td><input type="text" value=""></td>'; // 特殊特性分类
                tableHTML += '<td colspan="2"><input type="text" value=""></td>'; // 产品/过程规范/公差
                tableHTML += '<td><input type="text" value=""></td>'; // 评价/测量技术
                tableHTML += '<td><input type="text" value=""></td>'; // 样本量
                tableHTML += '<td><input type="text" value=""></td>'; // 频率
                tableHTML += '<td><input type="text" value=""></td>'; // 责任者
                tableHTML += '<td><input type="text" value=""></td>'; // 控制方法
                tableHTML += '<td><input type="text" value=""></td>'; // 措施
                tableHTML += '<td colspan="3"><input type="text" value=""></td>'; // 所有者/责任人
                tableHTML += '</tr>';
            }

            tableHTML += '</table>';
            tableContainer.innerHTML = tableHTML;
        });
    </script>
</body>
</html>