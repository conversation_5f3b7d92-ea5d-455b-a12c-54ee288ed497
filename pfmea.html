<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>过程失效模式及后果分析（PFMEA）- SAE J1739:2021</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: #fff;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 100vw;
            margin: 0 auto;
            padding: 10px 20px 30px 20px;
        }
        .main-title {
            text-align: center;
            font-size: 26px;
            font-weight: bold;
            color: #222;
            margin: 18px 0 8px 0;
            letter-spacing: 2px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }
        .info-table th, .info-table td {
            border: 1px solid #888;
            padding: 4px 8px;
            font-size: 14px;
            background: #f5f5f5;
        }
        .info-table th {
            background: #e0e0e0;
            font-weight: normal;
            text-align: left;
        }
        .info-table tr:nth-child(even) td {
            background: #fff;
        }
        .info-table input {
            width: 98%;
            border: none;
            background: transparent;
            font-size: 14px;
        }
        .remark-box {
            border: 1px solid #888;
            margin-bottom: 10px;
            padding: 6px 10px;
            background: #fff;
            font-size: 13px;
        }
        .pfmea-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
        }
        .pfmea-table th, .pfmea-table td {
            border: 1px solid #888;
            padding: 3px 6px;
            font-size: 13px;
            text-align: center;
        }
        .pfmea-table th {
            font-weight: normal;
        }
        .group-header {
            font-weight: bold;
            color: #222;
            border: 1.5px solid #888;
        }
        .bg-light-orange {
            background: #ffe5b4;
        }
        .bg-orange {
            background: #ff9900;
            color: #fff;
        }
        .bg-light-green {
            background: #e6f3d7;
        }
        .bg-green {
            background: #00b050;
            color: #fff;
        }
        .bg-yellow {
            background: #ffff00;
        }
        .bg-red {
            background: #ff0000;
            color: #fff;
        }
        .risk-M { background: #ffc000; }
        .risk-H { background: #ff0000; color: #fff; }
        .risk-L { background: #92d050; }
        .add-btn, .batch-btn {
            background: #0070c0;
            color: #fff;
            border: none;
            padding: 6px 18px;
            margin: 8px 8px 8px 0;
            border-radius: 3px;
            font-size: 15px;
            cursor: pointer;
        }
        .add-btn:hover, .batch-btn:hover {
            background: #005a9e;
        }
        .del-btn {
            background: #e81123;
            color: #fff;
            border: none;
            border-radius: 2px;
            padding: 2px 10px;
            cursor: pointer;
            font-size: 13px;
        }
        .del-btn:hover {
            background: #b50d1b;
        }
        input[type="text"] {
            width: 98%;
            border: none;
            background: transparent;
            font-size: 13px;
            text-align: center;
        }
        input[type="number"] {
            width: 16px;
            border: none;
            background: transparent;
            font-size: 13px;
            text-align: center;
        }
        /* S、O、D、SOD极致收缩专用样式 */
        .pfmea-table th.sodcol, .pfmea-table td.sodcol {
            width: 18px !important;
            min-width: 18px !important;
            max-width: 18px !important;
            padding: 0 !important;
            overflow: hidden !important;
            white-space: nowrap !important;
            text-align: center !important;
        }
        .pfmea-table input.sodinput {
            width: 14px !important;
            min-width: 14px !important;
            max-width: 14px !important;
            padding: 0 !important;
            border: none !important;
            font-size: 10px !important;
            box-sizing: border-box !important;
            overflow: hidden !important;
            text-align: center !important;
            background: transparent !important;
        }
        @media (max-width: 1200px) {
            .pfmea-table, .info-table { font-size: 12px; }
        }
    </style>
</head>
<body>
<div class="container" id="mainApp">
    <div class="main-title">PFMEA管理系统</div>
    <div id="projectSection">
        <div style="margin-bottom:10px;">
            <input id="newProjectName" type="text" placeholder="输入新项目名称" style="width:180px;">
            <button onclick="addProject()" class="add-btn">新建项目</button>
        </div>
        <table class="info-table" style="margin-bottom:18px;">
            <tr><th>项目名称</th><th>操作</th></tr>
            <tbody id="projectList"></tbody>
    </table>
    </div>
    <div id="pfmeaDetail" style="display:none;"></div>
</div>
<script>
    function getRiskClass(val) {
        if (val === 'H') return 'risk-H';
        if (val === 'M') return 'risk-M';
        if (val === 'L') return 'risk-L';
        return '';
    }
    // 风险优先级判定函数，简化版，按图片主要区间逻辑
    function calcRiskLevel(S, O, D) {
        if (S >= 9) {
            if (O >= 8 && D >= 7) return 'H';
            if (O >= 8 && D >= 5) return 'H';
            if (O >= 8 && D >= 2) return 'M';
            if (O >= 8) return 'L';
            if (O >= 6 && D >= 7) return 'H';
            if (O >= 6 && D >= 5) return 'H';
            if (O >= 6 && D >= 2) return 'M';
            if (O >= 6) return 'L';
            if (O >= 4 && D >= 7) return 'H';
            if (O >= 4 && D >= 5) return 'M';
            if (O >= 4 && D >= 2) return 'L';
            if (O >= 2 && D >= 7) return 'M';
            if (O >= 2 && D >= 5) return 'L';
            if (O >= 2 && D >= 2) return 'L';
        }
        if (S >= 7) {
            if (O >= 8 && D >= 7) return 'H';
            if (O >= 8 && D >= 5) return 'H';
            if (O >= 8 && D >= 2) return 'M';
            if (O >= 8) return 'L';
            if (O >= 6 && D >= 7) return 'H';
            if (O >= 6 && D >= 5) return 'M';
            if (O >= 6 && D >= 2) return 'L';
            if (O >= 4 && D >= 7) return 'M';
            if (O >= 4 && D >= 5) return 'L';
            if (O >= 2 && D >= 7) return 'L';
        }
        if (S >= 4) {
            if (O >= 8 && D >= 7) return 'H';
            if (O >= 8 && D >= 5) return 'H';
            if (O >= 8 && D >= 2) return 'M';
            if (O >= 8) return 'L';
            if (O >= 6 && D >= 7) return 'H';
            if (O >= 6 && D >= 5) return 'M';
            if (O >= 6 && D >= 2) return 'L';
            if (O >= 4 && D >= 7) return 'M';
            if (O >= 4 && D >= 5) return 'L';
            if (O >= 2 && D >= 7) return 'L';
        }
        if (S >= 2) {
            if (O >= 8 && D >= 7) return 'L';
            if (O >= 8 && D >= 5) return 'L';
            if (O >= 8 && D >= 2) return 'L';
            if (O >= 6 && D >= 7) return 'L';
            if (O >= 6 && D >= 5) return 'L';
            if (O >= 6 && D >= 2) return 'L';
            if (O >= 4 && D >= 7) return 'L';
            if (O >= 4 && D >= 5) return 'L';
            if (O >= 2 && D >= 7) return 'L';
        }
        return 'L';
    }
    function updateSODandRisk(tr, sIdx, oIdx, dIdx, sodIdx, riskIdx) {
        const s = Number(tr.children[sIdx].querySelector('input').value) || 0;
        const o = Number(tr.children[oIdx].querySelector('input').value) || 0;
        const d = Number(tr.children[dIdx].querySelector('input').value) || 0;
        // SOD自动计算
        const sod = s * o * d;
        const sodInput = tr.children[sodIdx].querySelector('input');
        sodInput.value = sod > 0 ? sod : '';
        // 风险优先级自动判定
        const riskSel = tr.children[riskIdx].querySelector('select');
        const riskLevel = calcRiskLevel(s, o, d);
        riskSel.value = riskLevel;
        riskSel.className = getRiskClass(riskLevel);
    }
    function addRow(data) {
        const tbody = document.getElementById('pfmeaTbody');
        const tr = document.createElement('tr');
        const fields = [
            'item', 'process', 'requirement', 'failureMode', 'failureEffect',
            'failureCause', 'controlP', 'controlD',
            'S', 'O', 'D', 'SOD', 'risk', 'prodChar', 'procChar',
            'action', 'respDate', 'actionTaken', 'S2', 'O2', 'D2', 'risk2'
        ];
        // 各栏位宽度与表头一致
        const colStyles = [
            'width:80px;', 'width:120px;', 'width:80px;', 'width:150px;', 'width:150px;', 'width:150px;',
            'width:120px;', 'width:120px;',
            'width:18px;text-align:center;', 'width:18px;text-align:center;', 'width:18px;text-align:center;', 'width:18px;text-align:center;',
            'width:60px;', 'width:60px;', 'width:60px;',
            'width:120px;', 'width:120px;', 'width:120px;',
            'width:18px;text-align:center;', 'width:18px;text-align:center;', 'width:18px;text-align:center;',
            'width:60px;', 'width:60px;'
        ];
        let sIdx = 8, oIdx = 9, dIdx = 10, sodIdx = 11, riskIdx = 12;
        for (let i = 0; i < fields.length; i++) {
            let td = document.createElement('td');
            td.setAttribute('style', colStyles[i]);
            if (fields[i] === 'risk' || fields[i] === 'risk2') {
                const sel = document.createElement('select');
                sel.innerHTML = '<option value="L">L</option><option value="M">M</option><option value="H">H</option>';
                sel.className = getRiskClass(data ? data[fields[i]] : 'L');
                sel.value = data ? data[fields[i]] : 'L';
                sel.disabled = true; // 只读
                td.appendChild(sel);
            } else if (["S", "O", "D", "S2", "O2", "D2"].includes(fields[i])) {
                const inp = document.createElement('input');
                inp.type = 'text';
                inp.inputMode = 'numeric';
                inp.setAttribute('maxlength','2');
                inp.setAttribute('style','width:14px;min-width:14px;max-width:14px;padding:0;border:none;font-size:10px;box-sizing:border-box;overflow:hidden;text-align:center;background:transparent;');
                inp.value = data ? data[fields[i]] || '' : '';
                td.appendChild(inp);
            } else if (fields[i] === 'SOD') {
                const sodSpan = document.createElement('span');
                sodSpan.className = 'sod-span';
                sodSpan.setAttribute('style','display:inline-block;width:14px;text-align:center;font-size:10px;');
                sodSpan.innerText = data ? data[fields[i]] || '' : '';
                td.appendChild(sodSpan);
            } else {
                const inp = document.createElement('input');
                inp.type = 'text';
                inp.value = data ? data[fields[i]] || '' : '';
                td.appendChild(inp);
            }
            tr.appendChild(td);
        }
        // 事件监听：S、O、D变化时自动计算
        const sInput = tr.children[sIdx].querySelector('input');
        const oInput = tr.children[oIdx].querySelector('input');
        const dInput = tr.children[dIdx].querySelector('input');
        sInput.oninput = oInput.oninput = dInput.oninput = function() {
            // 自动计算SOD
            const s = Number(sInput.value) || 0;
            const o = Number(oInput.value) || 0;
            const d = Number(dInput.value) || 0;
            const sod = s * o * d;
            tr.children[sodIdx].querySelector('.sod-span').innerText = sod > 0 ? sod : '';
            updateSODandRisk(tr, sIdx, oIdx, dIdx, sodIdx, riskIdx);
        };
        // 初始化时也计算一次
        setTimeout(() => {
            const s = Number(sInput.value) || 0;
            const o = Number(oInput.value) || 0;
            const d = Number(dInput.value) || 0;
            const sod = s * o * d;
            tr.children[sodIdx].querySelector('.sod-span').innerText = sod > 0 ? sod : '';
            updateSODandRisk(tr, sIdx, oIdx, dIdx, sodIdx, riskIdx);
        }, 0);
        // 删除按钮
        const tdDel = document.createElement('td');
        const btn = document.createElement('button');
        btn.innerText = '删除';
        btn.className = 'del-btn';
        btn.onclick = function() { tbody.removeChild(tr); };
        tdDel.appendChild(btn);
        tr.appendChild(tdDel);
        tbody.appendChild(tr);
    }
    function batchAddRows() {
        for (let i = 0; i < 5; i++) addRow();
    }
    // 本地存储相关
    function getProjects() {
        return JSON.parse(localStorage.getItem('pfmeaProjects')||'[]');
    }
    function saveProjects(list) {
        localStorage.setItem('pfmeaProjects', JSON.stringify(list));
    }
    function renderProjectList() {
        const list = getProjects();
        const tbody = document.getElementById('projectList');
        tbody.innerHTML = '';
        list.forEach((p,i)=>{
            const tr = document.createElement('tr');
            tr.innerHTML = `<td style='cursor:pointer;color:#0070c0;' onclick='openProject(${i})'>${p.name}</td><td><button class='del-btn' onclick='deleteProject(${i})'>删除</button></td>`;
            tbody.appendChild(tr);
        });
    }
    function addProject() {
        const name = document.getElementById('newProjectName').value.trim();
        if(!name) { alert('请输入项目名称'); return; }
        const list = getProjects();
        list.push({name, data:null});
        saveProjects(list);
        renderProjectList();
        document.getElementById('newProjectName').value = '';
    }
    function deleteProject(idx) {
        if(!confirm('确定删除该项目？')) return;
        const list = getProjects();
        list.splice(idx,1);
        saveProjects(list);
        renderProjectList();
    }
    function openProject(idx) {
        const projects = getProjects();
        const project = projects[idx];
        document.getElementById('projectSection').style.display = 'none';
        document.getElementById('pfmeaDetail').style.display = '';
        // 详情页内容
        document.getElementById('pfmeaDetail').innerHTML = `
    <div style='margin-bottom:10px;'>项目名称：<b>${project.name}</b></div>
    <div class="remark-box">本表源于SAE J1739:2021，详细信息可查看该标准</div>
    <button class="add-btn" onclick="addRow()">添加行</button>
    <button class="batch-btn" onclick="batchAddRows()">批量添加</button>
    <button class="add-btn" onclick="savePfmeaTable(${idx})">保存</button>
    <button class="add-btn" onclick="backToList()">返回项目列表</button>
    <div style="overflow-x:auto;max-width:100vw;">
    <table class="pfmea-table" id="pfmeaTable" style="table-layout:fixed;width:1512px;">
        <thead style="position:sticky;top:0;z-index:2;">
        <tr>
            <th colspan="6" style="background:#ffe5b4;font-weight:bold;">技术风险分析</th>
            <th colspan="2" style="background:#d9d9d9;font-weight:bold;">当前过程控制</th>
            <th colspan="7" style="background:#ff9900;color:#fff;font-weight:bold;">风险评估</th>
            <th colspan="3" style="background:#e6f3d7;font-weight:bold;">措施计划</th>
            <th colspan="5" style="background:#e6f3d7;font-weight:bold;">措施结果</th>
            <th style="background:#fff;"></th>
        </tr>
        <tr>
            <th style="width:80px;background:#ffe5b4;">项目(操作顺序)</th>
            <th style="width:120px;background:#ffe5b4;">过程功能</th>
            <th style="width:80px;background:#ffe5b4;">要求</th>
            <th style="width:150px;background:#ffe5b4;">潜在失效模式</th>
            <th style="width:150px;background:#ffe5b4;">潜在失效后果</th>
            <th style="width:150px;background:#ffe5b4;">潜在失效起因</th>
            <th style="width:120px;background:#d9d9d9;">当前过程控制-预防</th>
            <th style="width:120px;background:#d9d9d9;">当前过程控制-探测</th>
            <th style="width:18px;background:#ff9900;text-align:center;">S</th>
            <th style="width:18px;background:#ff9900;text-align:center;">O</th>
            <th style="width:18px;background:#ff9900;text-align:center;">D</th>
            <th style="width:18px;background:#ff9900;text-align:center;">SOD</th>
            <th style="width:60px;background:#ff9900;">风险优先级</th>
            <th style="width:60px;background:#ff9900;">产品特殊特性</th>
            <th style="width:60px;background:#ff9900;">过程特殊特性</th>
            <th style="width:120px;background:#e6f3d7;">建议措施</th>
            <th style="width:120px;background:#e6f3d7;">责任人及目标完成日期</th>
            <th style="width:120px;background:#e6f3d7;">采取的措施及实际完成日期</th>
            <th style="width:18px;background:#e6f3d7;text-align:center;">S</th>
            <th style="width:18px;background:#e6f3d7;text-align:center;">O</th>
            <th style="width:18px;background:#e6f3d7;text-align:center;">D</th>
            <th style="width:60px;background:#e6f3d7;">新的风险优先级</th>
            <th style="width:60px;background:#fff;">操作</th>
        </tr>
        </thead>
        <tbody id="pfmeaTbody"></tbody>
    </table>
    </div>
    `;
    // 渲染表格数据
    setTimeout(()=>{
        if(project.data && Array.isArray(project.data)) {
            project.data.forEach(row=>addRow(row));
        } else {
            addRow();
        }
    },0);
    // 绑定保存函数
    window.savePfmeaTable = function(idx) {
        const tbody = document.getElementById('pfmeaTbody');
        const rows = Array.from(tbody.children);
        const data = rows.map(tr=>{
            const tds = Array.from(tr.children);
            // 22列+操作
            return {
                item:tds[0].querySelector('input')?.value||'',
                process:tds[1].querySelector('input')?.value||'',
                requirement:tds[2].querySelector('input')?.value||'',
                failureMode:tds[3].querySelector('input')?.value||'',
                failureEffect:tds[4].querySelector('input')?.value||'',
                failureCause:tds[5].querySelector('input')?.value||'',
                controlP:tds[6].querySelector('input')?.value||'',
                controlD:tds[7].querySelector('input')?.value||'',
                S:tds[8].querySelector('input')?.value||'',
                O:tds[9].querySelector('input')?.value||'',
                D:tds[10].querySelector('input')?.value||'',
                SOD:tds[11].querySelector('input')?.value||'',
                risk:tds[12].querySelector('select')?.value||'',
                prodChar:tds[13].querySelector('input')?.value||'',
                procChar:tds[14].querySelector('input')?.value||'',
                action:tds[15].querySelector('input')?.value||'',
                respDate:tds[16].querySelector('input')?.value||'',
                actionTaken:tds[17].querySelector('input')?.value||'',
                S2:tds[18].querySelector('input')?.value||'',
                O2:tds[19].querySelector('input')?.value||'',
                D2:tds[20].querySelector('input')?.value||'',
                risk2:tds[21].querySelector('select')?.value||''
            };
        });
        const projects = getProjects();
        projects[idx].data = data;
        saveProjects(projects);
        alert('保存成功！');
    }
}
    function backToList() {
        document.getElementById('projectSection').style.display = '';
        document.getElementById('pfmeaDetail').style.display = 'none';
    }
    // 初始化
    window.onload = function() {
        renderProjectList();
        document.getElementById('pfmeaDetail').style.display = 'none';
    };
</script>
</body>
</html> 