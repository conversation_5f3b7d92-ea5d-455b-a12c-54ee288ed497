<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDA 6.3 P1 潜在供方分析</title>
    <style>
        :root {
            --primary-color: #203b55; /* 深蓝灰 */
            --secondary-color: #3498db; /* 亮蓝 */
            --accent-color: #e74c3c; /* 红色 */
            --success-color: #2ecc71; /* 绿色 */
            --warning-color: #f1c40f; /* 黄色 */
            --light-gray: #ecf0f1;
            --medium-gray: #bdc3c7;
            --dark-gray: #7f8c8d;
            --text-color: #333;
            --bg-color: #ffffff;
            --header-height: 60px;
            --footer-height: 50px;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--light-gray);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            font-size: 16px;
        }

        .app-header {
            background-color: var(--primary-color);
            color: white;
            padding: 0 20px;
            height: var(--header-height);
            display: flex;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .logo-img {
            height: 40px;
            width: 40px;
            margin-right: 10px;
            object-fit: contain;
            /* background-color: #fff; */ /* Removed for transparency */
            border-radius: 5px;
        }
        
        .company-name {
            font-size: 1.5em;
            font-weight: bold;
        }

        .nav-tabs {
            background-color: var(--secondary-color);
            padding: 0 20px;
            display: flex;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-wrap: nowrap;
            overflow-x: auto;
            white-space: nowrap;
        }
        .special-nav-tab {
            font-size: 2.5em;
            font-family: "微软雅黑", Arial, sans-serif;
            font-weight: normal;
        }
        .nav-tab.special-nav-tab {
            border: none;
            background: transparent;
            padding: 10px 20px;
            font-size: 18px;
            border-bottom: 3px solid transparent;
            transition: border-color 0.2s;
            color: #fff;
            outline: none;
        }
        .nav-tab.special-nav-tab.active,
        .nav-tab.special-nav-tab:hover {
            border-bottom: 3px solid #ffd600;
            color: #fff;
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <footer class="footer"></footer>
    <style>
        .nav-tab {
            padding: 12px 18px;
            cursor: pointer;
            color: white;
            border: none;
            background-color: transparent;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        .special-nav-tab {
            font-size: 1.2em !important;
            font-family: "微软雅黑", Arial, sans-serif;
            font-weight: normal;
        }

        .nav-tab:hover, .nav-tab.active {
            background-color: rgba(255,255,255,0.2);
        }

        .status-closed {
            background-color: var(--success-color);
            color: white;
        }

        .status-in-progress {
            background-color: var(--warning-color);
            color: var(--text-color);
        }

        .status-not-started {
            background-color: var(--accent-color);
            color: white;
            border-bottom: 3px solid var(--warning-color);
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page {
            display: none;
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }

        .page.active {
            display: block;
        }

        h2, h3 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 5px;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 0.95em; /* Slightly smaller font for tables */
        }

        th, td {
            border: 1px solid var(--medium-gray);
            padding: 8px; /* Adjusted padding */
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: var(--light-gray);
            font-weight: bold;
        }

        input[type="text"], input[type="date"], input[type="email"], input[type="tel"], textarea, select {
            width: calc(100% - 18px); /* Adjusted for padding and border */
            padding: 7px; /* Adjusted padding */
            border: 1px solid var(--medium-gray);
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 5px;
            font-size: 0.95em;
        }
        
        textarea {
            min-height: 50px; /* Adjusted min-height */
            resize: vertical;
        }

        button, .button-like {
            background-color: var(--secondary-color);
            color: white;
            padding: 8px 12px; /* Adjusted padding */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.95em; /* Adjusted font-size */
            margin: 5px 5px 5px 0;
            transition: background-color 0.3s;
        }

        button:hover, .button-like:hover {
            background-color: #2980b9; /* Darker blue */
        }
        
        .button-green { background-color: var(--success-color); }
        .button-green:hover { background-color: #27ae60; }
        .button-yellow { background-color: var(--warning-color); color: var(--text-color); }
        .button-yellow:hover { background-color: #f39c12; }
        .button-red { background-color: var(--accent-color); }
        .button-red:hover { background-color: #c0392b; }


        .question-block {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid var(--medium-gray);
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .question-header {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--primary-color);
        }
        .question-details {
            display: flex;
            gap: 12px;
            margin-bottom: 8px;
        }
        .min-req, .impl-ex {
            flex: 1;
            padding: 8px;
            background-color: #fff;
            border: 1px dashed var(--medium-gray);
            border-radius: 3px;
            font-size: 0.85em;
            white-space: pre-wrap;
        }
        .min-req h4, .impl-ex h4 { margin-top: 0; font-size: 0.95em; color: var(--dark-gray); }
        
        .project-evaluation-block {
            border: 1px solid #eee;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            background-color: #fff;
        }
        .project-evaluation-block h5 {
            margin-top: 0;
            margin-bottom: 5px;
            color: var(--secondary-color);
            font-size: 0.9em;
        }


        .evaluation-dropdown {
            padding: 7px;
            border-radius: 4px;
            border: 1px solid var(--medium-gray);
            font-weight: bold;
            min-width: 120px; /* Ensure dropdowns have some width */
        }
        .eval-g { background-color: #d4edda !important; color: #155724 !important; } 
        .eval-y { background-color: #fff3cd !important; color: #856404 !important; }
        .eval-r { background-color: #f8d7da !important; color: #721c24 !important; }
        .eval-ne { background-color: #e9ecef !important; color: #495057 !important; }

        .footer {
            background-color: #2c3e50;
            color: white;
            text-align: center;
            padding: 15px 0;
            height: var(--footer-height);
            box-sizing: border-box;
            font-size: 0.9em;
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100vw;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.08);
        }
        .footer a { color: var(--warning-color); text-decoration: none; }
        .footer a:hover { text-decoration: underline; }

        .form-group {
            margin-bottom: 12px;
        }
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: bold;
        }
        
        .matrix-table td { padding: 4px; }
        .matrix-table input, .matrix-table textarea, .matrix-table select { margin-bottom: 0; font-size: 0.9em;}

        .assessment-result-item {
            padding: 15px;
            border-radius: 5px;
            font-size: 1.1em;
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            min-height: 100px; /* 增加背景框高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .assessment-result-item.green { background-color: var(--success-color); color: white; }
        .assessment-result-item.yellow { background-color: var(--warning-color); color: var(--text-color); }
        .assessment-result-item.red { background-color: var(--accent-color); color: white; }
        .assessment-result-item.unknown { background-color: var(--light-gray); color: var(--text-color); }


        .star-question::after {
            content: '*';
            color: var(--accent-color);
            font-weight: bold;
            margin-left: 2px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .nav-tabs {
                 overflow-x: auto; 
                 white-space: nowrap;
        }
            .nav-tab {
                padding: 10px 12px; 
                font-size: 0.9em;
        }
            .special-nav-tab {
                font-size: 2.5em !important;
        }
            .nav-tab.active {
                border-bottom: 3px solid var(--warning-color);
        }
            .question-details {
                flex-direction: column;
        }
            .app-header {
                padding: 0 10px;
                height: auto; 
                flex-wrap: wrap;
        }
            .company-name { font-size: 1.2em; }
            .main-content {
                padding: 10px;
        }
            .page {
                padding: 10px;
        }
            input[type="text"], input[type="date"], input[type="email"], input[type="tel"], textarea, select {
                width: 100%; 
                box-sizing: border-box;
        }
             table, .matrix-table {
                display: block;
                overflow-x: auto; 
                white-space: nowrap;
        }
            .app-header div:last-child { 
                width: 100%;
                text-align: right;
                margin-top: 5px;
        }
        }
        
        .action-buttons button { margin-right: 8px; margin-bottom: 8px; }
        .hidden { display: none; }
        input[type="date"]::-webkit-datetime-edit-text,
        input[type="date"]::-webkit-datetime-edit-month-field,
        input[type="date"]::-webkit-datetime-edit-day-field,
        input[type="date"]::-webkit-datetime-edit-year-field {
            color: var(--text-color);
        }
        
        input[type="date"]::-webkit-calendar-picker-indicator {
            cursor: pointer;
        }
        
        /* 设置日期输入框的语言环境为英文 */
        input[type="date"] {
            -webkit-locale: "en";

        }

        input[type="date"]:valid:before,
        input[type="date"]:focus:before {
            display: none;
        }

        /* 悬浮保存按钮样式 */
        .floating-save-btn {
            position: fixed;
            bottom: 80px;
            right: 30px;
            width: 85px;
            height: 40px;
            background-color: var(--success-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1001;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            gap: 6px;
        }

        .floating-save-btn:hover {
            background-color: #27ae60;
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
        }

        .floating-save-btn:active {
            transform: scale(0.95);
        }

        .floating-save-btn .save-icon {
            font-size: 20px;
            margin-bottom: 0;
        }

        .floating-save-btn .save-text {
            font-size: 14px;
            line-height: 1;
            writing-mode: horizontal-tb;
            text-orientation: mixed;
            white-space: nowrap;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .floating-save-btn {
                bottom: 70px;
                right: 20px;
                width: 75px;
                height: 35px;
            }
            
            .floating-save-btn .save-icon {
                font-size: 16px;
            }
            
            .floating-save-btn .save-text {
                font-size: 11px;
                writing-mode: horizontal-tb;
                text-orientation: mixed;
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>

    <header class="app-header" style="background-color: var(--primary-color); color: white; padding: 0 20px; height: var(--header-height); display: flex; align-items: center; position: relative;">
        <div class="logo-container" id="logoContainer" style="flex:0 0 auto;display:flex;align-items:center;z-index:2;">
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALESURBVFhH7ZfNTttAEMf/F0SCSJWEEkJRQQH8AUpKaooKFF+AmqKmKogElRqgoqYkUAgqQvAJ8CFFSFBCj4HZZM9s7MSJ4tSgH3k023s2nz/nzNqYqRz/0V0ACRMAEiYAEiYAEiYAEiYAEiZcT3f39vb3y1s/Pz/fPzY3N/fbqampO2fPnn3v+vXrc+Pj4/eHh4efhIWEw2GvZDKZq1KpdK8f/f7+3iAuLq4B5vR0zK/3lIKn5+eYnZ0NaY9YLNYFCASu9eNfvpHkM8w9kglJJDY0NFA4HGYjQoJkZGTEd51f9gTz8vK+gKCjo4NUKhWXNguCVCoVsbKygqqqKoyMjODw8FDfQCAQkCgUCoVKLy8vUalUqKurYwNxpzMZFxcXQFNUFCGEZVmo29tbCAQCcLlciqZpH3qgK//+w4gAAB3J5OQkqNevXzeYjM5OTqDVanPj8fHxBvajgJeXF+Tn5wMyU1NTrVisy7+9jPzVjYyM9K9SqXRfXFxkI0KCRHZ6egpkHj313zM/efIEw8PDvN/e3rIFmDQYDAZGRkaClCcnJ0FXVxdvb2xsDDvGjY2NFoDBYGDoEXR0dFCtVoOUMBgM7J42GwZAIBCQCJG3t7d2wPQAwGazCQQClhV6uvf29vb34eHhr4mJCV1LSwvo7u4OyBgMBvC5BvPz830HgY6ODlKpVNxqMyIIBoOC4uJiKJfLoCiKQBAEBALoAGx5eXmAFFWKkNWyLGNvbwcQBOHBdV1HjLXJkKSpVquhWq2mLMsoGo1SFEVhGAbLsgRBkN83Wef3z1m03e8V7S6WZVm9XkcQBEEQBEEQhJSLxSISiQSZTMaqqipoNBoEgv99+X/zP4ZJgAQTJgASYAIkYAKQMAEoYQIQAkTACSECEAIhASGECEAIhAQQEhDCGAgJIISCDEgIBCEAImADEgIBCEQACEMAHiABCAAA+IAEIAACIAEIAACIAEIIAAAEiYAECAAAAABJRU5ErkJggg==" alt="Logo" class="logo-img" id="companyLogo" style="margin-right:8px;">
            <span class="company-name" id="companyNameDisplay" style="font-size:1.2em;">金马汽车部件有限公司</span>
            <input type="file" id="logoUpload" class="hidden" accept="image/*">
        </div>
        <div class="header-middle-title" style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);font-size:2em;font-weight:bold;color:#fff;white-space:nowrap;z-index:1;width:max-content;">VDA 6.3过层审核 潜在供方分析</div>
    </header>

    <nav class="nav-tabs" style="display: flex; align-items: center;">
        <button class="nav-tab special-nav-tab active" data-page="plan">审核计划</button>
        <button class="nav-tab special-nav-tab" data-page="header">基本信息</button>
        <button class="nav-tab special-nav-tab" data-page="questionnaire">P1提问表</button>
        <button class="nav-tab special-nav-tab" data-page="assessment">评估表</button>
        <button class="nav-tab special-nav-tab" data-page="actionPlan">措施计划</button>
        <button class="nav-tab special-nav-tab" data-page="checklist">审核清单</button>
        <button class="nav-tab special-nav-tab" data-page="manual">使用说明</button>
        <div class="nav-action-buttons" style="margin-left:auto; display: flex; align-items: center; gap: 10px;">
            <button id="saveDataBtn" class="button-green">保存数据</button>
            <button id="clearDataBtn" class="button-red">清除数据</button>
            <button id="loadDataBtn" class="button-yellow">加载数据</button>
            <button id="exportMarkdownBtn" class="button-blue">导出Markdown</button>
            <button id="exportExcelBtn" class="button-blue">导出Excel</button>
        </div>
    </nav>
<style>
.button-blue {
    background-color: #1976d2;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 1em;
    cursor: pointer;
    transition: background 0.2s;
    margin: 0 0 0 0;
}
.button-blue:hover {
    background-color: #125ea2;
}
</style>

    <main class="main-content">
        <section id="planPage" class="page active">
            <!-- 审核计划页面内容 -->
        </section>
        <section id="headerPage" class="page">
            <!-- 基本信息页面内容 -->
        </section>
        <section id="questionnairePage" class="page">
            <!-- P1提问表页面内容 -->
        </section>
        <section id="assessmentPage" class="page">
            <!-- 评估表页面内容 -->
        </section>
        <section id="actionPlanPage" class="page">
            <!-- 措施计划页面内容 -->
        </section>
        <section id="checklistPage" class="page">
            <!-- 审核清单页面内容 -->
        </section>
        <section id="manualPage" class="page">
            <!-- 使用说明页面内容 -->
        </section>
    </main>

    <footer class="footer">
    <div style="display:flex;align-items:center;justify-content:center;gap:32px; font-size: 1.1em;">
        <div style="white-space:nowrap;">版权所有 &copy; 吴志明</a> | 电话&amp;微信：13959240478 | <span style="font-family: 'KaiTi', '草书', 'Cursive', sans-serif; color: #FFFFFF !important; font-weight: bold; font-size: 1.2em;">弘扬匠心、传递知识、为企业创造价值！</span> </div>
    </div>
    <div id="slogan-img-preview" style="margin-top:8px;text-align:center;"></div>
</footer>

<!-- 悬浮保存按钮 -->
<button id="floatingSaveBtn" class="floating-save-btn" title="快速保存数据">
    <div class="save-icon">💾</div>
    <div class="save-text">保存</div>
</button>
    <script>
    function showSloganImg(event) {
        const file = event.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('slogan-img-preview').innerHTML = '<img src="' + e.target.result + '" style="max-width:400px;max-height:80px;">';
        };
        reader.readAsDataURL(file);
    }
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <script>
        // --- START OF VDA 6.3:2023 Questions Data (Chinese) ---
        const vdaQuestionsDataSource = { 
        "P2": {
            name: "P2: 项目管理",
            questions: [
                { id: "P2.1", text: "是否已建立了项目管理战略（包括项目组织机构）？", isStar: false, minReq: "存在项目管理的过程。\n确定了跨部门（官方翻译：跨学科）的项目组织机构。\n规定了项目团队成员的职责、能力和授权。\n将联络人信息通知顾客和供方。\n项目组织机构和相关升级管理满足顾客的要求。\n明确了升级标准（包括在供方管理中的升级），并且在存在偏差的时候采取措施。\n角色的定义符合开发方法和协作模型（敏捷型或非敏捷型）。\n考虑从正在进行的或以前的具有可比产品范围的项目中获得的经验（特别是经验教训）。\n识别、评价了项目风险，并通过适当的措施降低风险。", implEx: "定义项目经理/技术专家的角色、任务、能力以及责任\n多场所项目的项目接口\n项目组织机构图\n项目组的组成\n资质证明\n顾客对项目管理的特定要求\n如果是敏捷开发方法：定义升级机制（例：项目经理升级至产品负责人）\n定义升级流程中的联络人/决策者\n里程碑评价的记录，包括措施" },
                { id: "P2.2", text: "是否策划了项目实施需要的所有资源，且已经到位，并且报告了变更情况？", isStar: true, minReq: "基于项目协议，在资源策划中考虑了顾客要求。\n建立并落实了针对项目团队成员的资源规划。考虑了员工的工作负荷。\n当发生变更（截止期限、开发范围绩效等）时，那么就必须对资源策划开展评审和（必要时）进行调整。在资源策划中应特别关注关键路径。\n策划并批准了针对人员和设备需要的项目预算。\n项目组织机构发生变更时（与顾客的接口）需进行报告。\n已经策划了与软件相关活动的资源。", implEx: "资源策划的证明（考虑其他顾客项目）\n设备的资源策划（例如：开发试验台架、检验和实验室设施）\n软件开发活动的资源策划和软件项目管理" },
                { id: "P2.3", text: "是否编制了项目计划并与顾客达成一致，且落实了项目管理？", isStar: false, minReq: "项目计划满足顾客的特定要求。\n所有内部及顾客里程碑都应被完整的纳入项目计划。\n应评审项目计划中所定义的里程碑，检查所有计划的事项都已落实，并达到了要求的成熟度水平。\n如果产品特别需要法定授权程序，则项目计划中应包括程序的持续时间。\n项目计划发生变更时，应确保内部沟通。会对顾客产生影响的项目计划变更，则需要与顾客协商沟通。\n需要在升级过程（风险管理）中考虑对总体进度有影响的项目变更。\n来自项目计划的关键路径中应考虑相关的交付项。\n与质量相关的项目活动和采购活动必须是项目计划的组成部分。项目计划中可引用单独的详细计划。\n计划必须包括原型件和试生产件。\n应计划在各个对应时间段内的软件的范围。", implEx: "包含里程碑的项目计划\n有关技术和/或产品组的顾客特定要求\n顾客的项目计划\n顾客的截止日期\n顾客的里程碑\n顾客设定的目标（具体里程碑的测量）\n里程碑评估（评审）\n质量管理计划（例如：VDA MLA或APQP）\n特定国家或地区的认证要求（ECE、SAE、DOT、CCC、INMETRO、KBA等）\n关键生产线的法律法规批准过程（环境要求，或者其他）\nASPICE评估的策划，包括根据顾客规范定义的级别\n含里程碑的软件放行计划" },
                { id: "P2.4", text: "是否策划了与质量相关的项目活动，并监视了其符合情况？", isStar: false, minReq: "质量相关的项目活动满足顾客的特定要求。\n质量相关活动包含产品和过程保证措施。\n计划必须包含产品和过程要求的验证和确认。\n计划同样需要考虑关键零部件和供应项目（内部和外部供方）。\n应定期监控计划的符合性和目标的达成情况。\n已考虑与软件相关的质量相关活动。", implEx: "项目计划\n顾客的里程碑\n与质量计划相关的顾客要求\n质量管理计划（例如VDA MLA或APQP）\n审核和评价计划\n顾客规范\n特定的软件里程碑，例如：代码和模型评审\n创建和确认测试案例\n软件相关的KPI（指标、测试覆盖率、测试自动化水平、错误减少比例等）\n软件的质量批量/放行（SW-Q）\n风险评估（特殊特性、网络安全）" },
                { id: "P2.5", text: "项目的采购活动是否包含在项目计划中？", isStar: false, minReq: "在项目计划中应包含所有类型供方的供方选择和发包目标日期。\n计划中应包括生产线、机器、工具、测量和检验系统以及服务供方 （例如：开发、实验室、维护、软件）。\n计划中应包括发包目标日期、供方里程碑和放行的截止日期，并与整体计划相协调和匹配。", implEx: "项目计划\n里程碑计划\n决定是自制或外购" },
                { id: "P2.6", text: "项目组织是否确保了项目中的变更管理？", isStar: true, minReq: "项目中的变更管理需要满足顾客的特定要求。\n针对变更（供方、内部或者顾客发起的变更），应进行评价。需要时，调整项目计划。评价应包括对产品质量以及项目截止日期的风险评估。\n供方应主动参与到变更管理中。\n确保遵守规定的设计冻结。针对例外情况 顾客和供方应协商并记录。\n所有的变更必须记录。\n变更管理中，应规定顾客、内部和供方的负责人员。", implEx: "变更管理\n过程描述\n进度表\n变更表格\n产品和过程的变更历史\n变更评价\n变更批准\n软件：变更申请、变更管理\n因故障排除、架构和要求变化导致的变更" }
            ]
        },
        "P3": {
            name: "P3: 产品和过程开发的策划",
            questions: [
                { id: "P3.1", text: "是否明确了产品和过程的具体要求？", isStar: false, minReq: "确保明确了包括顾客要求以及法律和法规要求在内的应用在产品（和软件）上的功能性和非功能性要求。\n组织应识别并考虑与产品和过程相关的以往经验的要求。\n必须根据组织自身的要求、顾客要求、法律法规要求、制造技术以及产品目的/用途的基础上识别了特殊特性。\n顾客在供方和/或原材料选择方面的要求应被考虑在内。\n如果是顾客指定的供方（指定供方），应具备接口协议（三方协议）。\n顾客对于文档以及免费和开源软件（FOSS）放行的要求应被考虑在内。", implEx: "产品/生产过程开发：\n对于硬件和软件之间的接口要求（带有集成/嵌入式软件的产品）\n包括要求规范在内的询价及合同文件\n可追溯性方案\n订购和检验要求\n特性清单/参考样件\n包括功能安全特性在内的产品/过程特性\n采购条款\n物流要求（包装、JIT、JIS、托管）\n质量协议，包括QM特定要求\n时间计划\n互联网上的门户网站/信息平台\n经验教训\n环保、回收再利用要求\n能力要求\n对于放行的要求\n产品开发：\n规范、图纸\n软件规范\n生产过程开发：\n对于生产钱、工具、检验设备的要求以及生产和检测工位布局的要求\n关于搬运、包装、储存和标识的要求\n软件识别、软件配置以及确保软件正确安装的要求" },
                { id: "P3.2", text: "根据产品和生产过程的要求，是否对可行性进行了全面评估？", isStar: true, minReq: "采用跨学科程序评估可行性（包括潜在生产场所）。\n所有明确的产品和过程特殊要求(技术、功能、质量、物流、软件等)应针对可行性进行检查。\n在可行性研究中，应考虑物质和人力资源。\n可行性研究的结果应在提交报价前完成。\n应确保外购件的可行性。\n如果顾客要求无法被满足 应告知顾客 顾客可以在合同授予前批准该偏差。\n应具备生产线以及使用过程更新的程序刷写方案（如有要求）。", implEx: "产品/生产过程开发：\n顾客要求和标准\n时间安排，时间框架\n法律、标准、法规、环境影响\n产品责任要求\n可追溯性方案\n建筑，空间\n计算机辅助制造、计算机辅助质量\n产品/过程创新\n跨学科的可行性研究 （例如：销售、开发、采购、生产计划、生产、质量管理策划、物流）\n产品开发：\n实验室/试验设备\n并行软件开发 / 原型开发\n生产过程开发：\n产能监控\n材料到位情况\n制造选择、制造地点\n生产线、工具、生产/检验设备、辅料、实验室设施、运输方式、容器、存储\n变型（或翻译变体）管理，刷写方案" },
                { id: "P3.3", text: "是否详细策划了产品和生产过程开发的活动？", isStar: false, minReq: "在产品和生产过程开发策划时应根据零部件、软件和过程的复杂程度考虑策划的详细程度。\n在开发阶段，为保证产品和生产过程的开发，应使用合适的降低风险的方法以确保产品进入批量生产时满足要求的使用条件（功能 可靠性 安全性）。\n在产品和过程创新的情况下，应有一个应变方案。\n风险分析应是策划的一部分。\n检验策划方案包括对于批量生产、产品审核和再鉴定的要求。（再鉴定[官方翻译再评定]在VDA 6.5都已经改为全尺寸检验了，为什么这里没有改？）。\n时间表应包含所有产品和生产过程开发的信息（包括时间期限和持续时间、符合整体项目计划的里程碑、性能测试、产品和过程批准时间、软件标准等）。\n开发放行的方法和证据应符合顾客要求并且在出现偏差时寻求与顾客的澄清。\n软件工程过程已经被明确规定并且符合顾客要求。\n软件开发进程也应在策划中被考虑，以确保要求的软件功能已经测试并且最终在要求的时间点可用。", implEx: "产品/生产过程开发：\n整体项目计划 \n顾客要求\n顾客时间安排\n交货周期\n包括备选策略和网络安全在内的降低风险的方法（QFD、FMEA、HARA等）\n原型件/试生产策划\n定期检查开发进度状态（评审）\n针对投资（设施和生产线）的项目计划\n针对产品和过程开发全阶段的物流策划，包括包装\n备件方案\n产品开发：\n可靠性试验、功能试验、试生产计划\n开发阶段样件的截止日期\n要求分析\n架构设计\n实施\n测试（例如产品确认）\n敏捷项目管理的组件（产品待办列表、迭代待办列表、增量、就绪定义、完成定义、迭代计划，开发和运营)\n放行计划\n生产过程开发：\n工具截止日期（量产工具制造的零件） \n检验策划、检验设备策划、包括备件管理的维护保养策划" },
                { id: "P3.4", text: "是否计划了采购活动，并监视了其符合情况？", isStar: true, minReq: "选择准则应被定义以确保所选供方具备质量能力。\n对非顾客指定的新供方/新生产场地或新技术已经计划或实施了潜在供方分析。\n授予合同后应根据采购产品/服务的风险等级确定活动策划的范围。\n对于顾客要求在整个供应链中沟通有明确的规则。\n顾客要求也包括图纸要求，零件、软件或零部件规范要求，交付数量要求，截止日期要求，质量协议要求以及适用法规要求。\n针对由顾客指定供方（指定的供方）的协议应基于具体项目进行定义。\n已经确定和计划了采购生产线、机器、工具、测量和检测设备以及服务的相关活动（选择、授予合同、验证和批准）。\n监控了供方活动（例如：授予合同、截止日期、顾客和供方的里程碑）的进度。", implEx: "产品/生产过程开发：\n供方选择准则\nVDA 6.3潜在供方分析和/或针对软件的类似方法\nVDA 6.3 审核策划\n供方管理（供应商开发、传递顾客要求）、PPA程序、失效分析、质量、保修、沟通\n接口协议（根据DIA或接口协议进行服务）\n包括服务供方（例如：开发、实验室、维护、软件）在内的项目供方清单\n获得供货范围内项目和活动的风险分级（VDA MLA）\n第三方软件\n免费和开源软件(FOSS ) 放行的准则" },
                { id: "P3.5", text: "针对产品和生产过程开发的策划，是否考虑了必要的资源？", isStar: false, minReq: "项目所需的资源已经确定并予以记录。\n已规划了针对原型件制造、样件生产、试生产、性能测试以及批量生产实施所需的产能。\n资源策划应定期根据项目的变化加以调整，并且针对潜在的瓶颈采取了措施。\n当引入新技术和新产品时，应计划持续性的员工培训，并且计划持续性的员工培训，并且确保创造了必要的基础设施。\n内部运输的运输方式已经策划，例如包装和特殊运货车，所需数量也已经确定。", implEx: "产品/生产过程开发：\n员工培训策划，顾客服务（0公里和使用现场）以及其它\n资格矩阵\nCAx 设备\n为不同任务配备了有资质的人员\n预算、基础设施（如厂房）、检验设备（软硬件）、实验室设备、机器、生产线\n所有资源的产能策划\n产能测试、节拍生产、2日生产\n软件的提供和分发\n工具链策划（软件开发工具）\n产品开发：\n测试/检验/实验室设施（内部和外部）\n策划和实施测试以及修复程序故障所需的资源\n在硬件上安装软件所需的资源（如：刷写、编码、编程）\n生产过程开发：\n生产地点、工具、生产和检验设备、基础设施" },
                { id: "P3.6", text: "是否策划了针对顾客服务和使用现场失效分析的活动？", isStar: false, minReq: "顾客要求应在整个产品生命周期的供货方案中得以考虑 包括备件的供应。\n供货方案中包括能持续确保批量供货的应急计划。\n已针对交付和供应链策划了0公里和使用现场投诉的分析流程。考虑了顾客对使用现场失效分析的要求。\n投诉过程的接口已得到策划。\n已经建立并维护了一套软件的访问权限控制方案。\n已经进行了对利益相关方的分析。已经明确规定了沟通和升级路径。", implEx: "产品/生产过程开发：\n投资策划\n产品开发：\n顾客投诉分析中心的接口，包括相关软件\n诊断访问\n访问控制列表\n生产过程开发：\n标准检验和负载测试的检验计划\n定义了触发准则\nNTF 过程\n备件供应方案\n应急计划" }
            ]
        },
        "P4": {
            name: "P4: 产品和过程开发的实现",
            questions: [
                { id: "P4.1", text: "是否落实了产品和生产过程开发计划中的活动？", isStar: true, minReq: "已落实开发计划中定义的产品与生产过程开发活动，确保满足运行的状态（功能，可靠性，安全性）。\n在多部门协作的基础上进行风险分析（例如：FMEA，HARA），并根据项目进度不断修订。根据规划实施定义的措施，并对有效性进行检查。\n在相关文件（FMEA等）中定义和识别了特殊特性，并采取了措施确保其符合性。", implEx: "产品/生产过程开发：\n降低风险的方法（例如FMEA、HARA、FTA）\n实验设计（例如DOE、谢宁、田口等）\n防错原则\n产品开发：\n测试计划\n装配测试（官方翻译安装测试）和系统测试\nA、B、C、D样品\n耐久性测试\n环境模拟试验（例如：盐雾试验）\n根据策划进行汽车SPICE评估\n代码及软件发布管理\n可追溯性及应急方案\n产品变型管理（注：或产品变体管理，此概念可查询VDA 2）\n根据策划进行了需求分析、架构设计、实施了产品开发和测试\n生产过程开发：\n生产控制计划/检验计划" },
                { id: "P4.2", text: "为确保产品和生产过程的实现 人力资源是否到位并且人员具备资质？", isStar: false, minReq: "人力资源配置计划就位。\n员工的任务，能力要求，以及授权已经被定义并分配。此要求也适用于外包服务提供商。针对此要求需要保持适当的证据。\n在产品和生产过程的开发过程中，对于潜在可能出现的瓶颈以及附加要求（翻译成额外要求是不是更好），确保进行需求的定期评估。\n在产品和生产过程实现的所有阶段，都有资质合格的人员就位，并且已经明确对批量生产有关人员的要求。\n考虑了外包过程。", implEx: "产品/生产过程开发：\n顾客要求\n对于相关职位的总体要求\n确定的培训需求\n培训证明\n工作的方法知识\n外语知识\n软件开发：具备资质的[官方翻译：合格的]软件测试人员、集成经理等。\n有资质的敏捷开发人员（开发及运营经理、放行培训经理等）" },
                { id: "P4.3", text: "物质/非物质资源是否到位并且适用 以确保产品和生产过程的实现？", isStar: false, minReq: "确定资源的过程已经完成。\n资源提供指的是建筑物、测量和检验设备、实验室设备、机器、生产线、IT系统和基础设施的可用性及其利用率。\n考虑了外包过程。\n定期评估了产品和生产过程开发期间可能出现的瓶颈以及额外的需求。\n物质/非物质资源可用于产品和生产过程实现的所有阶段 并已明确对批量生产的要求。\n内部运输的运输工具 例如包装和专用载具 已经确定 并且数量充足。", implEx: "产品/生产过程开发：\n顾客要求\n与顾客和供方的技术接口\nERP系统\n支持过程，例如物流和IT\n产品开发：\n产品验证与确认的资源\n测试设定，例如：硬件在环仿真（HIL），测试板，评估板\n开发工具，为软件开发提供的工具链\n生产过程开发：\n设施策划\n设施布局\n生产线和机器的放行，线体许可\n数量和产出时间（官方翻译产量和产出时间）\n运输路径\n运输方式、容器、仓储\n量产启动前的产能（初始库存）" },
                { id: "P4.4", text: "是否具有产品和生产过程开发所要求的能力和放行证明？", isStar: true, minReq: "根据开发计划，所有零部件、总成、软件版本以及外购件/服务都能获得放行并证明其能力。\n测量和检验过程的初始能力已经过验证。\n材料数据已经确认并放行。\n风险分析中的措施（例如：FMEA，HARA）已被包含在产品与生产过程实现中，其有效性已被确认。\n对于集成（嵌入）软件的产品 软件相关方面的要求应考虑VDA 2的要求。\n按约定的日期完成生产过程和产品批准（PPA）。验证了特殊特性的生产过程参数的公差。", implEx: "产品/生产过程开发：\nPPA的结果，特别是符合法律和法规要求的声明（例如：IMDS，REACH，RoHS）\n例如：IMDS、REACH、RoHS\n顾客的开发放行\n生产过程开发：\n过程参数及其公差\n确认的物流方案（例如，通过发运试验验证包装的适用性 [官方翻译：样品运输包装的适用性]）\n能力证明\n测量和检验软件的确认\n产能研究\n生产线和工具的放行\n软件：\n每个版本的使用建议（发布说明） [官方翻译：每一次发布给予使用建议（发布说明）]\n根据顾客要求放行了第三方软件及开源软件(FOSS)\n提供测试结果和测试评价" },
                { id: "P4.5", text: "是否落实了策划的采购活动？", isStar: false, minReq: "策划的采购活动已经完成。这包括风险评估，项目计划，成熟度，放行以及遵守截止日期。\n根据风险分级策划的活动已经完成 已制定了措施并监视了其执行情况。\n根据项目进程，可提供采购产品和服务所必须的能力和放行（生产过程和产品放行）的证明。材料数据已确认并放行。\n已考虑到顾客相关项目的特定要求。\n已建立了面向采购产品/服务的，以确保顾客服务及使用现场失效分析的过程。", implEx: "失效模式及影响分析、危害分析和风险评估\nVDA 6.3 审核、潜在供方分析等\n符合PPA程序的放行、证据与能力测试\n标准检测与负载检测\nNTF过程及其触发标准已定义\n备件供应方案\n应急计划\n接口协议（根据DIA或接口协议提供服务）\n次级供方管理" },
                { id: "P4.6", text: "是否在产品和生产过程开发中确定并落实了制造和检验规范？", isStar: false, minReq: "制造和检验规范包含所有来自于产品和生产过程开发的检验特性（包括特殊特性）。需考虑到所有的组件、总成、分总成，以及物料，包括产品的制造过程。\n考虑了风险分析的结果（例如FMEA、HARA）。\n已具备生产控制计划。其可用于贯穿整个原型样件阶段 （如有顾客要求），试生产阶段和批量生产阶段。\n已确定产品审核、全尺寸检验和功能性试验的范围和相关要素。\n已制定维护规范。\n根据测试等级和顾客要求（例如：V模型），已对所有测试用例进行描述。\n在对应的时间节点，已放行要求的软件功能。", implEx: "产品/生产过程开发：\n风险分析\n产品开发：\n产品审核计划\n再鉴定计划（官方翻译再评定）\n生产过程开发：\n检验指导书\n作业指导书\n反应计划\n生产放行（首、末件，再放行）\n在线检验\n软件：\n确保软件正确安装的要求\n生产测试的放行标准" },
                { id: "P4.7", text: "是否在量产条件下进行了能力测试？", isStar: false, minReq: "在量产条件下进行能力测试，以确认利用所使用的资源，在规定的时间内，根据规范能够生产顾客要求的数量。\n如果能力测试确认未能达到要求，已制定相应措施。\n在确定节拍/产出时间时，应考虑软件在组件中的安装。\n注：依据审核的时机，相关能力测试的某些部分可能仍然处于策划阶段。\n该提问与产品开发无关！", implEx: "生产过程开发：\n批量生产条件：例如：工具、生产线、节拍时间、人员、生产和检验规范、测量与检测设备\n顾客要求\n能力测试、生产节拍\n确定最小数量（生产峰值和约定的灵活性）\n设备和设施的批量生产成熟度（测量报告）\n批量生产的人力配置方案\n包装要求\n软件：\n软件刷写时间包括测试、ROM（只读存储器）编程" },
                { id: "P4.8", text: "是否为确保顾客服务以及使用现场失效分析建立了过程？", isStar: false, minReq: "在过程中已建立了顾客对零部件贯穿产品生命周期的供应要求。\n完成了持续批量供应的策划过程，包括紧急情况的保障措施。\n根据交付范围已建立0公里和使用现场失效的分析过程。已考虑顾客对于使用现场失效分析的要求。\n对所在现场分析能力的要求已与顾客达成一致。\n若使用外部场所进行分析，则应规定接口，且具备所要求设备和分析产能可用性的证明。\n顾客服务中也应考虑新技术和产品。\n指定负责这些过程的人员已经具备资质，基础设施已到位。\n已明确规定使用现场的产品监控过程。\n已建立错误分析和诊断过程（控制单元中嵌入的软件）。\n若同意，已经明确了空中下载（OTA）软件的更新过程。", implEx: "产品/生产过程开发：\n进行标准检测和负载检测的检验设备\n已定义触发准则\n失效分析的检验计划\nNTF 过程\n生产过程开发：\n资质矩阵和培训证明\n检验系统和设备\n与外部分析场所的服务协议\n约定了分析用外包场所的服务协议\n备件供应方案\n应急计划" },
                { id: "P4.9", text: "是否针对从开发到批量生产的项目交接，建立了受控的方法？", isStar: false, minReq: "已建立将工作结果从项目阶段转移到批量生产的过程。\n项目计划中规定的活动已经实施。对于尚待澄清的方面，已经确定了最后期限并任命了负责人。\n成功的内部放行和顾客放行是批量交付放行的先决条件。按时执行来自内部和外部放行的措施。\n策划的人员已到位并具备资质。\n策划的用于批量生产的物质资源已到位。\n已明确规定并引入了保证生产启动的措施。\n对于集成（嵌入）软件的产品，应记录来自于开发阶段的结果（包括中间结果及其文档）。\n工业化进程被保证。", implEx: "产品/生产过程开发：\n顾客要求\nPPA 记录\n包含交接标准和验收报告的交接协议/检查表\n零件历史记录\n关键生产数据，如OEE、ppm、拒收率等。\n来自于正在进行项目的经验\n人力资源（生产工人、工艺工程师、维护人员等）\n物质资源（机器和生产线、建筑物、进出路线、检验设施、载具、包装等）\n变更日志、放行说明\n软件工业化：包括在批量生产中将软件刷写至控制单元，和代码编写 " }
            ]
        },
        "P5": {
            name: "P5: 供方管理",
            questions: [
                { id: "P5.1", text: "是否确保了只和获得批准的供方开展合作？", isStar: true, minReq: "在批量生产中，确保仅使用经过批准的供方。相关的批准标准已经确定。\n在选择供方以及评价其质量能力时 根据部件的风险分级 计划并落实了过程审核。\n已考虑对现有供方的质量绩效进行评价。\n使用适当的措施识别、评价并降低供应链（内部/外部）中的风险。\n如果是顾客指定的供方（指定供方），则应考虑接口协议。", implEx: "确定的供方选择准则\n质量管理协议\n在未满足准则的情况下：为降低风险而采取措施的证据\n评价供方的质量能力，例如：通过KPI（ppm、交付绩效）、升级水平\n供方的自我评估、供方审核结果\nASPICE评估结果\nVDA 6.3过程审核\nVDA 6.3潜在供方分析" },
                { id: "P5.2", text: "是否在供应链中考虑了顾客的要求？", isStar: false, minReq: "顾客要求的沟通是规范和可追溯的。\n在批量生产过程中，变更管理也被考虑在内。\n对于顾客指定的供方（指定供方），必须要有相关接口协议。", implEx: "要求来自：图纸、部件、软件或部件规范、里程碑计划、质量管理协议或其他有效标准\n特殊特性\n再鉴定要求\n有关投诉处理的要求\n法律和法规要求" },
                { id: "P5.3", text: "是否与供方就供货绩效约定了目标，并定期评价目标的达成情况？", isStar: false, minReq: "与所有直接供方签订了有关交付绩效的目标协议并加以落实。\n在规定的期限内，根据定义的准则检查和评价了供方绩效。\n如果未达到约定的目标，则应定义措施并监控包括期限在内的执行情况。\n如果是顾客指定的供方（指定供方），则应考虑接口协议。", implEx: "可测量的目标：交付数量、准时率、故障率、PPM、特殊交货、拒收、投诉的处理时间\n根据质量管理协议的升级准则\n在供方未达到要求的交付绩效情况下的原因和措施（开发计划）的证据" },
                { id: "P5.4", text: "针对采购的产品和服务，是否获得了必要的批准/放行？", isStar: true, minReq: "在批量生产中使用新的或更改的产品/生产过程之前，与顾客商定的所有采购的产品和服务都已放行。\n如果是顾客指定的供方（指向性供方），则应考虑接口协议。", implEx: "关于PPA程序协调的报告\nPPA报告\nPPA程序的参考零件\n极限样件\n供应链中的产品和生产过程变更\n针对小批量和单个需求的放行协议" },
                { id: "P5.5", text: "针对采购的产品和服务，是否确保了约定的质量？", isStar: true, minReq: "为了监控采购的产品和服务的质量，根据检验计划进行检验、记录和评价。\n如果出现偏差，将遵循标准的投诉过程。\n检验和测量设备适用于采购的产品和服务，充足可用且存储适当。检验工位布局合理（例如：环境控制、照明条件、清洁度和防止损坏和污染）。", implEx: "放行的检验程序\n样本大小（例如：跳批抽检）\n极限样件\nPPM评价，8D报告\n改进项目\n符合DIN EN 10204的材料证明\n量具/夹具\n图纸/规范\n订购和包装规范" },
                { id: "P5.6", text: "是否对进货产品进行了适当的交付和储存？", isStar: false, minReq: "材料和运载器具根据其使用状态进行交付和存储 以免损坏或混料。\n对于可能因温度、湿度、振动等损坏并影响最终产品质量的材料，定义了运输和储存条件并提供了证据。\n可疑/隔离材料有清晰的标识并防止未经授权的使用。\nFIFO/FEFO（有效期要求）和批次可追溯性在材料进一步加工时得到保证。该要求同样适用于剩余料。\n仓库管理系统中的物料库存数量与实际库存数量相一致。\n储存条件满足产品要求。", implEx: "包装\n标识（可追溯性/检验状态/使用状态）\n隔离仓库，隔离区域\n与批次相关的使用\n环境条件\n防止损坏/污染/腐蚀\n有序和清洁\n防止混料/错用的预防措施" },
                { id: "P5.7", text: "针对具体的任务，相关人员是否定义了职责并具备了资质？", isStar: false, minReq: "规定了员工在其相关工作领域的职责、任务和权限。\n员工根据其岗位要求进行资质认证。\n根据任务确定资质要求，并相应地计划和实施资格认证（官方翻译：落实资格）。\n要理解之前发生的针对采购的产品和服务的投诉（包括纠正措施）。", implEx: "了解规范、产品特性、客户要求和生产过程\n标准\n法律和法规要求\n包装要求\n质量程序\n岗位描述/任务和职能的描述\n资质矩阵\n供方审核员资质" }
            ]
        },
        "P7": {
            name: "P7: 顾客服务",
            questions: [
                { id: "P7.1", text: "质量管理体系和产品符合性相关的要求是否得到满足？", isStar: false, minReq: "内部和顾客特定的质量体系要求及其进一步开发的要求得到满足。组织内部的过程（包括外包过程）以及供应链都应考虑在内。\n根据顾客要求落实了全尺寸检验和功能性试验。\n满足顾客关于零件回收和再利用的要求。\n有符合必要的国家和国际法规的证明。", implEx: "与顾客的质量协议\n全尺寸检验方案，例如：进行产品审核、功能试验、耐久性试验\n质量管理体系认证\n符合性证明，例如，需通过型式认证、CCC、ECE、DOT、证书、检验报告的零件" },
                { id: "P7.2", text: "是否保障了顾客服务？", isStar: false, minReq: "确保在顾客组织内的各个领域都有合格的联系人。\n确保按顾客的规范进行联络沟通。\n确保对使用现场的产品监控。\n确保根据与顾客的特定协议 登录顾客平台 并及时更新/维护所要求的数据。", implEx: "有关产品使用的知识\n产品问题和有关产品或运输投诉的知识\n满足新的要求\n通报改善措施\n全球顾客服务\n当顾客要求无法被满足时，及时通知顾客\n需要的数据（例如：认证、联系人信息）" },
                { id: "P7.3", text: "是否保障了零件的供应？", isStar: true, minReq: "确保应急预案（包括针对产品/零件持续供货的应急预案）是有效且最新的。相关过程考虑了组织内部（包括外包过程）和供应链。相关应急预案还需要包括非物质产品，比如：软件等。\n已考虑风险及其对顾客的影响。\n组织应有相应的过程确保当发现产品供应出现短缺时，能立即通知顾客。相关信息应包括产品短缺的持续时间、程度、原因和已启动的措施。\n确保满足顾客在产品量产阶段和之后备件供应的要求。", implEx: "应急计划（如替代生产、供方、包装、运输）\n挑选的能力以及响应时间\n利用外部产能\n针对供应短缺进行的沟通\n涵盖引入特殊措施时授权进行决策和升级路径的规定\n零件的隔离\n供方参与备件供应\n提供软件更新" },
                { id: "P7.4", text: "发生投诉时，是否开展了失效分析，并且有效地落实了纠正措施？", isStar: true, minReq: "针对0公里和使用现场投诉，采用符合顾客规范的投诉处理程序。\n必须定义失效分析程序。有必要的人力和物质资源以确保准时处置。遵守与顾客约定的时限。当约定的失效分析时间计划无法满足时 应及时通知顾客。\n当出现使用现场投诉时，需按顾客要求开展使用现场失效分析（例如：VDA 使用现场失效分析和审核标准）。\n针对顾客指定的供方（指定供方），必须要有相关接口协议。", implEx: "处理投诉和使用现场失效分析过程\n内部/外部分析设施（实验室、测试和检测设备、人员）\n使用问题解决方法（8D）\n偏差发生时与顾客的信息流\n知识储备库、经验教训\n质量控制环\n风险分析（如FMEA、危害分析与风险评估）\n获取必要的放行文件（例如，PPA）" },
                { id: "P7.5", text: "针对具体任务，相关人员是否定义了职责并具备了资质？", isStar: false, minReq: "确定每位员工在自己的工作范围内相应的职责、任务和授权。\n培训需求是根据任务具体确定和实施的。\n员工要熟悉产品以及错误执行工作对零件供应和最终产品质量带来的后果。", implEx: "组织机构图和升级程序（官方翻译： 组织机构图和事态升级程序 ）\n了解相关知识的证据：产品、规范、顾客特定要求\n标准/法规（产品责任）\n预期用途\n失效分析\n评价方法（例如，审核、统计）\n质量技术（如，柏拉图、8D方法、因果图、5Why）\n外语知识" }
            ]
        }
    };
        const p6TemplateSource = { 
        name: "P6: 生产过程分析",
        questions: [
            { id: "P6.1.1", text: "是否在开发和批量生产之间进行了项目交接，并确保可靠的生产启动？", isStar: true, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "已根据规定的标准对项目移交至批量生产进行了记录存档。\n整个移交过程的责任有具体的规定并被告知。对未解决的问题有后续跟踪，并按计划实施了必要的措施。\n在首次量产发货之前 完成了生产过程和产品的全面批准/放行（PPA）。\n所需的文件均已到位，基于风险分析采取了确保生产启动的措施。\n所需数量的工装模具以及运输、包装、检验和测量设备均已到位。\n具备失效分析的能力。\n产品软件对应的是最新发布的版本。", implEx: "交接报告\n确定的措施及实施的时间表\n生产放行报告\n所有生产线组件（官方翻译：部件）和工装模具的放行\nPPA文件，包括PPA程序的顾客放行和参考样件\n特殊放行\n已发布的软件版本\n确保生产启动的示例：更高的检验频次、额外的检验、驻厂工程师\n安全投产方案" },
            { id: "P6.1.2", text: "材料是否在约定的时间，按所需的数量/生产批次大小被送至指定的位置？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "材料以约定的质量、正确的数量、正确的包装、在约定的时间被送至约定的地点，并附有正确的文件。可在指定的存储区域/工位获取零件/部件。\n在工作场所，按需提供产品和材料，并根据物流方案考虑订单数量/批量大小。\n明确定义了余料的使用及其可追溯性。\n对来自外包过程（包括分拣服务）重新投入使用的零件进行了规范。", implEx: "材料包括，例如：软件、包装、运载装置、部件和组件、原材料、半成品、交付给顾客的量产包装、操作材料、辅助材料和工艺材料\n适当的运输方式\n定义的存储位置和库存水平\n看板管理、JIT/JIS、FIFO/FEFO\n材料和软件的修订/变更状态\n关于零部件和容器的特殊要求（例如ESD保护、湿度、温度、残留物）" },
            { id: "P6.1.3", text: "是否对材料进行了适当的存储，是否所使用的运输工具/包装设备适合材料的特性？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "始终考虑并满足包装要求。\n在制造和组织内部的运输过程中，以及往返于服务供方的运输过程中，须使用适宜的运输工具，以免产品受损和污染。\n仓储区/加工工位/容器必须达到材料所需的整洁/清洁要求。要定义清洁周期，并加以监控。\n加工工位/装配线上的材料供应必须便于安全操作。\n通过适当的方法监控材料的规定存储时间和有效期。\n生产线和机器的运行及辅助材料，如果对产品/产品质量有直接的影响，要进行相应的监视。\n保护材料、运行和辅助材料免受环境和气候的影响。", implEx: "库存量\n储存条件\n放行的特殊和标准的运输容器\n防止材料损坏\n5S\n不过量填装（储存区域和容器）\n最长和最短存储时间，指定的临时存储时间， FIFO/FEFO" },
            { id: "P6.1.4", text: "材料是否具备必要的标识/记录/放行，并得以适当体现？", isStar: false, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "材料的放行状态清晰可辨 定义了捆装/批次/装运容器/零件上的放行标识。\n确保只有放行的材料/零件才被提供给生产/下道工序使用。\n根据定义的追溯性方案，确保从分供方到顾客的整个过程的可追溯性。\n顾客规范以及相关的法律和法规要求的标识被考虑在内。", implEx: "顾客关于标识和可追溯性的规范\n放行的零件/材料的标识（粘贴标签、标识、发料单、 VDA标识、数字矩阵代码(DMC)等）\n批准记录\n可追溯系统\n特殊放行的记录（数量、持续时间、标识类型等）\n库存管理系统" },
            { id: "P6.1.5", text: "是否对批量生产中的产品或生产过程变更进行了跟踪和记录？", isStar: true, section: "P6.1", sectionName: "P6.1 过程输入是什么？过程输入", minReq: "根据所描述的变更管理落实对产品和生产过程的变更。产品和生产过程的变更与顾客达成一致，根据顾客要求批准和放行（包括软件变更）。执行了PPA批准放行。变更状态的历史是完全可追溯的。\n使用材料/软件的正确、放行的版本。\n完成变更之后，检查风险分析是否需要更新。", implEx: "依据VDA 2中的触发矩阵或顾客规范\n变更的放行要形成文件\n变更前的多学科评价\n变更履历/零件履历（也适用于软件）\nDFMEA和PFMEA\n变更时的操作管理\n试生产\n唯一软件标识，软件完整性（构建号，哈希值）" },
            { id: "P6.2.1", text: "生产控制计划以及生产和检验文件中的要求是否完整，并得到有效落实？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "基于生产控制计划的生产和检验文件是完整的。\n与放行的机器/工具/辅助设备相关的数据在生产控制计划和/或生产和检验文件中注明。\n这些文件可在工位附近取用。\n在生产控制计划中描述了针对过程干扰所需的措施 并予以落实和记录。\n充分陈述了影响产品性能/质量的过程参数。\n规定了过程参数和检验特性的公差。\n过程控制图中的控制限是明确的、可识别的和合理的。\n记录了与过程要求和检验特性相关的偏差和已启动的措施。\n规范返工条件，作为风险分析的一部分进行评估，并在过程中加以保护（零件标识；重复检验等）。", implEx: "检验特性、检验设备、检验方法、检验频率、检验周期和再鉴定\n有关机器/工具/辅助设备的数据（识别编号）、过程参数和公差（压力、温度、时间、速度等）\n作业指导书（包括返工）\n检验指导书" },
            { id: "P6.2.2", text: "是否进行了生产过程的放行？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "对首件/末件和重新放行进行了特定过程的放行检验并记录。\n产品和生产过程的放行是必要的，并由授权的员工根据接收准则执行并记录。记录偏差和已启动的措施。\n在放行时，有必要的参考和极限样件。\n规定了重新放行的触发准则，例如：在生产中断之后。\n如果在收集检验零件后继续生产，则这些产品应是可以获得的，直到放行了检验零件。", implEx: "生产批次放行，包括重新放行\n首件放行/末件放行和重新放行\n工具图/参考件/设置用部件（例如：失效测试件、合格/不合格样件）\n重新放行的可能触发准则：\n生产中断后（例如：两班工作制的夜班、工具更换、材料/批次/产品更换）\n返修，工具更换\n变更设置数据（2016：生产数据修改）\n放行的返工" },
            { id: "P6.2.3", text: "是否在生产中对特殊特性进行了控制？", isStar: true, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "在生产控制计划中标记了顾客指定和组织识别的特殊特性以及定义的过程参数，并进行了系统的监控和记录。\n保持了偏差和纠正措施的记录。对影响产品特性的偏差 要由顾客批准。\n特殊特性的记录是可获得的。这些记录的保存时间和存档方式是明确的并满足顾客要求。", implEx: "图纸\n表明顾客特定的特殊特性的标识 例如：D/TLD、DS、DZ、R、S、F\n过程FMEA\n生产控制计划\nSPC评价\n质量控制图\n能力证明\n检验过程能力证明\n检验结果\n过程参数记录" },
            { id: "P6.2.4", text: "是否对可疑和不合格产品进行了控制？", isStar: true, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "可疑产品和不合格产品被分离、贴标签、记录并安全地从生产过程中移除。\n对这些产品直接标识，或在其容器上标识。\n由授权人员决定可疑产品的后续使用。\n允许返工的范围（包括检验和放行）在作业指导书中描述。\n明确标识隔离仓库和隔离区域。防止意外或未经授权使用隔离产品。", implEx: "表明产品状态的标识\n在生产中定义报废/返工工位\n隔离仓库和隔离区域，清理区域（官方翻译清洗区 ）\n关于拒收、返工和维修的文件\n授权" },
            { id: "P6.2.5", text: "是否能确保材料在流转过程中不发生混合/弄错？", isStar: false, section: "P6.2", sectionName: "P6.2 所有生产过程是否受控？过程流程", minReq: "确保不会发生材料混淆或者使用错误材料、软件或组件的情况。\n采取适当措施 确保尽早发现任何零件混淆或任何使用错误零件/错误安装零件的情况。\n可从标签上清楚看出产品的使用状态。\n明确定义了隔离零件、返工零件和可再利用产品的使用。确保可追溯性。\n必须标识设置标准件、设置用零件和参考件，并防止意外使用。", implEx: "过程FMEA\n防错方法\n生产设施的检查和检验\n批次的可追溯性\nFIFO/FEFO\n先进先出/先到期显出\n看板\n清除无效的标识\n价值流分析\n分选服务\n唯一的软件标识符，软件完整性（构建号，哈希键）" },
            { id: "P6.3.1", text: "员工是否能够完成分配的任务？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "对于每项任务/工作，已规定相应的要求。员工资质应符合要求。如果情况并非如此，则需制定资质认证计划。\n向员工提供的指导、培训和入职介绍以及资质证明都需做好记录。\n提供相关工作所需的特殊资质证明。\n如果过程发生变更，应提供培训/指导并进行记录。\n这些要求同样适用于临时员工。", implEx: "资格证明\n培训计划\n初始培训计划，包括证据\n在职培训\n资质矩阵\n关于产品和已发生失效的知识\n测量设备操作\n控制图的解释\n关于职业安全的培训/指导\n特殊特性的培训\n适用的资质证明（例如：焊接证书、视力检测结果、听力检测结果）" },
            { id: "P6.3.2", text: "员工是否了解监视产品和过程质量的职责和权限？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "描述和落实员工的职责、责任和权限。\n员工知道工作执行不当的后果。理解产品的作用/功能。也清楚它们得不到保证时会发生什么。\n定期向员工通报当前的质量绩效以及顾客投诉。\n这些要求同样适用于临时员工。", implEx: "作业/检验指导书\n岗位描述\n设置放行、首件检验、末件检验\n停止和开始过程的授权\n升级协议\n产品培训\n产品安全/产品责任培训" },
            { id: "P6.3.3", text: "是否具备必要的人力资源？", isStar: false, section: "P6.3", sectionName: "P6.3 哪些人力资源用于过程事项？人力资源", minReq: "所有班次都有员工配置计划。员工配置计划考虑了所需合格员工的数量。\n对于非持续使用的支持区域（如实验室、测量室），应制定相应的规定。\n员工配置计划考虑了顾客订单的波动和员工缺勤情况（如病假、休假、培训）。\n这些要求同样适用于临时员工。", implEx: "班次计划\n资格证明\n资质矩阵\n文件化的缺勤管理规则\n员工配置计划\n指导者（官方翻译 导师 ）" },
            { id: "P6.4.1", text: "生产设备是否适合满足顾客对于产品的具体要求？", isStar: true, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "必须证明，使用现有生产设施能够根据顾客要求落实生产过程，且生产的产品满足顾客规范的要求。\n生产设施、机器和生产线必须有能力满足具体产品和过程特性公差的要求。\n对影响过程的参数和软件进行保护，防止未经授权的访问。\n必须确定所选择的产品和过程特性的过程能力 并须提供能力证明。\n过程能力须满足内部及顾客规范的要求。过程能力至少达到Cpk≥1.33。对于有过程能力要求，却无法提供能力证明的特性，须开展100%检验。", implEx: "机器/过程能力证明\n关键过程参数的监控（如压力、时间、温度）\n备用工具的能力\n上料和取料系统\n量具、夹具等的再现性\n清洁度要求\n技术状态管理，确保按照唯一性标识提供软件，并保障软件的完整性（构建号，哈希键）" },
            { id: "P6.4.2", text: "生产设备和工具的维护保养是否受控？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "根据相应的风险，对所有机器、生产线、设备和工具都规定和实施了预防性和/或预见性维护保养（维护、检查和修理）。\n应记录已经实施的计划和非计划的维护活动 并分析潜在的改进措施。\n为执行必要的维护保养活动提供了所需的资源。\n有效地落实了对停产时间、设备利用率及工具寿命进行分析和优化的过程。\n须确保备件已到位并可用。\n对工具进行了管理，包含如下内容：\n• 工装履历，包括所有变更和工具寿命\n• 运行状态\n• 工具的标识\n这些要求也适用于外部服务提供商。", implEx: "与生产相关的物流设备，如叉车\n维护和服务计划\n全面生产维护（TPM）\n关键过程和瓶颈设备\n制造商提供的技术文件\n对于易损工具制定的预防性更换计划\n工具的运行记录表\n工具运行状态，如运行中、不可运行\n工具标识，如顾客财产、工具编号、索引" },
            { id: "P6.4.3", text: "是否能利用测量和检验设备有效监视质量要求的符合性？", isStar: true, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "使用的测量和检验系统适用于预期目的和实际生产操作 并包含在生产控制计划中。\n所使用的测量及检验系统均有能力证明。\n检验过程中使用的所有系统和装置都进行了标识。并监视了其有效性状态。建立并实施了监视测量和检验系统持续能力的过程。\n针对偏差，开展了针对过程、产品和顾客的风险评估。定义并有效实施了相关措施。\n对测量结果有影响的测量系统及相应的标准和参考物质也采用同样的方法进行监控。", implEx: "测量系统分析\n测量及检验过程能力\n替代测量装置\n嵌入式软件的完整性检查\n校准状态（检验贴纸、条码、刻字等）\n测量和检验用软件的确认\n参考件/参考物质\n监视测试设备" },
            { id: "P6.4.4", text: "生产和检验工位是否满足要求？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "工作场所及环境条件适合于产品和工作内容，以便预防或消除零件的污染、损坏、混料以及错用。\n以上要求同样适用于长期或临时设置的返工、分选及检验工位。\n工位的布局适合于要开展的工作。", implEx: "清洁和整洁，5S\n照明\n噪音污染\n环境控制\n洁净室\n静电防护\n工位布局\n工位周边布置/加工工位上零件的取放(官方翻译：工位周边环境及零件搬运)\n职业健康和安全" },
            { id: "P6.4.5", text: "是否正确地存放了工具、装置和检验设备？", isStar: false, section: "P6.4", sectionName: "P6.4 什么物质资源用于过程实现？物质资源", minReq: "妥善存放了所有工具、装置和检验设备。\n应确保设备的存放方式，使其不会受损及受到环境影响。\n保障了清洁和整洁。\n控制和记录设备的发放和使用。", implEx: "防止碰撞、污染和环境影响\n5S方法\n规定的存储位置，例如：在地板上做标记\n透明化仓库管理" },
            { id: "P6.5.1", text: "是否针为生产过程设定了目标？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "定义、监控和沟通了有关效果和效率的具体过程的目标。\n设定目标时，考虑了顾客的要求。\n定期将规定的目标与实际结果进行比较并记录。", implEx: "关键生产数据，例如：产量、质量指标、产出时间、缺陷成本、过程有效性数据、生产线和机器利用率\n一次质量合格率，直通率\n减少浪费（例如：拒收和返工，能源和加工材料）" },
            { id: "P6.5.2", text: "是否质量和生产过程数据的收集便于分析？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "定义并记录了可用于证明产品符合性（目标值）的质量及过程参数。对实际数据进行了分析并采取了适当的改进措施。\n记录了过程中的特殊事件。\n所记录的数据应可与产品和过程进行关联，这些数据可获取、清晰、可查阅并按规定进行了存档。满足可追溯性的要求。\n基于质量、成本及服务方面的发现，不断的确定改进的潜力。", implEx: "控制图\n检查表\n失效类型/失效频率\n拒收/返工\n记录了参数变更的过程数据表\n换班/机器日志\n节拍时间/产出时间\n故障信息（例如：停线、断电、程序错误信息）\n输出/可用性\n封存通知/分选措施\n可追溯性" },
            { id: "P6.5.3", text: "如果不能满足产品或生产过程的要求，是否分析了原因，并且验证了纠正措施的有效性？", isStar: true, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "如果不能满足产品或生产过程的要求，必须采取遏制措施以满足要求，直到证明了纠正措施是有效的。员工必须熟悉这些遏制措施。\n采用适宜的方法进行原因分析。\n须对重复失效进行记录，并且相应开展了更详细的原因分析。\n从原因分析导出了纠正措施，并监控其实施且验证了有效性。\n以事件为导向，对生产控制计划及风险分析进行了更新。\n若偏差已影响到已交付产品的特性，必须与顾客沟通。", implEx: "8D方法\n因果图\n田口DOE、谢宁DOE\n5Why方法\n过程能力分析\nDFMEA和PFMEA\n弃权/特殊放行\n追加的尺寸、材料、功能和耐久性测试" },
            { id: "P6.5.4", text: "是否定期开展过程和产品审核？", isStar: false, section: "P6.5", sectionName: "P6.5 过程的落实效果如何？效果和效率", minReq: "基于顾客要求和特定风险 制定并落实了产品和过程审核方案。\n开展的过程和产品审核须适合于识别特定风险和薄弱环节。\n审核中发现偏差时 应分析原因 制定纠正措施 监控措施的实施并进行有效性验证。\n若偏差已影响到发运的产品，必须与顾客沟通。", implEx: "规范\n特殊特性\n计划内的以及事件导向的审核方案\n审核频率\n审核结果、审核报告、措施计划\n审核员资质\n审核范围，例如：P5、P6、P7\n标识、包装\n具体零件和软件的变更状态" },
            { id: "P6.6.1", text: "是否根据需要确定产量/生产批量，并且有序的地运往下一道生产工序？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "使用适宜的运输方式将产品送至指定的存储区/保存地点。\n依据订单的数量/批次大小 将所需要数量的产品转移至指定的存储区/保存地点。\n产品状态（合格品、返工、拒收等）标识清晰（组件、容器）。\n清晰标识变更的状态。\n确保只有合格零件进入下一个生产过程步骤。\n制定了返回剩余产品的管理规则，包括数量的记录以及进一步的处理措施。", implEx: "看板管理\nFIFO/FEFO\n先进先出/先到期显出\nJIT/JIS\nJIT：Just In Time 准时化生产\nJIS：Just in sequence 准时化顺序供应\n仓库管理\n根据顾客需求确定生产数量\n技术清洁度" },
            { id: "P6.6.2", text: "是否对产品进行了适当的存储，所使用的运输/包装设备是否与产品的特性相适应？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "通过适宜的储存和包装，防止产品受到损坏。\n必须了解并落实内部的和顾客的特定包装要求。这也适用于已批准的替换包装。\n储存区域/容器必须满足相关清洁度要求。\n监控了规定的存储期限。\n产品在储存和运输过程中受到保护，防止外界环境和气候带来的影响。\n这些要求适用于生产过程中以及运输过程中的处置。", implEx: "防止受损\n电子零件的ESD保护\n技术清洁度\n清洁、整洁5S\n无过量装填（储存区域和容器）\n监视储存的时间和储存的数量（最长/最短的储存时间，指定的临时储存时间）\n关于包装的清洁规范\n足够数量的包装" },
            { id: "P6.6.3", text: "是否保持了必要的记录和放行？", isStar: false, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "产品的放行以及相关证据均有记录。\n特殊放行/弃权要有相关文档记录。该文档记录应包括相关产品的生产时间和/或数量。\n满足关于返工和返修记录的内部和顾客规范的要求。\n必须确保产品的可追溯性。\n满足顾客关于存档的要求。", implEx: "顾客规范\n顾客对存档期限的要求\n存档的要求/法规（例如：电子数据处理、纸张、防火、可读性要求）\n零件履历" },
            { id: "P6.6.4", text: "是否在产品交付时满足了顾客的要求？", isStar: true, section: "P6.6", sectionName: "P6.6 过程应产生什么？过程结果（输出）", minReq: "顾客对最终产品的特定要求（交付可靠性、质量目标、质量绩效等）必须明确。针对相关要求的满足情况需要持续监控、评估和记录。\n当出现偏差时，应分析原因，制定措施并执行，并确保其有效性。\n根据顾客要求对产品进行标识、储存和发运。\n当特殊放行/弃权时 需要按照要求在产品和包装上进行相应的标识。\n遵守内部和顾客的关于返工或返修产品的标识规范。\n要监管对供货产品的处置。\n涉及影响顾客的交付中断时，需要及时告知顾客，并与其协商进一步的程序。", implEx: "与顾客的质量协议\n包装规范\n目标协议\n发货审核\n关于标识的顾客规范（如：VDA 标签）\n针对特殊放行的（断点）标识" }
        ]
    };
        // --- END OF VDA 6.3:2023 Questions Data (Chinese) ---

        // Global state for audit data
        let auditData = {
            plan: [],
            planMeta: { preparedBy: '', preparationDate: '', approvedBy: '', approvalDate: '' },
            header: {
                reportNo: '',
                supplierName: '',
                supplierAddress: '',
                auditStartDate: '',
                auditEndDate: '',
                auditProject: '', // This field might become less relevant if using auditProjects matrix
                auditReason: '',
                auditProjects: [], // NEW: Array of { no: number, projectName: string, projectId: string }
                auditors: [{ name: '', type: '', email: '', phone: '' }],
                auditees: [{ name: '', position: '', department: '', email: '', phone: '' }]
        },
            p1Questions: [], 
            projectAssessments: {}, // NEW: Object keyed by projectId. e.g. { "projId1": { greenCount: ..., overallResult: ... }}
            actionPlan: [], // Each item will get a projectId
            checklist: []
        };
        
        const P1_QUESTION_IDS = [
            "P2.1", "P2.2", "P2.3", "P2.4", "P2.6",
            "P3.1", "P3.2", "P3.4",
            "P4.1", "P4.3", "P4.4",
            "P5.1", "P5.5", "P5.6",
            "P6.1.1", "P6.1.5",
            "P6.2.1", "P6.2.2", "P6.2.3", "P6.2.4",
            "P6.3.1", "P6.3.2",
            "P6.4.1", "P6.4.2", "P6.4.3", "P6.4.4",
            "P6.5.3", "P6.5.4",
            "P6.6.2", "P6.6.3", "P6.6.4",
            "P7.1", "P7.2", "P7.3", "P7.4"
        ];

        function getProjects() {
            return auditData.header.auditProjects || [];
        }
        
        function generateInternalProjectId(project, index) {
            return project.projectId || `_internal_proj_${index}`;
        }

        // Syncs p1Questions[i].projectEvaluations with auditData.header.auditProjects
        function syncP1QuestionProjectEvaluations() {
            const projects = getProjects();
            auditData.p1Questions.forEach(q => {
                const currentProjectEvaluations = q.projectEvaluations || [];
                const newProjectEvaluations = [];
                projects.forEach((p, index) => {
                    const internalId = generateInternalProjectId(p, index);
                    const existingEval = currentProjectEvaluations.find(pe => pe.projectId === internalId);
                    if (existingEval) {
                        newProjectEvaluations.push({ ...existingEval, projectName: p.projectName }); // Update projectName if changed
                } else {
                        newProjectEvaluations.push({
                            projectId: internalId,
                            projectName: p.projectName,
                            evaluation: 'n.e.',
                            auditRecord: '',
                            // attachment is per question, not per project-evaluation
                        });
                    }
                });
                q.projectEvaluations = newProjectEvaluations;
            });

            // After syncing, re-render relevant active pages
            if (document.getElementById('questionnairePage').classList.contains('active')) {
                renderQuestionnairePage();
            }
            updateAssessment(); // This will also trigger renderAssessmentPage if active
            if (document.getElementById('actionPlanPage').classList.contains('active')) {
                renderActionPlanTable();
            }
        }


        function generateReportNoForPlanItem(planItem, allPlanItems) {
            if (!planItem.startDate) {
                return "日期未填"; 
            }
            const startDate = new Date(planItem.startDate);
            if (isNaN(startDate.getTime())) {
                return "日期无效";
            }

            const year = startDate.getFullYear();
            const month = (startDate.getMonth() + 1).toString().padStart(2, '0');
            const prefix = `${year}${month}`;

            let maxSequence = 0;
            allPlanItems.forEach(item => {
                if (item.reportNo && item.reportNo.startsWith(prefix)) {
                    const sequenceStr = item.reportNo.substring(prefix.length);
                    const sequenceNum = parseInt(sequenceStr, 10);
                    if (!isNaN(sequenceNum) && sequenceNum > maxSequence) {
                        maxSequence = sequenceNum;
                    }
                }
            });

            const newSequence = (maxSequence + 1).toString().padStart(4, '0');
            return `${prefix}${newSequence}`;
        }

        function initializeP1Questions() {
            auditData.p1Questions = [];
            const allQuestionsMap = new Map();
            
            for (const elementKey in vdaQuestionsDataSource) {
                vdaQuestionsDataSource[elementKey].questions.forEach(q => {
                    allQuestionsMap.set(q.id, {...q, element: elementKey, elementName: vdaQuestionsDataSource[elementKey].name });
                });
            }
            p6TemplateSource.questions.forEach(q => {
                 allQuestionsMap.set(q.id, {...q, element: 'P6', elementName: p6TemplateSource.name });
            });

            P1_QUESTION_IDS.forEach(id => {
                const sourceQuestion = allQuestionsMap.get(id);
                if (sourceQuestion) {
                    auditData.p1Questions.push({
                        id: sourceQuestion.id,
                        text: sourceQuestion.text,
                        isStar: sourceQuestion.isStar,
                        minReq: sourceQuestion.minReq,
                        implEx: sourceQuestion.implEx,
                        element: sourceQuestion.element,
                        elementName: sourceQuestion.elementName,
                        projectEvaluations: [], // Will be populated by syncP1QuestionProjectEvaluations
                        attachment: '' 
                    });
                } else {
                    console.warn(`P1 Question ID ${id} not found in source data.`);
                }
            });
            syncP1QuestionProjectEvaluations(); // Initialize projectEvaluations arrays
        }


        // --- Page Rendering Functions ---
        function renderPlanPage() {
            const page = document.getElementById('planPage');
            page.innerHTML = `
                <h2>潜在供方分析计划</h2>
                <button id="addPlanRowBtn">添加计划行</button>
                <table id="planTable" class="matrix-table">
                    <thead>
                        <tr>
                            <th style="width: 50px;">操作</th>
                            <th>No.</th>
                            <th>报告编号</th>
                            <th style="width: 320px;">供应商名称</th>
                            <th>审核日期起</th>
                            <th>审核日期至</th>
                            <th>审核原因</th>
                            <th style="width: 100px;">审核组长</th>
                            <th>备注</th>
                            <th>删除</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <div style="margin-top: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div><label>编制:</label><input type="text" id="planPreparedBy" value="${auditData.planMeta.preparedBy || ''}"></div>
                    <div><label>编制时间:</label><input type="date" id="planPreparationDate" value="${auditData.planMeta.preparationDate || ''}"></div>
                    <div><label>审批:</label><input type="text" id="planApprovedBy" value="${auditData.planMeta.approvedBy || ''}"></div>
                    <div><label>审批时间:</label><input type="date" id="planApprovalDate" value="${auditData.planMeta.approvalDate || ''}"></div>
                </div>
            `;
            renderPlanTable();
            document.getElementById('addPlanRowBtn').addEventListener('click', addPlanRow);
            ['planPreparedBy', 'planPreparationDate', 'planApprovedBy', 'planApprovalDate'].forEach(id => {
                document.getElementById(id).addEventListener('change', (e) => {
                    auditData.planMeta[id.replace('plan','').toLowerCase()] = e.target.value;
                });
            });
        }

        function renderPlanTable() {
            const tbody = document.querySelector('#planTable tbody');
            tbody.innerHTML = '';
            auditData.plan.forEach((row, index) => {
                const tr = tbody.insertRow();
                tr.innerHTML = `
                    <td><button class="startAuditBtn" data-index="${index}">开始审核</button></td>
                    <td>${index + 1}</td>
                    <td><input type="text" value="${row.reportNo || '待生成'}" data-index="${index}" data-field="reportNo" readonly style="background-color: #eee; width: 100px;"></td>
                    <td><input type="text" value="${row.supplierName || ''}" data-index="${index}" data-field="supplierName" style="width: 320px;"></td>
                    <td><input type="date" value="${row.startDate || ''}" data-index="${index}" data-field="startDate"></td>
                    <td><input type="date" value="${row.endDate || ''}" data-index="${index}" data-field="endDate"></td>
                    <td><input type="text" value="${row.reason || ''}" data-index="${index}" data-field="reason"></td>
                    <td><input type="text" value="${row.leader || ''}" data-index="${index}" data-field="leader" style="width: 80px;"></td>
                    <td><textarea data-index="${index}" data-field="notes">${row.notes || ''}</textarea></td>
                    <td><button class="button-red removePlanRowBtn" data-index="${index}">删除</button></td>
                `;
            });
            tbody.querySelectorAll('input, textarea').forEach(input => {
                if(input.dataset.field !== 'reportNo') { 
                     input.addEventListener('change', updatePlanData);
                }
            });
            tbody.querySelectorAll('.removePlanRowBtn').forEach(btn => {
                btn.addEventListener('click', removePlanRow);
            });
            tbody.querySelectorAll('.startAuditBtn').forEach(btn => {
                btn.addEventListener('click', startAuditFromPlan);
            });
        }

        function addPlanRow() {
            auditData.plan.push({ reportNo: '', supplierName: '', startDate: '', endDate: '', reason: '', leader: '', notes: '' });
            renderPlanTable();
        }

        function updatePlanData(event) {
            const index = parseInt(event.target.dataset.index);
            const field = event.target.dataset.field;
            auditData.plan[index][field] = event.target.value;

            if (field === 'startDate') {
                const newReportNo = generateReportNoForPlanItem(auditData.plan[index], auditData.plan);
                auditData.plan[index].reportNo = newReportNo;
                const reportNoInput = document.querySelector(`#planTable tbody tr:nth-child(${index + 1}) input[data-field="reportNo"]`);
                if (reportNoInput) {
                    reportNoInput.value = newReportNo;
                }
            }
        }
        
        function removePlanRow(event) {
            const index = parseInt(event.target.dataset.index);
            auditData.plan.splice(index, 1);
            renderPlanTable();
        }
        
        function startAuditFromPlan(event) {
            const index = parseInt(event.target.dataset.index);
            const planItem = auditData.plan[index];

            if (!planItem.reportNo || planItem.reportNo === "日期未填" || planItem.reportNo === "日期无效") {
                alert("请先为该计划行填写有效的'审核日期起'以生成报告编号。");
                return;
            }
            
            const currentChecklist = auditData.checklist;
            const currentPlan = auditData.plan;
            const currentPlanMeta = auditData.planMeta;

            auditData = { 
                plan: currentPlan,
                planMeta: currentPlanMeta,
                header: {
                    reportNo: planItem.reportNo,
                    supplierName: planItem.supplierName,
                    supplierAddress: '', 
                    auditStartDate: planItem.startDate,
                    auditEndDate: planItem.endDate,
                    auditProject: '', 
                    auditReason: planItem.reason,
                    auditProjects: [], // Reset audit projects for new audit session
                    auditors: [{ name: planItem.leader, type: '审核组长', email: '', phone: '' }],
                    auditees: [{ name: '', position: '', department: '', email: '', phone: '' }]
                },
                p1Questions: [], // Will be initialized
                projectAssessments: {}, // Reset project assessments
                actionPlan: [], // Reset action plan
                checklist: currentChecklist
            };
            initializeP1Questions(); // This will also call syncP1QuestionProjectEvaluations

            renderHeaderPage(); 
            renderQuestionnairePage(); 
            updateAssessment(); 
            renderActionPlanTable(); 

            switchTab(document.querySelector('.nav-tab[data-page="header"]'));
            alert(`已从计划开始审核，报告编号: ${planItem.reportNo}`);
        }


        function renderHeaderPage() {
            const page = document.getElementById('headerPage');
            page.innerHTML = `
                <h2>基本信息</h2>
                <div class="form-group">
                    <label for="reportNo">报告编号:</label>
                    <input type="text" id="reportNo" value="${auditData.header.reportNo}" readonly style="background-color: #eee;">
                </div>
                <div class="form-group">
                    <label for="supplierName">供应商名称:</label>
                    <input type="text" id="supplierName" value="${auditData.header.supplierName}">
                </div>
                <div class="form-group">
                    <label for="supplierAddress">供应商地址:</label>
                    <input type="text" id="supplierAddress" value="${auditData.header.supplierAddress || ''}">
                </div>
   <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="form-group">
                        <label for="auditStartDate">审核开始日期:</label>
                        <input type="date" id="auditStartDate" value="${auditData.header.auditStartDate || ''}">
                    </div>
                    <div class="form-group">
                        <label for="auditEndDate">审核结束日期:</label>
                        <input type="date" id="auditEndDate" value="${auditData.header.auditEndDate || ''}">
                    </div>
                </div>
                </div>
                 <div class="form-group">
                    <label for="auditReason">审核原因:</label>
                    <input type="text" id="auditReason" value="${auditData.header.auditReason || ''}">
                </div>

                <h3>审核项目</h3>
                <button id="addAuditProjectBtn">添加审核项目</button>
                <table id="auditProjectsTable" class="matrix-table">
                    <thead><tr><th>No.</th><th>项目名称</th><th>项目编号</th><th>操作</th></tr></thead>
                    <tbody></tbody>
                </table>

                <h3>审核员信息</h3>
                <button id="addAuditorBtn">添加审核员</button>
                <table id="auditorsTable" class="matrix-table">
                    <thead><tr><th>No.</th><th>姓名</th><th>类别</th><th>邮箱</th><th>电话</th><th>操作</th></tr></thead>
                    <tbody></tbody>
                </table>

                <h3>被审核方人员信息</h3>
                <button id="addAuditeeBtn">添加被审核方人员</button>
                <table id="auditeesTable" class="matrix-table">
                    <thead><tr><th>No.</th><th>姓名</th><th>职务</th><th>部门</th><th>邮箱</th><th>电话</th><th>操作</th></tr></thead>
                    <tbody></tbody>
                </table>
            `;
            renderAuditProjectsTable();
            renderAuditorsTable();
            renderAuditeesTable();

            ['supplierName', 'supplierAddress', 'auditStartDate', 'auditEndDate', 'auditReason'].forEach(id => {
                document.getElementById(id).addEventListener('change', (e) => {
                    auditData.header[id] = e.target.value;
                });
            });
            document.getElementById('addAuditProjectBtn').addEventListener('click', addAuditProjectRow);
            document.getElementById('addAuditorBtn').addEventListener('click', addAuditor);
            document.getElementById('addAuditeeBtn').addEventListener('click', addAuditee);
        }

        function renderAuditProjectsTable() {
            const tbody = document.querySelector('#auditProjectsTable tbody');
            tbody.innerHTML = '';
            const projects = getProjects();
            projects.forEach((project, index) => {
                const tr = tbody.insertRow();
                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td><input type="text" value="${project.projectName || ''}" data-index="${index}" data-field="projectName"></td>
                    <td><input type="text" value="${project.projectId || ''}" data-index="${index}" data-field="projectId" placeholder="唯一ID"></td>
                    <td><button class="button-red removeAuditProjectBtn" data-index="${index}">删除</button></td>
                `;
            });
            tbody.querySelectorAll('input').forEach(input => {
                input.addEventListener('change', updateAuditProjectData);
            });
            tbody.querySelectorAll('.removeAuditProjectBtn').forEach(btn => {
                btn.addEventListener('click', removeAuditProjectRow);
            });
        }

        function addAuditProjectRow() {
            auditData.header.auditProjects.push({ projectName: '', projectId: '' });
            renderAuditProjectsTable();
            syncP1QuestionProjectEvaluations(); // Critical: update question structures
        }

        function updateAuditProjectData(event) {
            const index = parseInt(event.target.dataset.index);
            const field = event.target.dataset.field;
            auditData.header.auditProjects[index][field] = event.target.value;
            // If projectId changes, it's complex. For now, assume it doesn't change after creation or that sync handles it.
            // Simple re-sync is safest if IDs might change or be crucial.
            syncP1QuestionProjectEvaluations();
        }

        function removeAuditProjectRow(event) {
            const index = parseInt(event.target.dataset.index);
            auditData.header.auditProjects.splice(index, 1);
            renderAuditProjectsTable();
            syncP1QuestionProjectEvaluations(); // Critical: update question structures
        }


        function renderAuditorsTable() {
            const tbody = document.querySelector('#auditorsTable tbody');
            tbody.innerHTML = '';
            auditData.header.auditors.forEach((auditor, index) => {
                const tr = tbody.insertRow();
                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td><input type="text" value="${auditor.name || ''}" data-index="${index}" data-field="name"></td>
                    <td><input type="text" value="${auditor.type || ''}" data-index="${index}" data-field="type"></td>
                    <td><input type="email" value="${auditor.email || ''}" data-index="${index}" data-field="email"></td>
                    <td><input type="tel" value="${auditor.phone || ''}" data-index="${index}" data-field="phone"></td>
                    <td><button class="button-red removeAuditorBtn" data-index="${index}">删除</button></td>
                `;
            });
            tbody.querySelectorAll('input').forEach(input => {
                input.addEventListener('change', updateAuditorData);
            });
             tbody.querySelectorAll('.removeAuditorBtn').forEach(btn => {
                btn.addEventListener('click', removeAuditor);
            });
        }
        function addAuditor() {
            auditData.header.auditors.push({ name: '', type: '', email: '', phone: '' });
            renderAuditorsTable();
        }
        function updateAuditorData(event) {
            const index = parseInt(event.target.dataset.index);
            const field = event.target.dataset.field;
            auditData.header.auditors[index][field] = event.target.value;
        }
        function removeAuditor(event) {
            const index = parseInt(event.target.dataset.index);
            auditData.header.auditors.splice(index, 1);
            renderAuditorsTable();
        }

        function renderAuditeesTable() {
            const tbody = document.querySelector('#auditeesTable tbody');
            tbody.innerHTML = '';
            auditData.header.auditees.forEach((auditee, index) => {
                const tr = tbody.insertRow();
                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td><input type="text" value="${auditee.name || ''}" data-index="${index}" data-field="name"></td>
                    <td><input type="text" value="${auditee.position || ''}" data-index="${index}" data-field="position"></td>
                    <td><input type="text" value="${auditee.department || ''}" data-index="${index}" data-field="department"></td>
                    <td><input type="email" value="${auditee.email || ''}" data-index="${index}" data-field="email"></td>
                    <td><input type="tel" value="${auditee.phone || ''}" data-index="${index}" data-field="phone"></td>
                    <td><button class="button-red removeAuditeeBtn" data-index="${index}">删除</button></td>
                `;
            });
            tbody.querySelectorAll('input').forEach(input => {
                input.addEventListener('change', updateAuditeeData);
            });
            tbody.querySelectorAll('.removeAuditeeBtn').forEach(btn => {
                btn.addEventListener('click', removeAuditee);
            });
        }
        function addAuditee() {
            auditData.header.auditees.push({ name: '', position: '', department: '', email: '', phone: '' });
            renderAuditeesTable();
        }
        function updateAuditeeData(event) {
            const index = parseInt(event.target.dataset.index);
            const field = event.target.dataset.field;
            auditData.header.auditees[index][field] = event.target.value;
        }
        function removeAuditee(event) {
            const index = parseInt(event.target.dataset.index);
            auditData.header.auditees.splice(index, 1);
            renderAuditeesTable();
        }


        function renderQuestionnairePage() {
            const page = document.getElementById('questionnairePage');
            const projects = getProjects();
            let html = `<h2>P1 提问表 (共 ${auditData.p1Questions.length} 个提问)</h2>`;

            if (projects.length === 0) {
                html += `<p style="color: var(--accent-color); font-weight: bold;">请先在"基本信息"页面添加至少一个审核项目，才能开始提问表评估。</p>`;
                page.innerHTML = html;
                return;
            }
            
            html += `<div class="action-buttons">
                            <button id="evalAllGreenBtn" class="button-green">全部绿色</button>
                            <button id="evalRandomBtn" class="button-yellow">随机评价</button>
                            <button id="evalAllNeBtn">全部 N.E.</button>
                        </div>`;
            
            let currentElement = '';
            auditData.p1Questions.forEach((q, qIndex) => {
                if (q.elementName !== currentElement) {
                    if (currentElement !== '') html += `</div>`; 
                    html += `<h3>${q.elementName}</h3><div class="element-group">`;
                    currentElement = q.elementName;
                }
                
                html += `
                    <div class="question-block" id="q-${q.id.replace('.','_')}">
                        <div class="question-header ${q.isStar ? 'star-question' : ''}">${q.id}: ${q.text}</div>
                        <div class="question-details">
                            <div class="min-req"><h4>与评价相关的最低要求:</h4>${q.minReq}</div>
                            <div class="impl-ex"><h4>实施示例:</h4>${q.implEx}</div>
                        </div>`;

                (q.projectEvaluations || []).forEach((pe, projectIndex) => {
                    const project = projects[projectIndex]; // Assuming projectEvaluations are in sync
                    if (!project) return; // Should not happen if sync is correct

                    const evalClass = pe.evaluation ? 'eval-' + pe.evaluation.toLowerCase().replace('.', '') : 'eval-ne';
                    html += `
                        <div class="project-evaluation-block">
                            <h5>项目: ${pe.projectName || `项目 ${projectIndex + 1}`} (ID: ${pe.projectId})</h5>
                            <div class="form-group">
                                <label for="record-${qIndex}-${projectIndex}">审核记录:</label>
                                <textarea id="record-${qIndex}-${projectIndex}" data-qindex="${qIndex}" data-projectindex="${projectIndex}" class="audit-record-project">${pe.auditRecord || ''}</textarea>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <label for="eval-${qIndex}-${projectIndex}">评价:</label>
                                <select id="eval-${qIndex}-${projectIndex}" data-qindex="${qIndex}" data-projectindex="${projectIndex}" class="evaluation-dropdown ${evalClass}">
                                    <option value="G" ${pe.evaluation === 'G' ? 'selected' : ''} class="eval-g-option">绿色 (G)</option>
                                    <option value="Y" ${pe.evaluation === 'Y' ? 'selected' : ''} class="eval-y-option">黄色 (Y)</option>
                                    <option value="R" ${pe.evaluation === 'R' ? 'selected' : ''} class="eval-r-option">红色 (R)</option>
                                    <option value="n.e." ${pe.evaluation === 'n.e.' ? 'selected' : ''} class="eval-ne-option">N.E.</option>
                                </select>
                            </div>
                        </div>
                    `;
                });
                
                // Attachment per question (not per project-evaluation)
                html += `
                        <div style="margin-top:10px; display: flex; align-items: center; gap: 10px;">
                            <label for="attach-${qIndex}" class="button-like">为此提问添加附件</label>
                            <input type="file" id="attach-${qIndex}" data-qindex="${qIndex}" class="attachment-input hidden">
                            <span id="attach-name-${qIndex}" class="attachment-name">${q.attachment || '未选择文件'}</span>
                        </div>
                    </div> 
                `;
            });
            if (currentElement !== '') html += `</div>`; 
            page.innerHTML = html;

            page.querySelectorAll('.evaluation-dropdown').forEach(select => {
                select.addEventListener('change', (e) => {
                    const qIndex = parseInt(e.target.dataset.qindex);
                    const projectIndex = parseInt(e.target.dataset.projectindex);
                    auditData.p1Questions[qIndex].projectEvaluations[projectIndex].evaluation = e.target.value;
                    e.target.className = `evaluation-dropdown eval-${e.target.value.toLowerCase().replace('.','')}`; 
                    updateAssessment();
                    renderActionPlanTable(); 
                });
            });
            page.querySelectorAll('.audit-record-project').forEach(textarea => {
                textarea.addEventListener('input', (e) => {
                    const qIndex = parseInt(e.target.dataset.qindex);
                    const projectIndex = parseInt(e.target.dataset.projectindex);
                    auditData.p1Questions[qIndex].projectEvaluations[projectIndex].auditRecord = e.target.value;
                    renderActionPlanTable(); 
                });
            });
            page.querySelectorAll('.attachment-input').forEach(input => {
                input.addEventListener('change', (e) => {
                    const qIndex = parseInt(e.target.dataset.qindex);
                    const fileName = e.target.files.length > 0 ? e.target.files[0].name : '未选择文件';
                    auditData.p1Questions[qIndex].attachment = fileName;
                    document.getElementById(`attach-name-${qIndex}`).textContent = fileName;
                });
            });
            
            document.getElementById('evalAllGreenBtn').addEventListener('click', () => setAllEvaluations('G'));
            document.getElementById('evalRandomBtn').addEventListener('click', setRandomEvaluations);
            document.getElementById('evalAllNeBtn').addEventListener('click', () => setAllEvaluations('n.e.'));
        }
        
        function setAllEvaluations(evaluationType) {
            auditData.p1Questions.forEach(q => {
                if (q.projectEvaluations) {
                    q.projectEvaluations.forEach(pe => pe.evaluation = evaluationType);
                }
            });
            renderQuestionnairePage(); 
            updateAssessment();
            renderActionPlanTable();
        }

        function setRandomEvaluations() {
            auditData.p1Questions.forEach(q => {
                if (q.projectEvaluations) {
                    q.projectEvaluations.forEach(pe => {
                        const rand = Math.random();
                        if (rand < 0.6) pe.evaluation = 'G';      // 60% Green
                        else if (rand < 0.9) pe.evaluation = 'Y'; // 30% Yellow
                        else pe.evaluation = 'R';                 // 10% Red (or adjust as needed)
                        // N.E. is not typically set randomly in this context
                    });
                }
            });
            renderQuestionnairePage();
            updateAssessment();
            renderActionPlanTable();
        }


        function renderAssessmentPage() {
            const page = document.getElementById('assessmentPage');
            const projects = getProjects();
            let html = `<h2>评估结果</h2>`;

            if (projects.length === 0) {
                html += `<p>没有定义审核项目。请在"基本信息"页面添加项目以查看评估。</p>`;
                page.innerHTML = html;
                return;
            }
            
            projects.forEach((project, index) => {
                const internalId = generateInternalProjectId(project, index);
                const assessment = auditData.projectAssessments[internalId] || { greenCount: 0, yellowCount: 0, redCount: 0, neCount: 0, overallResult: '', overallResultText: '评估数据不可用' };
                
                html += `
                    <div style="margin-bottom: 30px; border: 1px solid #ccc; padding:15px; border-radius:5px; min-height: 100px;">
                        <h3>项目评估: ${project.projectName || `项目 ${index + 1}`} (ID: ${project.projectId || internalId})</h3>
                        <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                            <table style="width: auto; margin: 0;">
                                <thead>
                                    <tr>
                                        <th>评级</th>
                                        <th colspan="2">基于提问的评价</th>
                                    </tr>
                                    <tr>
                                        <th></th>
                                        <th>黄色</th>
                                        <th>红色</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td rowspan="1" style="font-weight:bold;">不合格供方 (R)</td>
                                        <td>超过12个</td>
                                        <td>一个或一个以上</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1" style="font-weight:bold;">有条件批准的供方 (Y)</td>
                                        <td>最多12个</td>
                                        <td>没有</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1" style="font-weight:bold;">批准的供方 (G)</td>
                                        <td>最多6个</td>
                                        <td>没有</td>
                                    </tr>
                                </tbody>
                            </table>
                            <table style="width: auto; margin: 0;">
                                <thead><tr><th>评价类型</th><th>数量</th></tr></thead>
                                <tbody>
                                <tr><td style="background-color: var(--success-color); color: white; font-weight:bold;">绿色 (G)</td><td>${assessment.greenCount}</td></tr>
                                <tr><td style="background-color: var(--warning-color); color: var(--text-color); font-weight:bold;">黄色 (Y)</td><td>${assessment.yellowCount}</td></tr>
                                <tr><td style="background-color: var(--accent-color); color: white; font-weight:bold;">红色 (R)</td><td>${assessment.redCount}</td></tr>
                                <tr><td style="background-color: var(--light-gray); color: var(--text-color); font-weight:bold;">N.E.</td><td>${assessment.neCount}</td></tr>
                            </tbody>
                        </table>
                            <div style="width: auto; margin: 0;">
                                <h4>此项目总体评估:</h4>
                                <div class="assessment-result-item ${assessment.overallResult ? assessment.overallResult.toLowerCase() : 'unknown'}">
                                    ${assessment.overallResultText}
                                </div>
                            </div>
                        </div>
                `;
            });
             html += `<p style="text-align: center; margin-top: 10px; font-size: 0.9em;">
                    (注意: P1潜在供方分析中，每个项目最多允许3个提问标记为N.E.，否则结果可能不具可比性。)
                </p>
                 <div style="text-align: center; margin-top: 20px;">
                     <button id="addToChecklistBtn" class="button-green">添加到审核清单</button>
                 </div>
                `;
            page.innerHTML = html;
            
            if (document.getElementById('addToChecklistBtn')) {
                 document.getElementById('addToChecklistBtn').addEventListener('click', addToChecklist);
            }
        }
        
        function updateAssessment() {
            const projects = getProjects();
            auditData.projectAssessments = {}; // Reset

            projects.forEach((project, projIndex) => {
                const internalId = generateInternalProjectId(project, projIndex);
                let g = 0, y = 0, r = 0, ne = 0;
                
                auditData.p1Questions.forEach(q => {
                    if (q.projectEvaluations && q.projectEvaluations[projIndex]) {
                        const pe = q.projectEvaluations[projIndex];
                        if (pe.evaluation === 'G') g++;
                        else if (pe.evaluation === 'Y') y++;
                        else if (pe.evaluation === 'R') r++;
                        else if (pe.evaluation === 'n.e.') ne++;
                    }
                });

                let overallResult = '';
                let overallResultText = '评价未定';

                if (y > 12 || r >= 1) {
                    overallResult = 'Red';
                    overallResultText = '不合格供方 (红色)';
                } else if (y <= 6 && r === 0) {
                    overallResult = 'Green';
                    overallResultText = '批准的供方 (绿色)';
                } else if (y <= 12 && r === 0) { 
                    overallResult = 'Yellow';
                    overallResultText = '有条件批准的供方 (黄色)';
                }
                
                if (ne > 3) {
                     overallResultText += (overallResult !== '' ? ' - ' : '') + 'N.E.数量超过3个,结果可能不具可比性';
                     if(overallResult === 'Green' || overallResult === 'Yellow') { // Downgrade if too many N.E.
                        // overallResult = 'Yellow'; // Or some other logic
                     }
                }
                 if (overallResult === '' && ne <=3) {
                     overallResultText += ' (请检查评价逻辑)';
                 }


                auditData.projectAssessments[internalId] = {
                    projectId: project.projectId, // Store original projectId as well
                    projectName: project.projectName,
                    greenCount: g, yellowCount: y, redCount: r, neCount: ne,
                    overallResult: overallResult,
                    overallResultText: overallResultText
                };
            });
            
            if (document.getElementById('assessmentPage').classList.contains('active')) {
                 renderAssessmentPage(); // Re-render the assessment page with new data
            }
        }

        function renderActionPlanPage() {
            const page = document.getElementById('actionPlanPage');
            page.innerHTML = `
                <h2>措施计划</h2>
                <table id="actionPlanTable">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th style="width: 50px;">项目名称/ID</th>
                            <th style="width: 45px;">提问编号</th>
                            <th style="width: 200px;">审核记录摘要</th>
                            <th>评价</th>
                            <th style="width: 60px;">责任人</th>
                            <th>原因分析</th>
                            <th>纠正措施</th>
                            <th style="width: 70px;">预计完成日期</th>
                            <th>状态</th>
                            <th style="width: 60px;">有效性验证</th>
                            <th style="width: 60px;">关闭日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <button id="addActionPlanRowBtn">手动添加措施行</button>
            `;
            renderActionPlanTable();
            document.getElementById('addActionPlanRowBtn').addEventListener('click', addManualActionPlanRow);

        }

        function renderActionPlanTable() {
            const tbody = document.querySelector('#actionPlanTable tbody');
            if (!tbody) return; 
            
            const projects = getProjects();
            const currentManualActionPlanItems = auditData.actionPlan.filter(item => !item.isAuto);
            auditData.actionPlan = [...currentManualActionPlanItems];

            projects.forEach((project, projIndex) => {
                const internalProjId = generateInternalProjectId(project, projIndex);
                auditData.p1Questions.forEach(q => {
                    if (q.projectEvaluations && q.projectEvaluations[projIndex]) {
                        const pe = q.projectEvaluations[projIndex];
                        if (pe.evaluation === 'Y' || pe.evaluation === 'R') {
                            const summary = pe.auditRecord ? (pe.auditRecord.substring(0, 200) + (pe.auditRecord.length > 200 ? '...' : '')) : '';
                            // Check if an item for this project and question already exists
                            let existingItem = auditData.actionPlan.find(item => item.isAuto && item.questionId === q.id && item.projectId === internalProjId);
                            if (!existingItem) {
                                existingItem = {
                                    projectId: internalProjId,
                                    projectName: project.projectName,
                                    questionId: q.id,
                                    responsible: '', rootCause: '', correctiveAction: '',
                                    estCompletionDate: '', status: '未开始', effectivenessVerification: '', closingDate: '',
                                    isAuto: true 
                                };
                                auditData.actionPlan.push(existingItem);
                            }
                            existingItem.auditRecordSummary = summary;
                            existingItem.evaluation = pe.evaluation;
                            existingItem.projectName = project.projectName; // Ensure project name is up-to-date
                        }
                    }
                    });
                });
            
            // Remove auto-generated items if their corresponding evaluation is no longer Y/R
            auditData.actionPlan = auditData.actionPlan.filter(item => {
                if (item.isAuto) {
                    const projectIndex = projects.findIndex(p => generateInternalProjectId(p, projects.indexOf(p)) === item.projectId);
                    if (projectIndex === -1) return false; // Project removed

                    const q = auditData.p1Questions.find(q => q.id === item.questionId);
                    if (!q || !q.projectEvaluations || !q.projectEvaluations[projectIndex]) return false; // Question or evaluation missing
                    
                    const pe = q.projectEvaluations[projectIndex];
                    return pe.evaluation === 'Y' || pe.evaluation === 'R';
                }
                return true; 
            });
            
            tbody.innerHTML = ''; 
            auditData.actionPlan.forEach((item, index) => {
                const tr = tbody.insertRow();
                let evalClass = '';
                if (item.evaluation) {
                    evalClass = 'eval-' + item.evaluation.toLowerCase().replace('.', '');
                }
                const displayProjectName = item.projectName || item.projectId || 'N/A';

                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${displayProjectName}</td>
                    <td>${item.questionId || '<input type="text" data-index="${index}" data-field="questionId" placeholder="手动输入" style="width: 50px;">'}</td>
                    <td style="max-width:260px;width:260px;word-break:break-all;">${item.auditRecordSummary || ''}</td>
                    <td class="${evalClass}">${item.evaluation || ''}</td>
                    <td><input type="text" value="${item.responsible || ''}" data-index="${index}" data-field="responsible" style="width: 70px;"></td>
                    <td><textarea data-index="${index}" data-field="rootCause" style="width: 150px;">${item.rootCause || ''}</textarea></td>
                    <td><textarea data-index="${index}" data-field="correctiveAction" style="width: 150px;">${item.correctiveAction || ''}</textarea></td>
                    <td><input type="date" value="${item.estCompletionDate || ''}" data-index="${index}" data-field="estCompletionDate" style="width: 110px;"></td>
                    <td>
                        <select data-index="${index}" data-field="status" style="width: 80px;">
                            <option value="未开始" ${item.status === '未开始' ? 'selected' : ''}>未开始</option>
                            <option value="进行中" ${item.status === '进行中' ? 'selected' : ''}>进行中</option>
                            <option value="已关闭" ${item.status === '已关闭' ? 'selected' : ''}>已关闭</option>
                        </select>
                    </td>
                    <td><textarea data-index="${index}" data-field="effectivenessVerification" style="width: 60px;">${item.effectivenessVerification || ''}</textarea></td>
                    <td><input type="date" value="${item.closingDate || ''}" data-index="${index}" data-field="closingDate" style="width: 110px;"></td>
                    <td><button class="button-red removeActionPlanRowBtn" data-index="${index}" style="padding: 2px 6px; font-size: 0.7em;">删除</button></td>
                `;
            });

            tbody.querySelectorAll('input, textarea, select').forEach(input => {
                input.addEventListener('change', (event) => {
                    updateActionPlanData(event);
                    const selectElement = event.target.closest('tr').querySelector('select[data-field="status"]');
                    if (selectElement) {
                        const row = selectElement.closest('tr');
                        const rootCause = row.querySelector('textarea[data-field="rootCause"]').value;
                        const correctiveAction = row.querySelector('textarea[data-field="correctiveAction"]').value;
                        const effectivenessVerification = row.querySelector('textarea[data-field="effectivenessVerification"]').value;
                        const closingDate = row.querySelector('input[data-field="closingDate"]').value;

                        selectElement.classList.remove('status-closed', 'status-in-progress', 'status-not-started');

                        if (effectivenessVerification && closingDate) {
                            selectElement.value = '已关闭';
                            selectElement.classList.add('status-closed');
                        } else if (rootCause && correctiveAction) {
                            selectElement.value = '进行中';
                            selectElement.classList.add('status-in-progress');
                        } else {
                            selectElement.value = '未开始';
                            selectElement.classList.add('status-not-started');
                        }
                    }
                });
            });

            tbody.querySelectorAll('select[data-field="status"]').forEach(selectElement => {
                function updateStatusColor() {
                    selectElement.classList.remove('status-closed', 'status-in-progress', 'status-not-started');
                    const row = selectElement.closest('tr');
                    const rootCause = row.querySelector('textarea[data-field="rootCause"]').value;
                    const correctiveAction = row.querySelector('textarea[data-field="correctiveAction"]').value;
                    const effectivenessVerification = row.querySelector('textarea[data-field="effectivenessVerification"]').value;
                    const closingDate = row.querySelector('input[data-field="closingDate"]').value;

                    if (effectivenessVerification && closingDate) {
                        selectElement.value = '已关闭';
                        selectElement.classList.add('status-closed');
                    } else if (rootCause && correctiveAction) {
                        selectElement.value = '进行中';
                        selectElement.classList.add('status-in-progress');
                    } else {
                        selectElement.value = '未开始';
                        selectElement.classList.add('status-not-started');
                    }
                }
                updateStatusColor(); // Initial call to set color based on current value
                selectElement.addEventListener('change', updateStatusColor);
            });
            tbody.querySelectorAll('.removeActionPlanRowBtn').forEach(btn => {
                btn.addEventListener('click', removeActionPlanRow);
            });
        }

        function addManualActionPlanRow() {
            const projects = getProjects();
            const firstProjectId = projects.length > 0 ? generateInternalProjectId(projects[0], 0) : null;
            const firstProjectName = projects.length > 0 ? projects[0].projectName : '未指定项目';

            auditData.actionPlan.push({
                projectId: firstProjectId, // Default to first project or null
                projectName: firstProjectName,
                questionId: '', auditRecordSummary: '手动添加', evaluation: '',
                responsible: '', rootCause: '', correctiveAction: '',
                estCompletionDate: '', status: '未开始', effectivenessVerification: '', closingDate: '',
                isAuto: false
            });
            renderActionPlanTable();
        }

        function updateActionPlanData(event) {
            const index = parseInt(event.target.dataset.index);
            const field = event.target.dataset.field;
            auditData.actionPlan[index][field] = event.target.value;
        }
        function removeActionPlanRow(event) {
            const index = parseInt(event.target.dataset.index);
            auditData.actionPlan.splice(index, 1);
            renderActionPlanTable();
        }

        function renderChecklistPage() {
            const page = document.getElementById('checklistPage');
            page.innerHTML = `
                <h2>审核清单</h2>
                <table id="checklistTable">
                    <thead><tr><th>No.</th><th>报告编号</th><th>审核日期</th><th>各项目结论摘要</th><th>操作</th></tr></thead>
                    <tbody></tbody>
                </table>
            `;
            renderChecklistTable();
        }
        
        function renderChecklistTable() {
            const tbody = document.querySelector('#checklistTable tbody');
            if(!tbody) return;
            tbody.innerHTML = '';
            auditData.checklist.forEach((item, index) => {
                const tr = tbody.insertRow();
                let projectConclusionsSummary = '无项目数据';

                if (item.fullData && item.fullData.projectAssessments && Object.keys(item.fullData.projectAssessments).length > 0) {
                    projectConclusionsSummary = Object.values(item.fullData.projectAssessments)
                        .map(pa => {
                            let colorStyle = '';
                            if (pa.overallResult) {
                                switch (pa.overallResult.toLowerCase()) {
                                    case 'green': colorStyle = 'color:var(--success-color);'; break;
                                    case 'yellow': colorStyle = 'color:var(--warning-color);'; break;
                                    case 'red': colorStyle = 'color:var(--accent-color);'; break;
                                }
                            }
                            return `${pa.projectName || pa.projectId}: <span style="${colorStyle} font-weight:bold;">${pa.overallResultText || 'N/A'}</span>`;
                        })
                        .join('<br>');
                } else if (item.fullData && item.fullData.assessment) { // Handle old format for display
                     projectConclusionsSummary = `旧格式: ${item.fullData.assessment.overallResultText || 'N/A'}`;
                }


                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td><a href="#" class="load-checklist-item" data-index="${index}">${item.reportNo}</a></td>
                    <td>${item.auditDate}</td>
                    <td>${projectConclusionsSummary}</td>
                    <td><button class="button-red removeChecklistItemBtn" data-index="${index}">删除</button></td>
                `;
            });
            tbody.querySelectorAll('.load-checklist-item').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadChecklistItem(parseInt(e.target.dataset.index));
                });
            });
            tbody.querySelectorAll('.removeChecklistItemBtn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    removeChecklistItem(parseInt(e.target.dataset.index));
                });
            });
        }

        function addToChecklist() {
            const currentReportNo = auditData.header.reportNo || `未命名报告-${new Date().toISOString().slice(0,10)}`;
            updateAssessment(); // Ensure projectAssessments is current

            if (!auditData.projectAssessments || Object.keys(auditData.projectAssessments).length === 0 && getProjects().length > 0) {
                 if (!confirm("当前没有项目被评估或项目列表为空，评估结果可能不完整。确定要添加到清单吗？")) {
                    return;
                }
            }
            
            const existingIndex = auditData.checklist.findIndex(item => item.reportNo === currentReportNo && item.auditDate === auditData.header.auditStartDate);

            const checklistItem = {
                reportNo: currentReportNo,
                auditDate: auditData.header.auditStartDate || new Date().toISOString().slice(0,10),
                fullData: JSON.parse(JSON.stringify(auditData)) 
            };

            if (existingIndex > -1) {
                if(confirm(`报告 "${currentReportNo}" 已存在于清单中。是否覆盖?`)){
                    auditData.checklist[existingIndex] = checklistItem;
                } else {
                    return;
                }
            } else {
                auditData.checklist.push(checklistItem);
            }
            
            renderChecklistTable();
            saveDataToLocalStorage(); 
            alert(`报告 "${checklistItem.reportNo}" 已添加到审核清单。`);
        }

        function loadChecklistItem(index) {
            if (auditData.checklist[index] && auditData.checklist[index].fullData) {
                const loadedAuditData = JSON.parse(JSON.stringify(auditData.checklist[index].fullData));
                
                const currentGlobalPlan = auditData.plan;
                const currentGlobalPlanMeta = auditData.planMeta;
                const currentGlobalChecklist = auditData.checklist;

                auditData = loadedAuditData;
                auditData.plan = currentGlobalPlan; 
                auditData.planMeta = currentGlobalPlanMeta;
                auditData.checklist = currentGlobalChecklist;

                // Ensure p1Questions is initialized if missing in old data
                if (!auditData.p1Questions || auditData.p1Questions.length !== P1_QUESTION_IDS.length) {
                    const oldP1Data = auditData.p1Questions; // temp store
                    initializeP1Questions(); // This sets up shells with empty projectEvaluations

                    // Try to restore evaluations if oldP1Data existed
                    if (oldP1Data && Array.isArray(oldP1Data)) {
                         auditData.p1Questions.forEach(newQ => {
                            const matchingOldQ = oldP1Data.find(oldQ => oldQ.id === newQ.id);
                            if (matchingOldQ) {
                                newQ.attachment = matchingOldQ.attachment || '';
                                if (matchingOldQ.projectEvaluations && Array.isArray(matchingOldQ.projectEvaluations)) {
                                    newQ.projectEvaluations.forEach(newPE => {
                                        const oldPE = matchingOldQ.projectEvaluations.find(op => op.projectId === newPE.projectId);
                                        if (oldPE) {
                                            newPE.evaluation = oldPE.evaluation || 'n.e.';
                                            newPE.auditRecord = oldPE.auditRecord || '';
                                        }
                                    });
                                } else if (matchingOldQ.evaluation && newQ.projectEvaluations.length > 0) {
                                    // very old format, apply to first project if exists
                                    newQ.projectEvaluations[0].evaluation = matchingOldQ.evaluation;
                                    newQ.projectEvaluations[0].auditRecord = matchingOldQ.auditRecord || '';
                                }
                            }
                        });
                    }
                } else {
                  // If p1Questions structure is fine, still ensure projectEvaluations are in sync with header.auditProjects
                  syncP1QuestionProjectEvaluations(); 
                  // Now, merge any loaded projectEvaluation data into the freshly synced structure
                   auditData.p1Questions.forEach((q_synced, qIdx) => {
                        const q_loaded = loadedAuditData.p1Questions[qIdx];
                        if (q_loaded && q_loaded.projectEvaluations) {
                            q_synced.projectEvaluations.forEach((pe_synced, peIdx) => {
                                const pe_loaded = q_loaded.projectEvaluations.find(pel => pel.projectId === pe_synced.projectId);
                                if (pe_loaded) {
                                    pe_synced.evaluation = pe_loaded.evaluation || 'n.e.';
                                    pe_synced.auditRecord = pe_loaded.auditRecord || '';
                                }
                            });
                        }
                    });
                }
                
                // Handle potential old assessment structure
                if (auditData.assessment && !auditData.projectAssessments) { // Old single assessment exists
                    auditData.projectAssessments = {}; // Initialize new structure
                    // If there's one project, can try to map old assessment to it.
                    const projects = getProjects();
                    if (projects.length === 1) {
                        const internalId = generateInternalProjectId(projects[0], 0);
                        auditData.projectAssessments[internalId] = {
                            ...auditData.assessment,
                            projectId: projects[0].projectId,
                            projectName: projects[0].projectName
                        };
                    }
                    delete auditData.assessment; // Remove old structure
                }


                renderAllPages();
                updateAssessment(); // This will refresh assessment page if active
                
                switchTab(document.querySelector('.nav-tab[data-page="header"]'));
                alert(`已加载报告 "${auditData.header.reportNo}"。`);
            } else {
                console.error(`无法加载清单项目索引 ${index}: 数据无效。`);
                alert(`无法加载清单项目索引 ${index}: 数据无效。`);
            }
        }
        
        function removeChecklistItem(index) {
            if (confirm(`确定要从清单中删除报告 "${auditData.checklist[index].reportNo}" 吗？`)) {
                auditData.checklist.splice(index, 1);
                renderChecklistTable();
                saveDataToLocalStorage();
            }
        }


        function renderManualPage() {
            const page = document.getElementById('manualPage');
            page.innerHTML = `
                <h2>使用说明</h2>
                <h3>1. 操作步骤</h3>
                <ol>
                    <li><strong>审核计划:</strong> 在"审核计划"页面规划您的潜在供方分析活动。点击"添加计划行"创建新计划。填写供应商名称和"审核日期起"后，系统会自动生成报告编号。点击某行的"开始审核"按钮，该计划的报告编号和供应商名称会自动填充到"基本信息"页面，并跳转至该页面开始详细审核。</li>
                    <li><strong>基本信息:</strong> 
                        <ul>
                            <li>填写本次审核的基本信息，包括报告编号、供应商信息、审核员及被审核方人员。</li>
                            <li>在"审核项目矩阵"中，点击"添加审核项目"来定义一个或多个具体的审核项目。每个项目应有唯一的"项目编号"。提问表中的评价将基于这些项目进行。</li>
                        </ul>
                    </li>
                    <li><strong>P1提问表:</strong> 
                        <ul>
                            <li>如果已在基本信息中定义审核项目，则此处每个提问下方会针对每个项目显示独立的"审核记录"框和"评价"下拉框。</li>
                            <li>为每个项目的每个提问记录发现并选择评价。</li>
                            <li>"全部绿色"、"随机评价"、"全部N.E."按钮将作用于所有项目的所有提问。</li>
                        </ul>
                    </li>
                    <li><strong>评估表:</strong> "评估表"页面会为"基本信息"中定义的每个审核项目分别显示评估统计和总体评估结果。</li>
                    <li><strong>措施计划:</strong> 对于每个项目中评价为黄色或红色的提问，会自动在"措施计划"中创建或更新条目。措施计划表会标明问题相关的项目。</li>
                    <li><strong>审核清单:</strong> 完成评估后，可在"评估表"页面点击"添加到审核清单"按钮，将本次审核的完整数据（包括各项目的评估）保存到清单中。"审核清单"页面的"各项目结论摘要"列会显示每个项目的评估结果。</li>
                    <li><strong>数据管理:</strong> 使用顶部工具栏的"保存数据"、"清楚数据"、"加载数据"、"导出Markdown"、"导出Excel"功能。</li>
                </ol>

                <h3>2. P1评价规则 (依据VDA 6.3 第5.5节) - (此规则适用于每个独立评估的项目)</h3>
                <p>根据评价为红色(R)/黄色(Y)/绿色(G)提问的数量，计算得出该项目潜在供方分析的总体评估结果：</p>
                <ul>
                    <li><strong>不合格供方 (红色):</strong>
                        <ul>
                            <li>黄色提问数量 > 12 个 <strong>或</strong></li>
                            <li>红色提问数量 ≥ 1 个</li>
                        </ul>
                    </li>
                    <li><strong>批准的供方 (绿色):</strong>
                        <ul>
                            <li>黄色提问数量 ≤ 6 个 <strong>并且</strong></li>
                            <li>红色提问数量 = 0 个</li>
                        </ul>
                    </li>
                     <li><strong>有条件批准的供方 (黄色):</strong>
                        <ul>
                            <li>黄色提问数量 ≤ 12 个 <strong>并且</strong></li>
                            <li>红色提问数量 = 0 个</li>
                            <li>(此条件在"批准的供方"不满足时适用，即黄色数量在7-12个之间且无红色)</li>
                        </ul>
                    </li>
                </ul>
                <p><strong>重要提示:</strong></p>
                <ul>
                    <li>如果一个提问没有被评价（显示为N.E.），必须说明原因。每个项目最多允许3个提问被标记为N.E.。否则，结果可能不具有可比性。</li>
                </ul>
                <h3>3. Logo上传与公司名称修改</h3>
                <p>点击页面左上角的公司Logo区域可上传新Logo。点击公司名称可修改名称。更改会保存到浏览器本地存储。</p>
            `;
        }
        
        function renderAllPages() {
            renderPlanPage();
            renderHeaderPage(); // Includes renderAuditProjectsTable
            renderQuestionnairePage();
            renderAssessmentPage(); // Relies on updateAssessment being called
            renderActionPlanPage(); // Includes renderActionPlanTable
            renderChecklistPage(); // Includes renderChecklistTable
            renderManualPage(); 
        }

        // --- Data Persistence ---
        function saveDataToLocalStorage() {
            try {
                localStorage.setItem('vdaP1AuditData_v4', JSON.stringify(auditData)); // Incremented version
                alert('数据已保存到浏览器本地存储！');
            } catch (e) {
                console.error("Error saving data to localStorage:", e);
                alert('保存数据失败！可能是本地存储已满或浏览器不支持。');
            }
        }
        function clearDataFromLocalStorage() {
    if (confirm('确定要清除所有本地保存的数据吗？此操作不可恢复！')) {
        localStorage.removeItem('vdaP1AuditData_v4');
        localStorage.removeItem('companyLogo_v4');
        localStorage.removeItem('companyName_v4');
        alert('本地数据已清除！页面将刷新为初始状态。');
        auditData = {
            plan: [],
            planMeta: {
                preparedBy: '',
                preparationDate: '',
                approvedBy: '',
                approvalDate: ''
            },
            header: {
                reportNo: '', supplierName: '', supplierAddress: '',
                auditStartDate: '', auditEndDate: '', auditProject: '', auditReason: '',
                auditProjects: [],
                auditors: [{ name: '', type: '', email: '', phone: '' }],
                auditees: [{ name: '', position: '', department: '', email: '', phone: '' }]
            },
            p1Questions: [],
            projectAssessments: {},
            actionPlan: [],
            checklist: []
        };
        initializeP1Questions();
        renderAllPages();
        updateAssessment();
        loadCompanyLogo();
    }
}

        function loadDataFromLocalStorage() {
            const savedData = localStorage.getItem('vdaP1AuditData_v4'); 
            if (savedData) {
                try {
                    const parsedData = JSON.parse(savedData);
                    
                    const defaultAuditSession = {
                        header: {
                            reportNo: '', supplierName: '', supplierAddress: '',
                            auditStartDate: '', auditEndDate: '', auditProject: '', auditReason: '',
                            auditProjects: [], // Important new field
                            auditors: [{ name: '', type: '', email: '', phone: '' }],
                            auditees: [{ name: '', position: '', department: '', email: '', phone: '' }]
                        },
                        p1Questions: [],
                        projectAssessments: {}, // Important new field
                        actionPlan: []
                    };

                    auditData = {
                        plan: parsedData.plan || [],
                        planMeta: { ...(auditData.planMeta || {}), ...(parsedData.planMeta || {}) },
                        header: { ...defaultAuditSession.header, ...(parsedData.header || {}) },
                        p1Questions: parsedData.p1Questions || [],
                        projectAssessments: parsedData.projectAssessments || {},
                        actionPlan: parsedData.actionPlan || [],
                        checklist: parsedData.checklist || []
                    };
                    
                    // Ensure auditProjects array exists
                    if (!auditData.header.auditProjects) auditData.header.auditProjects = [];

                    // Initialize or migrate P1 questions and their project evaluations
                    if (auditData.p1Questions.length === 0 || auditData.p1Questions.length !== P1_QUESTION_IDS.length || !auditData.p1Questions[0].projectEvaluations) {
                        const oldP1Data = auditData.p1Questions;
                        initializeP1Questions(); // Sets up shells, calls sync
                        
                        // If old data existed (even malformed), try to merge project-specific evaluations
                        if (oldP1Data && oldP1Data.length > 0) {
                            auditData.p1Questions.forEach(newQ => {
                                const matchingOldQ = oldP1Data.find(oldQ => oldQ.id === newQ.id);
                                if (matchingOldQ) {
                                    newQ.attachment = matchingOldQ.attachment || '';
                                    if (matchingOldQ.projectEvaluations && Array.isArray(matchingOldQ.projectEvaluations)) {
                                        newQ.projectEvaluations.forEach(newPE => {
                                            const oldPE = matchingOldQ.projectEvaluations.find(op => op.projectId === newPE.projectId);
                                            if (oldPE) {
                                                newPE.evaluation = oldPE.evaluation || 'n.e.';
                                                newPE.auditRecord = oldPE.auditRecord || '';
                                            }
                                        });
                                    } else if (matchingOldQ.evaluation && newQ.projectEvaluations.length > 0 && getProjects().length <= 1) {
                                        // Migrate very old single evaluation to the first (or only) project
                                        newQ.projectEvaluations[0].evaluation = matchingOldQ.evaluation;
                                        newQ.projectEvaluations[0].auditRecord = matchingOldQ.auditRecord || '';
                                    }
                                }
                            });
                        }
                    } else {
                        // Data structure seems okay, just run sync to ensure consistency
                        syncP1QuestionProjectEvaluations();
                    }

                    // Migrate old assessment structure if present
                    if (parsedData.assessment && !parsedData.projectAssessments) {
                        auditData.projectAssessments = {};
                        const projects = getProjects();
                        if (projects.length === 1) {
                             const internalId = generateInternalProjectId(projects[0], 0);
                             auditData.projectAssessments[internalId] = { 
                                ...parsedData.assessment, 
                                projectId: projects[0].projectId, 
                                projectName: projects[0].projectName 
                            };
                        } else if (projects.length === 0 && Object.keys(parsedData.assessment).length > 0) {
                            // If no projects defined but old assessment exists, it's orphaned.
                            // Could create a "Default Project" and assign, or just log.
                            console.warn("Loaded old assessment data but no projects defined to map it to.");
                        }
                    }
                    
                    auditData.plan.forEach(item => {
                        if (!item.reportNo || item.reportNo === "日期未填" || item.reportNo === "日期无效" || !/^\d{6}\d{4}$/.test(item.reportNo) ) {
                            item.reportNo = generateReportNoForPlanItem(item, auditData.plan);
                        }
                    });

                    renderAllPages();
                    updateAssessment(); // Recalculate and display assessments
                    alert('数据已从本地存储加载！');
                } catch (e) {
                    console.error("Error parsing saved data:", e);
                    alert('加载数据失败！保存的数据可能已损坏。将使用初始数据。');
                    auditData.header.auditProjects = []; // Ensure it's an empty array for fresh start
                    initializeP1Questions(); 
                    renderAllPages();
                    updateAssessment();
                }
            } else {
                alert('未找到本地保存的数据。将使用初始数据。');
                auditData.header.auditProjects = []; // Ensure it's an empty array for fresh start
                initializeP1Questions(); 
                renderAllPages();
                updateAssessment();
            }
        }
        
        // --- Export Functions ---
        function exportToMarkdown() {
            let md = `# VDA 6.3 P1 潜在供方分析报告\n\n`;
            md += `## 基本信息\n`;
            md += `- 报告编号: ${auditData.header.reportNo || 'N/A'}\n`;
            md += `- 供应商名称: ${auditData.header.supplierName || 'N/A'}\n`;
            // ... (other header fields)
            md += `\n### 审核项目\n`;
            if (getProjects().length > 0) {
                getProjects().forEach((p, i) => md += `- ${i+1}. ${p.projectName} (编号: ${p.projectId})\n`);
            } else { md += `- 无\n`; }
            md += `\n`;
            // ... (auditors, auditees)

            md += `## 各项目评估结果\n`;
            const projectsForMd = getProjects();
            if (projectsForMd.length > 0) {
                projectsForMd.forEach((p,idx) => {
                    const internalId = generateInternalProjectId(p, idx);
                    const assessment = auditData.projectAssessments[internalId];
                    md += `### 项目: ${p.projectName} (编号: ${p.projectId})\n`;
                    if (assessment) {
                        md += `- 绿色 (G): ${assessment.greenCount}\n`;
                        md += `- 黄色 (Y): ${assessment.yellowCount}\n`;
                        md += `- 红色 (R): ${assessment.redCount}\n`;
                        md += `- N.E.: ${assessment.neCount}\n`;
                        md += `- **总体评估:** ${assessment.overallResultText}\n\n`;
                    } else {
                        md += `- 无评估数据\n\n`;
                    }
                });
            } else { md += `无项目被评估。\n\n`; }

            md += `## P1提问表详情\n`;
            auditData.p1Questions.forEach(q => {
                md += `**${q.id}${q.isStar ? '*' : ''}**: ${q.text}\n`;
                if (q.projectEvaluations && q.projectEvaluations.length > 0) {
                    q.projectEvaluations.forEach(pe => {
                        md += `项目: ${pe.projectName} (ID: ${pe.projectId})\n`;
                        md += `- 评价: ${pe.evaluation}\n`;
                        md += `- 审核记录:\n`;
                        let record = (pe.auditRecord || '无记录')
                            .replace(/[ \t　]+$/gm, '')
                            .replace(/[\u200B-\u200D\uFEFF]/g, '')
                            .replace(/(\r\n|\r|\n){2,}/g, '\n')
                            .replace(/(\r\n|\r|\n)/g, '<br>');
                        md += `  ${record}\n`;
                    });
                } else {
                     md += `- 无项目特定评价记录。\n`;
                }
                if (q.attachment) md += `- 问题附件: ${q.attachment}\n`;
                md += `\n`;
            });

            // 不再导出措施计划部分
            const blob = new Blob([md], { type: 'text/markdown;charset=utf-8' });
            saveAs(blob, `VDA_P1_Report_${auditData.header.reportNo || '评估'}.md`);
        }

        function exportToExcel() {
            const wb = XLSX.utils.book_new();

            // Header Data Sheet
            const headerWSData = [
                ["报告编号", auditData.header.reportNo],
                ["供应商名称", auditData.header.supplierName],
                ["供应商地址", auditData.header.supplierAddress],
                ["审核开始日期", auditData.header.auditStartDate],
                ["审核结束日期", auditData.header.auditEndDate],
                ["审核原因", auditData.header.auditReason],
                [], ["审核项目列表"], ["No.", "项目名称", "项目编号"]
            ];
            getProjects().forEach((p, i) => headerWSData.push([i+1, p.projectName, p.projectId]));
            headerWSData.push([]); headerWSData.push(["审核员信息"]); /* ... auditors ... */
            headerWSData.push([]); headerWSData.push(["被审核方人员信息"]); /* ... auditees ... */
            // (Auditor/Auditee export part is simplified here for brevity, keep original logic)
            const headerWS = XLSX.utils.aoa_to_sheet(headerWSData);
            XLSX.utils.book_append_sheet(wb, headerWS, "基本信息");

            // P1 Questions Sheet
            const questionsWSData = [["要素", "提问编号", "星号", "提问内容", "最低要求", "实施示例", "项目名称/ID", "项目审核记录", "项目评价", "问题附件"]];
            auditData.p1Questions.forEach(q => {
                if (q.projectEvaluations && q.projectEvaluations.length > 0) {
                    q.projectEvaluations.forEach(pe => {
                        questionsWSData.push([q.elementName, q.id, q.isStar ? '*' : '', q.text, q.minReq, q.implEx, `${pe.projectName} (${pe.projectId})`, pe.auditRecord, pe.evaluation, q.attachment]);
                    });
                } else { // Case where a question might not have project evaluations (e.g. no projects defined)
                     questionsWSData.push([q.elementName, q.id, q.isStar ? '*' : '', q.text, q.minReq, q.implEx, "N/A", "N/A", "N/A", q.attachment]);
                }
            });
            const questionsWS = XLSX.utils.aoa_to_sheet(questionsWSData);
            XLSX.utils.book_append_sheet(wb, questionsWS, "P1提问表");

            // Assessment Results Sheet
            const assessmentWSData = [["项目名称/ID", "评价类型", "数量", "总体评估"]];
            Object.values(auditData.projectAssessments).forEach(pa => {
                assessmentWSData.push([`${pa.projectName} (${pa.projectId})`, "绿色 (G)", pa.greenCount, pa.overallResultText]);
                assessmentWSData.push(["", "黄色 (Y)", pa.yellowCount, ""]);
                assessmentWSData.push(["", "红色 (R)", pa.redCount, ""]);
                assessmentWSData.push(["", "N.E.", pa.neCount, ""]);
                assessmentWSData.push([]); // Spacer row
            });
            if (Object.keys(auditData.projectAssessments).length === 0) {
                assessmentWSData.push(["无项目评估数据", "", "", ""]);
            }
            const assessmentWS = XLSX.utils.aoa_to_sheet(assessmentWSData);
            XLSX.utils.book_append_sheet(wb, assessmentWS, "评估结果");
            
            // Action Plan Sheet
            const actionPlanWSData = [["No.", "项目名称/ID", "提问编号", "审核记录摘要", "评价", "责任人", "原因分析", "纠正措施", "预计完成日期", "状态", "有效性验证", "关闭日期"]];
            auditData.actionPlan.forEach((item, index) => {
                actionPlanWSData.push([index + 1, `${item.projectName} (${item.projectId})`, item.questionId, item.auditRecordSummary, item.evaluation, item.responsible, item.rootCause, item.correctiveAction, item.estCompletionDate, item.status, item.effectivenessVerification, item.closingDate]);
            });
            const actionPlanWS = XLSX.utils.aoa_to_sheet(actionPlanWSData);
            XLSX.utils.book_append_sheet(wb, actionPlanWS, "措施计划");

            // Plan Data Sheet (as before)
            const planWSData = [["No.", "报告编号", "供应商名称", "审核日期起", "审核日期至", "审核原因", "审核组长", "备注"]];
             auditData.plan.forEach((item, index) => {
                planWSData.push([index + 1, item.reportNo, item.supplierName, item.startDate, item.endDate, item.reason, item.leader, item.notes]);
            });
            planWSData.push([]);
            planWSData.push(["编制", auditData.planMeta.preparedBy, "编制日期", auditData.planMeta.preparationDate]);
            planWSData.push(["审批", auditData.planMeta.approvedBy, "审批日期", auditData.planMeta.approvalDate]);
            const planWS = XLSX.utils.aoa_to_sheet(planWSData);
            XLSX.utils.book_append_sheet(wb, planWS, "审核计划");


            // Checklist Summary Sheet
            const checklistWSData = [["No.", "报告编号", "审核日期", "项目", "结论"]];
            auditData.checklist.forEach((item, index) => {
                if (item.fullData && item.fullData.projectAssessments && Object.keys(item.fullData.projectAssessments).length > 0) {
                    Object.values(item.fullData.projectAssessments).forEach(pa => {
                         checklistWSData.push([index + 1, item.reportNo, item.auditDate, `${pa.projectName} (${pa.projectId})`, pa.overallResultText]);
                    });
                } else if (item.fullData && item.fullData.assessment) { // old format
                     checklistWSData.push([index + 1, item.reportNo, item.auditDate, "旧格式整体评估", item.fullData.assessment.overallResultText]);
                } else {
                     checklistWSData.push([index + 1, item.reportNo, item.auditDate, "无项目", "N/A"]);
                }
            });
            const checklistWS = XLSX.utils.aoa_to_sheet(checklistWSData);
            XLSX.utils.book_append_sheet(wb, checklistWS, "审核清单概要");

            XLSX.writeFile(wb, `VDA_P1_Report_${auditData.header.reportNo || '评估汇总'}.xlsx`);
        }


        // --- Tab Navigation ---
        const tabs = document.querySelectorAll('.nav-tab');
        const pages = document.querySelectorAll('.page');

        function switchTab(activeTab) {
            tabs.forEach(tab => tab.classList.remove('active'));
            pages.forEach(page => page.classList.remove('active'));
            activeTab.classList.add('active');
            const activePageId = activeTab.dataset.page + 'Page';
            const activePage = document.getElementById(activePageId);
            if (activePage) activePage.classList.add('active');
            
            if (activePageId === 'questionnairePage') {
                renderQuestionnairePage(); // Ensure it's rendered correctly if projects changed
            } else if (activePageId === 'assessmentPage') {
                updateAssessment(); // This will also render the assessment page content
            } else if (activePageId === 'actionPlanPage') {
                renderActionPlanTable(); 
            } else if (activePageId === 'checklistPage') {
                renderChecklistTable();
            }
        }

        tabs.forEach(tab => {
            tab.addEventListener('click', () => switchTab(tab));
        });
        
        // --- Logo Upload ---
        const logoContainer = document.getElementById('logoContainer');
        const logoUploadInput = document.getElementById('logoUpload');
        const companyLogoImg = document.getElementById('companyLogo');
        const companyNameDisplay = document.getElementById('companyNameDisplay');

        logoContainer.addEventListener('click', () => logoUploadInput.click());

        logoUploadInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    companyLogoImg.src = e.target.result;
                    localStorage.setItem('companyLogo_v4', e.target.result); 
                }
                reader.readAsDataURL(file);
            }
        });
        
        function loadCompanyLogo() {
            const savedLogo = localStorage.getItem('companyLogo_v4');
            if (savedLogo) companyLogoImg.src = savedLogo;
            const savedName = localStorage.getItem('companyName_v4');
            if (savedName) {
                companyNameDisplay.textContent = savedName;
            }
        }
        companyNameDisplay.addEventListener('click', (e) => {
            e.stopPropagation(); 
            const newName = prompt("请输入新的公司名称:", companyNameDisplay.textContent);
            if (newName && newName.trim() !== "") {
                companyNameDisplay.textContent = newName.trim();
                localStorage.setItem('companyName_v4', newName.trim());
            }
        });


        // --- Initialization ---
        document.addEventListener('DOMContentLoaded', () => {
    loadCompanyLogo();
    document.getElementById('saveDataBtn').addEventListener('click', saveDataToLocalStorage);
    document.getElementById('clearDataBtn').addEventListener('click', clearDataFromLocalStorage);
    document.getElementById('loadDataBtn').addEventListener('click', loadDataFromLocalStorage);
    document.getElementById('exportMarkdownBtn').addEventListener('click', exportToMarkdown);
    document.getElementById('exportExcelBtn').addEventListener('click', exportToExcel);

    // 悬浮保存按钮事件绑定
    const floatingSaveBtn = document.getElementById('floatingSaveBtn');
    if (floatingSaveBtn) {
        floatingSaveBtn.addEventListener('click', () => {
            saveDataToLocalStorage();
            // 添加点击反馈效果
            floatingSaveBtn.style.transform = 'scale(0.9)';
            setTimeout(() => {
                floatingSaveBtn.style.transform = 'scale(1)';
            }, 150);
        });
    }

    loadDataFromLocalStorage(); 
    
    const initialActiveTab = document.querySelector('.nav-tab.active');
    if (initialActiveTab) {
        switchTab(initialActiveTab);
    } else {
        if (tabs.length > 0) switchTab(tabs[0]);
    }
});

    </script>
</body>
</html>