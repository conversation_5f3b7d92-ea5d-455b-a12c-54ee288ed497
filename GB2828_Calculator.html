<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GB2828 抽样检验计算器</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .input-section {
            background-color: #e0f7fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #b2ebf2;
        }
        .input-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .input-group label {
            flex: 0 0 120px;
            font-weight: bold;
            color: #00796b;
        }
        .input-group select, .input-group input[type="number"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        .input-group .radio-group label {
            flex: unset;
            margin-right: 15px;
            font-weight: normal;
            color: #333;
        }
        .input-group .radio-group input[type="radio"] {
            margin-right: 5px;
        }
        .result-section {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ffcc80;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .result-table th, .result-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .result-table th {
            background-color: #ff9800;
            color: white;
        }
        .result-table td {
            background-color: #ffe0b2;
            font-weight: bold;
        }
        .result-table .aql-header {
            background-color: #d32f2f;
            color: white;
        }
        .result-table .sample-size-header {
            background-color: #c2185b;
            color: white;
        }
        .result-table .acceptance-header {
            background-color: #512da8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GB2828(ISO 2859) - 单次抽样计划</h1>
        <h2>抽样数量计算器</h2>

        <div class="input-section">
            <div class="input-group">
                <label for="inspectionMode">检验模式</label>
                <div class="radio-group">
                    <label><input type="radio" name="inspectionMode" value="normal" checked>正常检验</label>
                    <label><input type="radio" name="inspectionMode" value="tightened">加严检验</label>
                    <label><input type="radio" name="inspectionMode" value="reduced">放宽检验</label>
                </div>
            </div>
            <div class="input-group">
                <label for="batchSize">批量</label>
                <select id="batchSize">
                    <option value="2">2~8</option>
                    <option value="9">9~15</option>
                    <option value="16">16~25</option>
                    <option value="26">26~50</option>
                    <option value="51">51~90</option>
                    <option value="91">91~150</option>
                    <option value="151">151~280</option>
                    <option value="281">281~500</option>
                    <option value="501" selected>501~1200</option>
                    <option value="1201">1201~3200</option>
                    <option value="3201">3201~10000</option>
                    <option value="10001">10001~35000</option>
                    <option value="35001">35001~150000</option>
                    <option value="150001">150001~500000</option>
                    <option value="500001">500001及以上</option>
                </select>
            </div>
            <div class="input-group">
                <label for="inspectionLevel">检验等级</label>
                <select id="inspectionLevel">
                    <option value="I">I</option>
                    <option value="II" selected>II</option>
                    <option value="III">III</option>
                    <option value="S-1">S-1</option>
                    <option value="S-2">S-2</option>
                    <option value="S-3">S-3</option>
                    <option value="S-4">S-4</option>
                </select>
            </div>

        </div>

        <div class="result-section">
            <h3>计算结果</h3>
            <table class="result-table">
                <thead>
                    <tr>
                        <th></th>
                        <th class="aql-header">AQL</th>
                        <th class="sample-size-header">样本量</th>
                        <th class="acceptance-header">最大可接受</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>致命不合格</td>
                        <td id="aqlCritical">
                            <select id="aqlCriticalSelect">
                                <option value="0">0</option>
                                <option value="0.065">0.065</option>
                                <option value="0.10">0.10</option>
                                <option value="0.15">0.15</option>
                                <option value="0.25">0.25</option>
                                <option value="0.40">0.40</option>
                                <option value="0.65">0.65</option>
                                <option value="1.0">1.0</option>
                                <option value="1.5">1.5</option>
                                <option value="2.5">2.5</option>
                                <option value="4.0">4.0</option>
                                <option value="6.5">6.5</option>
                                <option value="10.0">10.0</option>
                                <option value="15.0">15.0</option>
                                <option value="25.0">25.0</option>
                                <option value="40.0">40.0</option>
                                <option value="65.0">65.0</option>
                                <option value="100.0">100.0</option>
                                <option value="150.0">150.0</option>
                                <option value="250.0">250.0</option>
                                <option value="400.0">400.0</option>
                                <option value="650.0">650.0</option>
                                <option value="1000.0">1000.0</option>
                            </select>
                        </td>
                        <td id="sampleSizeCritical">-</td>
                        <td id="acceptanceCritical">-</td>
                    </tr>
                    <tr>
                        <td>严重不合格</td>
                        <td id="aqlMajor">
                            <select id="aqlMajorSelect">
                                <option value="0">0</option>
                                <option value="0.065">0.065</option>
                                <option value="0.10">0.10</option>
                                <option value="0.15">0.15</option>
                                <option value="0.25">0.25</option>
                                <option value="0.40">0.40</option>
                                <option value="0.65">0.65</option>
                                <option value="1.0" selected>1.0</option>
                                <option value="1.5">1.5</option>
                                <option value="2.5">2.5</option>
                                <option value="4.0">4.0</option>
                                <option value="6.5">6.5</option>
                                <option value="10.0">10.0</option>
                                <option value="15.0">15.0</option>
                                <option value="25.0">25.0</option>
                                <option value="40.0">40.0</option>
                                <option value="65.0">65.0</option>
                                <option value="100.0">100.0</option>
                                <option value="150.0">150.0</option>
                                <option value="250.0">250.0</option>
                                <option value="400.0">400.0</option>
                                <option value="650.0">650.0</option>
                                <option value="1000.0">1000.0</option>
                            </select>
                        </td>
                        <td id="sampleSizeMajor">-</td>
                        <td id="acceptanceMajor">-</td>
                    </tr>
                    <tr>
                        <td>轻微不合格</td>
                        <td id="aqlMinor">
                            <select id="aqlMinorSelect">
                                <option value="0">0</option>
                                <option value="0.065">0.065</option>
                                <option value="0.10">0.10</option>
                                <option value="0.15">0.15</option>
                                <option value="0.25">0.25</option>
                                <option value="0.40">0.40</option>
                                <option value="0.65">0.65</option>
                                <option value="1.0">1.0</option>
                                <option value="1.5">1.5</option>
                                <option value="2.5">2.5</option>
                                <option value="4.0" selected>4.0</option>
                                <option value="6.5">6.5</option>
                                <option value="10.0">10.0</option>
                                <option value="15.0">15.0</option>
                                <option value="25.0">25.0</option>
                                <option value="40.0">40.0</option>
                                <option value="65.0">65.0</option>
                                <option value="100.0">100.0</option>
                                <option value="150.0">150.0</option>
                                <option value="250.0">250.0</option>
                                <option value="400.0">400.0</option>
                                <option value="650.0">650.0</option>
                                <option value="1000.0">1000.0</option>
                            </select>
                        </td>
                        <td id="sampleSizeMinor">-</td>
                        <td id="acceptanceMinor">-</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // JavaScript will go here to implement the calculation logic
        // Data tables for GB 2828.1-2012 will be defined here.
        // Functions to calculate sample size, Ac, Re based on inputs.



        let aqlData = {};

        // Load the JSON data
        fetch('aql_data.json')
            .then(response => {
                console.log('Fetch response received:', response);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Raw AQL Data loaded:', data);
                parsedAQLTables = parseAQLData(data);
                console.log('Parsed AQL Tables:', parsedAQLTables);
                calculateGB2828(); // Perform initial calculation after data is loaded
            })
            .catch(error => console.error('Error loading or parsing AQL data:', error));



        // Function to parse the AQL data from the JSON structure
        function parseAQLData(data) {
            const parsedData = {
                normal: {},
                tightened: {},
                reduced: {}
            };

            let currentMode = '';
            let aqlHeaders = [];
            let aqlAcReHeaders = [];

            data.forEach((row, rowIndex) => {
                console.log(`Processing row ${rowIndex}:`, row);
                if (typeof row[0] === 'string') {
                    if (row[0].includes('正常检验')) {
                        currentMode = 'normal';
                        console.log('Detected Normal Inspection Mode');
                    } else if (row[0].includes('加严检验')) {
                        currentMode = 'tightened';
                        console.log('Detected Tightened Inspection Mode');
                    } else if (row[0].includes('放宽检验')) {
                        currentMode = 'reduced';
                        console.log('Detected Reduced Inspection Mode');
                    }
                }

                // Identify AQL headers row (e.g., row with '0.010', '0.015')
                if (currentMode && row.some(cell => typeof cell === 'string' && cell.trim() === '0.010')) {
                    aqlHeaders = row.filter(h => h !== null && h !== undefined).map(h => typeof h === 'string' ? h.trim() : h);
                    console.log('Detected AQL Headers:', aqlHeaders);
                } 
                // Identify Ac Re headers row (e.g., row with 'Ac Re')
                else if (currentMode && row.some(cell => typeof cell === 'string' && cell.trim() === 'Ac Re')) {
                    aqlAcReHeaders = row.filter(h => h !== null && h !== undefined).map(h => typeof h === 'string' ? h.trim() : h);
                    console.log('Detected Ac Re Headers:', aqlAcReHeaders);
                } 
                // Identify data rows (e.g., row starting with a single letter A-R)
                else if (currentMode && typeof row[0] === 'string' && row[0].length === 1 && /[A-R]/.test(row[0])) {
                    const sampleSizeCode = row[0];
                    const rowData = {};
                    // Assuming AQL values start from index 2 in the aqlHeaders array
                    for (let i = 0; i < aqlHeaders.length; i++) {
                        const aqlValue = aqlHeaders[i];
                        // The actual Ac Re values are offset by 2 columns in the data row compared to aqlHeaders
                        const acReValue = row[i + 2]; 
                        if (acReValue && typeof acReValue === 'string') {
                            const parts = acReValue.split(' ').map(Number);
                            if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
                                rowData[aqlValue] = parts;
                            } else {
                                console.warn(`Invalid Ac Re format for AQL ${aqlValue} at row ${rowIndex}:`, acReValue);
                            }
                        } else {
                            console.warn(`Missing or invalid Ac Re value for AQL ${aqlValue} at row ${rowIndex}:`, acReValue);
                        }
                    }
                    parsedData[currentMode][sampleSizeCode] = rowData;
                    console.log(`Parsed data for ${currentMode} mode, code ${sampleSizeCode}:`, rowData);
                }
            });
            return parsedData;
        }

        function getSampleSizeCode(batchSize, inspectionLevel) {
            console.log(`getSampleSizeCode called with batchSize: ${batchSize}, inspectionLevel: ${inspectionLevel}`);
            let batchRange = '';
            if (batchSize >= 2 && batchSize <= 8) batchRange = '2~8';
            else if (batchSize >= 9 && batchSize <= 15) batchRange = '9~15';
            else if (batchSize >= 16 && batchSize <= 25) batchRange = '16~25';
            else if (batchSize >= 26 && batchSize <= 50) batchRange = '26~50';
            else if (batchSize >= 51 && batchSize <= 90) batchRange = '51~90';
            else if (batchSize >= 91 && batchSize <= 150) batchRange = '91~150';
            else if (batchSize >= 151 && batchSize <= 280) batchRange = '151~280';
            else if (batchSize >= 281 && batchSize <= 500) batchRange = '281~500';
            else if (batchSize >= 501 && batchSize <= 1200) batchRange = '501~1200';
            else if (batchSize >= 1201 && batchSize <= 3200) batchRange = '1201~3200';
            else if (batchSize >= 3201 && batchSize <= 10000) batchRange = '3201~10000';
            else if (batchSize >= 10001 && batchSize <= 35000) batchRange = '10001~35000';
            else if (batchSize >= 35001 && batchSize <= 150000) batchRange = '35001~150000';
            else if (batchSize >= 150001 && batchSize <= 500000) batchRange = '150001~500000';
            else if (batchSize >= 500001) batchRange = '500001及以上';

            if (sampleSizeCodeTable[batchRange]) {
                const code = sampleSizeCodeTable[batchRange][inspectionLevel];
                console.log(`Found sample size code: ${code} for batch range ${batchRange}, inspection level ${inspectionLevel}`);
                return code;
            }
            console.warn(`No sample size code found for batch range ${batchRange}, inspection level ${inspectionLevel}`);
            return null;
        }

        function getSampleSize(sampleSizeCode) {
            console.log(`getSampleSize called with sampleSizeCode: ${sampleSizeCode}`);
            const size = sampleSizeMap[sampleSizeCode];
            console.log(`Found sample size: ${size} for code ${sampleSizeCode}`);
            return size;
        }


            function calculateGB2828() {
                console.log('calculateGB2828 called');
                const batchSizeInput = document.getElementById('batchSize').value;
                const batchSize = parseInt(batchSizeInput.split('~')[0]);
                const inspectionLevel = document.getElementById('inspectionLevel').value;
                const inspectionMode = document.querySelector('input[name="inspectionMode"]:checked').value;

                console.log(`Inputs: Batch Size: ${batchSize}, Inspection Level: ${inspectionLevel}, Inspection Mode: ${inspectionMode}`);

                // Validate inputs
                if (isNaN(batchSize) || !inspectionLevel || !inspectionMode) {
                    console.warn('Invalid input for calculation. Batch Size:', batchSize, 'Inspection Level:', inspectionLevel, 'Inspection Mode:', inspectionMode);
                    document.getElementById('sampleSizeCritical').textContent = '-';
                    document.getElementById('sampleSizeMajor').textContent = '-';
                    document.getElementById('sampleSizeMinor').textContent = '-';
                    document.getElementById('acceptanceCritical').textContent = '-';
                    document.getElementById('acceptanceMajor').textContent = '-';
                    document.getElementById('acceptanceMinor').textContent = '-';
                    return;
                }

                const sampleSizeCode = getSampleSizeCode(batchSize, inspectionLevel);
                const sampleSize = getSampleSize(sampleSizeCode);

                console.log(`Sample Size Code: ${sampleSizeCode}, Sample Size: ${sampleSize}`);

                document.getElementById('sampleSizeCritical').textContent = sampleSize || '-';
                document.getElementById('sampleSizeMajor').textContent = sampleSize || '-';
                document.getElementById('sampleSizeMinor').textContent = sampleSize || '-';

                const fatalAQL = document.getElementById('aqlCriticalSelect').value;
                const seriousAQL = document.getElementById('aqlMajorSelect').value;
                const minorAQL = document.getElementById('aqlMinorSelect').value;

                console.log(`AQLs: Critical: ${fatalAQL}, Major: ${seriousAQL}, Minor: ${minorAQL}`);

                function getAcRe(sampleSizeCode, aql, modeTable) {
                    console.log(`getAcRe called with sampleSizeCode: ${sampleSizeCode}, aql: ${aql}, modeTable:`, modeTable);
                    // Ensure aql is a string to match keys in modeTable
                    const aqlKey = String(aql).trim();
                    if (sampleSizeCode && modeTable && modeTable[sampleSizeCode] && modeTable[sampleSizeCode][aqlKey]) {
                        const [ac, re] = modeTable[sampleSizeCode][aqlKey];
                        console.log(`Found Ac/Re: Ac=${ac}, Re=${re} for sampleSizeCode ${sampleSizeCode}, AQL ${aqlKey}`);
                        return `Ac=${ac}, Re=${re}`;
                    }
                    console.warn(`No Ac/Re found for sampleSizeCode ${sampleSizeCode}, AQL ${aqlKey} in modeTable.`, modeTable ? modeTable[sampleSizeCode] : 'modeTable or sampleSizeCode not found');
                    return '-';
                }

                // Use the parsed data based on inspection mode
                const currentAQLTable = parsedAQLTables[inspectionMode];

                console.log('Current AQL Table for mode', inspectionMode, ':', currentAQLTable);

                if (!currentAQLTable) {
                    console.warn('AQL table not found for inspection mode:', inspectionMode);
                    document.getElementById('acceptanceCritical').textContent = '-';
                    document.getElementById('acceptanceMajor').textContent = '-';
                    document.getElementById('acceptanceMinor').textContent = '-';
                    return;
                }

                document.getElementById('acceptanceCritical').textContent = getAcRe(sampleSizeCode, fatalAQL, currentAQLTable);
                document.getElementById('acceptanceMajor').textContent = getAcRe(sampleSizeCode, seriousAQL, currentAQLTable);
                document.getElementById('acceptanceMinor').textContent = getAcRe(sampleSizeCode, minorAQL, currentAQLTable);
            }

            // Event listeners for input changes
            document.getElementById('batchSize').addEventListener('change', calculateGB2828);
            document.getElementById('inspectionLevel').addEventListener('change', calculateGB2828);
            document.getElementById('aqlCriticalSelect').addEventListener('change', calculateGB2828);
            document.getElementById('aqlMajorSelect').addEventListener('change', calculateGB2828);
            document.getElementById('aqlMinorSelect').addEventListener('change', calculateGB2828);

            document.querySelectorAll('input[name="inspectionMode"]').forEach(radio => {
                radio.addEventListener('change', calculateGB2828);
            });

            // Initial calculation on page load - removed as it's now called after data load
            // document.addEventListener('DOMContentLoaded', calculateGB2828);
        </script>

    </body>
</html>