<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDA 6.8:2024 潜在供方分析工具</title>

    <style>
        /* --- 全局样式和重置 --- */
        :root {
            --primary-color: #005a9c; /* 专业蓝 */
            --secondary-color: #007bff; /* 亮蓝 */
            --accent-color: #28a745; /* 成功绿 */
            --danger-color: #dc3545; /* 危险红 */
            --warning-color: #ffc107; /* 警告黄 */
            --light-gray: #f8f9fa;
            --medium-gray: #e9ecef;
            --dark-gray: #343a40;
            --text-color: #212529;
            --header-footer-bg: #343a40;
            --header-footer-text: #ffffff;
            --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: var(--light-gray);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* --- 布局和容器 --- */
        .app-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
        }

        .page {
            display: none;
            animation: fadeIn 0.5s;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* --- 顶部和底部 --- */
        .app-header {
            background-color: var(--header-footer-bg);
            color: var(--header-footer-text);
            padding: 4px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            position: sticky;
            top: 0;
            z-index: 999;
        }

        /* New grade colors for potential supplier analysis */
        .grade-green {
            background-color: #e6ffe6; /* Light green */
            color: var(--accent-color); /* Green text */
        }

        .grade-yellow {
            background-color: #fffacd; /* Lemon Chiffon */
            color: var(--warning-color); /* Yellow text */
        }

        .grade-red {
            background-color: #ffe6e6; /* Light red */
            color: var(--danger-color); /* Red text */
        }

        
        .company-info {
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
        }

        #company-logo {
            max-height: 40px;
            max-width: 150px;
            border-radius: 4px;
            /* background-color: white; */
            /* padding: 2px; */
        }

        #company-name {
            font-size: 1.5em;
            font-weight: bold;
        }

        .app-nav ul {
            list-style: none;
            display: flex;
            gap: 5px;
        }

        .app-nav button {
            background-color: transparent;
            color: var(--header-footer-text);
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 1em;
            border-radius: 4px;
            transition: background-color 0.3s, color 0.3s;
        }

        .app-nav button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .app-nav button.active {
            background-color: var(--primary-color);
            color: var(--header-footer-text);
            font-weight: bold;
        }

        .app-footer {
            background-color: var(--header-footer-bg);
            color: var(--header-footer-text);
            text-align: center;
            padding: 15px;
            font-size: 0.9em;
        }
        
        /* --- 卡片和表单元素 --- */
        .card {
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            padding: 20px;
        }

        .card-header {
            font-size: 1.5em;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        h2, h3 {
            color: var(--primary-color);
            margin-top: 20px;
            margin-bottom: 15px;
        }
        h2 { font-size: 1.3em; }
        h3 { font-size: 1.1em; }

        input[type="text"],
        input[type="date"],
        input[type="number"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
            margin-top: 5px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 5px rgba(0, 90, 156, 0.3);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-grid-2col {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px 30px;
        }
        
        .form-grid-4col {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        /* --- 按钮样式 --- */
        button, .button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 12px;
            font-size: 1em;
            line-height: 1;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.1s;
            text-align: center;
            display: inline-block;
        }

        button:hover, .button:hover {
            background-color: #004170;
        }
        
        button:active, .button:active {
            transform: scale(0.98);
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .btn-danger { background-color: var(--danger-color); }
        .btn-danger:hover { background-color: #a71d2a; }
        .btn-success { background-color: var(--accent-color); padding: 8px 12px; }
        .btn-success:hover { background-color: #1e7e34; }
        .btn-secondary { background-color: #6c757d; padding: 8px 12px; }
        .btn-secondary:hover { background-color: #545b62; }
        .btn-warning { background-color: var(--warning-color); color: var(--dark-gray); padding: 8px 12px; }
        .btn-warning:hover { background-color: #d39e00; }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
            align-items: center;
            font-size: 0.8em;
        }
        .plan-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* --- 表格样式 --- */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
            vertical-align: middle;
        }

        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        tbody tr:hover {
            background-color: #e2e6ea;
        }
        
        .new-audit-reason-row td {
            background-color: #fff3cd;
            padding: 15px;
        }
        
        /* === NEW & IMPROVED: Audit Plan Table Layout === */
        #audit-plan-table {
            table-layout: fixed; /* Crucial for predictable column widths */
            width: 100%;
        }
        #audit-plan-table th, #audit-plan-table td {
            text-align: center;
            word-wrap: break-word; /* Prevents long text from breaking layout */
        }
        #audit-plan-table .start-audit-btn {
            font-size: 0.85em; /* Smaller font */
            padding: 6px 10px;
        }
        #audit-plan-table .delete-icon-btn {
            width: 28px;
            height: 60px;
            border-radius: 5px;
            padding: 0;
            font-size: 1.1em;
            font-weight: bold;
            line-height: 28px;
        }
        #audit-plan-table .plan-input {
            text-align: center;
            font-size: 0.9em;
        }
        #audit-plan-table .checkbox-group {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
            padding: 5px;
        }
        #audit-plan-table .checkbox-row {
            display: flex;
            gap: 10px;
        }
        #audit-plan-table .checkbox-group label {
            font-weight: normal;
            margin: 0;
        }
        #audit-plan-table td:nth-child(5) input { /* Audit Date */
            margin-bottom: 4px;
        }
        #audit-plan-table td:nth-child(5) input:last-child {
            margin-bottom: 0;
        }
        /* Column Widths */
        #audit-plan-table colgroup { display: none; } /* Hide old colgroup if present */
        #audit-plan-table th:nth-child(1), #audit-plan-table td:nth-child(1) { width: 7%; }  /* 操作 */
        #audit-plan-table th:nth-child(2), #audit-plan-table td:nth-child(2) { width: 4%; }  /* No. */
        #audit-plan-table th:nth-child(3), #audit-plan-table td:nth-child(3) { width: 10%; } /* 报告编号 */
        #audit-plan-table th:nth-child(4), #audit-plan-table td:nth-child(4) { width: 15%; } /* 审核对象 */
”        #audit-plan-table th:nth-child(5), #audit-plan-table td:nth-child(5) { width: 10%; } /* 审核起止日期 */
        #audit-plan-table th:nth-child(6), #audit-plan-table td:nth-child(6) { width: 9%; } /* 审核类别 */
        #audit-plan-table th:nth-child(7), #audit-plan-table td:nth-child(7) { width: 15%; text-align: center; } /* 过程要素 */
        #audit-plan-table th:nth-child(8), #audit-plan-table td:nth-child(8) { width: 8%; }  /* 审核组长 */
        #audit-plan-table th:nth-child(9), #audit-plan-table td:nth-child(9) { width: 5%; }  /* 操作 */
        #audit-plan-table th:nth-child(10),#audit-plan-table td:nth-child(10){ width: auto; } /* 备注 */


        /* --- 提问表样式 --- */
        .process-element > .card-header {
            cursor: pointer;
            position: relative;
        }
        .process-element > .card-header::after {
            content: '▼'; /* Down arrow for collapsed state by default */
            position: absolute;
            right: 20px;
            transition: transform 0.3s;
        }
        .process-element:not(.collapsed) > .card-header::after {
            content: '▲'; /* Up arrow when expanded */
        }
        .process-element.collapsed > .card-header::after {
            content: '▼'; /* Down arrow for collapsed state */
            transform: none; /* Remove transform */
        }
        .process-element.collapsed .element-content {
            display: none;
        }

        .question-block {
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fff;
        }
        .question-title {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        .question-title .critical-star {
            color: var(--danger-color);
            font-weight: bold;
            font-size: 1.2em;
        }
        .question-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
        }
        .question-details h4 {
            font-size: 1em;
            color: #555;
            margin-top: 0;
            margin-bottom: 5px;
        }
        .question-details ul, .question-details ol {
            padding-left: 20px;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        .question-evaluation {
            margin-top: 15px;
        }
        .evaluation-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .evaluation-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }
        .score-input-container {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .score-input-container select {
            width: 120px;
        }
        .l6-process-step-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary-color);
        }
        
        #validation-error-msg {
            display: none;
            color: var(--danger-color);
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            font-weight: bold;
        }

        /* --- 评价矩阵 --- */
        #evaluation-matrix-container .matrix-table {
            margin-bottom: 30px;
        }
        #evaluation-matrix-container th:nth-child(2),
        #evaluation-matrix-container td:nth-child(2) {
            width: 60%;
        }
        #evaluation-matrix-container td {
            text-align: center;
        }
        #evaluation-matrix-container td:first-child,
        #evaluation-matrix-container td:nth-child(2) {
            text-align: left;
        }

        .evaluation-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .summary-card {
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
        }
        .summary-card h3 {
            margin-top: 0;
        }
        .summary-card .score {
            font-size: 2.5em;
            font-weight: bold;
        }
        .summary-card .grade {
            font-size: 3em;
            font-weight: bold;
            margin: 10px 0;
        }
        .grade-A { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;}
        .grade-B { background-color: #fff3cd; color: #856404; border: 1px solid #ffeeba;}
        .grade-C { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;}
        .grade-invalid { background-color: #e9ecef; color: #495057; border: 1px solid #ced4da;}

        #downgrade-reasons ul {
            list-style-type: none;
            padding: 0;
            text-align: left;
            margin: 10px auto;
            max-width: 400px;
        }
        #downgrade-reasons li {
            padding: 8px 5px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        #downgrade-reasons li:last-child {
            border-bottom: none;
        }

        #element-summary-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .element-summary-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            text-align: center;
        }
        .element-summary-item .el-name {
            font-weight: bold;
        }
        .element-summary-item .el-score {
            font-size: 1.5em;
            margin-top: 5px;
        }

        /* --- 响应式设计 --- */
        @media (max-width: 1200px) {
            #audit-plan-table th, #audit-plan-table td {
                font-size: 0.9em;
            }
        
        }
        @media (max-width: 992px) {
            .app-header {
                flex-direction: column;
                gap: 10px;
            }
            .app-nav ul {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
        @media (max-width: 768px) {
            .main-content {
                padding: 10px;
            }
            .form-grid-2col, .form-grid-4col {
                grid-template-columns: 1fr;
            }
            .question-details {
                grid-template-columns: 1fr;
            }
            .score-input-container {
                flex-direction: column;
                align-items: flex-start;
            }
            .score-input-container select {
                width: 100%;
            }
        }
        
        /* Modal for company info */
        .modal {
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0;
            top: 0;
            width: 100%; 
            height: 100%; 
            overflow: auto; 
            background-color: rgba(0,0,0,0.4);
            animation: fadeIn 0.3s;
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 25px;
            border: 1px solid #888;
            width: 90%;
            max-width: 500px;
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close-button:hover, .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部 -->
        <header class="app-header">
            <div class="company-info" id="company-info-clickable">
                <img id="company-logo" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzAwNWE5YyIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIj48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAsMTZjLTQuNDIgMC04LTMuNTgtOC04czMuNTgtOCA4LTggOCAzLjU4IDggOC0zLjU4IDgtOCA4em0tMS0xNGgtMnY2aDJWN2ptNCAwaC0ydjZoMlY3eiIvPjwvc3ZnPg==" alt="公司Logo">
                <span id="company-name">金马汽车部件有限公司</span>
            </div>
            <nav class="app-nav">
                <ul>
                    <li><button class="nav-btn active" data-page="plan">审核计划</button></li>
                    <li><button class="nav-btn" data-page="info">基础信息</button></li>
                    <li><button class="nav-btn" data-page="questions">提问表</button></li>
                    <li><button class="nav-btn" data-page="matrix">评价矩阵</button></li>
                    <li><button class="nav-btn" data-page="actions">措施计划</button></li>
                    <li><button class="nav-btn" data-page="checklist">审核清单</button></li>
                    <li><button class="nav-btn" data-page="instructions">使用说明</button></li>
                </ul>
            </nav>
        </header>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 审核计划页面 -->
            <div id="page-plan" class="page active">
                <div class="card">
                    <div class="card-header plan-actions">
                        <div>
                            <input type="number" id="plan-year" value="2025" style="width: 100px; display: inline-block; vertical-align: middle;">
                            <span id="plan-title" style="width: 400px; display: inline-block; vertical-align: middle; margin-left: 10px; font-weight: bold;"></span>
                        </div>
                        <div class="action-buttons" style="margin-top: 0;">
                            <button id="add-plan-row" class="btn-success" style="pointer-events: auto;">新增计划</button>
                            <button id="save-plan-btn" class="btn-secondary" style="pointer-events: auto;">保存</button>
                            <button id="export-plan-btn" class="btn-warning" style="pointer-events: auto;">导出 (CSV)</button>
                        </div>
                    </div>
                    <div style="overflow-x: auto;">
                        <table id="audit-plan-table">
                            <thead>
                                <tr>
                                    <th>操作</th>
                                    <th>No.</th>
                                    <th>报告编号</th>
                                    <th>审核对象</th>
                                    <th>审核起止日期</th>
                                    <th>审核类别</th>
                                    <th>过程要素</th>
                                    <th>审核组长</th>
                                    <th>操作</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="form-grid-4col" style="margin-top: 30px; border-top: 1px solid #ccc; padding-top: 20px;">
                        <div class="form-group">
                            <label for="plan-prepared-by">编制人</label>
                            <input type="text" id="plan-prepared-by" class="persist-input" data-field="preparedBy">
                        </div>
                        <div class="form-group">
                            <label for="plan-prepared-date">编制时间</label>
                            <input type="date" id="plan-prepared-date" class="persist-input" data-field="preparedDate">
                        </div>
                        <div class="form-group">
                            <label for="plan-approved-by">审批人</label>
                            <input type="text" id="plan-approved-by" class="persist-input" data-field="approvedBy">
                        </div>
                        <div class="form-group">
                            <label for="plan-approved-date">审批时间</label>
                            <input type="date" id="plan-approved-date" class="persist-input" data-field="approvedDate">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础信息页面 -->
            <div id="page-info" class="page">
                <div class="card">
                    <div class="card-header">基础信息</div>
                    <div class="form-grid-2col">
                        <div class="form-group"><label for="info-auditee">受审核组织</label><input type="text" id="info-auditee" class="persist-input"></div>
                        <div class="form-group"><label for="info-duns">D-U-N-S 编码</label><input type="text" id="info-duns" class="persist-input"></div>
                        <div class="form-group"><label for="info-location">审核地点</label><input type="text" id="info-location" class="persist-input"></div>
                        <div class="form-group"><label for="info-contact-tel">联系电话</label><input type="text" id="info-contact-tel" class="persist-input"></div>
                        <div class="form-group"><label for="info-report-name">报告名称</label><input type="text" id="info-report-name" class="persist-input"></div>
                        <div class="form-group"><label for="info-audit-start-date">审核开始日期</label><input type="date" id="info-audit-start-date" class="persist-input"></div>
                        <div class="form-group"><label for="info-order-no">订单编号</label><input type="text" id="info-order-no" class="persist-input"></div>
                        <div class="form-group"><label for="info-audit-end-date">审核结束日期</label><input type="date" id="info-audit-end-date" class="persist-input"></div>
                        <div class="form-group"><label for="info-project">审核项目</label><input type="text" id="info-project" class="persist-input"></div>
                        <div class="form-group"><label for="info-report-date">报告日期</label><input type="date" id="info-report-date" class="persist-input"></div>
                        <div class="form-group"><label for="info-shift">审核班次</label><input type="text" id="info-shift" class="persist-input"></div>
                        <div class="form-group"><label for="info-report-no">报告编号</label><input type="text" id="info-report-no" class="persist-input"></div>
                        
                        <!-- L6 Process Steps input is REMOVED as per your confirmation -->
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">审核员信息</div>
                    <button id="add-auditor-row" class="btn-success">添加审核员</button>
                    <table id="auditors-table">
                        <thead><tr><th>No.</th><th>审核员姓名</th><th>审核员类别</th><th>邮箱</th><th>电话</th><th>操作</th></tr></thead>
                        <tbody></tbody>
                    </table>
                </div>

                <div class="card">
                    <div class="card-header">被审核组织人员信息</div>
                    <button id="add-auditee-person-row" class="btn-success">添加人员</button>
                    <table id="auditee-persons-table">
                        <thead><tr><th>No.</th><th>人员姓名</th><th>部门</th><th>职务</th><th>邮箱</th><th>电话</th><th>操作</th></tr></thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <!-- 提问表页面 -->
            <div id="page-questions" class="page">
                <div class="card">
                  <div class="card-header">提问表操作</div>
                  <div class="action-buttons">
                    <button id="score-random-all" class="btn-secondary">随机打分</button>
                    <button id="score-10-all" class="btn-success">全部打10分</button>
                    <button id="score-clear-all" class="btn-danger">删除所有评分</button>
                  </div>
                </div>
                <div id="no-elements-selected-msg" style="display: none;" class="card">请先在“审核计划”页面选择要审核的过程要素并开始审核。</div>
                <div id="questions-container">
                    <!-- 提问表将动态生成在这里 -->
                </div>
            </div>

            <!-- 评价矩阵页面 -->
            <div id="page-matrix" class="page">
                 <div class="card">
                    <div class="card-header">评价矩阵与结果</div>
                    <div id="validation-error-msg"></div>
                    <div class="action-buttons" style="margin-top: 0; margin-bottom: 20px;">
                        <button id="calculate-matrix" class="btn-success">计算/刷新结果</button>
                        <button id="add-to-checklist-btn" class="btn-secondary">添加到审核清单</button>
                    </div>
                    <div class="evaluation-summary">
                        <div class="summary-card" id="overall-summary-card">
                            <h3>总体符合性 (E<sub>G</sub>)</h3>
                            <div class="score" id="overall-score">-.--%</div>
                            <div class="grade" id="overall-grade">-</div>
                        </div>
                    </div>
                    <div id="process-element-summary">
                        <h2>各过程要素符合性 (E<sub>Ln</sub>)</h2>
                        <div id="element-summary-container" class="form-grid"></div>
                    </div>
                    <h2>得分矩阵表</h2>
                    <div id="evaluation-matrix-container">
                        <!-- 得分矩阵将动态生成在这里 -->
                    </div>
                </div>
            </div>
            
            <!-- 措施计划页面 -->
            <div id="page-actions" class="page">
                <div class="card">
                    <div class="card-header">不符合项措施计划</div>
                     <p>所有得分低于10分的关键提问将自动生成在此处，请填写改进措施。</p>
                     <div class="action-buttons" style="margin-top:0; margin-bottom: 20px;">
                        <button id="refresh-action-plan" class="btn-success">刷新措施计划</button>
                    </div>
                    <div style="overflow-x: auto;">
                        <table id="action-plan-table">
                            <thead>
                                <tr>
                                    <th>No.</th>
                                    <th>提问编号</th>
                                    <th>提问</th>
                                    <th>得分</th>
                                    <th>审核记录</th>
                                    <th>发现的问题</th>
                                    <th>负责人</th>
                                    <th>措施</th>
                                    <th>计划完成日期</th>
                                    <th>状态</th>
                                    <th>有效性验证</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 审核清单页面 -->
            <div id="page-checklist" class="page">
                <div class="card">
                    <div class="card-header">审核历史清单</div>
                    <div class="action-buttons" style="margin-top:0; margin-bottom: 20px;">
                        <label for="load-checklist-file-input" class="button btn-success">加载审核清单 (JSON)</label>
                        <input type="file" id="load-checklist-file-input" accept=".json" style="display: none;">
                        <!-- Changed button text to reflect JSON download -->
                        <button id="download-checklist-btn" class="btn-warning">批量下载全部清单 (JSON)</button> 
                    </div>
                    <div style="overflow-x: auto;">
                        <table id="audit-checklist-table">
                            <thead>
                                <tr>
                                    <th>No.</th>
                                    <th>报告编号</th>
                                    <th>审核日期</th>
                                    <th>总体评价等级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 使用说明页面 -->
            <div id="page-instructions" class="page">
                <div class="card">
                    <div class="card-header">VDA 6.8 潜在供方分析工具使用说明</div>
                    <h2>1. 操作步骤</h2>
                    <ol>
                        <li><strong>公司信息设置:</strong> 点击页面左上角的公司名称或Logo，在弹出的窗口中修改公司名称并上传Logo。所有信息会自动保存。</li>
                        <li><strong>审核计划:</strong> 在“审核计划”页面，点击“新增计划”来创建年度审核安排。填写报告编号、审核对象等信息，并勾选本次要审核的过程要素（L1-L7）。</li>
                        <li><strong>开始审核:</strong> 点击计划行中的“开始审核”按钮。系统将自动跳转到“基础信息”页面，并带入报告编号和**审核对象**。同时，“提问表”页面将只显示您在计划中勾选的过程要素。</li>
                        <li><strong>填写基础信息:</strong> 完善“基础信息”页面的所有内容。</li>
                        <li><strong>进行审核与评分:</strong>
                            <ul>
                                <li>进入“提问表”页面，对每个问题进行审核。</li>
                                <li>在“评价”下拉框中选择分数 (ne, 0, 4, 6, 8, 10)。ne代表“未评价”，不计入总分。</li>
                                <li>在“审核记录”文本框中填写详细的发现和证据。</li>
                                <li>可以使用“随机打分”、“全部打10分”、“删除所有评分”等快捷按钮辅助操作。</li>
                            </ul>
                        </li>
                        <li><strong>查看评价结果:</strong> 进入“评价矩阵”页面，点击“计算/刷新结果”按钮。系统将：
                            <ul>
                                <li>以过程要素为单位，分表展示所有提问的得分矩阵，避免横向过长。</li>
                                <li>计算并显示各过程要素(E<sub>Ln</sub>)和总体(E<sub>G</sub>)的符合性得分。</li>
                                <li>根据总体得分给出最终评级（绿色/黄色/红色）。</li>
                            </ul>
                        </li>
                        <li><strong>制定措施计划:</strong> “措施计划”页面会自动列出所有得分低于10分的不符合项。您需要在此页面补充负责人、纠正措施和计划完成日期等信息。点击“刷新措施计划”以更新列表。</li>
                        <li><strong>归档与历史查看:</strong> 在“评价矩阵”页面，点击“添加到审核清单”按钮，可将本次审核概要存入“审核清单”页面。
                            <ul>
                                <li>在“审核清单”页面，您可以查看所有历史审核。</li>
                                <li>点击清单中的报告编号或“加载”按钮，可以恢复查看该次审核的全部详细信息。</li>
                                <li>“加载审核清单”按钮用于加载之前批量下载的整个清单文件，并将其中的记录合并到当前清单中。</li>
                                <li>新增**“批量下载全部清单 (JSON)”**按钮，可一次性备份所有历史审核记录。</li>
                            </ul>
                        </li>
                    </ol>

                    <h2>2. 评价规则</h2>
                    <p>评价基于VDA 6.8:2024标准，核心评分和计算规则如下：</p>
                    <ul>
                        <li><strong>单项评分:</strong> 每个提问可评0, 4, 6, 8, 10分。分数代表了对要求的满足程度和风险等级。</li>
                        <li><strong>过程要素符合性 (E<sub>Ln</sub>):</strong> 对每个过程要素(L1-L7)单独计算。<code>E_Ln [%] = (要素内所有提问的实际得分总和 / 要素内所有已评价提问的可能得分总和) * 100</code></li>
                        <li><strong>总体符合性 (E<sub>G</sub>):</strong> 对所有被评价的提问进行计算。<code>E_G [%] = (所有已评价提问的实际得分总和 / 所有已评价提问的可能得分总和) * 100</code></li>
                        <li><strong>有效性验证:</strong> 每个被审核的过程要素（L1-L7），至少要有2/3的关键问题（带星号*）被评价（即得分不是'ne'），否则该要素及总体评价均无效。</li>
                    </ul>
                    
                    <h2>3. 供应商评级规则</h2>
                    <p>基于<strong>总体符合性 (E<sub>G</sub>)</strong> 进行判定：</p>
                    <ul>
                        <li><strong>绿色 (A级):</strong> E<sub>G</sub> ≥ 90%</li>
                        <li><strong>黄色 (B级):</strong> 80% ≤ E<sub>G</sub> &lt; 90%</li>
                        <li><strong>红色 (C级):</strong> E<sub>G</sub> &lt; 80%</li>
                    </ul>
                    <p><strong>注意：</strong> 本工具的主要目的是进行初步的潜在供方分析，上述评级规则为基础判定。在实际的VDA 6.8审核中，还需要综合考虑关键路径、过程要素以及具体的降级规则。</p>

                </div>
            </div>
            
            <div id="company-info-modal" class="modal">
              <div class="modal-content">
                <span class="close-button">&times;</span>
                <h2>编辑公司信息</h2>
                <div class="form-group">
                  <label for="modal-company-name">公司名称</label>
                  <input type="text" id="modal-company-name">
                </div>
                <div class="form-group">
                  <label for="modal-logo-upload">上传Logo (推荐透明背景PNG)</label>
                  <input type="file" id="modal-logo-upload" accept="image/*">
                </div>
                <button id="save-company-info" class="btn-success" style="margin-top: 15px;">保存</button>
              </div>
            </div>

        </main>

        <!-- 底部 -->
        <footer class="app-footer">
            版权所有 © 吴志明 | 电话&amp;微信：13959240478 |弘扬匠心、传递知识、为企业创造价值！
        </footer>
    </div>
    
    <!-- JS部分 -->
    <script>
        // Self-executing function to avoid polluting the global scope
        (function() {
            'use strict';

            //======================================================================
            // 1. VDA 6.8 QUESTIONS DATA
            //======================================================================
            const questionsData = {
                L1: {
                    title: "L1 项目管理/战略",
                    subElements: {
                        '1.1': {
                            title: "1.1 过程输入 - 什么进入该过程",
                            questions: [
                                { id: '1.1.1', text: '要求是否整合到项目管理中并持续考虑？', isCritical: true, minReqs: ['以下要求必须被考虑在内：\n	顾客要求\n	内部要求\n	ESG 要求', '必须具有识别、评价和实施约束力义务的过程。'], examples: ['满足顾客要求的需求规范', '顾客相关要求的概述', '识别约束力义务的过程说明', '法律'] },
                                { id: '1.1.2', text: '是否具备物流项目特定的数据和/或信息？', isCritical: false, minReqs: ['如下数据/信息可用：\no	批准和修改\no	预算\no	SCM 规范\no	合同\no	项目订单'], examples: ['项目文档', '项目结构计划', '预算计划', '订单文件', '批准的放行文件'] },
                                { id: '1.1.3', text: '是否已经检查项目目标的技术和经济可行性？', isCritical: false, minReqs: ['可行性是根据明确的标准定义的。', '结果是积极的，并且有记录。'], examples: ['可行性分析，包括结果'] },
                                { id: '1.1.4', text: '物流目标是否源自于上级公司战略？', isCritical: false, minReqs: ['公司战略包括供应安全方面。', '公司战略包括信息安全方面。', '公司战略包括 ESG 方面。'], examples: ['公司战略', '战略开发过程'] },
                            ]
                        },
                        '1.2': {
                            title: "1.2 过程流程：该过程是如何运行的？",
                            questions: [
                                { id: '1.2.1', text: '是否已经建立物流项目分类的标准和过程？', isCritical: false, minReqs: ['已经建立物流项目分类的标准，并描述项目的构成要素。', '定义的标准用于识别物流项目。', '定义供应链过程的变更管理。', '考虑战略目标。'], examples: ['标准目录', '文件化/批准的变更', '公司战略', '要求', '项目概述文件'] },
                                { id: '1.2.2', text: '供应链职能是否整合到产品创造过程中？', isCritical: false, minReqs: ['供应链职能的工作内容被定义为项目的一部分。', '相关的供应链工作人员参与到产品创造过程中。', '在产品创造过程中，遵守物流里程碑。', '考虑战略目标。'], examples: ['项目计划', '里程碑计划', '公司战略'] },
                                { id: '1.2.3', text: '是否已经建立项目管理（包括项目组织机构）？', isCritical: true, minReqs: ['存在项目管理过程并实施。', '已经建立变更管理过程。', '规定跨学科的项目组织机构。', '将联络人信息通知顾客和供方。', '考虑战略目标。'], examples: ['项目组织机构图，包括角色分配', '项目团队的构成', '项目接口', 'RACI 图'] },
                                { id: '1.2.4', text: '是否已经建立升级过程，包括协调沟通，并且得到有效实施？', isCritical: false, minReqs: ['项目组织机构和相关的升级管理满足顾客要求。规定升级的标准，并制定在不符合规范的情况下要采取的措施。', '已定义的措施得到持续实施，执行状态得到定期监视和沟通。', '考虑战略目标。', '所有相关员工都可以使用升级事项的相关功能（包括联络信息）。'], examples: ['内部和外部升级矩阵', '行动计划', '沟通计划'] },
                                { id: '1.2.5', text: '是否在项目阶段开始时就建立风险分析方法，并且得到实施？', isCritical: true, minReqs: ['已经识别、评估项目风险，并采取适当措施得到缓解。', '考虑从正在进行的或以前可比较的项目中获得经验（特别是经验教训）。', '具有灾难恢复策略。', '考虑战略目标。', '考虑工作环境的风险。'], examples: ['FMEA', '应急策略', 'SWOT 分析', 'PESTEL 分析'] },
                            ]
                        },
                        '1.3': {
                            title: "1.3 人力资源：哪些部门、角色、人员支持该过程？",
                            questions: [
                                { id: '1.3.1', text: '是否已经为项目结构中的所有必需任务定义要求概况？', isCritical: false, minReqs: ['存在人员部署时间表，其说明在什么时间必须填补项目团队中的哪些职位。', '工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行比较的基础。', '活动描述', '已经描述所需的专门知识，并涵盖所有过程。', '项目成员具备项目经验，并将经验教训付诸实践。', '将联络人信息通知顾客和供方。', '考虑战略目标。'], examples: ['项目组织机构图', '资格矩阵', '资格证明', '职业安全', '活动描述', '要求概况', '培训/资格证明 (例如： Scrum)', '初始培训计划', '适当的资格证明（例如：视力测试、听力测试、触觉测试）', '员工认可的职位描述'] },
                                { id: '1.3.2', text: '是否存在利用和实施自己组织之外的专业知识的概念？', isCritical: false, minReqs: ['项目团队拥有必要的联络网络，能够引入技术专业知识。', '确保联络网络的可用性。', '适当的预算可用于外部项目任务。', '考虑战略目标。'], examples: ['具有专业知识的人员概述', '利益相关方分析', '培训计划', '培训概念', '专利概念', '质量和时间', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '1.3.3', text: '员工是否了解自己在项目结构中的职责和权限？', isCritical: false, minReqs: ['员工完全了解自己的任务。', '已策划升级路径。', '定期向员工通知项目的最新情况。', '定义对过程中未正确执行的工作和/或错误的响应。', '员工能够识别项目进展中的中断情况，并能够启动适当的行动。', '沟通了过程变更。'], examples: ['功能描述', '升级矩阵', '沟通计划', '对错误操作的响应计划', '出现问题时的行动计划', '识别过程干扰', '安全作业行为/实践', '作业/检验指导', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限', '功能和要求'] },
                                { id: '1.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['具有防止波动的概念。', '为专家制定轮班时间表。', '所有活动的缺勤管理规则都已经到位。', '所有活动和角色都体现在组织机构图或团队结构中。', '内外部的接口是已知的。', '人力资源策划过程已经到位。'], examples: ['轮班时间表', '波动的 KPI', '接口矩阵', '应急策略', '资源策划，包括预算', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '1.4': {
                            title: "1.4 物质资源：使用哪些资源来实施该过程？",
                            questions: [
                                { id: '1.4.1', text: '是否具备合适的 IT 应急解决方案策略 ，包括定期数据备份？', isCritical: false, minReqs: ['项目管理必须通过灾难恢复策略，为必要的生产相关 IT 要素（例如：硬件、应用程序、托管）定义可容忍的最长停机时间。', '如果 IT 要素发生故障，项目管理必须确保恢复时间，而不会影响顾客要求的到达日期。', '存档可用，并且受到保护。', '数据必须备份在冗余和可靠的存储介质上。', '必须定期进行数据备份功能测试。'], examples: ['应急策略', '备份计划，包括升级级别', '存档要求', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', 'TISAX ?'] },
                                { id: '1.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: true, minReqs: ['遵守内部和外部要求。', '始终确保可用性。', '相关接口功能正常。', 'IT 基础设施有能力代表相关过程。'], examples: ['网络结构计划', '硬件和软件', '访问权限概述', '接口矩阵', 'KPIs 和系统可用性', '数据保护政策'] },
                                { id: '1.4.3', text: '工作站的设置是否符合人体工程学和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '逃生和救援路线清晰可见并保持畅通。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '危害评估', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] }
                            ]
                        },
                        '1.5': {
                            title: "1.5 有效性和效率：该过程的执行效果如何？",
                            questions: [
                                { id: '1.5.1', text: '针对项目管理，是否已经定义合适的 KPIs，包括目标值，并且被应用？', isCritical: true, minReqs: ['定义过程特定 KPIs，基于风险进行监视和沟通。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。'], examples: ['项目进展', '里程碑', '行动计划', '沟通计划', '风险评估', '资源利用'] },
                                { id: '1.5.2', text: '是否收集和沟通了可分析的过程数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs（目标值）。记录、评价和沟通实际数据。', '记录的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['KPI 系统', '存档概念', '可追溯性规范', 'KPI 定义'] },
                                { id: '1.5.3', text: '如果发生过程和目标偏差，是否分析原因并检查纠正措施的有效性？', isCritical: false, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '记录重复错误。必须相应开展更详细的原因分析。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录特殊事件。', '记录与目标值的偏差及其原因。'], examples: ['8D 报告', '5 whys 方法', '石川图', '行动计划', '审核'] },
                                { id: '1.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['审核', '精益管理', 'Kaizen', '5S', 'FMEA', '创意管理', 'PDCA 循环'] }
                            ]
                        },
                        '1.6': {
                            title: "1.6 过程输出 - 该过程的结果是什么？",
                            questions: [
                                { id: '1.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: false, minReqs: ['必须执行来自过程流程的最低要求。', '具有项目计划。', '定义项目组织机构。', '具有里程碑计划。', '资源计划，包括预算，已经到位。', '具有灾难恢复策略。', '已形成文档。', '所有过程结果均按照定义的标准存档。'], examples: ['项目计划', '战略', '里程碑计划', '预算计划', '标准目录'] },
                                { id: '1.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['使用经验教训的方法来定期更新结构/过程内容/工具/目标和战略。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['经验教训数据库', '不合格管理', '改进项目'] },
                                { id: '1.6.3', text: '是否已经交接给下游过程？', isCritical: true, minReqs: ['已完成验收和交接程序并形成记录。'], examples: ['交接报告', '成熟度水平'] }
                            ]
                        }
                    }
                },
                L2: {
                    title: "L2 物流过程的策划",
                    subElements: {
                        '2.1': {
                            title: '2.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '2.1.1', text: '上游过程的输出是否整合到策划中？', isCritical: false, minReqs: ['以下 1.6.1 中的批准项目被延续：\n	项目计划\n	项目组织\n	里程碑计划\n	资源策划，包括预算\n	灾 nạn恢复策略', '该过程开始之前，要有明确定义的工作指令。', '整合变更管理的结果。', '整合 CIP 的结果。'], examples: ['项目计划', '战略', '里程碑计划', '资源策划', '预算计划', '标准目录'] },
                                { id: '2.1.2', text: '要求是否整合到供应链策划中并持续考虑？', isCritical: false, minReqs: ['以下要求必须被考虑在内：\n	顾客要求\n	内部要求\n	ESG 要求', '必须具有识别、评价和实施约束力义务的过程。', '可提供用于过程策划的数据和/或信息。'], examples: ['经验教训数据库', '不合格管理', '改进项目', '交接报告', '成熟度水平', '顾客特定要求'] }
                            ]
                        },
                        '2.2': {
                            title: '2.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '2.2.1', text: '是否在物流过程的策划中建立风险分析方法，并且得到应用？', isCritical: true, minReqs: ['在策划过程中，已识别、评估风险，并采取适当措施得以缓解。', '考虑从正在进行的或以前可比较的项目中获得经验（特别是经验教训）。', '已识别的风险整合到指导/规范中。'], examples: ['FMEA', '潜在供方分析', 'SWOT 分析', '乌龟图'] },
                                { id: '2.2.2', text: '是否记录并沟通与策划状态的偏差/变更？', isCritical: false, minReqs: ['已经定义策划过程的变更管理过程。', '已经定义变更的升级过程。', '已经定义沟通概念。', '已经定义文件化过程。', '服务提供商、转运商和供方有义务将任何组织变更通知合同合作伙伴。'], examples: ['过程描述', '沟通计划', '升级矩阵', '合并、收购', '根据 VDA 2 的程序'] },
                                { id: '2.2.3', text: '是否在策划中定义物料主数据和物料流数据并持续监视？', isCritical: true, minReqs: ['物料主数据可用，并且可访问。', '为主数据的管理定义角色。', '建立过程，描述如何持续记录物料主数据的变化，并将其传达给受影响的部门。'], examples: ['功能描述', '变更管理', '访问权限', 'ERP 系统'] },
                                { id: '2.2.4', text: '针对已定义的物流过程，是否已经描述应急概念并进行测试？', isCritical: false, minReqs: ['必须制定应对资源短缺的应急策略。', '制定措施以确保在软件和/或硬件出现故障时向顾客供货。'], examples: ['应急策略', '升级矩阵', '替换系统'] },
                                { id: '2.2.5', text: '针对内部物流过程，包括包装和容器管理，是否在策划中进行定义，并且持续调整？', isCritical: true, minReqs: ['包装和容器的开发需要经过审批过程，并考虑了顾客特定要求和 ESG 要求。', '定义各种包装级别。', '建立符合包装标签要求的过程。', '内部物流的供应链概念已经到位。', '根据顾客要求策划产能，并具有实施内部物流的概念。', '过程描述、作业指导和其他相关文件都有足够详细的清晰描述。'], examples: ['过程相关/程序指令', '产能策划、最大产能、过程时间', '轮班模型', '仓储物流概念', 'FIFO 包装和容器概念', '包装级别概述', '替代包装', '供应链概念', '标签概述', '可回收或再循环的包装材料'] },
                                { id: '2.2.6', text: '是否具备运输概念，以确保定制化的供应？', isCritical: true, minReqs: ['已经确定哪些范围/路线需使用哪种类型的运输。', '针对瓶颈零件和特殊情况的运输，建立相关过程。', '时间窗口管理', '过程描述、作业指导和其他相关文件都有足够详细的清晰描述。'], examples: ['运输概念', '路线概念', '时间管理', '瓶颈管理'] },
                                { id: '2.2.7', text: '是否在策划中定义采购和供方管理过程，并且对其持续监视？', isCritical: false, minReqs: ['定义供方选择过程。', '定义供方评价标准。', '过程描述、作业指导和其他相关文件都有足够详细的清晰描述。'], examples: ['批准供方的清单', '供方管理过程', '审核报告'] }
                            ]
                        },
                        '2.3': {
                            title: '2.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '2.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。'], examples: ['项目组织机构图', '项目成员的要求概况', '资格矩阵', '资格证明', '职业安全'] },
                                { id: '2.3.2', text: '资格概念是否适合根据职位描述对员工进行资格认证？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义“初始培训”的类型和范围。', '定义法定、内部和再培训的类型和范围。', '资格认证措施的有效性在质量和时间方面得到保证。', '培训由合格的员工/培训师进行。', '培训的技术诀窍必须是可证明的。', '必须确保符合内部物流特殊特性的资格。', '确保与工作场所相关的资格。', '培训概念，包括适当的培训文件，已经到位。'], examples: ['具有专业知识的人员概述', '利益相关方分析', '培训计划', '培训概念', '专利概念', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '2.3.3', text: '员工是否了解自己有关监视供应链过程的职责和权限？', isCritical: false, minReqs: ['员工了解自己的作业和检验指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果。', '定义对不当工作和/或过程错误的响应。', '员工定期收到有关当前质量达到标准的信息，并了解顾客投诉的情况。', '如果过程发生变更，提供培训/指导并记录。'], examples: ['功能描述', '升级矩阵', '沟通计划', '对错误操作的响应计划', '出现问题时的行动计划', '识别过程干扰', '安全作业行为/实践', '作业/检验指导', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限', '功能和要求'] },
                                { id: '2.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['所有班次都有员工配置计划。员工配置计划考虑所需合格员工的数量。', '考虑生产量的变化。', '所有活动的缺勤管理规则都已经到位。', '所有活动和角色都体现在组织机构图或团队结构中。', '责任人已被任命，包括发出指示的授权。'], examples: ['轮班时间表', '被任命的人员', '波动的 KPI', '接口矩阵', '应急策略', '资源策划，包括预算', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '2.4': {
                            title: '2.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '2.4.1', text: '是否具备合适的 IT 应急解决方案策略 ，包括定期数据备份？', isCritical: false, minReqs: ['公司必须通过灾难恢复策略，为必要的生产相关 IT 要素（例如：硬件、应用程序、托管）定义可容忍的最长停机时间。', '如果 IT 要素发生故障，公司必须确保恢复时间，而不会影响顾客要求的到达日期。', '存档可用，并且受到保护。', '数据必须备份在冗余和可靠的存储介质上。', '必须定期进行数据备份功能测试。'], examples: ['应急策略', '备份计划，包括升级级别', '存档要求', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', 'TISAX ?'] },
                                { id: '2.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: true, minReqs: ['遵守内部和外部要求。', '始终确保可用性。', '相关接口功能正常。', 'IT 基础设施有能力代表相关过程。', '可以访问必要的顾客 IT 系统。'], examples: ['网络结构计划', '硬件和软件', '访问权限概述', '接口矩阵', 'KPIs 和系统可用性', '数据保护政策'] },
                                { id: '2.4.3', text: '工作站的设置是否符合人体工程学、特定产品和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '定义并明确指示逃生和救援路线。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] },
                                { id: '2.4.4', text: '是否策划供应链基础设施的物质资源，并适合代表整个价值流？', isCritical: false, minReqs: ['提供策划的数量。', '确保对各个仓库功能区域和路线进行标识。', '库存收货区有足够的保护措施。', '卸货区有足够的卸货能力。', '具有足够的存储空间。', '容器：标签、可视化/内部容器标签（操作材料）'], examples: ['整洁有序', '标签概念', '功能区域的定义', '布局', '存储系统的定义（例如：货架、高位货架、链式升降货架等）', '内部运输设备', '工作设备的测试证书'] }
                            ]
                        },
                        '2.5': {
                            title: '2.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '2.5.1', text: '是否使用里程碑计划跟踪包括项目内容在内的进度并沟通？', isCritical: true, minReqs: ['根据项目计划，记录符合性水平。'], examples: ['里程碑计划', '成熟度水平状态', '项目计划', '符合性水平', 'Scrum'] },
                                { id: '2.5.2', text: '如果发生过程和目标偏差，是否分析原因并检查纠正措施的有效性？', isCritical: false, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '记录重复错误。必须相应开展更详细的原因分析。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录特殊事件。', '记录与目标值的偏差及其原因。'], examples: ['行动计划', '8D 报告', 'KPI 矩阵', '升级矩阵', '有效性的证据', '待办事项', '销项'] },
                                { id: '2.5.3', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['5S', '审核', 'CIP', 'Kaizen', '六西格玛', '创意管理'] },
                                { id: '2.5.4', text: '在策划物流过程时，是否考虑可持续性方面？', isCritical: false, minReqs: ['考虑内部和外部要求。', '包装和容器的开发考虑到顾客特定要求和 ESG 要求，并对其进行监视。'], examples: ['需求规范', '顾客指南', '包装标准', '合同'] }
                            ]
                        },
                        '2.6': {
                            title: '2.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '2.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: true, minReqs: ['策划的所有（内部/外部）活动都根据里程碑计划实施和批准。', '必须完成以下过程：\n	基础设施计划\n	作业指导\n	物料主数据\n	控制计划\n	基于顾客要求的性能测试\n	根据策划实施员工资格认证', '所有过程结果均按照定义的标准存档。'], examples: ['里程碑计划', '交接报告', '资源策划，包括预算', '性能测试结果', '资格矩阵', 'ERP 系统', '成熟度水平'] },
                                { id: '2.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['使用经验教训的方法来定期更新结构/过程内容/工具/目标和战略。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['经验教训数据库', '行动清单', 'FMEA/乌龟图', '改进项目', '不合格管理', '资格矩阵'] },
                                { id: '2.6.3', text: '是否检查了策划物流过程的可行性，并且记录了批准情况？', isCritical: false, minReqs: ['已证实物流过程策划中的所有相关要求都得到满足。', '所有在项目管理（L1）中定义的任务/要求都被考虑到物流过程的策划中。'], examples: ['可行性分析的结果', '潜在供方审核', '审核（例如：VDA 6.8、MMOG/LE 或同等方法）', '交接报告', '供应链概念', '自我评估', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                },
                L3: {
                    title: "L3 供方和服务提供商管理",
                    subElements: {
                        '3.1': {
                            title: '3.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '3.1.1', text: '上游过程的输出是否整合到供方和服务提供商管理中？', isCritical: false, minReqs: ['可提供以下结果/文件：\n	可行性分析\n	经验教训\n	来自项目管理和物流策划的结果', '提供工作指令。'], examples: ['总计划', '经验教训', '反面案例', '项目结构计划', '报告'] }
                            ]
                        },
                        '3.2': {
                            title: '3.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '3.2.1', text: '是否已经定义过程来确保只和获得批准和具备质量能力的供方和服务提供商合作？', isCritical: true, minReqs: ['过程描述、作业指导和其他相关文件都有足够详细的清晰描述。', '根据既定标准（例如：质量能力、质量绩效），只选择具备质量能力的物流供方和服务提供商。', '在引进新产品或审查绩效时，使用正式的评价工具（例如：VDA 6.8、全球 MMOG/LE 或同等方法）。', '利用适当的措施识别、评价和降低供应链（内部/外部）中的风险。', '在供方开发过程中，对确定的措施进行监视。', '定义并记录接口。', '供应链参与相关采购小组，以选择物流服务提供商。'], examples: ['过程计划', '审核结果', '批准/替代供方和服务提供商的名单', '证书（质量管理体系）', '供方质量绩效概述', '定义的供方选择标准', '基于 KPIs 的质量能力评价', '升级级别', '供方审核结果'] },
                                { id: '3.2.2', text: '是否具备供方和服务合同，并且分析、考虑和遵守约束力义务？', isCritical: true, minReqs: ['对于由供方和服务提供商所执行的物流过程，服务范围由合同约定并进行监视。', '物流质量目标是本协议的一部分。', '规定了不遵守协议的后果。', '对相关约束力义务的分析及其满足情况进行监视。'], examples: ['合同', '约定的 KPIs', '绩效证明', '证书（质量管理体系）', '可行性分析'] },
                                { id: '3.2.3', text: '是否已经与供方和服务提供商约定应急管理计划，并且得到有效实施？', isCritical: false, minReqs: ['与供方和/或物流服务提供商约定一个成文的应急管理计划，包括责任和升级级别。', '基于已定义的风险制定应急方案。'], examples: ['应急方案', '供应瓶颈', '不可抗力', '物质资源短缺', '人力资源短缺'] },
                                { id: '3.2.4', text: '是否具备与供方和服务提供商协调的投诉过程？', isCritical: false, minReqs: ['定义跟踪和记录物流投诉的规范。', '定义投诉的处理过程，包括相关的顾客规范。', '定义故障纠正过程，包括合适的方法。', '通过立即措施确保供应。', '确定补偿（例如：恢复）额外费用的过程。'], examples: ['升级过程', '投诉指南', '作业指导', '联络人清单'] },
                                { id: '3.2.5', text: '是否已经识别、记录和沟通过程步骤的风险转移和过程拥有者？', isCritical: true, minReqs: ['风险转移受合同约束，所有相关方均已知晓并可利用。', '已与内部和外部接口约定并任命了过程拥有者。'], examples: ['合同', '过程流程图', 'RACI 图', 'Incoterm', '个别合同条款'] },
                                { id: '3.2.6', text: '是否已经在组织以及供方和服务提供商之间建立标准化的沟通？', isCritical: false, minReqs: ['及时、完整地沟通与物流服务有关的变更。', '为组织与供方和服务提供商定义接口和联络人。', '定期交流过程绩效和目标实现情况。如果出现目标偏差，则确定并实施纠正措施。', '定义电子数据交换（EDI）的使用和应用。', '提供相关顾客平台的访问权限。'], examples: ['车间管理，包括服务提供商', '定期沟通', '联络人名单，包括紧急联络人', 'IT 要求', '访问权限', '顾客反馈'] },
                                { id: '3.2.7', text: '是否策划和控制给外部服务提供商的外包？', isCritical: false, minReqs: ['定义分配给供方和/或外部服务提供商的绩效范围。', '供方和外部服务提供商的控制责任受到监管，并被整合到供应链中。', '定义与外部服务提供商的沟通规则，并为所有参与者所知。', '定义供方或外部服务提供商向顾客直接供货的调度控制。', '确保需求和能力管理。'], examples: ['合同', '需求规范', '接口矩阵', '沟通计划', '需求和产能策划'] }
                            ]
                        },
                        '3.3': {
                            title: '3.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '3.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。'], examples: ['职位描述', '要求概况', '培训/资格证明', '顾客特定要求', '资格矩阵', '初始培训计划'] },
                                { id: '3.3.2', text: '资格概念是否适合根据职位描述进行培训？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义“首日简报”的范围。', '确保资格认证措施的有效性。', '培训由合格的培训师进行。'], examples: ['初始培训计划', '专利概念', '在职培训', '培训概念', '职业安全', '培训计划', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '货物处置（例如：使用处置图标）', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '3.3.3', text: '员工是否了解自己在供方和服务提供商管理方面的职责和权限？', isCritical: false, minReqs: ['员工了解自己的作业和检验指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果。', '任何职责的转移都要进行记录。'], examples: ['应急计划/责任矩阵', '专业领域', '接口界定', '更改日志', '活动描述', '识别过程干扰', '安全作业行为/实践', '作业/检验指导'] },
                                { id: '3.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['存在员工配置计划，其考虑所需合格员工的数量。', '所有活动的缺勤管理规则都已经到位。', '所有活动和角色都体现在组织机构图或团队结构中。', '责任人已被任命，包括发出指示的授权。'], examples: ['轮班时间表', '被任命的人员', '最低人员配置', '人员需求', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '3.4': {
                            title: '3.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '3.4.1', text: '是否具备合适的 IT 应急解决方案策略 ，包括定期数据备份？', isCritical: false, minReqs: ['定义 IT 应急解决方案。', '存档可用，并且受到保护。', '数据必须备份在冗余和可靠的存储介质上。', '必须定期进行数据备份功能测试。'], examples: ['顾客特定的响应时间', '应急策略', '备份计划，包括升级级别', '存档要求', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试'] },
                                { id: '3.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: true, minReqs: ['遵守内部和外部要求。', '始终确保可用性。', '相关接口功能正常。', 'IT 基础设施有能力代表相关过程。'], examples: ['网络结构计划', '硬件和软件', '访问权限概述', '接口矩阵'] },
                                { id: '3.4.3', text: '工作站的设置是否符合人体工程学和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '定义并明确指示逃生和救援路线。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] }
                            ]
                        },
                        '3.5': {
                            title: '3.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '3.5.1', text: '针对供方和服务提供商管理，是否有合适的 KPIs，包括切合实际的目标值？', isCritical: true, minReqs: ['定义、监视和沟通过程特定的目标。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。'], examples: ['交付可靠性', '交付错误率', '供方控制', '目标符合性的早期指标', '供方评估', '交付质量'] },
                                { id: '3.5.2', text: '是否收集和沟通了可分析的供方和服务提供商的特定数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs (目标值)。记录、评价和沟通实际数据。', '记录的供方和服务提供商相关的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['主数据维护', '变更管理', '供方门户网站', '审核结果', '投诉管理'] },
                                { id: '3.5.3', text: '如果发生偏差，是否分析原因并检查纠正措施的有效性？', isCritical: true, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '记录重复错误。必须相应开展更详细的原因分析。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录特殊事件。', '记录与目标值的偏差及其原因。'], examples: ['5W', 'PDCA 循环', '石川图', '价值流分析', '8D 报告', '门户网站结果处理', '过程 FMEA', '偏差许可', '豁免'] },
                                { id: '3.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['CIP 文档', '建议方案', '落实经验教训的证据'] },
                                { id: '3.5.5', text: '如果发生变更，是否检查供方和服务管理过程？', isCritical: false, minReqs: ['顾客要求被考虑在内。', '在出现偏差时，会有行动清单和效果预测。', '变更均有记录。'], examples: ['供方可行性分析', '有审批过程的过程变更文档', '及时通知变更', '变更历史'] }
                            ]
                        },
                        '3.6': {
                            title: '3.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '3.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: true, minReqs: ['必须完成以下过程：\n	规定使用经批准的供方/服务提供商。\n	可提供评价结果（受评价的供方）。\n	记录供方绩效。\n	制定供方开发方案。', '所有过程结果均按照定义的标准存档。'], examples: ['协调的响应计划', '协调赔偿处理', '性能测试结果', '战略供方开发计划', '中央数据库', '供方评估', '供方清单'] },
                                { id: '3.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['分析相关供方的批准/评价。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['8D 报告', '5W', '跟踪审核', 'PDCA 循环', '石川图', '沟通计划', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                },
                L4: {
                    title: "L4 采购物流",
                    subElements: {
                        '4.1': {
                            title: '4.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '4.1.1', text: '采购物流中是否实施物流计划?', isCritical: true, minReqs: ['考虑战略目标和概念。'], examples: ['确保物料的可用性', '质量保证', '缩短交货时间', '成本优化', '供应链的可持续性和合规性'] },
                                { id: '4.1.2', text: '供方评价和控制数据是否整合到采购物流中？', isCritical: false, minReqs: ['考虑供方评价和绩效。', '考虑供方批准。'], examples: ['供方错误率', '供方质量', '供方可靠性', '批准的供方', '供方计分卡'] },
                                { id: '4.1.3', text: '要求是否整合到采购物流中并持续考虑？', isCritical: false, minReqs: ['以下要求必须被考虑在内：\n	顾客要求\n	内部要求\n	ESG 要求', '必须具有识别、评价和实施约束力义务的过程。', '数据和/或信息可用于采购物流过程。'], examples: ['ESG 要求', '风险管理', '顾客要求', '合同', '指南', 'QM 体系'] },
                                { id: '4.1.4', text: '计划生产量是否整合到物料需求计划中？', isCritical: false, minReqs: ['具有生产量计划，并且该计划会影响物料需求计划。', '实施主动变更管理。'], examples: ['生产计划', '变更申请', '生产需求分析', '物料清单和工作计划', '库存监视', '持续监视'] }
                            ]
                        },
                        '4.2': {
                            title: '4.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '4.2.1', text: '是否已经建立过程来确保根据需求供应物料？', isCritical: true, minReqs: ['过程描述、作业指导和其他相关文件都有足够详细的清晰描述。', '至少必须为以下过程定义和考虑过程规范：\n	处理采购申请/交付计划\n	触发手动订单\n	瓶颈管理\n	时间窗口控制', '及时处理采购申请。', '对未处理的采购申请进行监视，确保其得到处理。', '确保对异常订单数量（异常值）进行合理性检查。', '根据定义的准则和特殊因素，对系统 KPI 的变更进行调整、记录和沟通。', '定期检查安全库存、最小起订量和保质期。', '根据特定的标准确保货物进出控制。', '考虑容器和批次大小。'], examples: ['过程描述', '作业指导', '采购申请', '常规库存管理', 'ERP 系统', '沟通计划'] },
                                { id: '4.2.2', text: '是否已经根据需求和产能建立容器管理系统？', isCritical: false, minReqs: ['基于需求的容器管理，包括考虑来自供方的反馈。', '定义替代包装的用途和类型。', '独立和特殊的装载器具数量充足。', '描述退回流的概念，包括退回标准。', '库存以透明的方式呈现。'], examples: ['装载器具库存管理', '装载器具循环', '特殊装载器具', '替代包装', '交付频次'] },
                                { id: '4.2.3', text: '是否已经定义过程和概念（库存类型），用于系统性的库存审核和分析？', isCritical: true, minReqs: ['库存概念已经到位。', '监视物料需求的过程已经到位。', '根据相关且有效的规范进行库存检查。', '建立处理过期零件的过程。', '研究库存差异的原因（地点数量、零件数量和零件价值），进行重点分析，并在必要时采取改进措施。'], examples: ['库存记录', '补货间隔', '库存数据', '识别差异', '行动计划', '库存变动', '库存概念'] },
                                { id: '4.2.4', text: '是否已经建立升级过程，包括协调沟通，并且得到有效实施？', isCritical: false, minReqs: ['针对逾期项目、延迟交付和库存偏差，定义多阶段的升级过程。', '升级规则是已知的，并且被遵守。', '定期检查逾期项目，并将其升级给供方。', '为瓶颈管理定义过程（包含列入瓶颈控制、分配、终止和瓶颈控制的标准）。', '向顾客提供逾期项目的交付日期信息。'], examples: ['交叉供应过程', '交叉采购', '库存', '升级过程', '瓶颈控制', '责任矩阵', '触发标准'] },
                                { id: '4.2.5', text: '是否进行生产策划和控制？', isCritical: true, minReqs: ['在生成生产计划时，生产策划和控制系统必须自动考虑顾客要求。', '定义并遵守策划和生产订单的时间范围。', '顺序化生产阶段是相互联系和协调的。', '根据总周转时间的合理间隔提供生产订单反馈信息。', '在生产过程中表明生产状态。', '如果出现前期物料瓶颈，可尽早通知生产策划。', '批次大小和工装时间被考虑在内，并根据设定的标准在各个生产区域进行定义。', '基于设定的标准安排订单顺序。'], examples: ['物料需求计划', '交付取消', '物料可用性/批准', '已知瓶颈', '变体创建（JIS/JIT）', '已知拒收率', '在制品库存', '透明的生产排程和逾期项目跟踪', '预测', 'MES/ERP 系统', '行动控制', '供应链策划'] }
                            ]
                        },
                        '4.3': {
                            title: '4.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '4.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。'], examples: ['资格矩阵', '资格证明', '职业安全', '活动描述', '要求概况', '培训/资格证明', '顾客特定要求', '初始培训计划'] },
                                { id: '4.3.2', text: '资格概念是否适合根据职位描述进行培训？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义“首日简报”的范围。', '确保资格认证措施的有效性。', '培训由合格的培训师进行。'], examples: ['初始培训计划', '专利概念', '在职培训', '培训概念', '职业安全', '培训计划', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '货物处理（例如：读取处理图标）', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '4.3.3', text: '员工是否了解自己在采购物流方面的职责和权限？', isCritical: false, minReqs: ['员工了解自己的作业和检验指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果。', '定期向员工通知目标符合性和顾客投诉。', '向员工提供的指导、培训和入职介绍以及资格证明都需做好记录。', '如果过程发生变更，提供培训/指导并记录。'], examples: ['功能与需求', '功能描述', '升级矩阵', '沟通计划', '对错误操作的响应计划', '出现问题时的行动计划', '识别过程干扰', '安全作业行为/实践', '作业/检验指导', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限'] },
                                { id: '4.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['所有班次都有员工配置计划。员工配置计划考虑所需合格员工的数量。', '考虑生产量的变化。', '所有活动的缺勤管理规则都已经到位。', '所有活动和角色都体现在组织机构图或团队结构中。', '责任人已被任命，包括发出指示的授权。'], examples: ['轮班时间表', '被任命的人员', '波动的 KPI', '接口矩阵', '应急策略', '资源策划，包括预算', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '4.4': {
                            title: '4.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '4.4.1', text: '是否具备合适的 IT 应急解决方案策略 ，包括定期数据备份？', isCritical: true, minReqs: ['公司必须通过灾难恢复策略，为必要的生产相关 IT 要素（例如：硬件、应用程序、托管）定义可容忍的最长停机时间。', '如果 IT 要素发生故障，公司必须确保恢复时间，而不会影响顾客要求的到达日期。', '存档可用，并且受到保护。', '数据必须备份在冗余和可靠的存储介质上。', '必须定期进行数据备份功能测试。'], examples: ['应急策略', '备份计划，包括升级级别', '存档要求', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', 'TISAX ?'] },
                                { id: '4.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: true, minReqs: ['遵守内部和外部要求。', '始终确保可用性。', '相关接口功能正常。', 'IT 基础设施有能力代表相关过程。'], examples: ['ERP（策划和供应链）', '车间管理系统', 'MES（运营管理）', '泳道', '网络结构计划', '硬件和软件', '访问权限概述', '接口矩阵', 'KPIs 和系统可用性', '数据保护政策'] },
                                { id: '4.4.3', text: '工作站的设置是否符合人体工程学和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '定义并明确指示逃生和救援路线。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] },
                                { id: '4.4.4', text: '是否借助 EDI 标准来确保交付计划的可用性和相应使用？', isCritical: false, minReqs: ['确保 EDIs 是最新的、无差错的。', 'EDIs 用于后续过程。', '合适的硬件是可用的。', '系统之间的接口是安全的。'], examples: ['取消系统', '手动或自动处理 EDIs', '处理 EDIs 的时间范围', 'EDIs 的转换和沟通', 'ERP 系统'] }
                            ]
                        },
                        '4.5': {
                            title: '4.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '4.5.1', text: '针对采购物流，是否有合适的 KPIs，包括切合实际的目标值？', isCritical: true, minReqs: ['定义、监视和沟通过程特定的目标。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。'], examples: ['有效期', '没有需求的物料', '库存天数', '供方交付时间', '延期交货', '库存水平'] },
                                { id: '4.5.2', text: '是否收集和沟通了可分析的过程数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs (目标值)。记录、评价和沟通实际数据。', '记录的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['补货周转时间', '订单准确性', '供方质量', '供方可靠性', '订单数量', '订单周期'] },
                                { id: '4.5.3', text: '如果发生偏差，是否分析原因并检查纠正措施的有效性？', isCritical: true, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '记录重复错误。必须相应开展更详细的原因分析。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录特殊事件。', '记录与目标值的偏差及其原因。'], examples: ['供方投诉', '供方错误率', '根本原因分析（5W / 石川图）', '供应链的绩效能力', '8D 报告', 'FMEA 行动计划', '升级计划', '沟通计划'] },
                                { id: '4.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['供方评估', 'CIP', '改进计划', 'PDCA / Kaizen / FMEA', '经验教训'] },
                                { id: '4.5.5', text: '如果发生变更，是否检查采购物流过程？', isCritical: false, minReqs: ['顾客要求被考虑在内。', '在出现偏差时，会有行动清单和效果预测。', '变更均有记录。'], examples: ['行动计划', '责任清单', '变更管理工具', '过程文档', '变更申请', '文档修订'] }
                            ]
                        },
                        '4.6': {
                            title: '4.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '4.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: false, minReqs: ['必须完成以下过程：\n	确保供应安全\n	干预限度及相应的后续措施\n	最低库存水平/安全库存', '所有后续过程均使用当前和可信的 EDIs。', '所有过程结果均按照定义的标准存档。'], examples: ['成熟度符合性', '早期预警系统', '生产输出', '仓库管理', '订购和订单处理', '存档要求'] },
                                { id: '4.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['分析瓶颈以找出其原因，并以此作为经验教训方法的基础。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['持续的风险管理', '行动跟踪', '根本原因分析', '回顾', '最佳实践/良好实践', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                },
                L5: {
                    title: "L5 运输物流",
                    subElements: {
                        '5.1': {
                            title: '5.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '5.1.1', text: '上游过程的输出是否整合到运输物流中？', isCritical: false, minReqs: ['运输概念已经到位。', '可提供批准的运输服务提供商。', '委托的运输服务包括以下内容：\n	正确的数量 / 约定的质量 / 正确的产品 /正确的接收方 / 标签 / 装运单据 / 约定的截止日期\n	提供其他批准', '整合变更管理的结果。', '整合 CIP 和经验教训的结果。'], examples: ['批准的服务提供商概述', '运输策划（例如：行程、产品要求）', '升级概念', '沟通概念', '运输概念', 'CIP', 'Kaizen', '创意管理', '改善项目'] },
                                { id: '5.1.2', text: '是否把约束力义务整合到运输物流中并持续考虑？', isCritical: true, minReqs: ['以下要求必须被考虑在内：\n	法定要求\n	顾客要求\n	内部要求\n	ESG 要求', '必须具有识别、评价和实施约束力义务的过程。', '数据和/或信息可用于运输物流。'], examples: ['合同文件，包括需求规范', '包装要求', '产品要求（例如：危险品）', 'ESG 和安全法规', '作业指导', '过程描述', 'QM 体系', '贸易法、货运法、运输保险法、税法和海关法、交通法'] },
                                { id: '5.1.3', text: '是否已评估和处理了运输过程中的风险？', isCritical: false, minReqs: ['风险分析方法已定义并实施。', '评估和记录了潜在的风险及其对结果的影响。', '制定了风险规避和风险缓冲措施。'], examples: ['风险管理过程', '风险评估', '公司战略', '流程图', 'ESG 要求', '损益表', '法律法规概述'] }
                            ]
                        },
                        '5.2': {
                            title: '5.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '5.2.1', text: '是否按照运输要求来处理货物？', isCritical: true, minReqs: ['遵守装载、卸载和运输的操作规范。', '考虑并遵守包装概念（例如：可堆叠性）。', '整合并遵守装载方案。'], examples: ['JIS/JIT 装载指导', '装载矩阵', '包装方案', '操作规范的作业指导'] },
                                { id: '5.2.2', text: '是否实施并检查装载安全规范，并且记录偏差？', isCritical: false, minReqs: ['遵守法律法规，包括法律责任以及适用的规范和行业标准。', '考虑物理基本原理，包括作用在负载上的力。', '装载车辆/货物运输工具时要考虑负载分布。不得超过有效载荷、允许总重量以及轴向载荷要求。'], examples: ['装载指导', '法律法规要求概述', '装载控制文档', '负载分布图'] },
                                { id: '5.2.3', text: '是否考虑与运输相关的特殊特性？', isCritical: false, minReqs: ['运输设备和运输货物均按照相关规范张贴标签。', '所需文件已完成。', '描述并遵守对环境有影响的物料的处理。', '考虑运输设备的特定性能。'], examples: ['危险品（例如：电池）', '大件', '特殊运输', '温度和湿度监视', '装运单据', '货物标签（现场目视检查货物）'] },
                                { id: '5.2.4', text: '是否完整记录执行运输的所有相关规范并包含在指导中？', isCritical: true, minReqs: ['过程描述、作业指导和其他相关文件都有足够详细的清晰描述。', '描述发生错误或过程中断时，现场工作人员执行的程序。', '确保在工作环境中随时都能查阅作业指导和规范。', '明确定义接口和职责。', '过程描述和作业指导需经过批准过程，并受控和定期检查，以确保其是最新的。', '按照标准创建、控制和归档文件。'], examples: ['GPS 追踪器', '监控摄像头', '视频', '象形图', '海关', '退回过程', '运输订单', '作业指导', '程序说明', '文件管理系统', '合同', 'Incoterm'] },
                                { id: '5.2.5', text: '是否识别和分析执行不当的运输服务，并且采取了适当措施？', isCritical: false, minReqs: ['运输损坏管理概念已经到位。', '定义故障运输服务的标准。', '定义识别和预防运输风险的概念。', '描述针对故障运输服务的响应。', '需在风险装运点和接口处检查运输货物是否有损坏。'], examples: ['过程概述', '程序说明', '作业指导', '资源（例如：运输损坏管理部门）', '8D 报告', 'FMEA'] }
                            ]
                        },
                        '5.3': {
                            title: '5.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '5.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。', '定义必要的权限。', '定义相关指令。', '定期执行检查。', '定义与工作场所相关的资格。'], examples: ['指定人员（危险品、装载安全等）', '安全许可证', '活动描述', '要求概况', '内部审核员', '培训/资格证明（例如：叉车证、起重机操作证）', '资格矩阵', '初始培训计划', '货物处理（例如：读取处理图标）', '简报（例如：职业安全、ESG 要求）', '适当的资格证明（例如：视力测试、听力测试、触觉测试）', '员工认可的职位描述', '外语知识'] },
                                { id: '5.3.2', text: '资格概念是否适合根据职位描述对员工进行资格认证？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义和记录“初始培训”以及法定、内部和再培训的类型和范围。', '资格认证措施的有效性在质量和时间方面得到保证。', '培训由合格的员工/培训师进行。培训的技术诀窍必须是可证明的。', '必须确保符合内部物流特殊特性的资格。', '确保与工作场所相关的资格。如果过程发生变更，提供培训/指导并记录。', '培训概念，包括适当的培训文件，已经到位。'], examples: ['特定培训（航空货 运、危险品、堆垛系数、ESD 等）', '培训概念', '专利概念', '职业安全', '质量和时间', '培训组成', '培训计划', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '货物处理（例如：读取处理图标）', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '5.3.3', text: '员工是否了解自己有关监视产品和过程质量的职责和权限？', isCritical: false, minReqs: ['员工了解自己的作业和过程指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果和影响。', '定义对不当工作和/或过程错误的响应。', '任何职责的转移都要进行记录。'], examples: ['识别过程干扰', '安全作业行为/实践', '作业/检验指导', '职位描述', '阻止/取消阻止的授权', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限', '功能与需求'] },
                                { id: '5.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['存在员工配置计划，其考虑所需合格员工的数量。', '所有班次都有替代人员。', '所有活动和角色都体现在组织机构图或团队结构中。', '培训有素的员工可确保装载和危险品的安全。', '定义人员不足时的响应和措施。', '定义危机管理计划。', '责任人已被任命，包括发出指示的授权。'], examples: ['资格矩阵', '轮班时间表', '组织机构图', '培训证明', '替代规则', '包含角色和授权的应急计划'] }
                            ]
                        },
                        '5.4': {
                            title: '5.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '5.4.1', text: '是否具备合适的综合应急解决方案策略？', isCritical: true, minReqs: ['在过程策划的同时，需针对潜在的过程问题制定应急概念，这些概念描述了在紧急情况下执行实际服务过程的流程以及纠正问题所需的步骤。', '确保既定的解决方案实施落地，并成功测试和/或模拟。', '所需的备用解决方案是确定的、合适的、有效的，并可在需要时提供。', '确保可以使用应急解决方案。', '可根据策划提供 IT 应急解决方案。', '应急解决方案中考虑了升级机制。'], examples: ['业务连续性管理（BCM）', '替代产能（车辆、存储等）', '风险分析（例如：FMEA）', '危机管理（例如：不可抗力）', '应急策略', '备份计划，包括升级级别', '存档要求', '网络安全', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', 'TISAX?'] },
                                { id: '5.4.2', text: '工作站的设置是否符合人体工程学和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '定义并明确指示逃生和救援路线。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] },
                                { id: '5.4.3', text: '是否提供合适的运输方式，以确保连续且符合质量要求的物料流？', isCritical: false, minReqs: ['运输设备的合格证明是最新、有效和可用的。', '为运输（包括特殊运输）提供充足的运输方式。', '运输和装载设备的要求是根据载重、类型和大小来定义的。', '选择、确定数量和正确使用足够运输货物的负载固定材料。检查负载固定材料是否完好。'], examples: ['检查清单/检查表', '车辆检查（例如：根据 StVZO 第 29 章节进行主要检查）', '服务检查', '规范文件', '作业指导', '可运输条件（例如：无损坏）', '容器可用性、容器检查贴纸', '工作设备测试证书'] },
                                { id: '5.4.4', text: '运输的可用性是否得到系统性的保证？', isCritical: false, minReqs: ['遵守维护间隔，并及时采购替代品。', '提供合适的工具用于适当的维护。', '执行计划内和计划外的维护活动并分析潜在的改进。', '识别、标识、必要时分离故障或潜在故障的基础设施和运行设备，并向维护部门报告。', '系统地实施预防性和预测性维护活动。', '为维护提供资源。'], examples: ['TPM （全面生产维护）', '维护合同', '检查表', '检查和维护计划', '运输设备备件的可用性', '已完成维护工作的文档', '容器存储区'] }
                            ]
                        },
                        '5.5': {
                            title: '5.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '5.5.1', text: '针对运输物流，是否有合适的 KPIs，包括切合实际的目标值？', isCritical: true, minReqs: ['定义过程特定 KPIs，根据风险进行监视并沟通。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。', '记录与 ESG 相关的 KPIs。'], examples: ['KPI 规范文档', '指示板', '目标/实际对比', '沟通矩阵', 'CO2 足迹'] },
                                { id: '5.5.2', text: '是否收集和沟通了可分析的过程数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs (目标值)。记录、评价和沟通实际数据。', '记录的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['KPI 跟踪工具', 'KPI 分析', '沟通（例如：发布、指示板、会议等）', '跟踪 & 追溯'] },
                                { id: '5.5.3', text: '如果发生过程和目标偏差，是否分析原因并检查纠正措施的有效性？', isCritical: true, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '记录重复错误。必须相应开展更详细的原因分析。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录特殊事件。', '记录与目标值的偏差及其原因。'], examples: ['服务提供商投诉', '服务提供商错误率', '根本原因分析（5W / 石川图）', '供应链的绩效能力', '8D 报告', 'FMEA 行动计划', '升级计划', '沟通计划'] },
                                { id: '5.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['服务提供商评价', 'CIP', '改进计划', 'PDCA / Kaizen / FMEA', '经验教训'] },
                                { id: '5.5.5', text: '定期和发生变更时，是否对运输物流过程进行检查？', isCritical: false, minReqs: ['定义审核的类型和范围。', '顾客要求被考虑在内。', '在出现偏差时，会有行动清单和效果预测。', '变更均有记录。', '基于风险执行检查。'], examples: ['审核计划/审核方案', '过程描述', '文件管理系统（工作流）', '审核员资格', '顾客特定要求', 'VDA 系列标准', '内部检查表', '有效性检查', '行动清单'] },
                                { id: '5.5.6', text: '在选择运输类型和路线时，是否考虑到 ESG 方面？', isCritical: false, minReqs: ['实施 ESG 要求（环境、社会和治理）。', '目标是已知的，并进行沟通。', '对计划开展的 ESG 活动进行跟踪。'], examples: ['ESG 要求', '公司使命宣言', '公司战略', '法律注册', '提高效率的措施', '产能利用率'] }
                            ]
                        },
                        '5.6': {
                            title: '5.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '5.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: true, minReqs: ['运输物流部门确保以下内容：\n	完成交付\n	准时\n	到正确的位置\n	完好无损\n	供应安全', '按照要求签发装运单据。', '存档过程中考虑顾客特定要求和法律要求。', '确保正确执行（库存）过账。'], examples: ['存档要求', '组件质量', '交付可靠性', '符合性', '补货准备时间', '成本效益', '接口信息', '沟通计划', '升级计划', '生产和交付计划'] },
                                { id: '5.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['分析过程偏差以找出其原因，并以此作为经验教训方法的基础。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['持续的风险管理', '行动跟踪', '根本原因分析', '回顾', '最佳实践/良好实践', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                },
                L6: {
                    title: "L6 内部物流",
                    subElements: {
                        '6.1': {
                            title: '6.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '6.1.1', text: '上游过程的输出是否整合到内部物流中？', isCritical: false, minReqs: ['策划的批准结果已全部结转。', '已批准的供方可用。', '整合变更管理的结果。', '整合 CIP 和经验教训的结果。', '操作规范可用。'], examples: ['运输概念', '供应链概念', '包装概念', '物料流概念标准（例如：白皮书）', '交接报告', '批准文档', '过程策划'] },
                                { id: '6.1.2', text: '是否整合所有内部物流相关的要求并持续考虑？', isCritical: true, minReqs: ['以下要求必须被考虑在内：\n	顾客要求\n	内部要求\n	外部要求\n	ESG 要求', '必须具有识别、评价和满足所有相关要求的过程。', '数据和/或信息可用于内部物流。'], examples: ['法定/监管要求、可追溯性', '合同', '顾客要求', '整合到 QM 体系中', '约束力义务'] },
                                { id: '6.1.3', text: '是否在内部物流中准备和处理正确的订单数据？', isCritical: true, minReqs: ['主数据可用。', '提供数量结构。', '预测策划，包括产能策划已经到位。', '考虑性能测试结果。'], examples: ['过程时间', '容器移动', '补货准备时间', '最大产能', '过程控制数据', '物料主数据，例如： ESD', '优先级'] }
                            ]
                        },
                        '6.2': {
                            title: '6.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '6.2.1', text: '是否记录提供物流服务的所有相关规范并包含在指导中？', isCritical: true, minReqs: ['详细描述和记录与物流活动相关的规范。', '描述发生错误或过程中断时，现场工作人员执行的程序。', '确保在工作环境中随时都能查阅作业指导和规范。', '明确定义接口和职责。', '过程描述和作业指导需经过批准过程，并受控和定期检查，以确保其是最新的。', '按照标准创建、控制和归档文件。'], examples: ['变更管理', '作业指导', '过程描述', '应急计划', '文件控制', '适用的文件', '文件存档', '文件安全分类', '文件清单', '合规性规范'] },
                                { id: '6.2.2', text: '是否实施并检查装载安全规范，并且记录偏差？', isCritical: false, minReqs: ['遵守法律法规，包括法律责任以及适用的规范和行业标准。', '考虑物理基本原理，包括作用在负载上的力。', '装载车辆/货物运输工具时要考虑负载分布。不得超过有效载荷、允许总重量以及轴向载荷要求。'], examples: ['装载指导', '法律法规要求概述', '装载控制文档', '负载分布图'] },
                                { id: '6.2.3', text: '定义的指导中是否包含必要的检查？', isCritical: true, minReqs: ['在概述中已定义并记录过 程中监视质量的类型和范围。', '此定义至少包括以下控制项目：\n	控制标准\n	控制规范\n	文档化\n	响应计划\n	负责执行的人员', '建立投诉处理过程，包括定义的标准。'], examples: ['检查计划', '控制计划', '响应计划', '接收检查', '退回过程', '检查表', '作业指导'] },
                                { id: '6.2.4', text: '是否识别潜在故障和/或故障货物或物流过程，并且采取措施？', isCritical: true, minReqs: ['针对问题零件和/或关键零件（顶级零件），则要进行系统的接收检查。定义类型和范围。', '定义识别故障货物的标准。', '处理故障/潜在故障货物的过程描述可用。', '接口和风险转移被考虑在内。确保分离和标记。', '对隔离区实施库存管理。确保在货物被冻结的情况下更新可用库存。', '当货物出现故障时，存在约定的沟通程序。', '已启动确保供应的措施。', '在货物出现故障时，确保在供应链上冻结货物和/或调整。'], examples: ['错误检测', '货物冻结', '冻结过程', '隔离区', '隔离存储', '隔离卡', '作业指导', '响应计划', '沟通计划', '澄清过程和领域'] },
                                { id: '6.2.5', text: '是否能确保物料和零件在流转过程中不发生混合/弄错，并且考虑了特殊特性？', isCritical: false, minReqs: ['遵守产品和过程的特定要求。', '定义并遵守标签要求。标签必须防损且清晰易读。', '防止混合和弄错产品的概念已经到位。', '在贴标过程中必须保护组件。', '移除过期和无效的标签。', '遵守 ESD 要求。', '使用定义的包装辅助。'], examples: ['BBD', '硬件和软件', 'Poka-Yoke', '收据', '标签', '有害物质', '顶部箭头', '清除', '组件、容器的标签', '包装和存储区域', '可视化', '环境温度', '布局', '零件识别标记', '返工过程'] },
                                { id: '6.2.6', text: '是否已经建立控制和监视物料流的过程？', isCritical: true, minReqs: ['物料流的结构是透明的。定义升级和干预限度。', '物料的加工状态（材料移动）必须透明化，具有唯一的状态标识或目标指示。', '物料移动被正确和直接地发布。', '系统中的库存与实际库存相对应。', '如果出现库存差异，则会响应，并及时纠正。', '执行部门知道日常工作的预期范围。', '具有一个系统能够引入优先订单和/或订单变化，并确保优先处理。', '瓶颈管控过程已经到位。', '作为基本原则，在整个过程链中使用组件特定的资产管理方法。'], examples: ['订单状态', '布局', '发出通知', '订单量', '资产管理方法（例如：FIFO、LIFO、HIFO、FEFO）', '物料控制站', '物料过账', '瓶颈处理', '库存差异', '库存水平', '材料库存', '运输订单', '补货准备时间', '最小/最大规定', '干预限度', '处理时间（MTM 方法）'] },
                                { id: '6.2.7', text: '是否已经建立控制和监视运输设备的过程？', isCritical: false, minReqs: ['基于产品特定的特性，根据需要采购和重新订购运输设备（容器）。', '运输设备的库存是透明的；设备被分类并单独存放在规定的存储区域。规定退回运输设备的验收，并以验收和退回标准为依据。', '定义“NOK”运输设备的标签和分离。定义维修和更换的标准。', '定义替代运输设备。', '定义识别不符合要求的运输设备的标准。', '识别和处理脏污（标签污染和丢失）运输设备的过程已经到位。', '确保运输设备的适当使用。', '定义维修概念的类型和范围。'], examples: ['空箱管理', '可重复使用的包装', '一次性包装', '容器限度样本目录', '可重复使用性和清洁度', '特殊装载器具', '替代包装', '包装规范', '批准、运输设备管理系统', '维修理念', '识别系统'] },
                                { id: '6.2.8', text: '是否已经定义持续仓库优化的过程，并且实施适当的优化措施？', isCritical: false, minReqs: ['合适的存储策略和配套的仓库管理系统已经到位。', '定义位置分配的规则和程序，并系统地实施存储空间优化。', '存储空间适合存储的零件和装载器具/容器的类型和尺寸。', '定义处理过时零件的过程。'], examples: ['存储策略', '过时物料', '布局', '存储利用率', 'KPIs', '吞吐时间', '行动计划'] },
                                { id: '6.2.9', text: '是否适当存储/处理物料，用来保护产品特性？', isCritical: true, minReqs: ['在转运、存储和运输过程中，物料始终受到保护、免受损害。', '零件根据包装规范进行包装。', '货物的特性得以保护。', '遵守包装上的处理图标要求。', '保护部件在装载器具中免受丢失和损坏。', '定义容器的填充水平。', '识别 ESD 相关组件，并实施组件保护规范。', '定义并实施技术清洁度规范。', '已知并遵守有害物质和环境相关物质的存储特殊和法定规范。'], examples: ['遮蔽', '维持产品特性', '防风雨的物料处理', '保护质量的包装顺序和订单', '处理符号', '5S', '防腐', '高压电池的处理要求', '技术清洁度', '气候和机械影响', '存储要求', 'ESD 要求', '产品特定要求', 'BBD'] },
                                { id: '6.2.10', text: '是否根据顾客要求，对产能和灵活性进行策划和定义？', isCritical: true, minReqs: ['生产程序是已知的，如有变更，可根据顾客要求进行更新。', '产能及其影响因素是已知的并得到考虑。', '将产能与顾客要求进行比较并进行沟通。', '根据需要考虑和策划扩大产能的措施。'], examples: ['减少吞吐时间', '生产订单的优先顺序', '法律要求，例如：特殊班次', '外部生产的选项', '产能分配', '生产场地扩展的可能性', '合同', '性能能力'] }
                            ]
                        },
                        '6.3': {
                            title: '6.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '6.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。', '定义必要的权限。', '定期执行检查。', '定义与工作场所相关的资格。'], examples: ['职位描述', '要求概况', '内部审核员', '培训/资格证明（例如：叉车证、起重机操作证）', '资格矩阵', '初始培训计划', '货物处理（例如：读取处理图标）', '简报（例如：职业安全、ESG 要求）', '适当的资格证明（例如：视力测试、听力测试、触觉测试）', '员工认可的职位描述', '外语知识'] },
                                { id: '6.3.2', text: '资格概念是否适合根据职位描述对员工进行资格认证？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义和记录“初始培训”以及法定、内部和再培训的类型和范围。', '资格认证措施的有效性在质量和时间方面得到保证。', '培训由合格的员工/培训师进行。培训的技术诀窍必须是可证明的。', '必须确保符合内部物流特殊特性的资格。', '确保与工作场所相关的资格。如果过程发生变更，提供培训/指导并记录。', '培训概念，包括适当的培训文件，已经到位。'], examples: ['培训概念', '专利概念', '质量和时间', '培训组成', '培训计划', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '货物处理（例如：读取处理图标）', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '6.3.3', text: '员工是否了解自己有关监视产品和过程质量的职责和权限？', isCritical: true, minReqs: ['员工了解自己的作业和过程指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果和影响。', '定义对不当工作和/或过程错误的响应。', '任何职责的转移都要进行记录。'], examples: ['识别过程干扰', '安全作业行为/实践', '作业/检验指导', '职位描述', '阻止/取消阻止的授权', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限', '功能与需求'] },
                                { id: '6.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['存在员工配置计划，其考虑所需合格员工的数量。', '所有班次均提供替补。', '所有活动和角色都体现在组织机构图或团队结构中。', '培训有素的员工可确保装载和危险品的安全。', '考虑到生产量的变化，对策划中定义的职位进行人员配置。', '资格矩阵用于定期检查所需人员编制。', '确定人员不足时的响应和措施。', '定义危机管理计划。', '责任人已被任命，包括发出指示的授权。'], examples: ['轮班时间表', '被任命的人员', '最低人员配置', '员工要求', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '6.4': {
                            title: '6.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '6.4.1', text: '是否具备合适的综合应急解决方案策略？', isCritical: true, minReqs: ['在过程策划的同时，需针对潜在的过程问题制定应急概念，这些概念描述了在紧急情况下执行实际服务过程的流程以及纠正问题所需的步骤。', '确保既定的解决方案实施落地，并成功测试和/或模拟。', '所需的备用解决方案是确定的、合适的、有效的，并可在需要时提供。', '确保可以使用应急解决方案。', '可根据策划提供 IT 应急解决方案。', '应急解决方案中考虑了升级机制。'], examples: ['业务连续性管理（BCM）', '替代产能（车辆、存储等）', '风险分析（例如：FMEA）', '危机管理（例如：不可抗力）', '应急策略（例如：替代服务、分包 商、包装、运输）', '备份计划，包括升级级别', '存档要求', '网络安全', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', '替代能源供应', '使用外部产能', 'TISAX?'] },
                                { id: '6.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: true, minReqs: ['IT 基础设施（包括相关的 EDP 接口）在正确的地点和正确的时间可用。', '定义并设置访问权限。', '提供、批准和沟通软件和硬件的更新。', '将联络信息输入到顾客特定系统中。', '已考虑与信息安全相关的要求。', '关于软件和硬件处理的作业指导有文档记录、最新且可访问。', '物流 IT 系统的参数设置与基础设施和过程目标相适应。', '监视物流 IT 系统的功能；定义升级的干预限度。'], examples: ['网络结构计划', '软件（例如：仓库管理系统、生产策划系统、产能系统）', '硬件（例如：扫描仪、RFID）', '访问权限概述', '接口矩阵', 'KPIs 和系统可用性', '数据保护策略', '大数据', '区块链', '卡车/运输控制', '限度样本目录/决策工具'] },
                                { id: '6.4.3', text: '工作站的设置是否符合人体工程学、特定产品和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '实施法定、监管和公司规范。', '工作场所有助于保持产品特定的特性。', '定义工作场所的 ESD 要求。', '技术辅助可用并被使用。', '个人防护装备可用并被使用。'], examples: ['危害评估', '职业安全和事故预防指导', '个人防护装备', '适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '工作场所概念', '检查站', '整洁有序', '逃生和救援路线', '人体工程学方面', 'ESD 设备', 'HRC（人机协作）', '机器人学'] },
                                { id: '6.4.4', text: '物流基础设施是否能够满足产品和过程的特定要求？', isCritical: true, minReqs: ['物流基础设施状况良好，并定期接受检查。', '策划的物流基础设施已经到位、规模适当并获得批准。', '物流基础设施能够满足产品和过程的特定要求。', '确保对已定义的功能区域和基础设备要素进行标记。', '提供充足的社交区域。', '物流基础设施考虑 ESG 要求。', '对单独存储区域的访问概念进行规定。'], examples: ['整洁有序', '标签概念', '功能区域的定义', '布局', '存储系统的顾客特定要求（例如：货架、高位货架、链式升降货架等）、危险品存储、易失窃零件等）', '内部运输设备', '基础设施（例如：建筑物、货架、仓库、地板、照明条件、货架标签等）', 'ESD/ESG 要求', '技术清洁度', '气候要求', '访问授权', 'DTS（无人驾驶运输系统）', '叉车控制系统', '工业牵引控制系统'] },
                                { id: '6.4.5', text: '是否在正确的位置和正确的时间，以正确的数量和质量提供设备？', isCritical: false, minReqs: ['运行设备必须可用、功能齐全并按照策划获得批准。', '根据产能/顾客需求及时调整运行设备库存。', '运行设备库存必须按时补充。', '定期进行运行设备的盘点。', '定义运行设备的添加和清除。', '不使用时，运行设备的存放方式应保持其质量。', '考虑产品特定要求。'], examples: ['运行设备清单', '运行设备监视', '清洁计划', '维护计划', '运行设备的适用性', '职业安全', '运行设备的标签', '检查标签', '官方校准报告', '校准证书', '整洁有序', '不使用时，定义的存储位置', '损坏和安全存储', 'MSA（测量系统分析）', '重新订购系统', '智能包装'] },
                                { id: '6.4.6', text: '基础设施和设备的可用性是否得到保证？', isCritical: false, minReqs: ['遵守维护间隔，并及时采购替代品。', '提供合适的工具用于适当的维护。', '执行计划内和计划外的维护活动并分析潜在的改进。', '识别、标识、必要时分离故障或潜在故障的基础设施和运行设备，并向维护部门报告。', '系统地实施预防性和预测性维护活动。', '为维护提供资源。'], examples: ['TPM (全面生产维护)', '维护合同', '检查表', '检查和维护计划', '定义和监视功能相关备件的最低库存水平', '已完成维护工作的文档', '维修', '委托外部服务提供商进行维护工作', '整洁有序', '运行设备的运行准备状态', '补货准备时间', '资源规划', '智能仓库'] },
                                { id: '6.4.7', text: '是否在正确的位置、正确的时间，以正确的数量和质量提供装载器具和包装材料？', isCritical: false, minReqs: ['容器/装载器具、包装材料必须可用、功能齐全并按照策划获得批准。', '需要独特的识别标签、可视化/内部容器标签。', '根据产能/顾客需求及时调整运行设备库存。', '必须按时采购/补充库存。', '定期进行盘点。', '不使用时，提供适当的存储以保护其质量。', '遵守与产品和运输设备相关的要求。', '必须定义和标记合适的仓库和存储空间。', '容器/装载器具和包装材料的状况符合顾客要求。'], examples: ['清洁容器', '将故障容器从运营过程中分离出来', '容器维修', '防风雨存储', '功能性', '定义空箱的区域', '容器库存', '整洁有序', '对故障容器进行标记', '布局', '智能包装'] }
                            ]
                        },
                        '6.5': {
                            title: '6.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '6.5.1', text: '针对内部物流，是否已经定义合适的 KPIs，包括目标值，并且被应用？', isCritical: true, minReqs: ['定义过程特定 KPIs，根据风险进行监视并沟通。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。'], examples: ['目标定义', '车间管理', '跟踪和调整目标的过程', '投诉率', '吞吐时间', '存储利用率', '生产率', '空间利用率', '过程中断', '交付准备程度', '产能监视'] },
                                { id: '6.5.2', text: '是否收集和沟通了可分析的过程数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs (目标值)。记录、评价和沟通实际数据。', '记录的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['错误记录', '风险评估', '过程监视', '沟通计划', '行动计划', 'KPI 矩阵', '升级矩阵', '车间管理'] },
                                { id: '6.5.3', text: '如果发生过程和目标偏差，是否分析原因并检查纠正措施的有效性？', isCritical: true, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '根据根本原因分析采取措施，避免错误再次发生。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录与目标值的偏差及其原因。'], examples: ['失效分析方法', '8D 报告', '有效性', 'ESG 要求', '实施措施的责任', '实施措施的截止日期', '行动计划/行动清单', '存档程序', '重复出现的错误'] },
                                { id: '6.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['服务提供商评价', 'CIP', '行动计划', 'PDCA / Kaizen / FMEA', '经验教训', '创意管理', '过程稳定的措施'] },
                                { id: '6.5.5', text: '定期和发生变更时，是否对内部物流过程进行检查？', isCritical: false, minReqs: ['定义审核的类型和范围。', '顾客要求被考虑在内。', '在出现偏差时，根据根本原因分析制定行动清单。', '变更均有记录。', '基于风险执行检查。'], examples: ['变更管理', '核对记录', '组件变更', '审核方案和计划', '改进计划', '内部 ESD 审核', '过程描述', '文件管理系统（工作流）', '审核员资格', '顾客特定要求', 'VDA 标准', '有效性检查', '行动清单', '过程改进带来的变更'] },
                                { id: '6.5.6', text: '内部物流是否考虑到 ESG 方面？', isCritical: false, minReqs: ['实施 ESG 要求（环境、社会和治理）。', '目标是已知的，并进行沟通。', '对计划开展的 ESG 活动进行跟踪。'], examples: ['能源效率', '工作条件', '人机工程学', 'CO2 足迹', '危害评估', '健康管理'] }
                            ]
                        },
                        '6.6': {
                            title: '6.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '6.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: true, minReqs: ['内部物流部门确保按计划提供正确的材料：\n	正确的质量\n	准时\n	到正确的位置\n	正确的数量\n	使用正确的数据\n	合适的成本\n	附带必要的文档和标签/标记', '遵守文件的存档规范。'], examples: ['存档要求', '组件质量', '交付可靠性', '符合性', '补货准备时间', '成本效益', '接口信息', '沟通计划', '升级计划', '生产和交付计划'] },
                                { id: '6.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['分析过程偏差以找出其原因，并以此作为经验教训方法的基础。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['持续的风险管理', '行动跟踪', '根本原因分析', '回顾', '最佳实践/良好实践', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                },
                L7: {
                    title: "L7 顾客管理",
                    subElements: {
                        '7.1': {
                            title: '7.1 过程输入 - 什么进入该过程？',
                            questions: [
                                { id: '7.1.1', text: '上游过程的输出是否整合到顾客管理中？', isCritical: false, minReqs: ['策划的批准结果已全部结转。', '整合变更管理的结果。', '整合 CIP 和经验教训的结果。'], examples: ['标准(例如：白皮书）', '过程中断（例如：由于重新排序、运营物流、象征性罢工）', '行动（召回、宣传活动、信息等）', '顾客反馈', '沟通计划'] },
                                { id: '7.1.2', text: '是否把约束力义务整合到顾客管理中并持续考虑？', isCritical: true, minReqs: ['以下要求必须被考虑在内：\n	顾客要求\n	内部要求\n	ESG（环境、社会和治理）要求', '必须具有识别、评价和实施约束力义务的过程。'], examples: ['保存文件的义务（CSD）/文件义务', '法定/监管要求（例如：GDPR）', '合同', '整合到 QM 体系中'] },
                                { id: '7.1.3', text: '是否有正确的数据来衡量顾客满意度？', isCritical: false, minReqs: ['投诉管理数据可用。', '顾客满意度评估数据可用。', '可提供顾客平台访问权限。'], examples: ['多维度的投诉分析（投诉原因、产品组、存储区域、运输方式、路线、顾客等）', '损失、投诉、顾客反馈', '市场调研/顾客分析', '标杆研究', '服务水平/处理时间'] }
                            ]
                        },
                        '7.2': {
                            title: '7.2 过程流程：该过程是如何运行的？',
                            questions: [
                                { id: '7.2.1', text: '是否记录用于顾客管理的所有相关规范并包含在指导中？', isCritical: true, minReqs: ['详细描述和记录与顾客特定要求实施相关的规范。', '已描述一种处理顾客反馈的方法。', '描述和记录顾客门户网站的使用过程。', '定义的指导中包括必要的检验。', '定期在顾客门户网站上检查顾客特定文件的内容和当前有效性。', '确保在工作环境中随时都能查阅作业指导和规范。', '明确定义接口和职责。', '过程描述和作业指导需经过批准过程，并受控和定期检查，以确保其是最新的。'], examples: ['作业指导', '过程描述', '检验指导', 'QM 体系', '投诉手册', '全球顾客服务', '接口矩阵', '沟通计划', '联络人', '顾客门户网站的规范'] },
                                { id: '7.2.2', text: '是否在各个层面进行主动沟通，该沟通包括任何相关的变更？', isCritical: true, minReqs: ['建立有明确标准的过程，说明组织何时以及如何与顾客沟通：\n	必须确保联络的可用性。\n	组织知道顾客的联络人。\n	对询问和投诉的处理情况进行跟踪，并系统地提供回复。\n	主动沟通。\n	联络数据是最新的，并不断更新。', '实施和记录变更管理，并存档结果。', '沟通包括升级。'], examples: ['顾客关系管理（CRM 管理），例如：票务系统', '顾客数据库', '时事通讯', '热线/呼叫中心', '顾客大会/会议', '现场服务', '沟通计划', '变更管理', '升级计划', '顾客门户网站', '访问权限'] },
                                { id: '7.2.3', text: '是否策划和控制给外部服务提供商的外包？', isCritical: false, minReqs: ['定义分配给外部服务提供商的绩效范围。', '外部服务提供商的控制责任受到监管，并被整合到供应链中。', '定义与外部服务提供商的沟通规则，并为所有参与者所知。', '定义外部服务提供商向顾客直接供货的调度控制。'], examples: ['服务合同', '服务协议', '沟通矩阵', '驻存概述', '分供方管理', '控制供方', '关键账户'] }
                            ]
                        },
                        '7.3': {
                            title: '7.3 人力资源：哪些部门、角色、人员支持该过程？',
                            questions: [
                                { id: '7.3.1', text: '是否已经为所有活动定义要求概况，并且与要执行的活动相对应？', isCritical: false, minReqs: ['工作岗位的要求概况构成招聘过程的基础，也是与潜在新员工的简历进行相关比较的基础。', '每个职位都有活动描述。', '学校教育、先前知识、职业生涯和高等教育都需考虑在内。', '定义必要的权限。', '定期执行检查。', '定义与工作场所相关的资格。'], examples: ['资格矩阵', '资格证明', '职业安全', '活动描述', '要求概况', '培训/资格证明', '顾客特定要求', '初始培训计划', '适当的资格证明（例如：视力测试、听力测试、触觉测试）', '员工认可的职位描述', '外语知识'] },
                                { id: '7.3.2', text: '资格概念是否适合根据职位描述对员工进行资格认证？', isCritical: false, minReqs: ['针对每个职位，具有基于工作描述的培训计划。', '使用资格矩阵。', '定义和记录“初始培训”以及法定、内部和再培训的类型和范围。', '资格认证措施的有效性在质量和时间方面得到保证。', '培训由合格的员工/培训师进行。培训的技术诀窍必须是可证明的。', '确保与工作场所相关的资格。如果过程发生变更，提供培训/指导并记录。', '培训概念，包括适当的培训文件，已经到位。'], examples: ['外语知识', '产品使用和产品问题的知识', '具备专业知识的人员概述', '利益相关方分析', '培训计划', '培训概念', '专利概念', '质量和时间', '资格矩阵', '首日简报', '附有参考资料的初始培训计划', '简报（例如：职业安全、ESG 要求、数据保护）'] },
                                { id: '7.3.3', text: '员工是否了解自己在顾客管理控制方面的职责和权限？', isCritical: false, minReqs: ['员工了解自己的作业和过程指导。', '员工了解自己的活动描述。', '员工知道工作执行不当的后果和影响。', '定义对不当工作和/或过程错误的响应。', '任何职责的转移都要进行记录。', '提供进入顾客相关区域的访问权限。'], examples: ['功能与需求', '功能描述', '升级矩阵', '沟通计划', '对错误操作的响应计划', '出现问题时的行动计划', '识别过程干扰', '安全作业行为/实践', '作业/检验指导', '服务协议（例如：服务水平协议、目标/实际 KPI）', '整洁有序', '相关法定/监管要求的培训', 'IT 权限', '驻存', '关键账户'] },
                                { id: '7.3.4', text: '是否具备必要的人力资源？', isCritical: true, minReqs: ['存在员工配置计划，其考虑所需合格员工的数量。', '安排了替补。', '所有活动和角色都体现在组织机构图或团队结构中。', '资格矩阵用于定期检查所需人员编制。', '定义危机管理计划。', '为组织的顾客和顾客群定义询问和投诉联络人。', '责任人已被任命，包括发出指示的授权。'], examples: ['轮班时间表', '被任命的人员', '波动的 KPI', '接口矩阵', '应急策略', '资源策划，包括预算', '形成文档的替代规则', '缺勤计划', '组织机构图', '资格矩阵', '资格证明'] }
                            ]
                        },
                        '7.4': {
                            title: '7.4 物质资源：使用哪些资源来实施该过程？',
                            questions: [
                                { id: '7.4.1', text: '是否具备合适的 IT 应急解决方案策略 ，包括定期数据备份？', isCritical: false, minReqs: ['公司必须通过灾难恢复策略，为必要的生产相关 IT 要素（例如：硬件、应用程序、托管）定义可容忍的最长停机时间。', '如果 IT 要素发生故障，公司必须确保恢复时间，而不会影响顾客要求的到达日期。', '存档可用，并且受到保护。', '数据必须备份在冗余和可靠的存储介质上。', '必须定期进行数据备份功能测试。'], examples: ['应急策略', '备份计划，包括升级级别', '存档要求', '备份概念', '应急信息计划', '根据 VDA 1 进行功能测试', '顾客特定的响应时间', 'TISAX ?'] },
                                { id: '7.4.2', text: '是否具备合适的、普及的 IT 基础设施？', isCritical: false, minReqs: ['遵守内部和外部要求。', '始终确保可用性。', '相关接口，尤其顾客平台，功能正常。', 'IT 基础设施有能力代表相关过程。', '对顾客平台实施系统化的用户管理，包括替代规则。', '定义和沟通数据传输标准，并确保遵守。'], examples: ['网络结构计划', '硬件和软件', '访问权限概述', '接口矩阵', 'KPIs 和系统可用性', '数据保护政策', '与顾客系统的接口'] },
                                { id: '7.4.3', text: '工作站的设置是否符合人体工程学和安全原则？', isCritical: false, minReqs: ['在工作开始时就确保可用性。', '考虑个人需求和身体要求。', '定义并明确指示逃生和救援路线。'], examples: ['适当的照明', '噪音防护概念', '有关环境温度的规范', '工作场所分析', '办公室/空间概念', '整洁有序', '逃生和救援路线', '人体工程学方面'] }
                            ]
                        },
                        '7.5': {
                            title: '7.5 有效性和效率：该过程的执行效果如何？',
                            questions: [
                                { id: '7.5.1', text: '针对顾客管理，是否已经定义合适的 KPIs，包括目标值，并且被应用？', isCritical: true, minReqs: ['定义过程特定 KPIs，根据风险进行监视并沟通。', '目标是约定、具体、可衡量、可实现、切合实际和有计划的。', '进行基于风险的目标/实际对比。', '确保目标的时效性。', '向公司所有相关职位传达目标。', '定期收集和跟踪顾客满意度 KPI。'], examples: ['处理顾客咨询的时间和数量', '顾客服务费用', '顾客满意度指数（CSI）', '目标定义', '目标跟踪', '目标调整', '损失、投诉、顾客反馈', '市场调研/顾客分析', '标杆研究', '服务水平/处理时间'] },
                                { id: '7.5.2', text: '针对顾客管理，是否整合、评价和沟通了过程数据？', isCritical: false, minReqs: ['定义和记录必要的过程 KPIs (目标值)。整合、评价和沟通实际数据。', '记录的数据可与过程进行关联，数据可获取、清晰、可查阅并按规定进行存档。满足可追溯性的要求。', '用于确定 KPIs 的数据源是可信和合理的。必须确保在规定时间内的可分析性。'], examples: ['错误记录', '风险评估', '过程监视', '多维度的投诉分析（投诉原因、产品组、存储区域、运输方式、路线、顾客等）', '顾客调查', '顾客满意度指数（CSI，包括延期交货）'] },
                                { id: '7.5.3', text: '如果发生过程和目标偏差，是否分析原因并检查纠正措施的有效性？', isCritical: true, minReqs: ['如果未能满足过程要求，必须采取立即措施以满足要求，直到纠正措施被证明有效。员工熟悉这些立即措施。', '使用适宜的方法进行原因分析，并考虑顾客要求。', '根据根本原因分析采取措施，避免错误再次发生。', '制定纠正措施，监视其实施情况，并验证有效性。', '记录与目标值的偏差及其原因。'], examples: ['失效分析方法', '立即措施', '有效性', 'ESG 要求', '实施措施的责任', '实施措施的截止日期', '行动计划/行动清单', '存档程序', '重复出现的错误'] },
                                { id: '7.5.4', text: '是否实施持续过程改进的方法？', isCritical: false, minReqs: ['根据有关质量、成本和服务的发现，不断确定改进的潜力。', '在整个公司和所有供应链合作伙伴中采用持续改进绩效能力的过程。', '定期更新风险分析。', '激励员工提出持续改进的建议。'], examples: ['服务提供商评价', 'CIP', '行动计划', 'PDCA / Kaizen / FMEA', '经验教训', '创意管理', '过程稳定的行动'] },
                                { id: '7.5.5', text: '定期和发生变更时，是否对顾客管理过程进行检查？', isCritical: false, minReqs: ['定义审核的类型和范围。', '顾客要求被考虑在内。', '在出现偏差时，根据根本原因分析制定行动清单。', '变更均有记录。', '基于风险执行检查。'], examples: ['变更管理', '核对记录', '组件变更', '审核方案和计划', '改进计划', '内部 ESD 审核', '过程描述', '文件管理系统（工作流）', '审核员资格', '顾客特定要求', 'VDA 标准', '有效性检查', '行动清单', '过程改进带来的变更'] }
                            ]
                        },
                        '7.6': {
                            title: '7.6 过程输出 - 该过程的结果是什么？',
                            questions: [
                                { id: '7.6.1', text: '过程流程中的所有过程是否得到有效/高效的实施、审查和沟通，并且形成结果？', isCritical: true, minReqs: ['顾客管理确保：\n	透明的顾客满意度\n	与顾客同步数据\n	不断提高顾客满意度的行动\n	与顾客协调沟通'], examples: ['存档要求（防火、易读性、期限）', '顾客评价', '评级', '沟通计划', '行动计划', '顾客满意度指数（CSI）', '供应安全'] },
                                { id: '7.6.2', text: '是否定期使用经验教训的方法，来实施结果发现和潜在的改进？', isCritical: false, minReqs: ['分析顾客反馈以找出偏差和原因，并将其作为经验教训方法的基础。', '考虑来自接口的发现。', '记录偏差并用于改进措施。', '基于风险实施落实。'], examples: ['处理顾客反馈', '持续风险管理', '行动跟踪', '根本原因分析', '回顾', '最佳实践/良好实践', '戴明环', '每日 Scrum', 'Sprint 回顾'] }
                            ]
                        }
                    }
                }
            };


            //======================================================================
            // 2. STATE MANAGEMENT
            //======================================================================


            const initialState = {
                companyName: '金马汽车部件有限公司',
                companyLogo: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PC9zdmc+',
                currentPage: 'plan',
                planYear: new Date().getFullYear(),
                planTitle: '年度供应链物流过程审核计划',
                auditPlan: [
                    {
                        id: Date.now(),
                        reportNo: '',
                        target: '',
                        startDate: '',
                        endDate: '',
                        type: 'planned',
                        elements: [],
                        leadAuditor: '',
                        notes: '',
                        newReason: ''
                    }
                ],
                basicInfo: {},
                auditors: [],
                auditeePersons: [],
                scores: {},
                actionPlan: {},
                checklist: [],
                selectedProcessElements: [], // Holds keys like 'L1', 'L2' etc.
                // 'potential-supplier-analysis': true, // 已经通过auditPlan的elements来控制，此处不再需要
                currentAuditId: null,
                planSignatures: {}, // Added for testing
            };

            const LOCAL_STORAGE_KEY = 'vda68AuditToolData_v5.3'; // Increased version for clarity

            let state = {}; // Global state object

            function saveState() {
                try {
                    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(state));
                } catch (e) {
                    console.error("Error saving state to localStorage:", e);
                    alert("无法保存状态。可能是本地存储已满。");
                }
            }

            function loadState() {
                try {
                    const serializedState = localStorage.getItem(LOCAL_STORAGE_KEY);
                    if (serializedState === null) {
                        console.log('loadState - No serialized state found, returning initialState.');
                        return initialState;
                    }
                    const loadedState = JSON.parse(serializedState);
                    console.log('loadState - Successfully loaded state from localStorage.');
                    console.log('loadState - Loaded companyLogo:', loadedState.companyLogo ? loadedState.companyLogo.substring(0, 50) + '...' : 'N/A'); // Log first 50 chars
                    return { ...initialState, ...loadedState };
                } catch (e) {
                    console.error("Error loading state from localStorage:", e);
                    return initialState;
                }
            }

            function getQuestionById(qId) {
                if (!qId) return null;
                const elKey = 'L' + qId.charAt(0); // Get the element key (e.g., 'L1')
                if (!questionsData[elKey]) return null; // If element data not found
                
                // Search for the question in all sub-elements of the current element
                for (const subElKey in questionsData[elKey].subElements) {
                    const question = questionsData[elKey].subElements[subElKey].questions.find(q => q.id === qId);
                    if (question) return question; // Return the question if found
                }
                return null; // Question not found
            }

            //======================================================================
            // 2. FILTERING QUESTIONS TO ONLY CRITICAL ONES (*)
            //======================================================================
            function filterCriticalQuestions(data) {
                const filteredData = {};
                for (const elKey in data) {
                    filteredData[elKey] = {
                        title: data[elKey].title,
                        subElements: {}
                    };
                    for (const subElKey in data[elKey].subElements) {
                        filteredData[elKey].subElements[subElKey] = {
                            title: data[elKey].subElements[subElKey].title,
                            questions: data[elKey].subElements[subElKey].questions.filter(q => q.isCritical)
                        };
                    }
                }
                return filteredData;
            }
            
            // Apply filter to questionsData immediately
            const criticalQuestionsData = filterCriticalQuestions(questionsData);

            //======================================================================
            // 3. UI RENDERING & UPDATES
            //======================================================================
            function navigateTo(pageId) {
                document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
                document.querySelector(`#page-${pageId}`).classList.add('active');
                document.querySelectorAll('.nav-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.page === pageId);
                });
                state.currentPage = pageId;
                saveState();
            }

            function renderAll() {
                document.getElementById('company-name').textContent = state.companyName;
                document.getElementById('company-logo').src = state.companyLogo;
                document.getElementById('plan-year').value = state.planYear;
                document.getElementById('plan-title').value = state.planTitle;
                
                // Render plan signatures (prepared by, date, approved by, date)
                if (!state.planSignatures) {
                    state.planSignatures = {};
                }
                Object.keys(state.planSignatures).forEach(key => {
                    const input = document.querySelector(`#page-plan [data-field="${key}"]`);
                    if(input) input.value = state.planSignatures[key] || '';
                });

                renderAuditPlanTable();
                renderBasicInfo();
                renderAuditorsTable();
                renderAuditeePersonsTable();
                renderQuestions();
                renderActionPlan();
                renderChecklist();
                
                navigateTo(state.currentPage || 'plan');
            }

            function renderAuditPlanTable() {
                const tbody = document.getElementById('audit-plan-table').querySelector('tbody');
                tbody.innerHTML = '';
                state.auditPlan.forEach((plan, index) => {
                    const planTitleSpan = document.getElementById('plan-title');
                    if (planTitleSpan) {
                        if (plan.elements && plan.elements.includes('potential-supplier-analysis')) {
                            planTitleSpan.textContent = '年度潜在供方分析计划';
                        } else {
                            planTitleSpan.textContent = '年度供应链物流过程审核计划';
                        }
                    }
                    const tr = document.createElement('tr');
                    tr.dataset.id = plan.id;
                    tr.innerHTML = `
                        <td><button class="start-audit-btn btn-success" data-id="${plan.id}">开始审核</button></td>
                        <td>${index + 1}</td>
                        <td><input type="text" class="plan-input report-no-input" data-field="reportNo" value="${escapeMarkdown(plan.reportNo || '')}"></td>
                        <td><input type="text" class="plan-input" data-field="target" value="${escapeMarkdown(plan.target || '')}"></td>
                        <td>
                            <input type="date" class="plan-input plan-start-date" data-field="startDate" value="${plan.startDate || ''}">
                            <input type="date" class="plan-input" data-field="endDate" value="${plan.endDate || ''}">
                        </td>
                        <td><select class="plan-input" data-field="type"><option value="planned" ${plan.type === 'planned' ? 'selected' : ''}>计划审核</option><option value="new" ${plan.type === 'new' ? 'selected' : ''}>新增审核</option></select></td>
                        <td>
                            <div class="checkbox-group">
                                <div class="checkbox-row">
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L1" ${plan.elements && plan.elements.includes('L1') ? 'checked' : ''}> L1</label>
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L2" ${plan.elements && plan.elements.includes('L2') ? 'checked' : ''}> L2</label>
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L3" ${plan.elements && plan.elements.includes('L3') ? 'checked' : ''}> L3</label>
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L4" ${plan.elements && plan.elements.includes('L4') ? 'checked' : ''}> L4</label>
                                </div>
                                <div class="checkbox-row">
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L5" ${plan.elements && plan.elements.includes('L5') ? 'checked' : ''}> L5</label>
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L6" ${plan.elements && plan.elements.includes('L6') ? 'checked' : ''}> L6</label>
                                    <label><input type="checkbox" class="plan-checkbox" data-element="L7" ${plan.elements && plan.elements.includes('L7') ? 'checked' : ''}> L7</label>
                                </div>
                                <div class="checkbox-row">
                                    <label><input type="checkbox" class="plan-checkbox" data-element="potential-supplier-analysis" ${plan.elements && plan.elements.includes('potential-supplier-analysis') ? 'checked' : ''}> 潜在供方分析</label>
                                </div>
                            </div>
                        </td>
                        <td><input type="text" class="plan-input" data-field="leadAuditor" value="${escapeMarkdown(plan.leadAuditor || '')}"></td>
                        <td><button class="delete-icon-btn btn-danger" data-id="${plan.id}">删除</button></td>
                        <td><textarea class="plan-input" data-field="notes" rows="2">${escapeMarkdown(plan.notes || '')}</textarea></td>
                    `;
                    tbody.appendChild(tr);

                    // Render reason for new audit if applicable
                    if (plan.type === 'new') {
                        const reasonTr = document.createElement('tr');
                        reasonTr.classList.add('new-audit-reason-row');
                        reasonTr.dataset.reasonId = plan.id;
                        reasonTr.innerHTML = `<td colspan="10"><label>新增审核原因：</label><input type="text" class="plan-input" data-field="newReason" value="${escapeMarkdown(plan.newReason || '')}"></td>`;
                        tbody.appendChild(reasonTr);
                    }
                });
            }

            
            function renderBasicInfo() {
                // Populate input fields from state.basicInfo
                Object.keys(state.basicInfo).forEach(key => {
                    const input = document.getElementById(key);
                    if (input) {
                        input.value = state.basicInfo[key] || '';
                    }
                });
            }

            function renderGenericTable(tableId, dataArray, rowGenerator) {
                const tbody = document.getElementById(tableId).querySelector('tbody');
                tbody.innerHTML = '';
                dataArray.forEach((item, index) => {
                    const tr = rowGenerator(item, index);
                    tbody.appendChild(tr);
                });
            }
            
            function renderAuditorsTable() {
                renderGenericTable('auditors-table', state.auditors, (item, index) => {
                    const tr = document.createElement('tr');
                    tr.dataset.id = item.id;
                    tr.innerHTML = `
                        <td>${index + 1}</td>
                        <td><input type="text" class="table-input" data-field="name" value="${escapeMarkdown(item.name || '')}" data-table="auditors"></td>
                        <td><input type="text" class="table-input" data-field="type" value="${escapeMarkdown(item.type || '')}" data-table="auditors"></td>
                        <td><input type="email" class="table-input" data-field="email" value="${escapeMarkdown(item.email || '')}" data-table="auditors"></td>
                        <td><input type="text" class="table-input" data-field="phone" value="${escapeMarkdown(item.phone || '')}" data-table="auditors"></td>
                        <td><button class="delete-table-row-btn btn-danger" data-id="${item.id}" data-table="auditors">删除</button></td>
                    `;
                    return tr;
                });
            }
            
            function renderAuditeePersonsTable() {
                 renderGenericTable('auditee-persons-table', state.auditeePersons, (item, index) => {
                    const tr = document.createElement('tr');
                    tr.dataset.id = item.id;
                    tr.innerHTML = `
                        <td>${index + 1}</td>
                        <td><input type="text" class="table-input" data-field="name" value="${escapeMarkdown(item.name || '')}" data-table="auditeePersons"></td>
                        <td><input type="text" class="table-input" data-field="department" value="${escapeMarkdown(item.department || '')}" data-table="auditeePersons"></td>
                        <td><input type="text" class="table-input" data-field="position" value="${escapeMarkdown(item.position || '')}" data-table="auditeePersons"></td>
                        <td><input type="email" class="table-input" data-field="email" value="${escapeMarkdown(item.email || '')}" data-table="auditeePersons"></td>
                        <td><input type="text" class="table-input" data-field="phone" value="${escapeMarkdown(item.phone || '')}" data-table="auditeePersons"></td>
                        <td><button class="delete-table-row-btn btn-danger" data-id="${item.id}" data-table="auditeePersons">删除</button></td>
                    `;
                    return tr;
                });
            }

            function renderQuestions() {
                const container = document.getElementById('questions-container');
                container.innerHTML = '';
                
                const noElementsMsg = document.getElementById('no-elements-selected-msg');
                if (!state.selectedProcessElements || state.selectedProcessElements.length === 0) {
                    noElementsMsg.style.display = 'block';
                    return;
                }
                noElementsMsg.style.display = 'none';

                // Determine which questions data to use based on 'potential-supplier-analysis' selection
                const isPotentialSupplierAnalysisSelected = state.selectedProcessElements.includes('potential-supplier-analysis');
                const questionsToRender = questionsData; // Always use full questionsData here, filtering happens later

                // Iterate over the questions data
                Object.keys(questionsToRender).forEach(elKey => {
                    // Only render if the element is selected in the current audit plan
                    if (!state.selectedProcessElements.includes(elKey)) {
                        return;
                    }
                    
                    const elementData = questionsToRender[elKey]; // Use selected data
                    
                    const elementDiv = document.createElement('div');
                    elementDiv.className = 'process-element card'; // Start with default state, not explicitly expanded
                    elementDiv.innerHTML = `<div class="card-header"><h3 class="process-element-title">${elementData.title}</h3><span class="expand-icon">&#9650;</span></div><div class="element-content"></div>`;
                    const contentDiv = elementDiv.querySelector('.element-content');

                    // Add click listener to toggle collapse/expand
                    elementDiv.querySelector('.card-header').addEventListener('click', () => {
                        const isCollapsed = elementDiv.classList.toggle('collapsed');
                        const icon = elementDiv.querySelector('.expand-icon');
                        if (isCollapsed) {
                            icon.innerHTML = '&#9660;'; // Down arrow
                        } else {
                            icon.innerHTML = '&#9650;'; // Up arrow
                        }
                    });

                    // Set initial collapse/expand state based on 'potential-supplier-analysis' selection
                    if (isPotentialSupplierAnalysisSelected) {
                        elementDiv.classList.add('collapsed');
                        elementDiv.querySelector('.expand-icon').innerHTML = '&#9660;'; // Down arrow for collapsed
                    } else {
                        elementDiv.classList.remove('collapsed');
                        elementDiv.querySelector('.expand-icon').innerHTML = '&#9650;'; // Up arrow for expanded
                    }

                    for (const subElKey in elementData.subElements) {
                        const subElData = elementData.subElements[subElKey];
                        // Filter questions based on 'potential-supplier-analysis' selection
                        const questionsInSubElement = isPotentialSupplierAnalysisSelected
                            ? subElData.questions.filter(q => q.isCritical)
                            : subElData.questions;

                        if (questionsInSubElement.length === 0) continue; // Skip if no questions to render after filtering

                        const subElementTitle = document.createElement('h3');
                        subElementTitle.textContent = subElData.title;
                        contentDiv.appendChild(subElementTitle);
                        
                        questionsInSubElement.forEach(q => {
                            // Get scoreData for the question. Default to a single object for all non-L6 elements.
                            const scoreData = state.scores[q.id] || { score: 'ne', record: '' }; 
                            const questionBlock = document.createElement('div');
                            questionBlock.className = 'question-block';

                            let questionHTML = `
                                <div class="question-title">${q.id}${q.isCritical ? ' <span class="critical-star">*</span>' : ''} ${escapeMarkdown(q.text)}</div>
                                <div class="question-details">
                                    <div>
                                        <h4>与评价有关的最低要求:</h4>
                                        <ul>${q.minReqs.map(r => `<li>${escapeMarkdown(r)}</li>`).join('')}</ul>
                                    </div>
                                    <div>
                                        <h4>实施示例:</h4>
                                        <ul>${q.examples.map(e => `<li>${escapeMarkdown(e)}</li>`).join('')}</ul>
                                    </div>
                                </div>
                                <div class="question-evaluation" data-question-id="${q.id}">
                            `;
                            
                            // Render score input for all questions (including L6, but without step differentiation now)
                            questionHTML += `
                                <div class="evaluation-item">
                                    <div class="score-input-container">
                                        <select class="score-select" data-question-id="${q.id}">
                                            <option value="ne" ${scoreData.score === 'ne' ? 'selected' : ''}>ne</option>
                                            <option value="10" ${scoreData.score == 10 ? 'selected' : ''}>10</option>
                                            <option value="8" ${scoreData.score == 8 ? 'selected' : ''}>8</option>
                                            <option value="6" ${scoreData.score == 6 ? 'selected' : ''}>6</option>
                                            <option value="4" ${scoreData.score == 4 ? 'selected' : ''}>4</option>
                                            <option value="0" ${scoreData.score == 0 ? 'selected' : ''}>0</option>
                                        </select>
                                        <textarea class="score-record" data-question-id="${q.id}" placeholder="审核记录...">${escapeMarkdown(scoreData.record || '')}</textarea>
                                    </div>
                                </div>
                            `;
                            
                            questionHTML += `</div>`; // Close question-evaluation div
                            questionBlock.innerHTML = questionHTML;
                            contentDiv.appendChild(questionBlock);
                        });
                    }
                    container.appendChild(elementDiv);
                });
            }
            
            function renderActionPlan() {
                const tbody = document.getElementById('action-plan-table').querySelector('tbody');
                tbody.innerHTML = '';
                let counter = 1;
                
                // Iterate through all scored questions
                Object.keys(state.scores).forEach(qId => {
                    const scoreData = state.scores[qId];
                    const question = getQuestionById(qId); // Use original questions to get all details
                    const isPotentialSupplierAnalysisSelected = state.selectedProcessElements.includes('potential-supplier-analysis');

                // Only consider critical questions for the action plan if 'potential-supplier-analysis' is selected
                // Otherwise, consider all questions
                if (!question || (isPotentialSupplierAnalysisSelected && !question.isCritical)) return; 

                    // Process the score data for the action plan item
                    const processActionItem = (data) => {
                        if (data && data.score !== 'ne' && data.score < 10) { // Only for non-conforming items
                            const actionData = state.actionPlan[qId] || {}; // Get existing action data for this question
                            const tr = document.createElement('tr');
                            tr.dataset.key = qId; // Use question ID as the key
                            tr.innerHTML = `
                                <td>${counter++}</td>
                                <td>${qId}</td>
                                <td>${escapeMarkdown(question.text.split('\n')[0])}</td> <!-- Show only first line of question text -->
                                <td>${data.score}</td>
                                <td><textarea class="action-plan-input" data-field="auditRecord" readonly>${escapeMarkdown(data.record || '')}</textarea></td>
                                <td><textarea class="action-plan-input" data-field="problem">${escapeMarkdown(actionData.problem || '')}</textarea></td>
                                <td><input type="text" class="action-plan-input" data-field="responsible" value="${escapeMarkdown(actionData.responsible || '')}"></td>
                                <td><textarea class="action-plan-input" data-field="measure">${escapeMarkdown(actionData.measure || '')}</textarea></td>
                                <td><input type="date" class="action-plan-input" data-field="dueDate" value="${actionData.dueDate || ''}"></td>
                                <td><input type="text" class="action-plan-input" data-field="status" value="${escapeMarkdown(actionData.status || '')}"></td>
                                <td><textarea class="action-plan-input" data-field="validation">${escapeMarkdown(actionData.validation || '')}</textarea></td>
                            `;
                            tbody.appendChild(tr);
                        }
                    };

                    // Process the score data (it's no longer an array for L6 as steps are removed)
                    processActionItem(scoreData);
                });
            }
            
            function renderChecklist() {
                renderGenericTable('audit-checklist-table', state.checklist, (item, index) => {
                    const tr = document.createElement('tr');
                    tr.dataset.id = item.id;
                    tr.innerHTML = `
                        <td>${index + 1}</td>
                        <td><a href="#" class="load-checklist-item-link">${escapeMarkdown(item.reportNo || 'N/A')}</a></td>
                        <td>${item.auditDate || 'N/A'}</td>
                        <td>${item.overallGrade || 'N/A'}</td>
                        <td class="action-buttons" style="margin: 0; padding: 5px;">
                            <button class="export-btn" data-format="json">JSON</button>
                            <button class="export-btn" data-format="md">MD</button>
                            <button class="export-btn" data-format="csv">Excel</button>
                            <button class="load-checklist-item-btn btn-success">加载</button>
                            <button class="delete-checklist-item-btn btn-danger">删除</button>
                        </td>
                    `;
                    return tr;
                });
            }
            
            // --- NEW FUNCTION TO RENDER THE MATRIX TABLE ---
            function renderMatrixTable(results) {
                const container = document.getElementById('evaluation-matrix-container');
                container.innerHTML = ''; // Clear previous content

                // Iterate over the *selected* process elements to render their matrix tables
                state.selectedProcessElements.forEach(elKey => {
                    const elementData = questionsData[elKey]; // Use full questions data
                    
                    // If elementData is null or has no questions, skip rendering this section
                    if (!elementData || Object.values(elementData.subElements).every(subEl => subEl.questions.length === 0)) {
                        return;
                    }

                    const tableContainer = document.createElement('div');
                    tableContainer.innerHTML = `<h3>${elementData.title}</h3>`;
                    
                    const table = document.createElement('table');
                    table.className = 'matrix-table'; // Add class for styling
                    table.innerHTML = `
                        <thead>
                            <tr>
                                <th>提问编号</th>
                                <th>提问内容</th>
                                <th>得分</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    `;
                    const tbody = table.querySelector('tbody');

                    // Determine which questions to display based on 'potential-supplier-analysis' selection
                    const isPotentialSupplierAnalysisSelected = state.selectedProcessElements.includes('potential-supplier-analysis');

                    Object.values(elementData.subElements).forEach(subEl => {
                        const questionsToDisplay = isPotentialSupplierAnalysisSelected
                            ? subEl.questions.filter(q => q.isCritical)
                            : subEl.questions;

                        questionsToDisplay.forEach(q => {
                            let scoreText = '-'; // Default if no score or question not evaluated
                            const scoreData = results.scores[q.id]; // Use results.scores which is derived from the current state

                            if (scoreData && scoreData.score !== undefined) { // Check if scoreData and score are valid
                                if (scoreData.score !== 'ne') { // If not 'ne', display the score
                                    scoreText = scoreData.score;
                                }
                            }
                            
                            const tr = document.createElement('tr');
                            // Truncate question text for better display in the matrix if it's too long
                            const displayQuestionText = q.text.split('\n')[0].substring(0, 80) + (q.text.split('\n')[0].length > 80 ? '...' : '');

                            let scoreClass = '';
                            const scoreValue = parseFloat(scoreText);
                            if (!isNaN(scoreValue)) {
                                if (scoreValue >= 90) {
                                    scoreClass = 'grade-green';
                                } else if (scoreValue >= 80) {
                                    scoreClass = 'grade-yellow';
                                } else {
                                    scoreClass = 'grade-red';
                                }
                            }

                            tr.innerHTML = `
                                <td>${q.id}${q.isCritical ? '*' : ''}</td>
                                <td>${escapeMarkdown(displayQuestionText)}</td>
                                <td class="${scoreClass}">${scoreText}</td>
                            `;
                            tbody.appendChild(tr);
                        });
                    });
                    
                    tableContainer.appendChild(table);
                    container.appendChild(tableContainer);
                });
            }
            // --- END OF NEW FUNCTION ---


            //======================================================================
            // 4. EVENT LISTENERS
            //======================================================================
            function setupEventListeners() {
                // Navigation
                const appNav = document.querySelector('.app-nav');
                if (appNav) {
                    appNav.addEventListener('click', e => {
                        if (e.target.matches('.nav-btn')) {
                            navigateTo(e.target.dataset.page);
                        }
                    });
                } else {
                    console.log('Element with class .app-nav not found.');
                }

                // Company Info Modal
                const modal = document.getElementById('company-info-modal');
                document.getElementById('company-info-clickable').addEventListener('click', () => modal.style.display = 'block');
                modal.querySelector('.close-button').addEventListener('click', () => modal.style.display = 'none');
                window.addEventListener('click', e => { if(e.target == modal) modal.style.display = 'none'; });
                document.getElementById('save-company-info').addEventListener('click', handleSaveCompanyInfo);
                document.getElementById('modal-logo-upload').addEventListener('change', handleLogoUpload);
                console.log('Company info and logo upload listeners set.');
                
                // Plan Page
                const planYearInput = document.getElementById('plan-year');
                if (planYearInput) {
                    planYearInput.addEventListener('change', function() {
                        state.planYear = parseInt(this.value);
                        saveState();
                        renderAll(); // Add renderAll to update the displayed year
                    });
                } else {
                    console.log('Element with ID plan-year not found.');
                }

                const planTitleInput = document.getElementById('plan-title');
                if (planTitleInput) {
                    planTitleInput.addEventListener('input', (e) => {
                        state.planTitle = e.target.value;
                        saveState();
                    });
                } else {
                    console.log('Element with ID plan-title not found for input listener.');
                }

                const potentialSupplierAnalysisCheckbox = document.querySelector('[data-element="potential-supplier-analysis"]');
                if (potentialSupplierAnalysisCheckbox) {
                    potentialSupplierAnalysisCheckbox.addEventListener('change', (e) => {
                        state['potential-supplier-analysis'] = e.target.checked;
                        saveState();
                    });
                } else {
                    console.log('Element with data-element="potential-supplier-analysis" not found.');
                }

                // This listener seems to be a duplicate of the one above, removing it.
                // document.getElementById('plan-title').addEventListener('input', function() {
                //     state.planTitle = this.value;
                //     saveState();
                // });

                const addPlanRowBtn = document.getElementById('add-plan-row');
                if (addPlanRowBtn) {
                    addPlanRowBtn.addEventListener('mousedown', (e) => { e.preventDefault(); handleAddPlanRow(); });
                    addPlanRowBtn.style.pointerEvents = 'auto'; // Ensure button is clickable
                    console.log('Add plan row listener set for add-plan-row.');
                } else {
                    console.log('Element with ID add-plan-row not found.');
                }

                const savePlanBtn = document.getElementById('save-plan-btn');
                if (savePlanBtn) {
                    savePlanBtn.addEventListener('mousedown', (e) => { e.preventDefault(); handleSavePlan(e); });
                    savePlanBtn.style.pointerEvents = 'auto'; // Ensure button is clickable
                    console.log('Save plan button listener set for save-plan-btn.');
                } else {
                    console.log('Element with ID save-plan-btn not found.');
                }

                const exportPlanBtn = document.getElementById('export-plan-btn');
                if (exportPlanBtn) {
                    exportPlanBtn.addEventListener('mousedown', (e) => { e.preventDefault(); exportPlanToCsv(); });
                    exportPlanBtn.style.pointerEvents = 'auto'; // Ensure button is clickable
                    console.log('Export plan button listener set for export-plan-btn.');
                } else {
                    console.log('Element with ID export-plan-btn not found.');
                }
                const auditPlanTable = document.getElementById('audit-plan-table');
                if (auditPlanTable) {
                    auditPlanTable.addEventListener('click', e => {
                        if(e.target.matches('.delete-icon-btn')) handleDeletePlanRow(e.target.closest('tr').dataset.id);
                        if(e.target.matches('.start-audit-btn')) handleStartAudit(e.target.dataset.id);
                    });
                    auditPlanTable.addEventListener('change', e => {
                        if(e.target.matches('.plan-input') || e.target.matches('.plan-checkbox')) handlePlanInputChange(e);
                    });
                } else {
                    console.log('Element with ID audit-plan-table not found.');
                }
                // Moved plan-year listener here to ensure it's within setupEventListeners
                const pagePlan = document.getElementById('page-plan');
                if (pagePlan) {
                    pagePlan.addEventListener('change', e => {
                        if(e.target.id === 'plan-year'){
                            state.planYear = e.target.value;
                            saveState();
                        } else if(e.target.matches('.persist-input')) { // Handle signatures
                            if(!state.planSignatures) state.planSignatures = {};
                            state.planSignatures[e.target.dataset.field] = e.target.value;
                            saveState();
                        }
                    });
                } else {
                    console.log('Element with ID page-plan not found.');
                }

                // Basic Info Page
                const pageInfo = document.getElementById('page-info');
                if (pageInfo) {
                    pageInfo.addEventListener('input', e => {
                        // Save basic info changes directly to state
                        if(e.target.id && e.target.id.startsWith('info-')) {
                            state.basicInfo[e.target.id] = e.target.value;
                            saveState();
                        }
                    });
                } else {
                    console.log('Element with ID page-info not found for input listener.');
                }
                
                // Generic table handlers for Auditors and Auditee Persons
                const addAuditorRowBtn = document.getElementById('add-auditor-row');
                if (addAuditorRowBtn) {
                    addAuditorRowBtn.addEventListener('click', () => handleAddTableRow('auditors', { id: Date.now() }, renderAuditorsTable));
                } else {
                    console.log('Element with ID add-auditor-row not found.');
                }

                const addAuditeePersonRowBtn = document.getElementById('add-auditee-person-row');
                if (addAuditeePersonRowBtn) {
                    addAuditeePersonRowBtn.addEventListener('click', () => handleAddTableRow('auditeePersons', { id: Date.now() }, renderAuditeePersonsTable));
                } else {
                    console.log('Element with ID add-auditee-person-row not found.');
                }

                if (pageInfo) {
                    pageInfo.addEventListener('click', e => {
                         if(e.target.matches('.delete-table-row-btn')) {
                            const table = e.target.dataset.table;
                            const id = e.target.dataset.id;
                            handleDeleteTableRow(table, id, () => {
                                if (table === 'auditors') renderAuditorsTable();
                                else if (table === 'auditeePersons') renderAuditeePersonsTable();
                            });
                        }
                    });
                } else {
                    console.log('Element with ID page-info not found for click listener.');
                }
                
                if (pageInfo) {
                    pageInfo.addEventListener('change', e => {
                        if(e.target.matches('.table-input')) {
                            handleTableInputChange(e.target.dataset.table, e.target.closest('tr').dataset.id, e.target.dataset.field, e.target.value);
                        }
                    });
                } else {
                    console.log('Element with ID page-info not found for change listener.');
                }
                
                // Questions Page Event Handlers
                const questionsContainer = document.getElementById('questions-container');
                if (questionsContainer) {
                    questionsContainer.addEventListener('click', e => {
                        if (e.target.matches('.process-element > .card-header')) {
                            e.target.parentElement.classList.toggle('collapsed');
                        }
                    });
                    questionsContainer.addEventListener('change', e => {
                        if (e.target.matches('.score-select') || e.target.matches('.score-record')) {
                            handleScoreChange(e.target);
                        }
                    });
                } else {
                    console.log('Element with ID questions-container not found.');
                }
                const scoreRandomAllBtn = document.getElementById('score-random-all');
                 if (scoreRandomAllBtn) {
                     scoreRandomAllBtn.addEventListener('click', handleRandomScoring);
                 } else {
                     console.log('Element with ID score-random-all not found.');
                 }

                 const score10AllBtn = document.getElementById('score-10-all');
                 if (score10AllBtn) {
                     score10AllBtn.addEventListener('click', () => handleSetAllScores(10));
                 } else {
                     console.log('Element with ID score-10-all not found.');
                 }

                 const scoreClearAllBtn = document.getElementById('score-clear-all');
                 if (scoreClearAllBtn) {
                     scoreClearAllBtn.addEventListener('click', handleClearAllScores);
                 } else {
                     console.log('Element with ID score-clear-all not found.');
                 }

                // Matrix Page Event Handlers
                const calculateMatrixBtn = document.getElementById('calculate-matrix');
                 if (calculateMatrixBtn) {
                     calculateMatrixBtn.addEventListener('click', calculateAndDisplayResults);
                 } else {
                     console.log('Element with ID calculate-matrix not found.');
                 }
                const addToChecklistBtn = document.getElementById('add-to-checklist-btn');
                 if (addToChecklistBtn) {
                     addToChecklistBtn.addEventListener('click', handleAddToChecklist);
                 } else {
                     console.log('Element with ID add-to-checklist-btn not found.');
                 }

                // Action Plan Page Event Handlers
                const refreshActionPlanBtn = document.getElementById('refresh-action-plan');
                if (refreshActionPlanBtn) {
                    refreshActionPlanBtn.addEventListener('click', renderActionPlan);
                } else {
                    console.log('Element with ID refresh-action-plan not found.');
                }
                 const actionPlanTable = document.getElementById('action-plan-table');
                 if (actionPlanTable) {
                     actionPlanTable.addEventListener('change', e => {
                         if(e.target.matches('.action-plan-input')) handleActionPlanInputChange(e.target);
                     });
                     actionPlanTable.addEventListener('click', e => {
                         if(e.target.matches('.delete-action-row-btn')) handleDeleteActionRow(e.target.dataset.id);
                     });
                 } else {
                     console.log('Element with ID action-plan-table not found.');
                 }
                 const exportActionPlanBtn = document.getElementById('export-action-plan-btn');
                 if (exportActionPlanBtn) {
                     exportActionPlanBtn.addEventListener('click', exportActionPlanToCsv);
                 } else {
                     console.log('Element with ID export-action-plan-btn not found.');
                 }
                
                
                // Checklist Page Event Handlers
                // Listener for loading a file containing the entire checklist
                document.getElementById('load-checklist-file-input').addEventListener('change', handleLoadChecklistFromFile);
                // Listener for the batch download button (downloads the entire checklist as JSON)
                document.getElementById('download-checklist-btn').addEventListener('click', handleDownloadChecklist); 
                // Listener for actions within the checklist table (export, load item, delete item)
                document.getElementById('audit-checklist-table').addEventListener('click', e => {
                    const target = e.target;
                    const tr = target.closest('tr'); // Find the parent row of the clicked element
                    
                    if (!tr) return; // If the click was not inside a table row, do nothing

                    const id = tr.dataset.id; // Get the ID associated with this row (checklist item ID)
                    
                    if (target.matches('.delete-checklist-item-btn')) {
                        handleDeleteChecklistItem(id);
                    } else if (target.matches('.load-checklist-item-btn') || target.matches('.load-checklist-item-link')) {
                        handleLoadChecklistItem(id);
                    } else if (target.matches('.export-btn')) {
                        // The format is in the data-format attribute of the button
                        handleExport(target.dataset.format, id); 
                    }
                });
            }
            
            // --- Event Handler Implementations ---
            function handleSavePlan(e) {
                saveState();
                const btn = e.target;
                const originalText = btn.textContent;
                btn.textContent = '已保存!';
                btn.classList.add('btn-success'); // Visual feedback
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-secondary'); // Revert to original style
                }, 2000);
            }

                    function handleAddPlanRow() {
            console.log('handleAddPlanRow function called');
                // Add a new plan object to the auditPlan array
                if (!state.auditPlan) {
                    state.auditPlan = [];
                }
                state.auditPlan.push({
                    id: Date.now(), // Unique ID for the row
                    reportNo: '',                    fullState: null, // Will be populated when audit starts
                    target: '',
                    startDate: '',
                    endDate: '',
                    type: 'planned', // Default to planned
                    elements: [], // Array to store selected process elements (e.g., ['L1', 'L2'])
                    leadAuditor: '',
                    notes: '',
                    newReason: '' // For 'new' audit type
                });
                renderAuditPlanTable(); // Re-render the table to show the new row
                saveState(); // Save the updated state
            }

            function handleDeletePlanRow(id) {
                if (confirm('确定要删除此计划行吗？')) {
                    // Filter out the plan with the matching ID
                    state.auditPlan = state.auditPlan.filter(p => p.id != id);
                    renderAuditPlanTable(); // Re-render the table
                    saveState(); // Save the updated state
                }
            }
            
            function handlePlanInputChange(e) {
                const tr = e.target.closest('tr');
                const id = tr.dataset.id;
                const plan = state.auditPlan.find(p => p.id == id);
                if (!plan) return;

                if (e.target.matches('.plan-input')) {
                    plan[e.target.dataset.field] = e.target.value;

                    // Auto-generate report number if start date changes
                    if (e.target.dataset.field === 'startDate') {
                        const reportNoInput = tr.querySelector('.report-no-input');
                        if (reportNoInput) {
                            const startDate = e.target.value;
                            if (startDate) {
                                const dateObj = new Date(startDate);
                                const year = dateObj.getFullYear();
                                const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                                const prefix = `${year}${month}`;

                                // Find existing report numbers for the current month and year
                                const existingReportNumbers = state.auditPlan
                                    .filter(p => p.reportNo && p.reportNo.startsWith(prefix))
                                    .map(p => parseInt(p.reportNo.substring(6)))
                                    .filter(num => !isNaN(num));

                                let nextSerialNumber = 1;
                                if (existingReportNumbers.length > 0) {
                                    nextSerialNumber = Math.max(...existingReportNumbers) + 1;
                                }

                                const generatedReportNo = `${prefix}${nextSerialNumber.toString().padStart(3, '0')}`;
                                reportNoInput.value = generatedReportNo;
                                plan.reportNo = generatedReportNo;
                            }
                        }
                    }

                    // If the type changes to 'new', re-render to show the reason input
                    if (e.target.dataset.field === 'type' && e.target.value === 'new') {
                        renderAuditPlanTable(); // Re-render to display the new reason field if type is 'new'
                    } else if (e.target.dataset.field === 'type' && e.target.value === 'planned') {
                        // If type changes back to 'planned', we might want to remove the reason field visually if it was there
                        renderAuditPlanTable(); // Re-render to ensure consistent UI
                    }
                } else if (e.target.matches('.plan-checkbox')) {
                    const element = e.target.dataset.element;
                    if (e.target.checked) {
                        // Add element if it's checked and not already in the array
                        if (!plan.elements.includes(element)) plan.elements.push(element);
                    } else {
                        // Remove element if it's unchecked
                        plan.elements = plan.elements.filter(el => el !== element);
                    }

                    // Update planTitle based on 'potential-supplier-analysis' checkbox state
                    const planTitleSpan = document.getElementById('plan-title');
                    if (planTitleSpan) {
                        if (element === 'potential-supplier-analysis') {
                            if (e.target.checked) {
                                planTitleSpan.textContent = '年度潜在供方分析计划';
                            } else {
                                planTitleSpan.textContent = '年度供应链物流过程审核计划';
                            }
                        }
                    }
                }
                saveState(); // Save changes
            }

            function handleStartAudit(id) {
                const plan = state.auditPlan.find(p => p.id == id);
                if (!plan) return;
                
                // Warn about unsaved changes in the current audit before starting a new one
                if (state.currentAuditId && state.currentAuditId !== plan.id) {
                     if (!confirm('开始新的审核将丢失当前审核未保存的进度，确定要继续吗？')) {
                        return; 
                     }
                }

                state.currentAuditId = plan.id; // Set the current audit ID
                state.selectedProcessElements = [...plan.elements]; // Set the selected elements for the new audit
                
                // Reset data for the new audit
                state.basicInfo = { 
                    ...initialState.basicInfo, // Reset to defaults from initial state
                    'info-report-no': plan.reportNo || '', // Pre-fill report number from plan
                    'info-auditee': plan.target || '',     // Pre-fill auditee from plan
                    'info-audit-start-date': plan.startDate || '', // Pre-fill start date
                    'info-audit-end-date': plan.endDate || ''   // Pre-fill end date
                    // 'info-process-steps' is removed from here
                };
                // Reset auditors: if lead auditor is in the plan, use that, otherwise start fresh.
                state.auditors = plan.leadAuditor ? [{id: Date.now(), name: plan.leadAuditor, type: '组长', email: '', phone: ''}] : [];
                state.auditeePersons = []; // Clear previous auditee persons
                state.scores = {}; // Clear all previous scores
                state.actionPlan = {}; // Clear all previous action plan data
                state.planSignatures = {}; // Clear plan signatures as well

                renderAll(); // Re-render UI with reset data
                navigateTo('info'); // Navigate to basic info page to start the audit
                alert(`已开始对 "${plan.target || '新审核'}" 的分析。请在“基础信息”页面完善相关信息。`);
            }

            // Generic handler for adding rows to tables like Auditors, Auditee Persons
            function handleAddTableRow(tableName, newItem, renderFunc) {
                state[tableName].push(newItem);
                renderFunc(); // Re-render the table
                saveState();
            }
            
            // Generic handler for deleting rows from tables
            function handleDeleteTableRow(tableName, id, renderFunc) {
                if (confirm('确定删除此行吗？')) {
                    state[tableName] = state[tableName].filter(item => item.id != id); // Filter out the item to delete
                    renderFunc(); // Re-render the table
                    saveState(); // Save the updated state
                }
            }
            
            // Generic handler for table input changes (e.g., editing auditor details)
            function handleTableInputChange(tableName, id, field, value) {
                const item = state[tableName].find(i => i.id == id); // Find the item by ID
                if (item) {
                    item[field] = value; // Update the specific field
                    saveState(); // Save the state
                }
            }
            
            // Handler for score changes (select or textarea)
            function handleScoreChange(target) {
                const qId = target.dataset.questionId;
                // For L6, the score data is no longer processed as an array of steps, just a single object.
                if (!state.scores[qId]) state.scores[qId] = {}; // Ensure it's an object
                 
                 if (target.matches('.score-select')) {
                    state.scores[qId].score = target.value;
                } else { // .score-record
                    state.scores[qId].record = target.value;
                }
                saveState(); // Save the state after any score change
            }
            
            // Handler for "Random Score All" button
            function handleRandomScoring() {
                if(!confirm('这将覆盖所有当前可见问题的评分，确定吗？')) return;
                
                const visibleQuestionEvals = document.querySelectorAll('#questions-container .question-evaluation');
                if (visibleQuestionEvals.length === 0) {
                    alert('没有可供评分的问题。');
                    return;
                }

                let zeroCount = 0, fourCount = 0, sixCount = 0;
                // Convert NodeList to Array to use reduce, then calculate total questions
                const totalQuestions = Array.from(visibleQuestionEvals).reduce((sum, qEval) => sum + qEval.querySelectorAll('.score-select').length, 0);
                
                // Define counts for each score to distribute them somewhat evenly, with emphasis on higher scores
                const maxZeros = Math.max(0, Math.floor(totalQuestions * 0.05)); // ~5% zeros
                const maxFours = Math.max(0, Math.floor(totalQuestions * 0.15)); // ~15% fours
                const maxSixes = Math.max(0, Math.floor(totalQuestions * 0.25)); // ~25% sixes

                visibleQuestionEvals.forEach(qEval => {
                    const scoreSelects = qEval.querySelectorAll('.score-select');
                    
                    scoreSelects.forEach(select => {
                        let score;
                        const rand = Math.random();
                        if (rand < 0.05 && zeroCount < maxZeros) { score = 0; zeroCount++; }
                        else if (rand < 0.20 && fourCount < maxFours) { score = 4; fourCount++; }
                        else if (rand < 0.45 && sixCount < maxSixes) { score = 6; sixCount++; }
                        else if (rand < 0.85) { score = 8; }
                        else { score = 10; }
                        select.value = score;
                        handleScoreChange(select); // Update state immediately
                    });
                });
                alert('随机打分完成！');
            }

            // Handler for "Set All to Score X" button
            function handleSetAllScores(score) {
                if(!confirm(`确定将所有当前可见问题的评分都设为 ${score} 分吗？`)) return;
                document.querySelectorAll('#questions-container .score-select').forEach(select => {
                    select.value = score;
                    handleScoreChange(select); // Update state immediately
                });
                alert(`所有评分已设置为 ${score} 分。`);
            }
            
            // Handler for "Clear All Scores" button
            function handleClearAllScores() {
                if(!confirm('确定要清除所有当前可见问题的评分和记录吗？')) return;
                document.querySelectorAll('#questions-container .score-select').forEach(select => {
                    select.value = 'ne'; // Reset to 'ne'
                    handleScoreChange(select); // Update state immediately
                });
                document.querySelectorAll('#questions-container .score-record').forEach(textarea => {
                    textarea.value = ''; // Clear records
                    handleScoreChange(textarea); // Update state immediately
                });
                alert('所有评分和记录已清除。');
            }
            
            // Handler for input changes in the Action Plan table
            function handleActionPlanInputChange(target) {
                const tr = target.closest('tr');
                const key = tr.dataset.key; // Get the key (question ID)
                const field = target.dataset.field; // e.g., 'problem', 'responsible'
                if(!state.actionPlan[key]) state.actionPlan[key] = {}; // Initialize if it doesn't exist
                state.actionPlan[key][field] = target.value; // Update the field
                saveState(); // Save the changes
            }
            
            // Handler to save company info from modal
            function handleSaveCompanyInfo() {
                console.log('handleSaveCompanyInfo called again for debugging');
                console.log('handleSaveCompanyInfo called');
                state.companyName = document.getElementById('modal-company-name').value;
                document.getElementById('company-name').textContent = state.companyName; // Update header display
                document.getElementById('company-info-modal').style.display = 'none'; // Close the modal
                saveState(); // Save the company name
            }

            // Handler for uploading a new company logo
            function handleLogoUpload(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = e => {
                        const img = new Image();
                        img.onload = () => {
                            const MAX_WIDTH = 200; // 设置最大宽度
                            const MAX_HEIGHT = 200; // 设置最大高度
                            let width = img.width;
                            let height = img.height;

                            if (width > height) {
                                if (width > MAX_WIDTH) {
                                    height *= MAX_WIDTH / width;
                                    width = MAX_WIDTH;
                                }
                            } else {
                                if (height > MAX_HEIGHT) {
                                    width *= MAX_HEIGHT / height;
                                    height = MAX_HEIGHT;
                                }
                            }

                            const canvas = document.createElement('canvas');
                            canvas.width = width;
                            canvas.height = height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(img, 0, 0, width, height);

                            // 导出为PNG格式
                            state.companyLogo = canvas.toDataURL('image/png');

                            document.getElementById('company-logo').src = state.companyLogo; // Update the logo image in the header
                            saveState(); // 保存压缩后的图片数据
                            console.log('handleLogoUpload - saveState() called and companyLogo updated.');
                            console.log('Saved state in localStorage:', localStorage.getItem(LOCAL_STORAGE_KEY));

                        };
                        img.src = e.target.result; // Read the file as a data URL
                    };
                    reader.readAsDataURL(file); // Read the file as a data URL
                }
            }
            
            // Handler for "Add to Checklist" button
            function handleAddToChecklist() {
                // Get report number and audit end date from basic info, or use placeholders
                const reportNo = state.basicInfo['info-report-no'] || `未指定-${Date.now()}`; 
                const auditEndDate = state.basicInfo['info-audit-end-date'] || new Date().toISOString().split('T')[0];
                
                const results = calculateScores(state); // Calculate scores and perform validation
                if (!results) { // If calculation failed due to validation errors
                    alert("无法添加到清单，因为评分尚未计算或评价无效。\n请先在“评价矩阵”页面点击“计算/刷新结果”，并确保每个过程要素至少有 2/3 的关键问题（带*）被评价。");
                    return; // Stop the process if validation failed
                }
                
                // Check if an item with the same report number already exists in the checklist
                const existingItemIndex = state.checklist.findIndex(item => item.reportNo === reportNo);

                if (existingItemIndex > -1) {
                    // If it exists, ask the user if they want to overwrite it
                    if (!confirm(`报告 "${reportNo}" 已存在于清单中，要用当前数据覆盖它吗？`)) return; // If user cancels, stop here
                    state.checklist.splice(existingItemIndex, 1); // Remove the old item from the checklist
                }

                // Create a new checklist item object
                const checklistItem = {
                    id: Date.now(), // Unique ID for the checklist entry
                    reportNo: reportNo,
                    auditDate: auditEndDate, // Use end date as audit date
                    overallGrade: results.overallGrade, // Store the calculated overall grade
                    fullState: JSON.parse(JSON.stringify(state)) // Save the entire current state for later loading
                };
                
                state.checklist.push(checklistItem); // Add the new item to the checklist
                renderChecklist(); // Update the checklist table display
                saveState(); // Save the updated checklist
                alert(`报告 "${reportNo}" 已成功添加到审核清单。`);
            }

            // Handler for deleting an item from the checklist
            function handleDeleteChecklistItem(id) {
                if(confirm('确定要从清单中永久删除此项审核记录吗？此操作无法撤销。')) {
                    state.checklist = state.checklist.filter(item => item.id != id); // Filter out the item to be deleted
                    renderChecklist(); // Re-render the checklist table
                    saveState(); // Save the updated checklist
                }
            }

            // Handler for loading an item from the checklist back into the active audit
            function handleLoadChecklistItem(id) {
                const item = state.checklist.find(i => i.id == id); // Find the checklist item by its ID
                if(item && confirm(`将加载报告 "${item.reportNo}" 的数据。\n注意：当前所有未保存到清单的修改将丢失。确定要加载吗？`)) {
                    state = JSON.parse(JSON.stringify(item.fullState)); // Load the entire saved state from the checklist item
                    renderAll(); // Re-render the entire UI based on the loaded state
                    saveState(); // Save the newly loaded state as the current state
                    alert(`已成功加载报告 "${item.reportNo}" 的数据。`);
                }
            }
            
            // Handler for loading a checklist file (JSON) containing multiple entries
            function handleLoadChecklistFromFile(event) {
                const file = event.target.files[0];
                if (!file) return; // If no file is selected, do nothing

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const loadedChecklist = JSON.parse(e.target.result);
                        // Validate that the loaded data is an array
                        if (!Array.isArray(loadedChecklist)) {
                            throw new Error("JSON file does not contain a valid checklist array.");
                        }
                        // Confirm before merging to prevent accidental data loss
                        if (confirm('加载的审核清单将与现有清单合并。如果报告编号重复，将提示您是否覆盖。确定要加载吗？')) {
                            let updatedCount = 0;
                            let newCount = 0;
                            
                            loadedChecklist.forEach(newItem => {
                                // Basic validation for each item in the loaded checklist
                                if (!newItem.reportNo || !newItem.fullState || !Array.isArray(newItem.fullState.selectedProcessElements)) {
                                    console.warn("Skipping invalid checklist item (missing reportNo, fullState, or selectedProcessElements):", newItem);
                                    return; // Skip invalid items
                                }
                                
                                // Check if an item with the same report number already exists
                                const existingIndex = state.checklist.findIndex(oldItem => oldItem.reportNo === newItem.reportNo);
                                
                                if (existingIndex > -1) {
                                    // If it exists, ask user if they want to overwrite
                                    if(confirm(`报告 "${newItem.reportNo}" 已存在。要用加载的数据覆盖它吗？`)) {
                                        state.checklist[existingIndex] = newItem; // Overwrite existing item
                                        updatedCount++;
                                    }
                                } else {
                                    state.checklist.push(newItem); // Add new item if report number is unique
                                    newCount++;
                                }
                            });
                            renderChecklist(); // Re-render the checklist table
                            saveState(); // Save the merged checklist
                            alert(`清单加载完成！新增 ${newCount} 条记录，更新 ${updatedCount} 条记录。`);
                        }
                    } catch (err) {
                        alert('加载失败，文件格式不正确或内容无效。');
                        console.error(err);
                    }
                };
                reader.readAsText(file); // Read the file content as text
                event.target.value = ''; // Clear the file input for the next selection
            }
            
            // Handler for the batch download button (downloads the entire checklist as JSON)
            function handleDownloadChecklist() {
                if (state.checklist.length === 0) {
                    alert("审核清单为空，没有内容可供下载。");
                    return;
                }
                const dataStr = JSON.stringify(state.checklist, null, 2); // Pretty-print the JSON
                const blob = new Blob(["\uFEFF" + dataStr], {type: "application/json;charset=utf-8;"}); // Create a Blob with JSON data
                downloadBlob(blob, `VDA6.8_审核清单_${new Date().toISOString().split('T')[0]}.json`); // Download the JSON file
            }


            //======================================================================
            // 5. CALCULATIONS & LOGIC
            //======================================================================
            // Get all questions (original data, not just critical) for a given element key
            function getAllQuestionsForElement(selectedElements) {
                let questions = [];
                selectedElements.forEach(elKey => {
                    const elementData = questionsData[elKey]; // Use ORIGINAL questions data to get ALL details
                    if (elementData) {
                        Object.values(elementData.subElements).forEach(subEl => {
                            questions.push(...subEl.questions);
                        });
                    }
                });
                return questions;
            }

            // Calculate scores, compliance, and perform validation
            function calculateScores(auditState) {
                let totals = {
                    overall: { possible: 0, awarded: 0 },
                    critical: { possible: 0, awarded: 0 },
                    elements: {}, // Stores { EL1: { possible: x, awarded: y }, ... }
                };
                
                const { selectedProcessElements, scores } = auditState;
                let evaluatedQuestionsCountPerElement = {}; // Count how many questions were actually evaluated (score not 'ne') per element
                let totalCriticalQuestionsCountPerElement = {}; // Count all *critical* questions in the element
                
                // Initialize counts and totals for selected elements
                selectedProcessElements.forEach(elKey => {
                    totals.elements[elKey] = { possible: 0, awarded: 0 };
                    evaluatedQuestionsCountPerElement[elKey] = 0;
                    totalCriticalQuestionsCountPerElement[elKey] = 0;
                    
                    // Count total critical questions for this element using the filtered criticalQuestionsData
                    const elementData = criticalQuestionsData[elKey]; 
                    if (elementData) {
                        Object.values(elementData.subElements).forEach(subEl => {
                            totalCriticalQuestionsCountPerElement[elKey] += subEl.questions.length;
                        });
                    }
                });

                // Iterate through all scored questions in the state
                for (const qId in scores) {
                    const question = getQuestionById(qId); // Get original question details
                    if (!question) continue; // Skip if question data is not found
                    
                    const elKey = 'L' + qId.charAt(0); // Determine the process element (e.g., 'L1')
                    
                    // Only process scores for questions belonging to the selected elements for this audit
                    if (!selectedProcessElements.includes(elKey)) continue; 

                    const scoreData = scores[qId]; // This is now a single object {score, record} for all elements

                    // Helper function to process a single score value
                    const processScoreValue = (currentScore, isCritical) => {
                        const scoreValue = parseFloat(currentScore); // Convert score to a number
                        if (!isNaN(scoreValue)) { // If the score is a valid number (not 'ne' or undefined)
                            // Increment count of evaluated questions for this element
                            if (elKey) evaluatedQuestionsCountPerElement[elKey]++;
                            
                            // Only update overall and element totals if not in 'potential-supplier-analysis' mode
                            // OR if in 'potential-supplier-analysis' mode and the question is critical
                            if (!selectedProcessElements.includes('potential-supplier-analysis') || isCritical) {
                                totals.overall.possible += 10; // Max score for a question is 10
                                totals.overall.awarded += scoreValue;

                                if (elKey && totals.elements[elKey]) {
                                    totals.elements[elKey].possible += 10;
                                    totals.elements[elKey].awarded += scoreValue;
                                }
                            }

                            // Update critical totals if the question is critical and 'potential-supplier-analysis' is selected
                            if (isCritical && selectedProcessElements.includes('potential-supplier-analysis')) {
                                totals.critical.possible += 10;
                                totals.critical.awarded += scoreValue;
                            }
                        }
                    };

                    // Process scoreData (it's a single object now)
                    if (scoreData && scoreData.score !== undefined) {
                        processScoreValue(scoreData.score, question.isCritical);
                    }
                }
                
                // --- Validation Logic (only for 'potential-supplier-analysis' mode) ---
                let validationFailed = false;
                let errorMessages = [];

                if (selectedProcessElements.includes('potential-supplier-analysis')) {
                    selectedProcessElements.forEach(elKey => { // Iterate through elements selected for this audit
                        const totalCriticalQuestionsInElement = totalCriticalQuestionsCountPerElement[elKey] || 0;
                        const evaluatedCount = evaluatedQuestionsCountPerElement[elKey] || 0;
                        
                        // If there are no critical questions for this element, skip validation for it
                        if (totalCriticalQuestionsInElement === 0) return; 

                        // Calculate the minimum required evaluated questions (2/3 of total critical questions)
                        const requiredCount = Math.ceil(totalCriticalQuestionsInElement * (2/3));
                        
                        // Check if the number of evaluated questions meets the minimum requirement
                        if (evaluatedCount < requiredCount) {
                             validationFailed = true;
                             errorMessages.push(`过程要素 ${elKey} 的评价不足 (已评价 ${evaluatedCount} 项 / 共 ${totalCriticalQuestionsInElement} 项关键问题, 至少需要 ${requiredCount} 项)。`);
                        }
                    });
                    
                    // Additional check: Ensure no critical question remains 'ne' or un-scored.
                    selectedProcessElements.forEach(elKey => {
                        const elementData = criticalQuestionsData[elKey]; // Use filtered data for critical questions
                        if (elementData) {
                            Object.values(elementData.subElements).forEach(subEl => {
                                subEl.questions.forEach(q => {
                                    const qId = q.id;
                                    const scoreData = scores[qId];
                                    let isScored = false; // Flag to check if the question has been scored at all
                                    
                                    // Check if the score is valid (not 'ne' or undefined)
                                    if (scoreData && scoreData.score !== 'ne' && scoreData.score !== undefined) {
                                        isScored = true;
                                    }

                                    // If the question hasn't been scored, validation fails
                                    if (!isScored) {
                                        validationFailed = true;
                                        // Add error message if not already added by the count check
                                        if (!errorMessages.some(msg => msg.includes(`问题 "${qId} ${q.text.split('\n')[0]}"`))) {
                                            errorMessages.push(`问题 "${qId} ${q.text.split('\n')[0]}" 未被打分（包括 'ne'）。`);
                                        }
                                    }
                                });
                            });
                        }
                    });
                }

                const validationErrorMsg = document.getElementById('validation-error-msg');
                if (validationFailed) {
                    validationErrorMsg.innerHTML = '<strong>评价无效:</strong><br>' + errorMessages.join('<br>');
                    validationErrorMsg.style.display = 'block'; // Show the error message
                    return null; // Return null to indicate invalid state
                } else {
                    validationErrorMsg.style.display = 'none'; // Hide error message if validation passed
                }

                // Calculate compliance percentages if validation passed
                const overallCompliance = totals.overall.possible > 0 ? (totals.overall.awarded / totals.overall.possible) * 100 : 100;
                const criticalCompliance = totals.critical.possible > 0 ? (totals.critical.awarded / totals.critical.possible) * 100 : 100;
                
                const elementCompliance = {};
                for(const elKey in totals.elements) {
                    elementCompliance[elKey] = totals.elements[elKey].possible > 0 ? (totals.elements[elKey].awarded / totals.elements[elKey].possible) * 100 : 100;
                }

                // Determine overall grade based on compliance percentage
                let finalGrade = 'C'; // Default to C (Red)
                if (overallCompliance >= 90) finalGrade = 'A'; // Green
                else if (overallCompliance >= 80) finalGrade = 'B'; // Yellow
                
                // Return all calculated results
                return {
                    overallCompliance, criticalCompliance, elementCompliance,
                    overallGrade: finalGrade, downgradeReasons: [], // No downgrades logic implemented
                    allQuestions: getAllQuestionsForElement(selectedProcessElements), // Pass original questions data for complete info
                    scores: scores // Pass the actual scores object
                };
            }

            // Function to calculate scores and then display them in the UI
            function calculateAndDisplayResults() {
                const results = calculateScores(state);
                
                // If calculateScores returned null (validation failed)
                if (!results) {
                    // Display invalid state for overall summary
                    document.getElementById('overall-score').textContent = '无效';
                    document.getElementById('overall-grade').textContent = '-';
                    document.getElementById('overall-summary-card').className = 'summary-card grade-invalid'; // Apply invalid grade style
                    
                    // Clear previous matrix content and element summaries
                    document.getElementById('element-summary-container').innerHTML = '';
                    document.getElementById('evaluation-matrix-container').innerHTML = '';
                    
                    return; // Exit the function
                }
                
                // If validation passed, display the results
                document.getElementById('overall-score').textContent = results.overallCompliance.toFixed(2) + '%';
                const overallGradeElement = document.getElementById('overall-grade');
                const overallCard = document.getElementById('overall-summary-card');
                overallCard.className = 'summary-card'; // Reset classes

                // Determine display grade and class based on 'potential-supplier-analysis' mode
                if (state.selectedProcessElements.includes('potential-supplier-analysis')) {
                    if (results.overallCompliance >= 90) {
                        overallGradeElement.textContent = '绿 (批准的供应商)';
                        overallCard.classList.add('grade-green');
                    } else if (results.overallCompliance >= 80) {
                        overallGradeElement.textContent = '黄 (有条件批准的供应商)';
                        overallCard.classList.add('grade-yellow');
                    } else {
                        overallGradeElement.textContent = '红 (被禁止的供应商)';
                        overallCard.classList.add('grade-red');
                    }
                } else {
                    if (results.overallGrade === 'A') {
                        overallGradeElement.textContent = 'A（能够满足质量要求）';
                    } else if (results.overallGrade === 'B') {
                        overallGradeElement.textContent = 'B（一定程度上能够满足质量要求）';
                    } else {
                        overallGradeElement.textContent = 'C（无法满足质量要求）';
                    }
                    if (results.overallGrade === 'A') {
                        overallCard.classList.add('grade-green');
                    } else if (results.overallGrade === 'B') {
                        overallCard.classList.add('grade-yellow');
                    } else {
                        overallCard.classList.add('grade-red');
                    }
                }
                
                // Render Element Summaries
                const elContainer = document.getElementById('element-summary-container');
                elContainer.innerHTML = '';
                // Iterate over the *selected* process elements to display their summaries, maintaining order
                state.selectedProcessElements.forEach(elKey => {
                    const score = results.elementCompliance[elKey];
                    if (score !== undefined) { // Only display if there's a calculated score for this element
                        const item = document.createElement('div');
                        item.className = 'element-summary-item';
                        // Set border color based on score for visual feedback
                        let borderColor = 'var(--primary-color)'; // Default color
                        if (score < 80) borderColor = 'var(--warning-color)'; // Yellow threshold
                        if (score < 70) borderColor = 'var(--danger-color)'; // Red threshold
                        
                        item.style.borderColor = borderColor; // Apply dynamic border color
                        item.innerHTML = `<div class="el-name">${elKey}</div><div class="el-score">${score.toFixed(2)}%</div>`;
                        elContainer.appendChild(item);
                    }
                });

                // --- Render the Matrix Tables ---
                renderMatrixTable(results); // This function will display the detailed scores per question
            }

            //======================================================================
            // 6. EXPORT/IMPORT
            //======================================================================
                    function exportPlanToCsv() {
            console.log('exportPlanToCsv function called');
                if (state.auditPlan.length === 0) {
                    alert("审核计划是空的，无法导出。");
                    return;
                }
                // Helper to escape strings for CSV, handling quotes and newlines
                const escapeCsv = (str) => `"${(str || '').toString().replace(/"/g, '""').replace(/\r?\n/g, ' ')}"`; 
                let csvContent = "No.,报告编号,审核对象,开始日期,结束日期,审核类别,过程要素,审核组长,备注\n";
                
                state.auditPlan.forEach((plan, index) => {
                    const row = [
                        index + 1,
                        plan.reportNo,
                        plan.target,
                        plan.startDate,
                        plan.endDate,
                        plan.type === 'new' ? '新增审核' : '计划审核',
                        plan.elements.join('; '), // Elements are usually strings like "L1", "L2"
                        plan.leadAuditor,
                        plan.notes
                    ];
                    csvContent += row.map(escapeCsv).join(',') + '\n';
                });
                
                const blob = new Blob(["\uFEFF" + csvContent], {type: "text/csv;charset=utf-8;"}); // Add BOM for Excel compatibility
                downloadBlob(blob, `潜在供方分析计划_${state.planYear}.csv`);
            }

            // Handle exporting a specific checklist item in various formats
            function handleExport(format, checklistId) {
                const item = state.checklist.find(i => i.id == checklistId); // Find the checklist item by ID
                if (!item) {
                    alert('找不到指定的审核记录。请先将当前审核添加到清单中。');
                    return;
                }
                const auditState = item.fullState; // Get the full saved state from the checklist item
                const reportNo = item.reportNo;
                
                // Sanitize reportNo for use in filenames (replace potentially problematic characters)
                const safeReportNo = reportNo ? reportNo.replace(/[\s/\\?%*:|"<>]/g, '_') : `audit_${checklistId}`;
                const filename = `VDA6.8_分析_${safeReportNo}`;
                
                const results = calculateScores(auditState); // Recalculate scores and validate the saved state
                if(!results){
                    alert("无法生成报告，因为此审核记录的数据无效（未满足 2/3 评价要求或有未评分项）。请检查原始数据。");
                    return;
                }

                if (format === 'json') {
                    const dataStr = JSON.stringify(auditState, null, 2); // Pretty-print JSON
                    const blob = new Blob([dataStr], {type: "application/json;charset=utf-8;"});
                    downloadBlob(blob, `${filename}.json`);
                } else if (format === 'md') {
                    const mdContent = generateMarkdownReport(auditState, results);
                    const blob = new Blob([mdContent], {type: "text/markdown;charset=utf-8;"});
                    downloadBlob(blob, `${filename}.md`);
                } else if (format === 'csv') {
                    const csvContent = generateCsvReport(auditState, results);
                    const blob = new Blob(["\uFEFF" + csvContent], {type: "text/csv;charset=utf-8;"});
                    downloadBlob(blob, `${filename}.csv`);
                }
            }
            
            // Helper function to download a Blob object
            function downloadBlob(blob, filename) {
                const url = URL.createObjectURL(blob); // Create a temporary URL for the blob
                const a = document.createElement('a'); // Create a link element
                a.href = url;
                a.download = filename; // Set the download attribute for the filename
                document.body.appendChild(a); // Append the link to the body
                a.click(); // Programmatically click the link to trigger download
                document.body.removeChild(a); // Clean up the temporary link element
                URL.revokeObjectURL(url); // Release the object URL to free up memory
            }

            // Function to generate a Markdown report from audit state and results
            function generateMarkdownReport(s, results) {
                let md = `# VDA 6.8 潜在供方分析报告\n\n`;
                md += `## 基础信息\n\n`;
                md += `| 项目 | 内容 |\n|---|---|\n`;
                md += `| **报告编号** | ${escapeMarkdown(s.basicInfo['info-report-no']) || ''} |\n`;
                md += `| **受审核组织** | ${escapeMarkdown(s.basicInfo['info-auditee']) || ''} |\n`;
                md += `| **审核地点** | ${escapeMarkdown(s.basicInfo['info-location']) || ''} |\n`;
                md += `| **审核日期** | ${escapeMarkdown(s.basicInfo['info-audit-start-date']) || ''} 至 ${escapeMarkdown(s.basicInfo['info-audit-end-date']) || ''} |\n\n`;
                
                md += `## 评价总结\n\n`;
                md += `| 类别 | 得分/结果 |\n|---|---|\n`;
                md += `| **总体符合性 (E<sub>G</sub>)** | **${results.overallGrade}** (${results.overallCompliance.toFixed(2)}%) |\n`;
                
                md += `### 各过程要素符合性 (E<sub>Ln</sub>)\n\n`;
                md += `| 要素 | 符合性 (%) |\n|---|---|\n`;
                // Iterate over original selected elements to ensure order and inclusion
                s.selectedProcessElements.forEach(elKey => {
                    const score = results.elementCompliance[elKey];
                    if (score !== undefined) {
                        md += `| ${elKey} | ${score.toFixed(2)} |\n`;
                    }
                });
                md += '\n';

                md += `## 得分矩阵表\n\n`;
                // Use filtered criticalQuestionsData for the matrix to only show critical questions
                s.selectedProcessElements.forEach(elKey => {
                    const elementData = criticalQuestionsData[elKey];
                    // Skip if no critical questions exist for this element after filtering
                    if (!elementData || Object.values(elementData.subElements).every(subEl => subEl.questions.length === 0)) {
                        return;
                    }

                    md += `### ${elementData.title}\n`;
                    md += `| 提问编号 | 提问内容 | 得分 |\n`;
                    md += `|---|---|---|\n`;
                    Object.values(elementData.subElements).forEach(subEl => {
                         subEl.questions.forEach(q => {
                              let scoreText = '-';
                            const scoreData = results.scores[q.id];
                            if (scoreData && scoreData.score !== undefined && scoreData.score !== 'ne') {
                                scoreText = scoreData.score;
                            }
                            // Escape pipes and newlines in Markdown table cells
                            md += `| ${q.id}${q.isCritical ? '\\*' : ''} | ${escapeMarkdown(q.text.split('\n')[0])} | ${scoreText} |\n`; // Only first line of text for brevity
                        });
                    });
                    md += '\n';
                });

                md += `## 不符合项与措施计划\n\n`;
                md += `| 提问编号 | 提问内容 | 是否关键 | 得分 | 审核记录 | 发现的问题 | 负责人 | 措施 | 完成日期 |\n`;
                md += `|---|---|---|---|---|---|---|---|---|\n`;
                // Loop through scores to find non-conforming items for action plan
                Object.keys(results.scores).forEach(qId => {
                    const question = getQuestionById(qId); // Get original question details
                    // Only consider critical questions for the action plan
                    if (!question || !question.isCritical) return; 

                    const processRow = (scoreData, actionData) => {
                        if (scoreData && scoreData.score !== 'ne' && scoreData.score < 10) { // Filter for non-conforming items (score < 10)
                             const row = [
                                qId, question.text, question.isCritical ? '是' : '否',
                                scoreData.score, scoreData.record,
                                actionData.problem, actionData.responsible, actionData.measure,
                                actionData.dueDate,
                            ];
                            // Map each cell and join with '|' for Markdown table row
                            md += `| ${row.map(cell => escapeMarkdown(cell)).join(' | ')} |\n`;
                        }
                    };

                    const scoreData = results.scores[qId];
                    // Process score data (now a single object for all elements)
                    processRow(scoreData, s.actionPlan[qId] || {});
                });
                
                return md;
            }

            // Function to generate a CSV report
            function generateCsvReport(s, results) {
                const escapeCsv = (str) => `"${(str || '').toString().replace(/"/g, '""').replace(/\r?\n/g, ' ')}"`; // Escape quotes and newlines
                let csv = "类别,值\n"; // Header for summary data
                csv += `报告编号,${escapeCsv(s.basicInfo['info-report-no'])}\n`;
                csv += `审核日期,${escapeCsv(s.basicInfo['info-audit-end-date'])}\n`;
                csv += `总体符合性 (EG),${escapeCsv(results.overallCompliance.toFixed(2) + '%')}\n`;
                csv += `总体评级,${escapeCsv(results.overallGrade)}\n`;
                
                // Iterate over the *selected* process elements to include their compliance scores
                csv += "\n过程要素符合性:\n";
                s.selectedProcessElements.forEach(elKey => {
                    const score = results.elementCompliance[elKey];
                    if (score !== undefined) {
                         csv += `要素 ${elKey},${escapeCsv(score.toFixed(2) + '%')}\n`;
                    }
                });
                csv += "\n\n"; // Section break

                csv += "措施计划\n";
                // CSV header for the action plan details
                csv += "提问编号,提问内容,是否关键,得分,审核记录,发现的问题,负责人,措施,计划完成日期,状态,有效性验证\n";

                // Loop through scores to find non-conforming items for action plan
                Object.keys(results.scores).forEach(qId => {
                    const question = getQuestionById(qId); // Get original question details
                    // Only consider critical questions for the action plan
                    if (!question || !question.isCritical) return; 

                    const processRow = (scoreData, actionData) => {
                        if (scoreData && scoreData.score !== 'ne' && scoreData.score < 10) { // Filter for non-conforming items (score < 10)
                            const row = [
                                qId, question.text, question.isCritical ? '是' : '否',
                                scoreData.score, scoreData.record,
                                actionData.problem, actionData.responsible, actionData.measure,
                                actionData.dueDate, actionData.status, actionData.validation,
                            ];
                            csv += row.map(escapeCsv).join(',') + '\n'; // Escape each cell and join with comma
                        }
                    };

                    const scoreData = results.scores[qId];
                    // Process score data (now a single object for all elements)
                    processRow(scoreData, s.actionPlan[qId] || {}); // Pass action data if available
                });

                return csv; // Return the generated CSV content
            }
            
            // Helper function to escape text for Markdown tables
            function escapeMarkdown(text) {
                if (!text) return '';
                // Escape pipe characters and replace newlines with HTML break tags for proper rendering in Markdown table cells.
                // Using <br> is more robust than just replacing newlines for Markdown tables which might render single newlines as spaces.
                return text.toString().replace(/\|/g, '\\|').replace(/\n/g, '<br>');
            }

            //======================================================================
            // 7. INITIALIZATION
            //======================================================================
            function init() {
                localStorage.removeItem(LOCAL_STORAGE_KEY); // Clear local storage to ensure initialState is used
                state = loadState(); // Load saved state from local storage when the page loads
                setupEventListeners(); // Set up all event listeners for user interaction
                renderAll(); // Initial rendering of all UI elements based on the loaded state
            }

            // Ensure the script runs only after the DOM is fully loaded and parsed
            document.addEventListener('DOMContentLoaded', init);

        })();
</script>
            document.addEventListener('DOMContentLoaded', init);

        })();
</script>
</body>