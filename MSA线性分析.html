<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSA 线性分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 1600px;
            width: 95%;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #0056b3;
            text-align: center;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9e9e9;
            border-radius: 5px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input[type="number"],
        .input-group input[type="text"],
        .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
         .input-group textarea {
             height: 150px;
         }
        button {
            display: inline-block;
            background: #0056b3;
            color: #fff;
            padding: 10px 15px;
            border: 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #004494;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px 6px;
            text-align: center;
            min-width: 75px;
            max-width: 85px;
            white-space: nowrap;
            font-size: 0.9em;
        }

        .data-table th:first-child, .data-table td:first-child {
            min-width: 60px;
            max-width: 70px;
        }

        .data-table th:nth-child(2), .data-table td:nth-child(2) {
            min-width: 70px;
            max-width: 80px;
        }
        .data-table th {
            background-color: #f8f9fc;
            color: #2c3e50;
            font-weight: 600;
        }
        .results, .instructions {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9e9e9;
            border-radius: 5px;
        }
        .results h3, .instructions h3 {
            color: #0056b3;
        }
        .chart-container {
            margin-top: 20px;
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
        }
        canvas {
            max-width: 100%;
            height: auto;
        }
        .footer {
            margin-top: 30px;
            padding: 15px;
            background-color: #333;
            color: white;
            text-align: center;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .footer a {
            color: #fff;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .hidden {
            display: none;
        }
         .judgment {
            font-weight: bold;
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
         }
        .judgment.pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .judgment.conditional {
             background-color: #fff3cd;
             color: #856404;
             border: 1px solid #ffeeba;
        }
        .judgment.fail {
             background-color: #f8d7da;
             color: #721c24;
             border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MSA 线性分析工具</h1>

        <div class="section">
            <h2>数据输入</h2>
            <div class="input-group">
                <label for="numParts">零件个数:</label>
                <input type="number" id="numParts" value="5" min="1">
            </div>
            <div class="input-group">
                <label for="numMeasurements">测量次数:</label>
                <input type="number" id="numMeasurements" value="12" min="1">
            </div>
            <div class="input-group">
                <label for="referenceValues">参考值 (用逗号分隔):</label>
                <input type="text" id="referenceValues" value="2,4,6,8,10">
            </div>
             <div class="input-group">
                <label for="processVariation">过程变差 (用于 %EV 计算):</label>
                <input type="number" id="processVariation" value="6" min="0">
            </div>
             <div class="input-group">
                <label for="decimalPlaces">随机生成小数点位数:</label>
                <input type="number" id="decimalPlaces" value="2" min="0">
            </div>
            <div class="input-group">
                <button onclick="generateRandomData()">随机生成测量数据 (±10% 参考值)</button>
                <button onclick="clearData()">清空数据</button>
            </div>
            <div class="input-group">
                <label for="measurementData">测量数据 (每行一个零件，每列一次测量，使用 Tab 或逗号分隔):</label>
                <textarea id="measurementData"></textarea>
            </div>
             <div class="input-group">
                <button onclick="pasteData()">粘贴数据</button>
            </div>
             <div class="input-group">
                <button onclick="loadSampleData()">加载示例数据</button>
                 <button onclick="loadLastAnalysis()">加载最后一次分析数据</button>
            </div>
        </div>

        <div class="section">
            <h2>测量数据</h2>
        </div>
        <div style="overflow-x: auto; width: calc(100% - 30px); border: 1px solid #ddd; border-radius: 5px; margin: 0 15px 20px 15px; background: white; padding: 15px;">
            <table class="data-table" id="measurementTable" style="min-width: 1300px; width: auto; table-layout: fixed;">
                <thead>
                    <tr>
                        <th>零件号</th>
                        <th>参考值</th>
                        </tr>
                </thead>
                <tbody>
                    </tbody>
            </table>
        </div>

        <div class="section">
            <h2>分析结果</h2>
            <div class="results">
                <h3>统计结果</h3>
                <p id="overallBiasResult"></p>
                <p id="repeatabilityResult"></p>
                <p id="percentEVResult"></p>
                 <p id="linearitySlopeResult"></p>
                 <p id="linearityInterceptResult"></p>
                 <p id="linearityPValueResult"></p>
                 <p id="biasAtZeroPValueResult"></p>
                 <p id="rSquaredResult"></p>

                 <h3>判定结果</h3>
                 <p id="repeatabilityJudgment" class="judgment"></p>
                 <p id="linearityJudgment" class="judgment"></p>
                 <p id="biasJudgment" class="judgment"></p>
                 <p id="overallJudgment" class="judgment"></p>

            </div>
             <button onclick="performAnalysis()">执行分析</button>
        </div>

         <div class="section">
            <h2>分析图表</h2>
            <div class="chart-container">
                <h3>线性分析图 (偏差 vs. 参考值) - 含 95% 置信区间</h3>
                <canvas id="linearityChart"></canvas>
            </div>
             <div class="chart-container">
                <h3>测量数据分布图</h3>
                <canvas id="dataDistributionChart"></canvas>
            </div>
        </div>

        <div class="section">
            <h2>
                <button onclick="toggleInstructions()">显示/隐藏 说明</button>
            </h2>
            <div id="instructionsContent" class="instructions hidden">
                <h3>使用方法和操作说明</h3>
                <p>1. 输入零件个数、测量次数和每个零件的参考值（用逗号分隔）。</p>
                <p>2. 可以选择“随机生成测量数据”或将测量数据粘贴到文本区域，然后点击“粘贴数据”。粘贴数据时，每行对应一个零件，每列对应一次测量，数据之间使用 Tab 或逗号分隔。</p>
                <p>3. 点击“执行分析”按钮。</p>
                <p>4. 分析结果和图表将在下方显示。</p>
                 <p>5. “加载示例数据”按钮将加载预设的示例数据。</p>
                 <p>6. “加载最后一次分析数据”按钮（基于浏览器本地存储）将尝试加载您上次保存的数据。</p>

                <h3>判断准则 (参考 MSA 第四版手册及通用实践)</h3>
                <p><strong>重复性 (%EV):</strong></p>
                 <ul>
                     <li>%EV (Equipment Variation) 表示量具重复性对总变差的贡献。</li>
                     <li>计算公式为 %EV = 100 * (Repeatability 标准差 / 总变差)。总变差通常使用过程变差或规范范围的六分之一。</li>
                     <li>判断标准 (通用参考):</li>
                     <ul>
                         <li style="color: green;">%EV < 10%: 量具重复性可接受。</li>
                         <li style="color: orange;">10% <= %EV < 30%: 量具重复性可能可接受，取决于应用和重要程度，可能需要改进。</li>
                         <li style="color: red;">%EV >= 30%: 量具重复性不可接受，需要改进量具。</li>
                     </ul>
                 </ul>

                <p><strong>线性度 (Linearity):</strong></p>
                <ul>
                    <li>线性分析图显示了偏差随参考值的变化趋势。</li>
                    <li>通常通过拟合偏差与参考值的线性回归方程来评估线性度。</li>
                    <li>关注回归方程的斜率 (Slope) 和其 P 值。</li>
                    <li>如果斜率的 P 值小于显著性水平 (通常为 0.05)，则认为线性度是显著的，即偏差随参考值呈线性变化。此时需要根据斜率的大小判断线性误差是否可接受。</li>
                    <li>如果斜率的 P 值大于显著性水平，则认为线性度不显著，即偏差不随参考值呈线性变化。</li>
                    <li>R² 值表示参考值的变化解释了多少偏差的变化。R² 越高，线性关系越强。</li>
                     <li><strong>图表判断辅助:</strong> 线性分析图中的 95% 置信区间表示在给定参考值下，平均偏差的真实值有 95% 的概率落在此区间内。如果回归线（表示预测的平均偏差）的整个 95% 置信区间包含零线（偏差为零），则通常认为线性度可接受（即没有显著的线性偏倚）。如果整个区间不包含零线，则线性偏倚显著。</li>
                </ul>
                <p><strong>偏倚 (Bias):</strong></p>
                 <ul>
                     <li>整体偏倚是所有测量的平均偏差。</li>
                     <li>更重要的是评估在整个量具操作范围内偏倚是否可接受，这通常通过线性回归的截距 (Intercept) 和其 P 值来判断（截距代表参考值为零时的偏倚）。</li>
                     <li>如果截距的 P 值小于显著性水平 (通常为 0.05)，则认为偏倚在参考值为零时是显著的。</li>
                      <li>需要结合线性度一起判断。如果线性度显著，量具的偏倚会随参考值变化，需要考虑在不同范围内评估偏倚或重新校准。</li>
                      <li>如果线性度不显著，可以主要关注整体偏倚是否接近于零或在可接受范围内。</li>
                       <li><strong>图表判断辅助:</strong> 偏倚的判断也可以通过检验回归线（表示预测的平均偏差）是否在整个量具操作范围内与零线（偏差为零）的 95% 置信区间重叠来辅助判断。如果整个区间不包含零线，表示在操作范围内存在显著偏倚。</li>
                 </ul>
                 <p><strong>综合判断:</strong></p>
                 <ul>
                     <li>一个可接受的测量系统通常要求重复性、偏倚和线性度都在可接受的范围内。</li>
                     <li>如果重复性 (%EV) 不可接受，通常需要首先改进量具的重复性问题，线性度和偏倚的分析可能意义不大。</li>
                     <li>如果重复性可接受，则进一步评估线性度和偏倚。</li>
                     <li>如果线性度显著，需要评估线性偏倚的大小是否可接受。</li>
                     <li>如果线性度不显著，主要评估整体偏倚是否可接受。</li>
                 </ul>


                <h3>统计公式说明</h3>
                <p><strong>偏差 (Bias):</strong> 对于每个测量值，偏差 = 测量值 - 参考值。</p>
                <p><strong>平均偏差 (Average Bias):</strong> 所有测量偏差的总和除以测量总次数。</p>
                <p><strong>重复性标准差 (Repeatability Standard Deviation, σ<sub>repeatability</sub>):</strong> 基于同一零件多次测量值的标准差的 Pooled Estimator。计算方法通常是每个零件测量值的标准差的平方的平均值的平方根，或者使用 ANOVA 方法的结果。</p>
                 <p><strong>%EV:</strong> %EV = 100 * (σ<sub>repeatability</sub> / 总变差)</p>
                 <p><strong>线性回归 (Bias vs. Reference Value):</strong> 拟合回归方程 Bias = Intercept + Slope * Reference Value。</p>
                 <ul>
                     <li>斜率 (Slope): 表示偏差随参考值变化的速率。</li>
                     <li>截距 (Intercept): 表示参考值为零时的偏差。</li>
                     <li>R²: 回归方程的决定系数，表示参考值解释了偏差总变异的比例。</li>
                     <li>斜率 P 值: 检验斜率是否显著异于零。（注：本工具中的 P 值和 t 值是近似计算，精确计算需使用统计库或查找表）</li>
                     <li>截距 P 值: 检验截距是否显著异于零。（注：本工具中的 P 值和 t 值是近似计算，精确计算需使用统计库或查找表）</li>
                 </ul>
                 <p><strong>95% 置信区间 (Confidence Interval) for the Mean Predicted Bias:</strong></p>
                  <p>预测偏差 ± t<sub>(1 - α/2, n-2)</sub> * 标准误</p>
                  <p>其中：</p>
                  <ul>
                      <li>预测偏差 = 截距 + 斜率 * 参考值</li>
                      <li>t<sub>(1 - α/2, n-2)</sub> 是自由度为 n-2 时，置信水平为 1 - α (通常 α = 0.05, 1 - α/2 = 0.975) 的 t 分布临界值。（注：本工具中的 P 值和 t 值是近似计算，精确计算需使用统计库或查找表）</li>
                      <li>标准误 = sqrt(MSE * (1/n + (参考值 - 平均参考值)² / (参考值平方和 - n * 平均参考值²)))</li>
                      <li>MSE (Mean Squared Error): 回归残差平方和除以自由度 (n-2)。</li>
                  </ul>

                 <h3>保存和导出功能</h3>
                 <p>“保存当前分析”和“加载最后一次分析数据”功能基于浏览器本地存储实现，数据保存在您的浏览器中。</p>
                 <p>“导出到 Excel”将数据导出为 CSV 文件，可在 Excel 中打开。</p>
                 <p>“导出到 Markdown”将数据和结果导出为 Markdown 格式文本。</p>
                 <button onclick="saveAnalysis()">保存当前分析</button>
                 <button onclick="exportData('excel')">导出到 Excel</button>
                 <button onclick="exportData('markdown')">导出到 Markdown</button>
            </div>
        </div>

        <div class="footer">
            版权所有 © 龍少 | 电话&微信：18616939009 | 更多资料下载地址：<a href="http://iatf16949.ysepan.com" target="_blank">http://iatf16949.ysepan.com</a> | 你可以山寨我的文件，但你无法复制我的思想！
        </div>
    </div>

    <script>
        let measurementData = [];
        let linearityChart;
        let dataDistributionChart;

        function updateMeasurementTable() {
            const numParts = parseInt(document.getElementById('numParts').value);
            const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
            const referenceValuesInput = document.getElementById('referenceValues').value;
            const referenceValues = referenceValuesInput.split(',').map(val => parseFloat(val.trim()));

            const tableHead = document.getElementById('measurementTable').querySelector('thead tr');
            const tableBody = document.getElementById('measurementTable').querySelector('tbody');

            // Clear previous table content
            tableHead.innerHTML = '<th>零件号</th><th>参考值</th>';
            tableBody.innerHTML = '';

            // Add measurement columns to header
            for (let i = 1; i <= numMeasurements; i++) {
                const th = document.createElement('th');
                th.textContent = `测量 ${i}`;
                tableHead.appendChild(th);
            }

            // Add rows for each part
            for (let i = 0; i < numParts; i++) {
                const tr = document.createElement('tr');
                const partNumTd = document.createElement('td');
                partNumTd.textContent = i + 1;
                tr.appendChild(partNumTd);

                const refValTd = document.createElement('td');
                refValTd.textContent = referenceValues[i] !== undefined && !isNaN(referenceValues[i]) ? referenceValues[i].toFixed(2) : ''; // Display reference value
                tr.appendChild(refValTd);

                // Add cells for measurement data input
                for (let j = 0; j < numMeasurements; j++) {
                    const td = document.createElement('td');
                    td.contentEditable = true; // Make cells editable
                    td.addEventListener('input', updateMeasurementDataFromTable); // Add event listener for input
                    tr.appendChild(td);
                }
                tableBody.appendChild(tr);
            }

             // Initialize measurementData array based on table structure
             measurementData = Array(numParts).fill(0).map(() => Array(numMeasurements).fill(null));
        }

         function updateMeasurementDataFromTable() {
             const numParts = parseInt(document.getElementById('numParts').value);
             const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
             const tableBody = document.getElementById('measurementTable').querySelector('tbody');

             measurementData = []; // Clear existing data
             for(let i = 0; i < numParts; i++) {
                 const row = tableBody.rows[i];
                 const partMeasurements = [];
                 for(let j = 0; j < numMeasurements; j++) {
                     const cell = row.cells[j + 2]; // +2 to skip Part Num and Reference Value columns
                     const value = parseFloat(cell.textContent.trim());
                     partMeasurements.push(isNaN(value) ? null : value);
                 }
                  measurementData.push(partMeasurements);
             }
         }


        function generateRandomData() {
            const numParts = parseInt(document.getElementById('numParts').value);
            const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
            const referenceValuesInput = document.getElementById('referenceValues').value;
            const referenceValues = referenceValuesInput.split(',').map(val => parseFloat(val.trim()));
            const decimalPlaces = parseInt(document.getElementById('decimalPlaces').value);

            if (referenceValues.length !== numParts || referenceValues.some(isNaN)) {
                alert('参考值数量与零件个数不匹配或参考值无效！');
                return;
            }

            measurementData = [];
            const tableBody = document.getElementById('measurementTable').querySelector('tbody');
             tableBody.innerHTML = ''; // Clear existing rows

            for (let i = 0; i < numParts; i++) {
                const partMeasurements = [];
                const tr = document.createElement('tr');
                const partNumTd = document.createElement('td');
                partNumTd.textContent = i + 1;
                tr.appendChild(partNumTd);

                const refValTd = document.createElement('td');
                refValTd.textContent = referenceValues[i].toFixed(decimalPlaces);
                tr.appendChild(refValTd);

                for (let j = 0; j < numMeasurements; j++) {
                    const ref = referenceValues[i];
                    const min = ref * 0.9;
                    const max = ref * 1.1;
                    const randomValue = (Math.random() * (max - min) + min).toFixed(decimalPlaces);
                    partMeasurements.push(parseFloat(randomValue));

                     const td = document.createElement('td');
                     td.contentEditable = true;
                     td.textContent = randomValue;
                     td.addEventListener('input', updateMeasurementDataFromTable);
                     tr.appendChild(td);
                }
                measurementData.push(partMeasurements);
                 tableBody.appendChild(tr);
            }
            document.getElementById('measurementData').value = measurementData.map(row => row.join('\t')).join('\n');
             updateMeasurementDataFromTable(); // Ensure internal data array is updated from the new table content
        }

        function pasteData() {
            const data = document.getElementById('measurementData').value.trim();
            if (!data) return;

            const rows = data.split('\n');
            const numParts = rows.length;
            const measurements = rows.map(row => row.split(/[\t,]/).map(val => parseFloat(val.trim())));

            // Validate pasted data
             const validMeasurements = measurements.filter(row => row.every(val => !isNaN(val) && val !== null)); // Filter out rows with any invalid data
             if (validMeasurements.length === 0 || validMeasurements.length !== numParts) {
                  alert('粘贴数据无效或与零件个数不匹配，请检查每行数据是否只包含数字。');
                   return;
             }


            const numMeasurements = validMeasurements.length > 0 ? validMeasurements[0].length : 0;
             if (numMeasurements === 0 || validMeasurements.some(row => row.length !== numMeasurements)) {
                  alert('粘贴数据列数不一致或无效，请检查每行测量次数是否相同且为数字。');
                  return;
             }


             document.getElementById('numParts').value = numParts;
             document.getElementById('numMeasurements').value = numMeasurements;

            measurementData = validMeasurements;
             updateMeasurementTableWithData();
        }

         function updateMeasurementTableWithData() {
             const numParts = parseInt(document.getElementById('numParts').value);
             const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
             const referenceValuesInput = document.getElementById('referenceValues').value;
             const referenceValues = referenceValuesInput.split(',').map(val => parseFloat(val.trim()));

             if (referenceValues.length !== numParts || referenceValues.some(isNaN)) {
                 alert('参考值数量与零件个数不匹配或参考值无效！无法更新表格数据。');
                 return;
             }


             const tableHead = document.getElementById('measurementTable').querySelector('thead tr');
             const tableBody = document.getElementById('measurementTable').querySelector('tbody');

             // Clear previous table content
             tableHead.innerHTML = '<th>零件号</th><th>参考值</th>';
             tableBody.innerHTML = '';

             // Add measurement columns to header
             for (let i = 1; i <= numMeasurements; i++) {
                 const th = document.createElement('th');
                 th.textContent = `测量 ${i}`;
                 tableHead.appendChild(th);
             }

             // Add rows with data
             for (let i = 0; i < numParts; i++) {
                 const tr = document.createElement('tr');
                 const partNumTd = document.createElement('td');
                 partNumTd.textContent = i + 1;
                 tr.appendChild(partNumTd);

                 const refValTd = document.createElement('td');
                 refValTd.textContent = referenceValues[i] !== undefined && !isNaN(referenceValues[i]) ? referenceValues[i].toFixed(2) : '';
                 tr.appendChild(refValTd);

                 for (let j = 0; j < numMeasurements; j++) {
                     const td = document.createElement('td');
                     td.contentEditable = true;
                     td.textContent = measurementData[i] && measurementData[i][j] !== undefined && !isNaN(measurementData[i][j]) ? measurementData[i][j] : '';
                     td.addEventListener('input', updateMeasurementDataFromTable);
                     tr.appendChild(td);
                 }
                 tableBody.appendChild(tr);
             }
         }

        function clearData() {
            document.getElementById('measurementData').value = '';
            measurementData = [];
             updateMeasurementTable();
             document.getElementById('overallBiasResult').textContent = '';
             document.getElementById('repeatabilityResult').textContent = '';
             document.getElementById('percentEVResult').textContent = '';
             document.getElementById('linearitySlopeResult').textContent = '';
             document.getElementById('linearityInterceptResult').textContent = '';
             document.getElementById('linearityPValueResult').textContent = '';
             document.getElementById('biasAtZeroPValueResult').textContent = '';
             document.getElementById('rSquaredResult').textContent = '';

             // Clear judgments
             document.getElementById('repeatabilityJudgment').textContent = '';
             document.getElementById('repeatabilityJudgment').className = 'judgment';
             document.getElementById('linearityJudgment').textContent = '';
              document.getElementById('linearityJudgment').className = 'judgment';
             document.getElementById('biasJudgment').textContent = '';
              document.getElementById('biasJudgment').className = 'judgment';
             document.getElementById('overallJudgment').textContent = '';
              document.getElementById('overallJudgment').className = 'judgment';


             if (linearityChart) linearityChart.destroy();
             if (dataDistributionChart) dataDistributionChart.destroy();

             // Optional: Clear saved data from local storage as well
             // localStorage.removeItem('lastMSAAnalysis');
        }

         function loadSampleData() {
             document.getElementById('numParts').value = 5;
             document.getElementById('numMeasurements').value = 12;
             document.getElementById('referenceValues').value = "2,4,6,8,10";
              document.getElementById('processVariation').value = 6;
             document.getElementById('decimalPlaces').value = 2;

             const sampleData = `2.1,2.2,2.0,2.1,2.2,2.1,2.0,2.1,2.2,2.1,2.0,2.1
4.1,4.2,4.3,4.1,4.2,4.2,4.1,4.3,4.2,4.1,4.2,4.3
6.0,6.1,6.0,6.2,6.1,6.0,6.1,6.2,6.1,6.0,6.1,6.2
8.1,8.0,8.1,8.2,8.0,8.1,8.2,8.1,8.0,8.1,8.2,8.0
10.0,10.1,10.0,10.1,10.2,10.1,10.0,10.2,10.1,10.0,10.1,10.2`;

             document.getElementById('measurementData').value = sampleData;
             pasteData(); // Load data from the textarea
         }

         function saveAnalysis() {
             const analysisData = {
                 numParts: parseInt(document.getElementById('numParts').value),
                 numMeasurements: parseInt(document.getElementById('numMeasurements').value),
                 referenceValues: document.getElementById('referenceValues').value,
                 processVariation: parseFloat(document.getElementById('processVariation').value),
                 decimalPlaces: parseInt(document.getElementById('decimalPlaces').value),
                 measurementData: measurementData // Use the internal measurementData array
             };
             try {
                localStorage.setItem('lastMSAAnalysis', JSON.stringify(analysisData));
                alert("当前分析数据已保存到浏览器本地存储。");
             } catch (e) {
                 alert("保存数据到本地存储失败：" + e);
             }
         }

         function loadLastAnalysis() {
             const savedData = localStorage.getItem('lastMSAAnalysis');
             if (savedData) {
                 try {
                     const analysisData = JSON.parse(savedData);
                     document.getElementById('numParts').value = analysisData.numParts || 5;
                     document.getElementById('numMeasurements').value = analysisData.numMeasurements || 12;
                     document.getElementById('referenceValues').value = analysisData.referenceValues || "2,4,6,8,10";
                     document.getElementById('processVariation').value = analysisData.processVariation || 6;
                     document.getElementById('decimalPlaces').value = analysisData.decimalPlaces || 2;

                     // Restore measurement data and update table
                     measurementData = analysisData.measurementData || [];
                     updateMeasurementTableWithData(); // Use the function to populate the table from measurementData

                     alert("已加载最后一次保存的分析数据。");
                 } catch (e) {
                      alert("加载保存的数据失败：" + e);
                 }
             } else {
                 alert("没有找到之前保存的分析数据。");
             }
         }

        function performAnalysis() {
            const numParts = parseInt(document.getElementById('numParts').value);
            const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
            const referenceValuesInput = document.getElementById('referenceValues').value;
            const referenceValues = referenceValuesInput.split(',').map(val => parseFloat(val.trim()));
            const processVariation = parseFloat(document.getElementById('processVariation').value);

             // Re-read data from table to ensure latest edits are included
             updateMeasurementDataFromTable();


            if (measurementData.length === 0 || measurementData.length !== numParts || measurementData.some(row => row.length !== numMeasurements || row.some(val => val === null || isNaN(val)))) {
                alert('测量数据不完整或包含无效值，请检查数据输入。');
                return;
            }
             if (referenceValues.length !== numParts || referenceValues.some(isNaN)) {
                 alert('参考值数量与零件个数不匹配或参考值无效！');
                 return;
             }
             if (isNaN(processVariation) || processVariation <= 0) {
                 alert('请输入有效的过程变差（大于零）。');
                 return;
             }


            // Calculations
            const biases = [];
            const averageBiasesPerPart = [];
            let totalBiasSum = 0;
            const squaredDifferencesForRepeatability = []; // For repeatability calculation

            for (let i = 0; i < numParts; i++) {
                const ref = referenceValues[i];
                let sumBiasPerPart = 0;
                let sumSquaredDiffPerPart = 0; // For repeatability
                const measurementsForPart = measurementData[i];


                // Calculate average for repeatability calculation
                const averageMeasurementForPart = measurementsForPart.reduce((sum, val) => sum + val, 0) / numMeasurements;


                for (let j = 0; j < numMeasurements; j++) {
                    const measurement = measurementsForPart[j];
                    const bias = measurement - ref;
                    biases.push({ ref: ref, bias: bias });
                    sumBiasPerPart += bias;
                    totalBiasSum += bias;

                    // For repeatability calculation
                    sumSquaredDiffPerPart += Math.pow(measurement - averageMeasurementForPart, 2);
                }
                averageBiasesPerPart.push({ ref: ref, avgBias: sumBiasPerPart / numMeasurements });
                 squaredDifferencesForRepeatability.push(sumSquaredDiffPerPart);
            }

            const overallAverageBias = totalBiasSum / (numParts * numMeasurements);

             // Calculate repeatability standard deviation (Pooled Standard Deviation Method - simplified)
             let totalSumSquaredDiff = squaredDifferencesForRepeatability.reduce((sum, val) => sum + val, 0);
              let pooledVariance = 0;
              if ((numParts * numMeasurements - numParts) > 0) {
                pooledVariance = totalSumSquaredDiff / (numParts * numMeasurements - numParts);
              }
             const repeatabilityStdDev = Math.sqrt(pooledVariance);


             // Calculate %EV
             const percentEV = processVariation > 0 ? (repeatabilityStdDev / processVariation) * 100 : Infinity;


            // Linear Regression (Bias vs. Reference Value)
            // Using simplified linear regression formulas
            let sumRef = 0;
            let sumBias = 0;
            let sumRefSquared = 0;
            let sumRefBias = 0;
            const n = biases.length; // Total number of bias points

            for (let i = 0; i < n; i++) {
                sumRef += biases[i].ref;
                sumBias += biases[i].bias;
                sumRefSquared += biases[i].ref * biases[i].ref;
                sumRefBias += biases[i].ref * biases[i].bias;
            }

            const meanRef = sumRef / n;
            const meanBias = sumBias / n;

             let slope = 0;
             let intercept = meanBias; // If denominator is zero, slope is 0, intercept is mean bias
             const denominator = (n * sumRefSquared - sumRef * sumRef);
             if (denominator !== 0) {
                 slope = (n * sumRefBias - sumRef * sumBias) / denominator;
                 intercept = meanBias - slope * meanRef;
             }


             // Calculate R-squared
             let totalSumOfSquares = 0;
             let residualSumOfSquares = 0;
             for(let i = 0; i < n; i++) {
                 totalSumOfSquares += Math.pow(biases[i].bias - meanBias, 2);
                 residualSumOfSquares += Math.pow(biases[i].bias - (intercept + slope * biases[i].ref), 2);
             }
             const rSquared = totalSumOfSquares !== 0 ? 1 - (residualSumOfSquares / totalSumOfSquares) : 0;


             // Calculate standard error of the slope and intercept for t-tests
             const degreesOfFreedom = n - 2;
              let meanSquaredError = 0;
              if (degreesOfFreedom > 0) {
                meanSquaredError = residualSumOfSquares / degreesOfFreedom;
              }

             const sumSquaredDifferencesOfRefs = sumRefSquared - n * meanRef * meanRef;
             const stdErrorSlope = degreesOfFreedom > 0 && sumSquaredDifferencesOfRefs > 0 ? Math.sqrt(meanSquaredError / sumSquaredDifferencesOfRefs) : 0;
             const stdErrorIntercept = degreesOfFreedom > 0 && sumSquaredDifferencesOfRefs > 0 ? Math.sqrt(meanSquaredError * (1/n + meanRef * meanRef / sumSquaredDifferencesOfRefs)) : 0;


             // Calculate t-statistics
             const tStatisticSlope = stdErrorSlope !== 0 ? slope / stdErrorSlope : (slope > 0 ? Infinity : (slope < 0 ? -Infinity : 0));
             const tStatisticIntercept = stdErrorIntercept !== 0 ? intercept / stdErrorIntercept : (intercept > 0 ? Infinity : (intercept < 0 ? -Infinity : 0));


             // Placeholder for P-value - requires a proper statistical function or lookup table.
             // Using a common approximation: for df > ~30, t-critical for alpha=0.05 (two-tailed) is approx 2.
             // For smaller df, it's larger. This is a significant simplification.
             const alpha = 0.05;
             // A common simplification for illustration: use a fixed t-critical or approximate based on df.
             // For illustrative judgment based on t-value:
             const tCriticalForJudgment = 2; // Simplified threshold


            // Display Results
            document.getElementById('overallBiasResult').textContent = `整体平均偏倚: ${overallAverageBias.toFixed(4)}`;
            document.getElementById('repeatabilityResult').textContent = `重复性标准差 (σ repeatability): ${repeatabilityStdDev.toFixed(4)}`;
            document.getElementById('percentEVResult').textContent = `%EV (重复性对总变差的贡献): ${isFinite(percentEV) ? percentEV.toFixed(2) + '%' : '无法计算 (过程变差为零)'}`;
            document.getElementById('linearitySlopeResult').textContent = `线性回归斜率: ${slope.toFixed(4)}`;
            document.getElementById('linearityInterceptResult').textContent = `线性回归截距: ${intercept.toFixed(4)}`;
            document.getElementById('linearityPValueResult').textContent = `线性度 (斜率) P值 (基于近似): ${Math.abs(tStatisticSlope) > tCriticalForJudgment && degreesOfFreedom > 0 ? '< ' + alpha : '>=' + alpha} (t=${tStatisticSlope.toFixed(3)}, df=${degreesOfFreedom})`; // Simplified P-value display
            document.getElementById('biasAtZeroPValueResult').textContent = `参考值零点偏倚 (截距) P值 (基于近似): ${Math.abs(tStatisticIntercept) > tCriticalForJudgment && degreesOfFreedom > 0 ? '< ' + alpha : '>=' + alpha} (t=${tStatisticIntercept.toFixed(3)}, df=${degreesOfFreedom})`; // Simplified P-value display
            document.getElementById('rSquaredResult').textContent = `R²: ${rSquared.toFixed(4)}`;

             // Judgment based on results

             // Repeatability Judgment
             const repeatabilityJudgmentElement = document.getElementById('repeatabilityJudgment');
             repeatabilityJudgmentElement.textContent = `重复性判定 (%EV ${isFinite(percentEV) ? percentEV.toFixed(2) + '%' : 'N/A'}): `;
             repeatabilityJudgmentElement.className = 'judgment'; // Reset classes
             if (!isFinite(percentEV)) {
                  repeatabilityJudgmentElement.textContent += "无法判定 (过程变差为零)。";
                  repeatabilityJudgmentElement.classList.add('conditional'); // Cannot judge
             } else if (percentEV < 10) {
                 repeatabilityJudgmentElement.textContent += "可接受。";
                 repeatabilityJudgmentElement.classList.add('pass');
             } else if (percentEV < 30) {
                 repeatabilityJudgmentElement.textContent += "条件接受 (需改进)。";
                 repeatabilityJudgmentElement.classList.add('conditional');
             } else {
                 repeatabilityJudgmentElement.textContent += "不可接受。";
                 repeatabilityJudgmentElement.classList.add('fail');
             }

             // Linearity Judgment (based on approximate t-value)
             const linearityJudgmentElement = document.getElementById('linearityJudgment');
             linearityJudgmentElement.textContent = `线性度判定 (基于近似 t 值): `;
              linearityJudgmentElement.className = 'judgment'; // Reset classes
              if (degreesOfFreedom <= 0) {
                   linearityJudgmentElement.textContent += "数据不足，无法判定线性度。";
                   linearityJudgmentElement.classList.add('conditional'); // Cannot judge
              } else if (Math.abs(tStatisticSlope) > tCriticalForJudgment) {
                   linearityJudgmentElement.textContent += `斜率可能显著异于零 (|t|=${Math.abs(tStatisticSlope).toFixed(3)} > ${tCriticalForJudgment})，线性度可能显著，请检查线性误差大小及 95% 置信区间是否包含零线。`;
                    linearityJudgmentElement.classList.add('conditional'); // Often requires further investigation
              } else {
                   linearityJudgmentElement.textContent += `斜率可能不显著异于零 (|t|=${Math.abs(tStatisticSlope).toFixed(3)} <= ${tCriticalForJudgment})，线性度可能不显著。`;
                   linearityJudgmentElement.classList.add('pass');
              }

              // Bias Judgment (at zero, based on approximate t-value)
             const biasJudgmentElement = document.getElementById('biasJudgment');
             biasJudgmentElement.textContent = `偏倚判定 (零点，基于近似 t 值): `;
             biasJudgmentElement.className = 'judgment'; // Reset classes
             if (degreesOfFreedom <= 0) {
                  biasJudgmentElement.textContent += "数据不足，无法判定偏倚。";
                  biasJudgmentElement.classList.add('conditional'); // Cannot judge
             } else if (Math.abs(tStatisticIntercept) > tCriticalForJudgment) {
                  biasJudgmentElement.textContent += `截距可能显著异于零 (|t|=${Math.abs(tStatisticIntercept).toFixed(3)} > ${tCriticalForJudgment})，零点偏倚可能显著。`;
                   biasJudgmentElement.classList.add('conditional'); // Bias at zero is significant
             } else {
                  biasJudgmentElement.textContent += `截距可能不显著异于零 (|t|=${Math.abs(tStatisticIntercept).toFixed(3)} <= ${tCriticalForJudgment})，零点偏倚可能不显著。`;
                  biasJudgmentElement.classList.add('pass');
             }

             // Overall Judgment (Simplified)
             const overallJudgmentElement = document.getElementById('overallJudgment');
             overallJudgmentElement.textContent = `综合判定 (基于当前分析结果): `;
              overallJudgmentElement.className = 'judgment'; // Reset classes

             if (!isFinite(percentEV) || percentEV >= 30) {
                  overallJudgmentElement.textContent += "测量系统重复性不可接受或无法判定，需优先改进量具。";
                  overallJudgmentElement.classList.add('fail');
             } else if (percentEV >= 10 && percentEV < 30) {
                 overallJudgmentElement.textContent += "重复性条件接受，请评估线性度和偏倚，可能需要改进。";
                  overallJudgmentElement.classList.add('conditional');
             } else { // %EV < 10
                 if (degreesOfFreedom <= 0) {
                      overallJudgmentElement.textContent += "重复性可接受，但数据不足，无法判定线性度和偏倚。";
                       overallJudgmentElement.classList.add('conditional');
                 } else if (Math.abs(tStatisticSlope) > tCriticalForJudgment || Math.abs(tStatisticIntercept) > tCriticalForJudgment) {
                      overallJudgmentElement.textContent += "重复性可接受，但线性度或偏倚可能显著，需进一步评估和改进。";
                       overallJudgmentElement.classList.add('fail'); // Or conditional, depends on magnitude
                 } else {
                      overallJudgmentElement.textContent += "重复性、线性度、偏倚可能都可接受 (基于近似)。";
                      overallJudgmentElement.classList.add('pass');
                 }
             }


            // Charting
            renderLinearityChart(biases, intercept, slope, meanRef, meanSquaredError, sumSquaredDifferencesOfRefs, n, degreesOfFreedom);
             renderDataDistributionChart(referenceValues, measurementData);
        }

        function renderLinearityChart(biases, intercept, slope, meanRef, mse, sumSquaredDifferencesOfRefs, n, degreesOfFreedom) {
            const ctx = document.getElementById('linearityChart').getContext('2d');
            if (linearityChart) linearityChart.destroy();

            // Sort biases by reference value to ensure lines are drawn correctly
            biases.sort((a, b) => a.ref - b.ref);

            // Prepare data for the regression line and confidence intervals
            const allRefs = biases.map(b => b.ref);
            const minRef = allRefs.length > 0 ? Math.min(...allRefs) : 0;
            const maxRef = allRefs.length > 0 ? Math.max(...allRefs) : 10;

            const referenceRange = maxRef - minRef;
            const plotPoints = 100; // Number of points to plot for lines
            const step = referenceRange > 0 ? referenceRange / (plotPoints - 1) : 1; // Avoid division by zero

            const regressionLineData = [];
            const lowerCiData = [];
            const upperCiData = [];

            // Approximate t-value for 95% CI and n-2 degrees of freedom.
            // For a precise value, use a statistical library or a t-distribution lookup function.
             let tValue = 1.96; // Standard normal approximation for large df
             // Using a simple lookup for common small dfs:
             if (degreesOfFreedom > 0) {
                 const tTable = { // Approximate t-values for 95% CI (two-tailed)
                      1: 12.71, 2: 4.303, 3: 3.182, 4: 2.776, 5: 2.571,
                      6: 2.447, 7: 2.365, 8: 2.306, 9: 2.262, 10: 2.228,
                      11: 2.201, 12: 2.179, 13: 2.160, 14: 2.145, 15: 2.131,
                      20: 2.086, 25: 2.060, 30: 2.042, 40: 2.021, 60: 2.000,
                      120: 1.980
                 };
                  // Find the closest df in the table or use the approximation for larger df
                  const dfs = Object.keys(tTable).map(Number);
                  const closestDf = dfs.reduce((prev, curr) =>
                       Math.abs(curr - degreesOfFreedom) < Math.abs(prev - degreesOfFreedom) ? curr : prev
                   );

                  tValue = degreesOfFreedom in tTable ? tTable[degreesOfFreedom] : (degreesOfFreedom > 120 ? 1.96 : tTable[closestDf]);

             } else {
                  tValue = 0; // Cannot calculate CI with <= 2 data points
             }


            for (let i = 0; i < plotPoints; i++) {
                const currentRef = minRef + step * i;
                const predictedBias = intercept + slope * currentRef;

                 // Calculate Standard Error of the Predicted Bias
                 // Handle division by zero
                 const sePredictedBias = degreesOfFreedom > 0 && sumSquaredDifferencesOfRefs > 0
                                        ? Math.sqrt(mse * (1/n + Math.pow(currentRef - meanRef, 2) / sumSquaredDifferencesOfRefs))
                                         : 0;


                const marginOfError = tValue * sePredictedBias;
                const lowerCi = predictedBias - marginOfError;
                const upperCi = predictedBias + marginOfError;

                regressionLineData.push({ x: currentRef, y: predictedBias });
                lowerCiData.push({ x: currentRef, y: lowerCi });
                upperCiData.push({ x: currentRef, y: upperCi });
            }


            linearityChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '偏差数据点',
                        data: biases.map(b => ({ x: b.ref, y: b.bias })),
                        backgroundColor: 'rgba(0, 123, 255, 0.6)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 1,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    },
                    {
                        label: '线性回归线',
                        data: regressionLineData,
                        type: 'line',
                        fill: false,
                        borderColor: 'rgba(255, 0, 0, 1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        showLine: true,
                        tension: 0 // Draw straight lines
                    },
                    {
                        label: '95% 置信区间 (下限)',
                        data: lowerCiData,
                        type: 'line',
                        fill: '+1', // Fill area between this dataset and the next one
                        borderColor: 'rgba(0, 128, 0, 0.5)',
                        backgroundColor: 'rgba(0, 128, 0, 0.1)', // Shaded area color
                        borderWidth: 1,
                        pointRadius: 0,
                        showLine: true,
                         tension: 0, // Draw straight lines
                         hidden: degreesOfFreedom <= 0 || sumSquaredDifferencesOfRefs <= 0 // Hide if CI cannot be calculated
                    },
                     {
                        label: '95% 置信区间 (上限)',
                        data: upperCiData,
                        type: 'line',
                        borderColor: 'rgba(0, 128, 0, 0.5)',
                        borderWidth: 1,
                        pointRadius: 0,
                        showLine: true,
                         tension: 0, // Draw straight lines
                         hidden: degreesOfFreedom <= 0 || sumSquaredDifferencesOfRefs <= 0 // Hide if CI cannot be calculated
                    },
                     {
                         label: '零线',
                         data: [{ x: minRef, y: 0 }, { x: maxRef, y: 0 }],
                         type: 'line',
                         borderColor: 'rgba(0, 0, 0, 0.5)',
                         borderDash: [5, 5],
                         borderWidth: 1,
                         pointRadius: 0,
                         showLine: true,
                         tension: 0
                     }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: {
                                display: true,
                                text: '参考值'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '偏差'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.dataset.type === 'scatter') {
                                         label += `(参考值: ${context.parsed.x.toFixed(2)}, 偏差: ${context.parsed.y.toFixed(4)})`;
                                    } else {
                                         label += `(参考值: ${context.parsed.x.toFixed(2)}, 值: ${context.parsed.y.toFixed(4)})`;
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }

         function renderDataDistributionChart(referenceValues, measurementData) {
             const ctx = document.getElementById('dataDistributionChart').getContext('2d');
             if (dataDistributionChart) dataDistributionChart.destroy();

             const datasets = [];
             // Ensure referenceValues are used as labels for scatter plot X-axis
             const uniqueReferenceValues = [...new Set(referenceValues)].sort((a, b) => a - b);


             for(let i = 0; i < referenceValues.length; i++) {
                 const ref = referenceValues[i];
                 // Filter out null or NaN measurements for this part
                 const validMeasurements = measurementData[i].filter(val => val !== null && !isNaN(val));

                 const data = validMeasurements.map(measurement => ({ x: ref, y: measurement }));
                 datasets.push({
                     label: `零件 ${i+1} (参考值: ${ref})`,
                     data: data,
                     backgroundColor: `hsl(${i * 60 % 360}, 70%, 50%)`, // Use HSL for distinct colors, cycle hue
                     borderColor: `hsl(${i * 60 % 360}, 70%, 50%)`,
                     pointRadius: 5,
                     pointHoverRadius: 7,
                     showLine: false // Don't connect points
                 });
             }


             dataDistributionChart = new Chart(ctx, {
                 type: 'scatter',
                 data: {
                     datasets: datasets
                 },
                 options: {
                     responsive: true,
                     scales: {
                         x: {
                             type: 'linear',
                             position: 'bottom',
                             title: {
                                 display: true,
                                 text: '参考值'
                             },
                             ticks: {
                                 // Display ticks only at the unique reference values
                                 callback: function(value, index, values) {
                                     if (uniqueReferenceValues.includes(value)) {
                                         return value;
                                     }
                                     return '';
                                 }
                             }
                         },
                         y: {
                             title: {
                                 display: true,
                                 text: '测量值'
                             }
                         }
                     },
                      plugins: {
                         tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                     label += `(参考值: ${context.parsed.x.toFixed(2)}, 测量值: ${context.parsed.y.toFixed(4)})`;
                                    return label;
                                }
                            }
                        }
                      }
                 }
             });
         }


         function toggleInstructions() {
             const instructionsContent = document.getElementById('instructionsContent');
             const button = document.querySelector('.section h2 button');
             if (instructionsContent.classList.contains('hidden')) {
                 instructionsContent.classList.remove('hidden');
                 button.textContent = '隐藏说明';
             } else {
                 instructionsContent.classList.add('hidden');
                 button.textContent = '显示/隐藏 说明';
             }
         }

         function exportData(format) {
            const numParts = parseInt(document.getElementById('numParts').value);
            const numMeasurements = parseInt(document.getElementById('numMeasurements').value);
            const referenceValuesInput = document.getElementById('referenceValues').value;
            const referenceValues = referenceValuesInput.split(',').map(val => parseFloat(val.trim()));

             if (measurementData.length === 0 || measurementData.some(row => row.some(val => val === null || isNaN(val)))) {
                 alert('没有完整的测量数据可以导出。');
                 return;
             }


            let fileContent = "";
            let fileName = `MSA_Linear_Analysis.${format}`;

            if (format === 'excel') {
                // Export as CSV
                let csvContent = "零件号,参考值";
                for (let i = 1; i <= numMeasurements; i++) {
                    csvContent += `,测量 ${i}`;
                }
                csvContent += "\n";

                for (let i = 0; i < numParts; i++) {
                    csvContent += `${i + 1},${referenceValues[i] !== undefined && !isNaN(referenceValues[i]) ? referenceValues[i] : ''}`;
                    for (let j = 0; j < numMeasurements; j++) {
                        csvContent += `,${measurementData[i][j] !== null && !isNaN(measurementData[i][j]) ? measurementData[i][j] : ''}`;
                    }
                    csvContent += "\n";
                }

                 // Add results to CSV (simple text representation)
                 csvContent += "\n\n分析结果:\n";
                 csvContent += document.getElementById('overallBiasResult').textContent.replace(/, /g, '; ') + "\n"; // Replace comma in text to avoid breaking CSV
                 csvContent += document.getElementById('repeatabilityResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('percentEVResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('linearitySlopeResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('linearityInterceptResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('linearityPValueResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('biasAtZeroPValueResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('rSquaredResult').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('repeatabilityJudgment').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('linearityJudgment').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('biasJudgment').textContent.replace(/, /g, '; ') + "\n";
                 csvContent += document.getElementById('overallJudgment').textContent.replace(/, /g, '; ') + "\n";


                fileContent = csvContent;
                fileName = "MSA_Linear_Analysis.csv";
                downloadFile(fileContent, fileName, 'text/csv;charset=utf-8;');

            } else if (format === 'markdown') {
                // Export as Markdown
                let mdContent = "# MSA 线性分析结果\n\n";

                mdContent += "## 数据输入\n";
                mdContent += `- 零件个数: ${numParts}\n`;
                mdContent += `- 测量次数: ${numMeasurements}\n`;
                mdContent += `- 参考值: ${referenceValuesInput}\n`;
                mdContent += `- 过程变差: ${processVariation}\n\n`;

                mdContent += "## 测量数据\n";
                mdContent += "| 零件号 | 参考值 |";
                for (let i = 1; i <= numMeasurements; i++) {
                    mdContent += ` 测量 ${i} |`;
                }
                mdContent += "\n|---|---|";
                 for (let i = 1; i <= numMeasurements; i++) {
                    mdContent += `---|`;
                }
                mdContent += "\n";

                for (let i = 0; i < numParts; i++) {
                    mdContent += `| ${i + 1} | ${referenceValues[i] !== undefined && !isNaN(referenceValues[i]) ? referenceValues[i].toFixed(2) : ''} |`;
                    for (let j = 0; j < numMeasurements; j++) {
                         mdContent += ` ${measurementData[i][j] !== null && !isNaN(measurementData[i][j]) ? measurementData[i][j] : ''} |`;
                    }
                    mdContent += "\n";
                }
                mdContent += "\n";


                 mdContent += "## 分析结果\n";
                 mdContent += `- ${document.getElementById('overallBiasResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('repeatabilityResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('percentEVResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('linearitySlopeResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('linearityInterceptResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('linearityPValueResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('biasAtZeroPValueResult').textContent}\n`;
                 mdContent += `- ${document.getElementById('rSquaredResult').textContent}\n\n`;

                 mdContent += "### 判定结果\n";
                 mdContent += `- ${document.getElementById('repeatabilityJudgment').textContent}\n`;
                 mdContent += `- ${document.getElementById('linearityJudgment').textContent}\n`;
                 mdContent += `- ${document.getElementById('biasJudgment').textContent}\n`;
                 mdContent += `- ${document.getElementById('overallJudgment').textContent}\n\n`;

                 mdContent += "## 分析图表\n";
                 mdContent += "（图表需要单独导出图片）\n";
                 // You could potentially add markdown image links here if you export charts as images


                fileContent = mdContent;
                fileName = "MSA_Linear_Analysis.md";
                 downloadFile(fileContent, fileName, 'text/markdown;charset=utf-8;');


            } else if (format === 'pdf' || format === 'word') {
                alert(`导出到 ${format.toUpperCase()} 功能需要更复杂的库或服务器端处理，未在此实现。`);
                return;
            } else {
                alert("不支持的导出格式。");
                return;
            }

        }

        // Helper function to trigger file download
        function downloadFile(content, fileName, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }


        // Initial table update on page load
        window.onload = updateMeasurementTable;

    </script>
</body>
</html>